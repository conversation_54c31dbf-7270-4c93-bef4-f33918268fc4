<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AiEduForge系统架构图</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        h1 {
            margin-bottom: 5px;
        }
        h2 {
            font-size: 1.2em;
            color: #666;
            margin-top: 0;
            margin-bottom: 30px;
        }
        .architecture {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .layer {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }
        .component {
            flex: 1;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            color: #333;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 80px;
        }
        .component h3 {
            margin-top: 0;
            margin-bottom: 5px;
        }
        .component p {
            margin: 0;
            font-size: 0.9em;
        }
        .frontend {
            background-color: #a5d8ff;
            border: 2px solid #1971c2;
        }
        .backend {
            background-color: #b2f2bb;
            border: 2px solid #2f9e44;
        }
        .ai-service {
            background-color: #e8d9f4;
            border: 2px solid #9c36b5;
        }
        .database {
            background-color: #ffc9c9;
            border: 2px solid #e03131;
        }
        .websocket {
            background-color: #ffd8a8;
            border: 2px solid #e67700;
        }
        .file-storage {
            background-color: #d0bfff;
            border: 2px solid #5f3dc4;
        }
        .arrow {
            display: flex;
            justify-content: center;
            padding: 10px 0;
        }
        .arrow svg {
            width: 30px;
            height: 30px;
        }
        .modules {
            margin-top: 30px;
            border: 2px solid #333;
            border-radius: 6px;
            padding: 15px;
        }
        .modules h3 {
            text-align: center;
            margin-top: 0;
        }
        .module-row {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-top: 15px;
        }
        .module {
            flex: 1;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 0.9em;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .auth {
            background-color: #ffec99;
            border: 1px solid #f08c00;
        }
        .content {
            background-color: #b2f2bb;
            border: 1px solid #2f9e44;
        }
        .ai {
            background-color: #a5d8ff;
            border: 1px solid #1971c2;
        }
        .analytics {
            background-color: #ffc9c9;
            border: 1px solid #e03131;
        }
        .notification {
            background-color: #e8d9f4;
            border: 1px solid #9c36b5;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AiEduForge 系统架构图</h1>
        <h2>基于开源AI大模型的教学实训智能体软件</h2>
        
        <div class="architecture">
            <!-- 前端、后端、AI服务层 -->
            <div class="layer">
                <div class="component frontend">
                    <h3>前端</h3>
                    <p>Vue 3 + Element Plus</p>
                    <p>Vite + Pinia</p>
                </div>
                <div class="component backend">
                    <h3>后端</h3>
                    <p>Spring Boot 3.5.3</p>
                    <p>Spring Security + JWT</p>
                </div>
                <div class="component ai-service">
                    <h3>AI服务</h3>
                    <p>OpenRouter API</p>
                    <p>Google Gemma 3 模型</p>
                </div>
            </div>
            
            <!-- 箭头 -->
            <div class="layer">
                <div class="arrow">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <polyline points="19 12 12 19 5 12"></polyline>
                    </svg>
                </div>
                <div class="arrow">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <polyline points="19 12 12 19 5 12"></polyline>
                    </svg>
                </div>
                <div class="arrow">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <polyline points="19 12 12 19 5 12"></polyline>
                    </svg>
                </div>
            </div>
            
            <!-- 数据库层 -->
            <div class="layer">
                <div class="component websocket">
                    <h3>WebSocket通知系统</h3>
                    <p>实时消息推送</p>
                </div>
                <div class="component database">
                    <h3>数据库</h3>
                    <p>MySQL 8.0</p>
                    <p>MyBatis Plus</p>
                </div>
                <div class="component file-storage">
                    <h3>文件存储系统</h3>
                    <p>教学资源管理</p>
                </div>
            </div>
            
            <!-- 核心功能模块 -->
            <div class="modules">
                <h3>核心功能模块</h3>
                <div class="module-row">
                    <div class="module auth">
                        <h4>用户认证</h4>
                        <p>JWT Token认证</p>
                        <p>角色权限管理</p>
                    </div>
                    <div class="module content">
                        <h4>教学内容管理</h4>
                        <p>课程管理</p>
                        <p>教学内容编辑</p>
                    </div>
                    <div class="module ai">
                        <h4>AI智能辅助</h4>
                        <p>智能内容生成</p>
                        <p>自动题目生成</p>
                    </div>
                    <div class="module analytics">
                        <h4>学习分析</h4>
                        <p>学习行为分析</p>
                        <p>教学效果统计</p>
                    </div>
                    <div class="module notification">
                        <h4>通知系统</h4>
                        <p>实时通知推送</p>
                        <p>通知分类管理</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 AiEduForge - 让AI赋能教育，让学习更智能更高效</p>
        </div>
    </div>
</body>
</html>""