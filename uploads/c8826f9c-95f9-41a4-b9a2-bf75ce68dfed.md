# 【西北大学】844计算机科学与技术/软件工程-复试





# C++程序设计系列

## 第一章 [面向对象](https://so.csdn.net/so/search?q=面向对象&spm=1001.2101.3001.7020)基础

### 1.1 经典的程序设计的泛型有哪些，各自有什么特点？

泛型分为:
1、过程式程序设计。
2、基于对象的程序设计。
3、面向对象的程序设计。
4、泛型程序设计。

### 1.2 面向对象方法有哪些特征？

1、任何事物都可以看作成对象。
2、面向对象的程序就是一组对象。
3、每个对象都有自己的存储区和唯一标识与其他对象区分。
4、每个对象都有自己的类型。
5、属于特定类型的所有对象具有相同的类型并且能收到相同的信息。

### 1.3 什么是封装？什么是信息隐藏和实现隐藏？

封装是将一组相关的概念聚集在一个单元内并且用单独的一个名字来引用。信息隐藏是指外部不能看到对象内部的信息，实现隐藏是指不能从外部看到对象的实现细节。

### 1.4 什么是对象？什么是类？对象和类有什么关系？

为了描述一组对象在结构和行为上的共性可以创造抽象数据类型称之为类，类是创建对象的模板。

### 1.5 什么是继承？继承层次中子类和父类有什么关系？子类如何区别父类？

继承是在已有类的基础上定义新类，这个新类自动拥有已有类型的属性和操作，并且可以增加特有的功能或者修改所得到的功能。

### 1.6 什么是多态性？

是指一个操作名字或属性名字可以在多个类中定义并且在各个类中有不同的实现。

### 2.1 经典的程序设计的泛型有哪些，各自有什么特点？

泛型分为：
**过程式程序设计：**
特点：以过程和算法为中心，程序由一组函数或过程组成，强调任务的分解。
代表语言：C、Pascal。

**基于对象的程序设计：**
特点：引入对象概念，但没有全面支持面向对象的特性（如继承、多态）。
代表语言：早期的C++、JavaScript的面向对象特性。

**面向对象的程序设计：**
特点：以对象为中心，支持封装、继承和多态，强调对象的交互和抽象。
代表语言：C++、Java、Python。

**泛型程序设计：**
特点：通过模板或泛型机制实现类型无关的编程，减少代码重复，提高复用性。
代表语言：C++的模板，Java和C#的泛型。

### 2.2 面向对象方法有哪些特征？

任何事物都可以看作对象：

- 所有实体（如数据、行为）都抽象为对象。

面向对象的程序是一组对象的集合：

- 程序的核心是对象之间的交互，而非单独的过程或数据。

对象具有存储区和唯一标识：

- 每个对象在内存中占有独立的存储空间，并通过标识区分其他对象。

对象具有类型：

- 每个对象隶属于一个类，类定义了对象的属性和行为。

同一类的对象具有相同特性：

- 相同类的对象共享行为定义，但拥有独立的状态。

### 2.3 什么是封装？什么是信息隐藏和实现隐藏？

**封装：**
将数据（属性）和操作（方法）结合到一个单元中，以实现对数据的保护和逻辑的一致性。
**信息隐藏：**
对象的内部状态和实现细节对外部不可见，外界只能通过提供的接口访问对象。
**实现隐藏：**
对外隐藏具体的实现方式，使得外界无法直接操作对象的实现逻辑，从而提供抽象性和灵活性。

### 2.4 什么是对象？什么是类？对象和类有什么关系？

**对象：**
是程序运行时内存中的实例，包含数据（状态）和操作（行为）。
比如：张三是一个学生对象。

**类：**
是对象的抽象描述，定义了对象的属性和行为，是对象的模板。
比如：学生类是描述学生对象的抽象模型。

**关系：**
类是对象的定义或蓝图，对象是类的具体实例。
比如：学生类定义了学生的特征和行为，而张三是学生类的一个实例。

### 2.5 什么是继承？继承层次中子类和父类有什么关系？子类如何区别父类？

**继承：**
子类通过继承获取父类的属性和方法，可以扩展或重写这些行为。

**子类和父类的关系：**
子类是父类的一种特殊化。子类继承了父类的结构和行为，同时可以拥有自己的独特特性。

**子类如何区别父类：**
子类可以重写父类的方法以实现自己的行为（多态）。
子类可以扩展新的属性和方法，而这些在父类中不存在。

### 2.6 什么是多态性？

**多态性：**

- **定义**：同一个操作在不同类中具有不同的实现形式。
- **实现**：
- - 重载：同一类中同名方法参数不同。
- - 重写：子类重新定义父类的方法行为。
- **作用**：
- - 提高程序的灵活性和可扩展性。
- - 允许通过父类引用调用子类的具体实现。

## 第四章 函数 函数和类和对象

### 4.1 说明带参数的宏与内联函数有什么不同

1、编译器处理方式不同：带参数的宏是在预处理阶段进行处理，它会将所有使用该宏的地方直接替换成宏定义中的代码，并且没有任何类型检查和错误检查。而内联函数是在编译阶段进行处理，编译器会将内联函数的代码插入到函数调用的地方，可以进行类型检查和错误检查。
2、参数求值次数不同：带参数的宏在被调用时，宏定义中的代码会被简单地文本替换，因此宏参数的求值次数可能比较多，这可能会导致不符合预期的行为。而内联函数在被调用时，参数的求值次数与普通函数相同。
3、代码生成方式不同：带参数的宏在被处理时，每次都会生成新的代码，这可能会导致生成的代码量较大。而内联函数在编译时被处理成了一份代码，可以被多次使用，这有助于减小代码量，提高代码的可读性和可维护性。
4、调试方式不同：带参数的宏在调试时可能会产生困难，因为调试器看到的是宏定义中的代码，而不是调用宏时的代码。而内联函数则可以通过调试器进行单步调试，从而更容易地进行调试。

### 4.2 全局变量和局部变量有什么区别是怎么实现的操作系统和编译器是怎么知道？

操作系统和编译器通过符号表（symbol table）来识别和管理变量。
1、作用域不同：全局变量在整个程序中都可以访问，而局部变量只在它所在的代码块可以访问。
2、存储位置不同：全局变量存储在程序的数据段中，而局部变量通常存储在栈或寄存器中。
3、生命周期不同：全局变量的生命周期与程序的运行时间一致，而局部变量的生命周期只存在于其所在的代码块中，在离开该代码块时会被销毁。

## 第五章 类和对象

### 5.1 C中的Class和C++的 struct有什么区别

默认访问权限：在 C++ 中，class 的默认访问权限是私有的，而 struct 的默认访问权限是公有的。
继承：class 支持私有继承、公有继承和保护继承，而 struct 只支持公有和保护继承。
模板参数限制：class 作为模板参数时可以是任何类型，而 struct 只能作为模板参数用于构建类型。这是因为 Class 可以包含成员函数和成员类型，而 struct 只能包含成员变量和成员类型。

### 5.2 数组与链表有什么区别各有什么优缺点

内存分配方式：数组在内存中是连续的一段空间，而链表中的节点可以存储在任意位置，通过指针链接。
访问元素的方式：数组可以通过下标直接访问元素。而链表需要从头节点开始顺序遍历。
插入和删除元素的效率：数组在插入和删除元素时需要移动其他元素，时间复杂度为O(n)。而链表在插入和删除元素时只需要修改指针，时间复杂度为O(1)。
优缺点：数组的优点是访问元素快速，而链表的优点是插入和删除元素快速。在需要频繁访问元素但不需要频繁插入和删除元素时，数组是一个更好的选择。而在需要频繁插入和删除元素但不需要频繁访问元素时，链表是更好的选择。此外，链表可以支持动态分配内存，而数组在声明时需要确定大小。

### 5.3 简述成员函数全局函数和友元函数这些概念的异同

成员函数是一个属于类的函数，它可以访问类的私有成员变量和保护成员变量。成员函数可以被类的对象调用，并且可以重载。
全局函数是一个在任何类或命名空间之外定义的函数。全局函数不属于任何类，不能访问类的私有成员变量和保护成员变量。全局函数也可以重载。
友元函数是一个定义在类外部但是有权访问类私有成员的非成员函数。友元函数必须在类定义中进行声明，并且可以访问类的所有私有成员变量和保护成员变量。友元函数也可以重载。
异同点：
定义位置：成员函数定义在类的内部，全局函数和友元函数定义在类的外部。
访问权限：成员函数可以访问类的私有成员变量和保护成员变量，而全局函数和友元函数不能访问类的私有成员变量和保护成员变量。
调用方式：成员函数必须通过类的对象调用，而全局函数和友元函数可以直接调用或通过对象调用。
重载：成员函数、全局函数和友元函数都可以重载。
归属关系：成员函数属于类，而全局函数和友元函数不属于任何类。
作用范围：成员函数的作用范围限定于类的对象，而全局函数和友元函数的作用范围不受限制。

### 5.4 完整举例const限定符的作用，跟C语言的const有什么区别

在C语言中，用const修饰的变量，其本质上还是个变量，可以通过指针来修改该变量的值。在C++中 ，在编译的时候，当碰到用const修饰的变量时，编译器是直接将变量的值和变量的符号对应起来一起存到符号表中。不会为const变量分配空间。在编译的时候如果前面有extern和取地址符 & 时，会为变量分配存储空间是为了兼容C语言

### 5.5 完整举例static关键字的作用，跟C语言Java中的static有什么区别？

在C语言中，static关键字可以用于函数和变量，用于函数时表示该函数只能在当前文件中被调用，用于变量时表示该变量只能在当前文件中被访问。在C++中，static关键字可以用于类的成员变量和成员函数，用于成员变量时表示该变量是类的静态成员，所有对象共享该变量，用于成员函数时表示该函数是类的静态成员函数，可以直接通过类名调用。

### 5.6 构造函数是否有返回值类型？

构造函数没有返回值类型。

### 5.7 什么是拷贝构造函数？在什么情况下被调用？

拷贝构造函数是一种特殊的构造函数，函数的名称必须和类名称一致，它的唯一的一个参数是本类型的一个引用变量，该参数是const类型，不可变的。例如：类X的拷贝构造函数的形式为X(const X & x)。
调用情况：
1、当用类的一个对象去初始化该类的另一个对象的时候。
2、如果函数的形参是类的对象，调用此函数的时候，进行形参和实参相结合时，拷贝构造函数被调用。
3、如果函数的返回值是类的一个对象，函数执行完成返回调用者时，会建立一个匿名对象，在对匿名对象进行拷贝构造。

### 5.8 什么是深拷贝？什么是浅拷贝？他们有什么区别？

浅拷贝：当编写一个类时，并且没有添加拷贝构造，此时系统会默认添加一个拷贝构造（浅拷贝）。浅拷贝是指：创建一个新对象时，把对象的初始属性都复制一份，若是存在引用类型，则拷贝的是其内存地址，当它的值发生改变时，另一个的值也会受到改变。若存在析构函数，可能会出现析构多次的情况。
深拷贝：深拷贝就是将对象从内存中完全拷贝出来，并且重新开辟一片空间来进行存放，当其中一个值发生改变时并不会影响另一个的值，两者互不影响。

### 5.9 头文件中的infdef/deifine/endif有什么区别？

当多个文件中同时包含相同的头文件，将头文件放到`#ifndef/#deifne #endif`中防止大量冲突。`#ifndef <标识> #define <标识> … … #endif`

### 5.10 什么时候友元是有用的？说明使用友元的优缺点

友元是一种定义在类外部的普通函数，但它需要在类体内进行声明，为了与该类的成员函数加以区别，在声明时前面加以关键字friend。友元不是成员函数，但是它可以访问类中的私有成员。
`friend ostream& operator<<(ostream& _cout, const Date& d);`
友元的作用在于提高程序的运行效率，但是，它破坏了类的封装性和隐藏性，使得非成员函数可以访问类的私有成员。

## 第六章 运算符重载

### 6.1 何时需要定义赋值运算符

如果一个类需要进行赋值操作，就需要定义赋值运算符。

```cpp
class MyClass {
public:
    MyClass& operator=(const MyClass& other) { // 实现赋值操作的代码
        return *this;
    }
};
123456
```

对于含有指针成员的类来说，使用默认的赋值运算符可能会导致指针的浅拷贝问题。在这种情况下，需要自定义赋值运算符，以确保指针的深拷贝，避免出现潜在的内存泄漏和错误。

### 6.2 举例说出你所了解的必须以类成员方式定义的运算符

赋值运算符（=）、下标运算符（[]）、函数调用运算符（()）、成员访问运算符（->）。这些运算符必须被定义为成员函数的原因是它们需要访问类的私有成员，只有成员函数才能访问类的私有成员。另外，它们的左操作数都是类的对象，因此只有成员函数才能访问对象的成员。

### 6.3 C++编译器为类自动产生的四个默认成员函数分别是什么

默认成员函数包括默认构造函数、析构函数、拷贝构造函数和拷贝赋值运算符。

```cpp
Mystring(const char* str=nullptr);			   
~Mystring();							   
Mystring(const Mystring& other);			   	// 要进行深拷贝
Mystring& operator=(const Mystring& other);  	//重载操作符
1234
```

## 第七章 包含与继承

### 7.1 包含和继承是[面向对象](https://so.csdn.net/so/search?q=面向对象&spm=1001.2101.3001.7020)语言提供的两种重要的代码复用机制阐述它们有什么区别如何选择应用

包含是指在一个类中嵌入另一个类的对象，通过调用该对象的方法来实现功能复用。这种方式可以实现代码的模块化和灵活性，但需要手动管理对象的生命周期和内存分配。
继承是指一个类可以从另一个类继承属性和方法，从而实现代码的复用和扩展。这种方式可以减少代码的重复，提高代码的可维护性和可扩展性，但也可能导致代码的耦合度过高，难以维护和扩展。
在选择应用时，需要根据具体的需求和设计原则进行权衡。一般来说，如果需要实现简单的功能复用，可以选择包含；如果需要实现复杂的继承关系和代码复用，可以选择继承。

### 7.2 继承等级是什么？

公有继承（public inheritance）：派生类可以访问基类的公有成员和保护成员，但不能访问基类的私有成员。
保护继承（protected inheritance）：派生类可以访问基类的公有成员和保护成员，但不能访问基类的私有成员。
私有继承（private inheritance）：派生类可以访问基类的公有成员和保护成员，但不能访问基类的私有成员。
虚拟继承（virtual inheritance）：用于解决多重继承带来的问题，将同一基类的多个派生类共享同一个基类子对象，避免多次复制基类数据成员。防止菱形继承。class A; class B::virtual public A{}; class C::virtual public A{};
只要是父类中private成员无论以什么方式继承，儿子都无法访问；如果是公有继承儿子中的访问权限不变；如果是保护继承，儿子中父亲除了private成员其余均为protected成员；如果是私有继承，均为private成员。
子类不会继承父类的构造函数、析构函数和赋值操作符

### 7.3 继承中的类的赋值兼容原则

1、子类对象可以当作父类的对象使用（子类是特殊的父类）
2、子类对象可以直接赋值父类对象
3、子类对象可以直接初始化父类对象
4、父类指针可以直接指向子类对象
5、父类引用可以直接引用子类对象

## 第八章 虚函数和多态性

### 8.1 什么是虚函数？什么是纯虚函数？为什么引入虚函数和纯虚函数？

被 virtual 关键字修饰的成员函数称为虚函数。在虚函数后面添加 =0 ，虚函数就成为纯虚函数。纯虚函数只是一个接口，它的具体实现需要由子类来完成，子类必须实现纯虚函数，否则子类也会变成抽象类。
引入纯虚函数可以将基类的某些函数强制为虚函数，并且必须被子类实现。引入虚函数和纯虚函数的目的在于实现多态性。通过定义虚函数和纯虚函数，可以使得子类可以覆盖基类中的函数，从而实现子类对于函数的重写和扩展。而通过运行时动态绑定的方式，可以根据对象的实际类型来确定调用相应的函数，从而实现多态性。

### 8.2 析构函数应该是虚函数吗？为什么？

当一个类被继承时，如果其析构函数不是虚函数，那么在使用基类指针或引用指向派生类对象并删除这个对象时，可能会导致只有基类部分被析构，派生类部分的资源没有被正确地释放，从而导致内存泄漏或者其它的错误。这是因为在这种情况下，编译器只会调用基类的析构函数，而不会调用派生类的析构函数，因为指针或引用的静态类型是基类类型，因此需要使用虚析构函数来确保正确地释放资源。
总之，如果一个类可能会被继承，那么就应该将其析构函数声明为虚函数，以确保正确释放派生类的资源。

### 8.3 为什么默认的析构函数不是虚函数？

当类中有虚成员函数时，类会自动进行一些额外工作。这些额外的工作包括生成虚函数表和虚表指针，虚表指针指向虚函数表。每个类都有自己的虚函数表，虚函数表的作用就是保存本类中虚函数的地址，我们可以把虚函数表形象地看成一个数组，这个数组的每个元素存放的就是各个虚函数的地址。当我们创建一个类时，系统默认我们不会将该类作为基类，所以就将默认的析构函数定义成非虚函数，这样就不会占用额外的内存空间。同时，系统也相信程序开发者在定义一个基类时，会显示地将基类的析构函数定义成虚函数，此时该类才会维护虚函数表和虚表指针。

## 第九章 模板

### 9.1 模板有什么特点？什么时候使用？

泛型性：模板是一种泛型编程技术，它可以让我们编写与数据类型无关的代码，实现一次编写、多次使用的目的。
灵活性：模板提供了灵活的参数化方式，可以在不同的上下文中使用不同的数据类型，同时也可以使用不同的算法和数据结构。
可重用性：模板可以用于定义通用的类和函数，可以适用于不同的数据类型和算法，从而实现代码的重用。
高效性：模板编译时生成代码，不会引入额外的运行时开销，因此模板可以产生高效的代码。
适用情况：
当需要编写可以适用于多种数据类型的算法或数据结构时，可以使用模板来实现泛型编程。
当需要编写通用的库或框架时，可以使用模板来实现通用性和可重用性。
当需要实现类型安全的代码时，可以使用模板来进行参数化，并在编译期间进行类型检查。
当需要实现高效的代码时，可以使用模板来生成高效的代码，并避免运行时的开销。

### 9.2 定义模板参数时使用typename与使用class有什么区别吗

使用 typename 或 class 来定义模板参数是等价的，只是语法上略有不同，可以根据个人习惯来选择。在模板参数列表中使用 typename 更符合直觉，因为模板参数往往代表一个类型，而不是一个类。

```c++
template class MyClass {};
template typename myname {};
```



## 第十一章 输入/输出流

### 11.1输入输出

`cin>>` 输入操作符会丢弃前导空白读取数据 遇到空白时停止读入，要输入空白采用`cin.get()`
`oct` 八进制 `hex` 十六进制 `dec` 十进制

## 第十二章 标准模板库

### 12.1 读取存放string对象的list容器可以使用什么迭代器类型

采用正向迭代器和双向迭代器。

```cpp
std::list<std::string> my_list {"hello", "world", "!"};
for (auto it = my_list.begin(); it != my_list.end(); ++it) {
    std::cout << *it << " ";
}// 输出：hello world !
for (auto it = my_list.rbegin(); it != my_list.rend(); ++it) {
    std::cout << *it << " ";
}// 输出：! world hello
1234567
```

其中，正向迭代器和双向迭代器都是const迭代器的子类，因此也可以用于读取list中的元素。

### 12.2 为什么不可以使用容器来存储iostream对象

因为容器元素类型必须支持赋值操作及复制,而iostream类型不支持赋值和复制。

### 12.3 C++标准库为vector对象提供的内存分配策略是什么

C++标准库为vector对象提供了一种动态数组的实现，使用连续的内存来存储元素，而内存的分配由allocator完成。vector对象的内存分配策略可以概括为以下几个步骤：
vector对象的内存空间在创建时是空的，只有在添加元素时才会动态分配内存。
当vector对象需要增加元素时，首先会检查当前的内存空间是否足够，如果不够，则会向操作系统请求更多的内存。vector使用一个大小为N的缓冲区来存储元素，当缓冲区已满时，会重新分配一个更大的缓冲区。
重新分配缓冲区的大小通常是当前缓冲区的两倍，这个策略可以有效减少内存分配的次数，提高vector的性能。重新分配缓冲区后，会将原来缓冲区中的元素复制到新的缓冲区中。
如果vector对象的元素数量减少，而剩余的内存空间较多，则可以通过调用vector的shrink_to_fit()函数来释放多余的内存空间，减少内存占用。

### 12.4 说明容器vector、deque和list有什么区别

内部实现方式：
vector是一个动态数组，使用连续的内存来存储元素；deque是一个双端队列，使用多个连续的缓冲区来存储元素；list是一个双向链表，每个元素都存储指向前后元素的指针。
访问元素的效率：
vector和deque都可以通过下标访问元素，时间复杂度为O(1)；而list只能通过迭代器访问元素，时间复杂度为O(n)，其中n是元素数量。因此，在需要随机访问元素时，vector和deque比list更高效。
插入和删除元素的效率：
在vector中插入或删除元素时，需要将后面的元素依次移动或者复制，时间复杂度为O(n)；而在deque和list中插入或删除元素时，只需要调整前后元素的指针，时间复杂度为O(1)。但是，在deque中插入或删除元素时，需要重新分配缓冲区，可能会导致内存复制的开销。
内存分配和使用效率：
vector和deque都使用动态分配的内存，可以自动调整内存空间大小以适应元素数量的变化；而list使用指针来链接元素，不需要连续的内存空间，但是每个元素需要单独分配内存，可能会造成内存碎片。

## 附录 习题

### 1 第一章-[面向对象](https://so.csdn.net/so/search?q=面向对象&spm=1001.2101.3001.7020)基础-练手题

传送门：
[第一章 面向对象基础](https://blog.csdn.net/qq_45400167/article/details/136384011)

#### 1.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8cfdb72b439743c02db9cf5e56581f9d.png)
面向对象技术最核心的特征是 封装 和 继承。
C++ 是一种混合的[面向对象程序设计](https://so.csdn.net/so/search?q=面向对象程序设计&spm=1001.2101.3001.7020)语言，支持 面向对象和 泛型 编程方法。

### 2 第四/五章-函数和类和对象-练手题

传送门：
[第四/五章 函数和类和对象](https://blog.csdn.net/qq_45400167/article/details/136551912)

#### 2.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/acf87b632d904ca36ba1b3b91a6965e4.png)

```cpp
#include <iostream>
#include <cmath>
#include <sstream>

class Point {
private:
    double x;
    double y;

public:
    // 构造函数
    Point() : x(0.0), y(0.0) {} // 默认构造函数，初始化为原点
    Point(double x_val, double y_val) : x(x_val), y(y_val) {}

    // (3) 计算当前点到原点的距离
    double distanceToOrigin() const {
        return std::sqrt(x * x + y * y);
    }

    // (4) 计算当前点到另一个点的距离
    double distanceTo(const Point& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        return std::sqrt(dx * dx + dy * dy);
    }

    // (5) 获取点的 x, y 坐标值
    double getX() const { return x; }
    double getY() const { return y; }

    // (6) 设置点的 x, y 坐标
    void setX(double x_val) { x = x_val; }
    void setY(double y_val) { y = y_val; }

    // (7) 移动点到新位置
    void move(double dx, double dy) {
        x += dx;
        y += dy;
    }

    // (8) 获取点的字符串表示，格式为 "(x, y)"
    std::string toString() const {
        std::stringstream ss;
        ss << "(" << x << ", " << y << ")";
        return ss.str();
    }
};

int main() {
    Point p1; // 默认构造函数，p1 = (0, 0)
    Point p2(3.0, 4.0); // p2 = (3, 4)

    std::cout << "p1: " << p1.toString() << std::endl;
    std::cout << "p2: " << p2.toString() << std::endl;

    std::cout << "Distance from p2 to origin: " << p2.distanceToOrigin() << std::endl;
    std::cout << "Distance from p1 to p2: " << p1.distanceTo(p2) << std::endl;

    p2.move(1.0, -2.0); // 移动 p2 到 (4, 2)
    std::cout << "p2 after move: " << p2.toString() << std::endl;

    return 0;
}
```

代码解释：

- 成员变量: x 和 y 是 private 的，体现了封装性。
- 构造函数: 提供了默认构造函数（初始化为原点）和带参数的构造函数。
- 方法: 实现了题目要求的所有功能（计算距离、获取坐标、设置坐标、移动、字符串表示）。
- const 关键字: 许多方法都用 const 修饰，表示这些方法不会修改对象的状态。
- 字符串转换: 使用 std::stringstream 来方便地构建字符串。

### 3 第六/七/八章-运算符重载/包含与继承/虚函数和多态性-练手题

传送门：
[第六/七/八章 运算符重载/包含与继承/虚函数和多态性](https://blog.csdn.net/qq_45400167/article/details/136595597)

#### 3.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4c4ea34d3224fbd63f7b843cb99a7d45.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/55a58b7b79315a3725f19e7e39bb478e.png)
这是一个典型的面向对象程序设计题目，要求设计一个订单类 Order，其中包含多个订单行 OrderLine。 下面给出C++的完整代码示例：

```cpp
#include <iostream>
#include <vector>
#include <string>
#include <iomanip> // 用于格式化输出

class OrderLine {
public:
    int productId;    // 商品编号
    std::string name; // 商品名称
    double price;       // 商品单价
    int amount;       // 订购的商品数量

    OrderLine(int id, const std::string& n, double p, int a)
        : productId(id), name(n), price(p), amount(a) {}

    double getSubtotal() const { return price * amount; }

    void display() const {
        std::cout << std::left << std::setw(10) << productId
                  << std::setw(20) << name
                  << std::setw(10) << std::fixed << std::setprecision(2) << price
                  << std::setw(5) << amount
                  << std::setw(10) << std::fixed << std::setprecision(2) << getSubtotal()
                  << std::endl;
    }
};

class Order {
private:
    std::vector<OrderLine> orderLines;

public:
    // (2) 成员函数 addOrderLine(OrderLine) 向订单中添加一行
    void addOrderLine(const OrderLine& line) {
        orderLines.push_back(line);
    }

    // (3) 成员函数 removeOrderLine(int productId) 删除订单中指定编号的商品的订单行
    void removeOrderLine(int productId) {
        for (auto it = orderLines.begin(); it != orderLines.end(); ++it) {
            if (it->productId == productId) {
                orderLines.erase(it);
                return; // 移除一个即可
            }
        }
    }

    // (4) 成员函数 totalPrice() 计算并返回一张订单的总金额
    double totalPrice() const {
        double total = 0.0;
        for (const auto& line : orderLines) {
            total += line.getSubtotal();
        }
        return total;
    }

    // (5) 成员函数 displayOrder() 显示订单，每行显示一种商品的订购信息，最后显示订单总金额
    void displayOrder() const {
        std::cout << "----------------------------------------------------------" << std::endl;
        std::cout << std::left << std::setw(10) << "Product ID"
                  << std::setw(20) << "Name"
                  << std::setw(10) << "Price"
                  << std::setw(5) << "Amount"
                  << std::setw(10) << "Subtotal"
                  << std::endl;
        std::cout << "----------------------------------------------------------" << std::endl;
        for (const auto& line : orderLines) {
            line.display();
        }
        std::cout << "----------------------------------------------------------" << std::endl;
        std::cout << "Total: " << std::fixed << std::setprecision(2) << totalPrice() << std::endl;
    }
};

int main() {
    OrderLine p1(101, "Pen", 9.5, 3);
    OrderLine p2(203, "Cup", 15.98, 2);

    Order order;
    order.addOrderLine(p1);
    order.addOrderLine(p2);

    std::cout << "Initial Order:" << std::endl;
    order.displayOrder();

    std::cout << "\nRemoving product 101..." << std::endl;
    order.removeOrderLine(101);
    order.displayOrder();

    return 0;
}
```

**代码解释**：

1. OrderLine 类：
   - 包含商品编号 (productId)、商品名称 (name)、商品单价 (price) 和订购数量 (amount)。
   - 提供了一个构造函数用于初始化这些成员。
   - getSubtotal() 方法用于计算单个订单行的金额。
   - display() 方法用于格式化输出订单行的信息。
2. Order 类：
   - 使用 std::vector 来存储多个 OrderLine 对象。
   - addOrderLine() 方法用于向订单中添加新的订单行。
   - removeOrderLine() 方法用于根据商品编号删除订单行。 这里用迭代器来处理删除操作，确保在删除元素后迭代器不会失效。
   - totalPrice() 方法用于计算订单的总金额。
   - displayOrder() 方法用于显示订单的详细信息，包括每个订单行的信息和总金额。 使用std::setw 和 std::fixed std::setprecision用于格式化输出，使其更易读。
3. main() 函数：
   - 创建了两个 OrderLine 对象。
   - 创建了一个 Order 对象。
   - 将两个 OrderLine 对象添加到 Order 对象中。
   - 显示初始订单信息。
   - 删除商品编号为 101 的订单行。
   - 再次显示订单信息，验证删除操作。
4. 关键点：
   - 封装性： Order 类封装了订单行的存储和管理，以及计算总金额的逻辑。
   - 数据结构选择： std::vector 是一个动态数组，适合存储不定数量的 OrderLine 对象。
   - 迭代器： 使用迭代器安全地删除 vector 中的元素。
   - 格式化输出： 使用 iomanip 库中的函数来格式化输出，使输出更易读。
   - const 正确性： 对不会修改对象状态的方法使用了 const 关键字。

#### 3.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6b8ff61f7497f254a26f7d04918bef28.png)

**(1) 你设计的 sumArea 函数基于什么技术？是否需要对之前定义的类进行修改？如何修改？**

- 技术： sumArea 函数可以基于多态性 (Polymorphism) 来实现。
- 是否需要修改： 需要修改。 之前的 Rectangle, Circle, Triangle 类应该有一个共同的基类（例如 Shape），并且 area() 函数应该是基类中的虚函数 (virtual function)。
- 如何修改：
  - 定义基类 Shape： 创建一个抽象基类 Shape，其中包含一个纯虚函数 virtual double area() = 0;。
  - 继承基类： 让 Rectangle、Circle 和 Triangle 类继承自 Shape 类，并实现各自的 area() 函数。 这些类必须override area()函数
  - 使用引用或指针： sumArea 函数的参数应该是指向 Shape 对象的指针或引用，而不是具体的类对象。

**(2) 根据你的设计思想，给出 sumArea 函数的完整定义。**

```c
#include <iostream>

// 1. 定义基类 Shape
class Shape {
public:
    // 纯虚函数，强制派生类必须实现
    virtual double area() const = 0;
    // 虚析构函数，防止内存泄漏
    virtual ~Shape() {}
};

// 2. 继承基类 Rectangle
class Rectangle : public Shape {
private:
    double width;
    double height;
public:
    Rectangle(double w, double h) : width(w), height(h) {}
    double area() const override { return width * height; }
};

// 2. 继承基类 Circle
class Circle : public Shape {
private:
    double radius;
public:
    Circle(double r) : radius(r) {}
    double area() const override { return 3.14159 * radius * radius; }
};

// 2. 继承基类 Triangle
class Triangle : public Shape {
private:
    double base;
    double height;
public:
    Triangle(double b, double h) : base(b), height(h) {}
    double area() const override { return 0.5 * base * height; }
};

// 全局函数 sumArea
double sumArea(const Shape* shape1, const Shape* shape2) {
    return shape1->area() + shape2->area();
}

int main() {
    Rectangle rect(5, 10);
    Circle circle(3);
    Triangle triangle(4, 6);

    std::cout << "Rectangle area: " << rect.area() << std::endl;
    std::cout << "Circle area: " << circle.area() << std::endl;
    std::cout << "Triangle area: " << triangle.area() << std::endl;

    Shape* shape1 = ▭
    Shape* shape2 = &circle;
    std::cout << "Sum of Rectangle and Circle areas: " << sumArea(shape1, shape2) << std::endl;

    return 0;
}
```

**代码解释**：

- Shape 类: 定义了纯虚函数 area()，使得 Shape 成为抽象类。任何继承自 Shape 的类都必须实现 area() 函数。
- 虚析构函数:~Shape() 定义为虚函数，这在涉及到继承和多态时非常重要。它可以确保在删除基类指针指向的派生类对象时，调用正确的析构函数，从而防止内存泄漏。
- Rectangle、Circle、Triangle 类: 继承自 Shape 类，并分别实现了 area() 函数，计算各自的面积。 使用 override 关键字表明覆盖基类的虚函数，增加代码可读性和健壮性。
- sumArea 函数: 接收两个 Shape 类的指针，通过指针调用 area() 函数，实现多态性。 由于 area() 是虚函数，所以实际调用的是指针所指向的对象的 area() 函数，而不是 Shape 类的 area() 函数。

**多态性的优点**：

- 代码复用： sumArea 函数可以处理任何 Shape 类型的对象，而不需要为每种图形编写不同的函数。
- 可扩展性： 如果需要添加新的图形类型，只需要继承 Shape 类并实现 area() 函数，就可以直接在 sumArea 函数中使用，而不需要修改 sumArea 函数本身。
- 灵活性： 可以在运行时决定对象的类型，从而实现更灵活的设计。





# 数据库系统概论系列

## 第一章 绪论

### 1.1 什么是数据库？数据库的三个基本特点。

数据库是长期存储在计算机内，有组织，可共享的大量数据的集合。数据库中的数据根据一定的数据模型组织，存储管理，具有较小的冗余度和较高的数据独立性，易扩展性，可大量共享。

数据库的三个基本特点：永久存储，有组织，可共享。

### 1.2 什么是数据库管理系统？（DBMS Database Management System）

数据库管理系统是位于用户和操作系统之间的数据管理软件。和操作系统一样是计算机的基础性软件。

数据库管理系统的功能：
（1）数据定义
（2）数据组织，存储，管理
（3）数据操纵
（4）数据库创建和维护
（5）事务的运行和管理
（6）其他功能{和其他软件通信，异构数据库访问，数据转换}

### 1.3 什么是数据库系统？（DBS）

数据库系统是数据库，数据库管理系统，数据库管理员和应用程序组成的存储，管理，处理，维护数据的系统。
（可以理解为他是一个大范围。DBS=DB+DBMS+DBA+APP）

### 1.4 数据管理系统的三个阶段

第一阶段：人工管理。缺点：没有共享性，冗余度极大。需要人工操作，效率低。
第二阶段：文件管理。缺点：共享性小，冗余度大。独立性差
第三阶段：数据库系统管理。缺点：共享性大，冗余度小。较高独立性，效率高。

### 1.5 数据模型组成

数据模型由数据结构、数据操作，数据的完整性约束三部分组成。
数据结构：数据库的组成对象以及对象之间的联系。
常用的数据模型：层次模型，网状模型，关系模型，面向对象模型。

### 1.6 关系模型优点

(1) 基于严格的数学概念
(2) 数据结构清晰易懂
(3) 独立性高

### 1.7 模式

模式：模式是数据库中全体数据的逻辑结构和特点的描述。
数据库的三级模型：模式，内模式，外模式

模式是全体公共数据视图。一个数据库只有一个模式。
内模式是数据内部存储方式。一个数据库只有一个内模式。外模式是用户视图。一个数据库可以有多个外模式。
从内到外的顺序是：内模式——模式——外模式（1：1：n）

### 1.8 数据库二级映像

数据库二级映像：外模式/模式映像，模式/内模式映像。两个映像实现了模式之间的联系和转换。保证了数据物理独立性和逻辑独立性。

### 1.9 数据库的约束

数据库完整性约束：实体完整性；参照完整性；用户定义完整性。
实体完整性：主属性（主键）不能取空值（不能不存在，无意义）；
参照完整性：外键必须要么取空值，要么等于被参照表元组的主键。
用户定义完整性：根据具体语义要求进行的约束（比如，成绩要求0-100之间）

### 1.10 数据库中的B/S和C/S的区别是什么？

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/5fba1f0426f4b1f8a41c32f8dc596471.png)

## 第二、三章 关系数据库和标准语言SQL

### 2.1 关系

关系代数：传统集合运算：并、差、交、笛卡尔积；
专门关系运算：选择σ（行），投影π（列），连接，除；
等值连接：相同属性不会合并，成为两列。
自然连接：相同属性合并（剔除相同）。
悬浮元组：因为合并而被丢弃的元组。（没有对应的可合并的元组）
外连接：悬浮元组全保留，没有的属性写NULL；
左外连接：保留左关系中的悬浮元组；
右外连接：保留右关系中的悬浮元组

### 2.2 SQL语言的特点

（1） 语句简单，逻辑清晰，综合统一
（2）高度非过程化
（3）面向集合
（4）同一种语法结构提供多种使用方式。

### 2.3 SQL语句总结（别看代码了，这个再看就过一遍注意点）

#### （1） 模式创建和定义：

```sql
create schema “s-t”(模式名) authorization user1（用户名）;
```

可以在该语句后直接创建表/视图/授权。

#### （2） 删除模式：

```sql
drop schema “s-t” cascade/restrict [cascade]
```

cascade级联，删除该模式的同时删除模式下所有数据库对象。
Restrict限制，删除时如果该模式下还有其他数据库对象则拒绝删除]

#### （3） 基本表定义：

```sql
Create table student（
Sno char(9) primary key, //属性名 数据类型 列级约束；
Sname char(20) unique,
Age smallint,
Sdept char(29) //系名
Foreign key (sdept) references dept(dname) //我乱写的，表级完整性约束。
```

主键由多个属性构成，必须用表级完整性约束。Primary key(sno,cno)这样。

#### （4） 显示当前搜索路径：

```sql
show serach_path(用户，模式)
```

管理员设置路径：

```sql
set serach_path to +用户名，模式名
```

#### （5） 修改基本表：

```sql
//属于修改表中的列相关。
Alter table student add s_birth DATE; //增列
Alter table student alter column(这个column可写可不写) age int;//修改列类型
Alter table student drop column s_birth; //删列
Alter table student drop constraint primary key(sno);//删完整性约束
```

#### （6） 删除基本表

(突然发现删除时都有cascade/restrict规定)

```sql
Drop table student cascade;
```

#### （7） 索引：

用于加快查询速度。
顺序文件索引，B+索引，散列（hash）索引，位图索引。
索引由系统自动选择，用户不能选，属于内模式。
索引的SQL语言：

```sql
create unique index(关键字是index) stusno on student(sno);	//对student表的sno建立升序唯一索引stusno
Alter index stusno rename to snoindex;		//对索引改名
Drop index stusno;		//删除，没有cascade和restrict。根本没有和其他的索引之类有联系就不考虑级联。
```

#### （8） 数据查询：

```sql
Select （all / distinct 后者在查询时合并相同结果）列名……From 表名/视图Where 条件Group by 列名 having 条件（常＋聚集函数）Order by 列名 asc/desc （排序）
```

注意事项：
where 和 having 的区别：
1、执行时机不同：where是分组之前进行过滤，不满足where条件不参与分组；having是分组后对结果进行过滤。
2、判断条件不同：where不能对聚合函数进行判断，而having可以。
执行顺序：FROM -> WHERE -> GROUP BY -> SELECT -> ORDER BY -> LIMIT

#### （9） 连接查询：

```sql
等值连接：
Select student.,sc. From student,sc Where student.sno=sc.sno;
```

自然连接则会合并相同列，这时候查询语句就会变成

```sql
Select student.sno,sname,sage,cno From student,sc Where student.sno=sc.sno;
```

注意事项：
1、自然连接一定是等值连接，但等值连接不一定是自然连接。
2、等值连接要求相等的分量，不一定是公共属性；而自然连接要求相等的分量必须是公共属性。3、等值连接不把重复的属性除去；而自然连接要把重复的属性除去。

```sql
外连接
Select student.sno,sname,sage,cno From student left outer join sc on(student.sno=sc.sno)
```

#### （10） 嵌套查询：

不相关子查询：子查询条件不依赖父查询
相关子查询：子查询查询条件依赖父查询
区别：不相关子查询一次查询，相关子查询循环求解。联合查询时UNION ALL 会有重复结果，UNION 不会。
联合查询比使用or效率高，不会使索引失效

#### （11） 数据更新：

插入数据，修改数据，删除记录（这都是对行的操作，区别alter table语句）

```sql
插入
Insert Into student(sno,sname,sex,age) Values(‘20000’,’张三’，‘男‘，18);
```

```sql
更新
Update student Set age=22 Where sno=’20000’;
```

```sql
删除
Delete From student Where sno=’20000’;
```

#### （12） 空值：

判断用 is null 或者 is not null

#### （13） 视图：

视图是一张虚表。只存放视图的定义，不存放其真正的结构。对视图的操作也会转化成对基本表的操作。
创建

```sql
Create view is_student As select sno,sname From stuent Where sdept=’is’With check potion 
//用来保证对视图更新的时候，满足子查询条件
注意事项;
with cascaded check option 级联检查，不管上面开没开检查选项都会检查。
with local check option 本地检查，先在本地检查，再向上检查，上面没开检查选项就不检查。
```

```sql
删除
Drop view is_student cascade;
```

视图消解：对视图进行查询操作的时候，首先进行有效性检查，检查相关定义是否存在，如果存在就在数据字典中取出视图的定义，把定义中的子查询和用户的查询结合起来，转换成对基本表的查询，再执行修正了的查询。
视图和派生表是有区别的。视图永久性，派生表临时。

##### 13.1 视图的作用

（1） 视图能够简化用户的操作
（2） 使用户能以多种角度看待同一数据
（3）视图可以帮助用户屏蔽真实表结构变化带来的影响保证了数据的独立。
（4） 通过视图用户只能查询和修改他们所能看到的数据，保证了数据的安全。

## 4 第四章 数据库安全性

### 4.1 数据库安全性定义

数据库的安全性指的是保护数据库以防止不合法使用造成的数据泄露、更改、破坏。

### 4.2 数据库不安全因素

（1） 非授权用户对数据库的恶意存取和破坏
（2） 敏感数据泄露
（3） 环境脆弱

### 4.3 安全性控制

（1） 用户身份鉴别：静态口令（密码），动态口令（验证码），生物特征（指纹，人脸），智能卡。
（2） 存取控制：自主存取控制：grant,revoke语句。通过对存取权限的设定来进行安全性控制，对数据本身没有安全性标记。
强制存取控制：对数据本身进行密级标记，无论数据怎么复制，标记和数据总是一体的。只有符合密级标记要求的用户才能操纵数据。适用于安全性要求较高的场合。
（3） 视图
（4） 审计：一种事后检查的方式，审计把用户对数据库的所有操作自动记录放在审计日记里。用于安全性检查。

#### 4.3.1 sql控制

Grant语句：

```sql
Grant select on table student To U1 With grant option  //允许U1授权给其他用户
```

REVOKE语句：

```sql
Revoke insert On table sc 
From u1 (cascade/restrict) //cascade 级联收回U1授权出去的所有权限 Restrict 拒绝操作
```

创建数据库用户：

```sql
Create user u1 with DBA/RESOURCE/CONNECT //创建数据库模式的权限在创建数据库用户的时候才授权
```

DBA：所有权限 CONNECT只能登陆查询和操纵，不能创建别的东西 RESOURCE 可以创建表和查询、操纵。不能创建用户和模式 （默认connect）

### 4.4 数据库角色

角色使权限的合集，一般为具有相同权限的用户创建一个角色。
创建

```sql
Create role r1
```

授权 和给用户授权一样，可以给角色授权其他角色和用户：

```sql
Grant 角色1，角色2 To 角色3 With admin option
```

收回权限 和用户一样

## 5 第五章 数据库完整性

### 5.1 数据库完整性

定义：是指数据的正确性和相容性。是为了防止不符合语义的，不正确的数据。
为了为维护数据库的完整性，DBMS必须实现：
(1)完整性约束机制
(2)完整性检查方法
(3)进行违约处理

### 5.2 完整性约束

#### 5.2.1实体完整性（主键）：

列级约束，表级约束。主码唯一，且非空。
全表扫描 `Primary key();`

#### 5.2.2 参照完整性（外键）：

`Foreign key(sno) references student(sno);`
系统保证，表在外部键上的取值要么是父表中某一主键，要么取空值，以此保证两个表之间的连接，确保了实体的参照完整性。

#### 5.2.3 用户定义完整性：

A. 属性上的约束条件 Not null（非空） Unique（唯一） Check语句：`sex char(2) check (sex in (‘男’，‘女‘));` 违约就拒绝执行
B. 元组上的约束条件，可以设置不同属性之间的相互制约 `Check （sex=’女’ or name not like ‘Ms.%’）`

#### 5.2.4 例子

完整性约束命名子句

```sql
Constraint c1 check (sex in (‘男’，‘女‘))； //C1是约束名
Constraint c2 primary key (sno);
```

完整性约束都是在`create table/alter table`的时候写 `Alter table student Add constraint c3 check(age<20)`

### 5.3 断言（更一般性的约束）

//限制数据库最多60个人选

```sql
创建
Create assertion ass_st 
	Check(60>=( 
        Select count(*) 
        From course,scWhere course.cno=sc.cno and cname=’数据库’
    ))
```

删除

```sql
Drop assertion ass_st
```

### 5.4 触发器

定义：触发器是用户定义在关系表上的一类由事件驱动的特殊过程。事件——条件——动作，当事件发生时，检查条件，条件符合就执行。
触发器分为insert，update，delete三种触发器

#### 5.4.1 A. 创建触发器

```sql
Create trigger t1 
Before/after 
select/update/insert ( of sname 指定哪一列) 
	on student Referencing new/old row 
	as R1 (给新的、旧的行取个名字，方便下面操作) 
For each row/statement 
When (条件) 触发体
```

例：

```sql
create trigger t1 After update of grade on sc Referencing Old row as oldtuple New row as newtuple
For each row When (newtuple.grade>=1.1* oldtuple.grade)
Insert into sc_u(sno,cno,oldgrade,newgrade) Values(oldtuple.sno,oldtuple.cno,oldtuple.grade,newtuple.grade)
```

#### 5.4.2 B. 行级触发器

For each row 当触发器事件发生，表有多少行，就执行多少次触发器动作体
语句级触发器
For each statement 当触发器事件发生，只执行一次

#### 5.4.3 C. 删除触发器

```sql
Drop trigger 触发器 on 表名
```

## 第六章 关系数据理论

### 6.1 规范化

函数依赖：
关系r中不可能存在两个元组在X属性上相等，在Y属性上不相等。成为X->Y。Y函数依赖于X。X决定Y。一个学生的学号能决定学生的姓名，也可称姓名属性依赖于学号。学号->学生姓名
函数依赖从性质上分为完全函数依赖、部分函数依赖和传递函数依赖。
完全函数依赖和部分函数依赖：
例，(sno,con)->grade是完全函数依赖 (sno,cno)->sdept是部分函数依赖因为sno->sdept。
传递函数依赖 A->B B->C 所以A->C
候选码：U是R上的属性。U完全函数依赖于K，K为R的候选码
超码: U部份依赖于K，K为U的超码。候选码是最小的超码
候选码多于一个选定一个为主码

### 6.2 范式

#### 1NF 第一范式:

关系中的每一个分量必须是不可分的数据项。规范化：一个低级的范式的关系模型通过模式分解可以转换成若干高一级的范式。

#### 2NF 第二范式：

每一个非主属性完全函数依赖于任何一个候选码，也就是说第二范式是消除了非主属性对码的部份依赖。例如：SLC(sno，sdept，sloc，cno，grade）满足第一范式，不满足第二范式。（sno，cno）->sdept是部分函数以来。要把表拆开。SLC1(sno，sloc，sdept) SLC2(sno，cno，grade)

#### 3NF 第三范式：

每一个非主属性既不传递依赖于码，也不部分依赖于码，第三范式消除了非主属性对码的传递依赖。例如：SLC1(sno，sloc，sdept) 满足第二范式，不满足第三范式。再拆SD(sno，sdept) SL(sno，sloc)

#### BCNF 范式：

每一个决定因素都有码。不允许主键的一部分被另一部分或者其他部分决定。BC范式一定满足第三范式，但是第三范式不一定满足BC范式。例如：（S,J)->T,(S,T)->J T->J 所有属性都是主属性，满足第三范式，（但T不是码，不满足BCNF）
多值依赖 设R(U)是一个属性集合U上的一个关系模式，X, Y, 和Z是U的子集，并且Z=U-X-Y，多值依赖X->->Y成立当且仅当对R的任一个关系r，r在(X,Z)上的每个值对应一组Y的值，这组值仅仅决定于X值而与Z值无关。Z为空时，X->->Y平凡的多值依赖，多值依赖具有对称性，多值依赖具有传递性，X->->Y, Y->->Z则X->->Z-Y

#### 4NF

限制不允许有非平凡非函数依赖的多值依赖

### 6.3 规范化小结

规范化的基本思想：逐步消除数据依赖中不合适的部分，达到某种程度上的分离，即“一事一地”的设计原则。规范化的目的就是为了解决插入异常，删除异常，数据冗余等问题。规范化实质上是概念的单一化。

## 第七章 数据库设计

### 7.1 数据库设计

数据库设计是指对于一个给定的应用环境，设计构造数据库和应用系统使之能够有效地存储和管理数据。满足各种用户地应用需求。
“三分技术，七分管理，十二分基础数据”。重要环节是数据的收集，整理，组织和不断更新。

### 7.2 数据库设计六个阶段

1、需求分析
2、概念结构设计
3、逻辑结构设计
4、物理结构设计
5、数据库实施
6、数据库运行和维护

### 7.3 需求分析主要任务

通过详细调查现实世界要处理的对象，明确用户的各种需求，以此确定系统要实现的功能。
用户的要求：
（1） 信息要求
（2）处理要求
（3）安全性和完整性要求

### 7.4 数据字典

是关于所有数据库数据的描述，即元数据。在需求分析阶段建立，在设计过程中不断修改、完善。在数据库设计过程中有很重要的地位。通常包括数据项、数据结构、数据流、数据存储等。

### 7.5 概念结构设计

将需求分析得到的用户需求抽象为概念模型的过程就是概念结构设计。是整个数据库设计的关键。
概念模型能真实、充分的反映现实世界。易于理解，易于更改，易于向各种数据模型转换。是数据模型地基础。

### 7.6 E-R模型：实体、属性、实体之间的联系。

（1）实体型的联系：一对一 （1：1）；一对多（1：n）;多对多（m:n）
（2）实体用矩形，属性用椭圆形，联系用菱形表示

### 7.7 概念结构设计过程

（1）确定属性和实体，属性必须不能具有描述的性质，不包含其他属性。
（2） 找到属性之间的联系，（一对一之类）
（3） 画E-R图
（4） 大型系统可能会需要集成（先设计子系统的E-R图，然后集成）

### 7.8 E-R图的集成

（就是将子系统得E-R图集成在一起）一般分两步：合并，修改重构
合并需要消除冲突：属性冲突、命名冲突、结构冲突。
1、属性冲突：属性值的类型、取值范围，单位不一致。
2、命名冲突：同名异义，异名同义。
3、结构冲突：同一对象在不同应用中具有不同的抽象。实体联系在不同的关系中为不同的类型。

### 7.9 E-R图向关系模型的转换（就是概念模型转逻辑模型）

一个实体型转换为一个关系模型，关系的属性就是实体的属性，关系的码就是实体的码。
根据实体间的联系转换成不同的关系模型（1：1，1：n, m:n）

### 7.10 数据模型的优化

（1） 确定数据依赖
（2） 对于各关系模型之间的数据依赖进行极小化处理，消除冗余的联系
（3） 按照数据依赖理论对关系模型逐一进行分析
（4） 找到合适的范式来进行分解和合并

### 7.11 数据库的物理设计通常分两步：

（1） 确定数据库的物理结构：存取方法和存储结构
（2） 对物理结构进行评价，重点是时间和空间效率。之后再进行进一步操作，修改或者数据库实施

### 7.12 存取方法：快速存取数据库中的数据技术

1、B+树索引，hash索引，聚簇方法。
2、前两者都是建立索引，具体概念参照数据结构
聚簇方法：为了提高某个属性的查询速度，把这个属性上具有相同值的元素集中存放在连续的物理块中称为聚簇，该属性称为聚簇码。一个数据库可以建立多个聚簇，一个关系只能加入一个聚簇。聚簇的开销是相当大的，会导致物理位置发生变化.
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6342abdea69a111e289df4ad92613a29.png)
B+Tree与 B-Tree 的区别：
1、所有的数据都会出现在叶子节点。
2、叶子节点形成一个单向链表。
MySQL 索引数据结构对经典的B+Tree 进行了优化。在原B+Tree 的基础上，增加一个指向相邻叶子节点的链表指针，就形成了带有顺序指针的 B+Tree，提高区间访问的性能。变成了双向循环链表。

为什么 InnoDB 存储引擎选择使用 B+Tree 索引结构？
答：相对于二叉树，层级更少，搜索效率高。对于 B-Tree，无论是叶子节点还是非叶子节点，都会保存数据，这样导致一页中存储的键值减少，指针也跟着减少，要同样保存大量数据，只能增加树的高度，导致性能降低。相对于 Hash 索引，B+Tree 支持范围匹配及排序操作。

### 7.13确定数据库的存储结构

考虑三方面：存取时间、存储空间利用率、维护代价三方面。

### 7.14 数据库的实施

设计人员根据上面的概念模型、逻辑模型、物理设计对数据库进行程序设计、编码。调试，最后运行。
数据要分批分期地组织数据入库，待运行稳定后再大批入库。也要做好转储和恢复的工作防止出现故障。

### 7.15数据库的运行和维护

（1） 数据库的转储和恢复
（2） 数据库的安全性和完整性控制
（3） 性能的监督、分析和改造
（4） 数据库的重组织与重构造。
数据库重组织不修改原设计的逻辑结构和物理结构，而数据库的重构造会部分修改模式和内模式。

## 第八章 数据库编程

### 8.1 嵌入式SQL

作为非过程化的查询语言SQL：操作统一、面向集合、功能丰富、使用简单。
但是：由于这种特性也导致了他难以实现应用业务中的逻辑控制。为了克服这个弱点就有了SQL编程。
嵌入式SQL：将SQL嵌入到程序设计语句中，数据库管理系统一般采用预编译的方法处理。就是识别出嵌入式语言，然后转成主语言。区分SQL语句和主语言语句：加前缀（exec sql, #sql）

### 8.2 嵌入式sql语句与主语言之间的通信

（1） 嵌入式SQL——>主语言执行状态信息，SQL通信区
（2） 主语言——>sql提供参数，主变量
（3） SQL语句——>主语言查询结果，主变量和游标
整个流程：（C语言）

```sql
EXEC SQL BEGIN DECLARE SECTION；//主变量声明开始****
EXEC SQL END DECALRE SECTION;//主变量声明结束
Long SQLCODE;
EXEC SQL INCLUDE SQLCODE;//定义通信区
//主程序开始
EXEC SQL CONNECT TO … //连接数据库
EXEC SQL DECLARE SX CURSOR FOR+后面接一个SQL语句 //定义游标
EXEC SQL OPEN SX； //打开游标
循环从游标中取值
EXEC SQL FETCH SX INTO +主变量
```

这个时候用SQLCA.SQLCODE判断游标中是否还有没取出的值。Sqlcode!=0则退出循环

```sql
EXEC SQL CLOSE SX; //关闭游标
EXEC SQL COMMIT WORK; //提交更新
EXEC SQL DISCOUNNECT … //断开数据库连接
```

### 8.3 游标

游标：是用来存放SQL语句执行结果的缓冲区。必须使用游标的情况：多条记录的select语句，current形式的update和delete语句。
（1） 说明游标 EXEC SQL DECLARE C1 (游标名) CURSOR FOR +select 语句
（2） 打开游标EXEC SQL OPEN C1‘
（3） 推进游标指针取当前记录 EXEC SQL FETCH C1 INTO 主变量1，主变量2…(主变量和指示变量前加冒号：)
（4） 关闭游标 EXEC SQL CLOSE C1

### 8.4 动态SQL

主变量里用一个字符串寸SQL语句，可以将某些参数设为`？`
之后准备，再执行

```sql
EXEC SQL BEGIN DECLARE SECTION
Const char *stmt=”insert into student(sno) values(?);” //主变量定义
EXEC SQL END DECLARE SECTION
EXEC SQL PREPARE mystmt FROM :stmt; //准备语句prepare，给这个语句起个名字叫 mystmt
EXEC SQL EXECUTE mystmt USING 100; //执行语句execute
```

### 8.5 过程化SQL

基本结构是块，所有的过程化SQL程序都是由块组成的。
先DECLARE 执行时用BEGIN +SQL语句/流程控制语句 EXCEPTION 异常处理部分 END;
流程控制语句：IF； IF-THEN; (条件) LOOP, WHILE-LOOP, FOR-LOOP（循环） 赋值语句 变量名：=表达式

### 8.6 过程化SQL块：命名块，匿名块。

匿名块：每次执行时都要进行编译，不能被存储到数据库里。也不能在其他块里调用。
命名块：过程和函数是命名块，被编译后保存在数据库中，称为持久性存储模块，可以反复调用。

### 8.7 存储过程：

过程化SQL经编译和优化存储在数据库服务器中，不用编译，调用就可以了
（1） 创建存储过程 Create or replace procedure 过程名(参数1，2，3) AS 过程化SQL块
（2） 执行存储过程 Call /perform procedure 过程名（参数1，2，3）
（3） 修改存储过程 Alter procedure 过程名1 rename to 过程名2 //改名
Alter procedure 过程名 compile //重新编译
（4） 删除 Drop procedure 过程名； 函数和存储过程差不多，不同之处是函数必须指定返回类型

## 第九章 关系查询处理和查询优化

### 9.1 查询处理

查询处理：关系数据库管理系统执行查询语句的过程。把用户提交给关系数据库管理系统的查询语句转换为高效的查询执行计划。
分为四个阶段：
1.查询分析：扫描，词法分析，语法分析。
2.查询检查：语义检查。
3.查询优化：代数优化、物理优化。

4. 查询执行

选择操作：全表扫描，索引扫描
连接操作：嵌套循环（暴力算法），排序——合并算法，索引连接算法，hash-join算法

### 9.2 查询优化

查询优化：提高查询效率，使查询代价更小
物理优化：对存取路径和底层操作算法的优化（基于规则，基于代价估算，两者结合）
代数优化：对代数表达式进行优化（等价变换规则、启发式规则，优化查询树）

### 9.3 SQL 优化

插入数据：
大量插入采用load指令
主键优化：
表数据都是按照逐渐顺序组织存放的，采用页分裂和页合并来优化主键的索引组织表。、
order by优化：
建立合适的索引，多字段排序遵循最左前缀法则。尽量使用覆盖索引，select*容易出现回表查询，降低效率。在出现大数据量排序时，适当增加缓冲区大小。（覆盖索引是select的数据列只用从索引中就能够取得，不必读取数据行，换句话说查询列要被所建的索引覆盖。）
group by优化：
建立合适的索引，遵循最左前缀法则。
count优化：
1）count(主键)：InnoDB引擎会遍历整张表，把每行的主键id值都取出来，返回给服务层，服务层拿到主键后，直接按行进行累加（主键不可能为空）。
2）count(字段)：没有not null约束的话，InnoDB引擎会遍历整张表把每一行的字段值都取出来，返回给服务层，服务层判断是否为null，不为null，计数累加；有not null约束的话，InnoDB引擎会遍历整张表把每一行的字段值都取出来，返回给服务层，直接按行进行累加。
3）count(1)：InnoDB 引擎遍历整张表，但不取值。服务层对于返回的每一层，放一个数字 1 进去，直接按行进行累加。
4）count(\*)：InnoDB 引擎并不会把全部字段取出来，而是专门做了优化，不取值，服务层直接按行进行累加。按效率排序：count(字段) < count(主键) < count(1) < count()，所以尽量使用 count()**
*update优化：InnoDB 的行锁是针对索引加的锁，不是针对记录加的锁，并且该索引不能失效，否则会从行锁升级为表锁。对于有主键索引的只会锁该行，没有索引的会把整张表加锁。**

## 第十章 数据库恢复技术

### 10.1 事务的基本概念

事务：用户定义的一个数据库序列，要么不做，要么全做，是一个不可分割的工作单位。
BEGIN TRANSACTION
COMMIT
ROLLBACK //表示回滚，指事务遇到某些故障，系统将撤销操作，回滚到事务开始时的状态

### 10.2 事务的ACID特性

四个特性:
原子性（atomicity）
一致性（consistency）
隔离性（isolation）
持续性（durability）
原子性：要么不做，要么全做。不可拆分；
一致性：和原子性密切相关。事务的执行结果必须是使数据库从一个状态到另一个状态。
隔离性：一个事务的执行不能被其他事务干扰
持续性：一个事务一旦提交对数据库的影响就是永久性的。
保持事务的ACID特性是事务管理的重要任务。可能遭到的破坏因素有：
（1）多个事务并发执行，不同事物交叉执行。
（2）强制停止

### 10.3 数据库恢复

数据库的恢复：尽管数据库系统中采取了各种保护措施来防止数据库的安全性和完整性被破坏，但是还有一些是不可避免的。数据库管理系统必须具备把数据库从错误状态恢复到某一已知的正确状态的功能。

### 10.4 故障种类

事务故障类型：事务内部的故障、系统故障、介质故障、计算机病毒
事务撤销：在不影响其他事务运行的情况下，强制回滚该事务，撤销事务做出的任何操作。使得事务好像没有启动一样。——内部
事务故障的恢复策略：
1、反向扫描日志文件查找该事务的更新操作，对该事务的更新操作进行逆操作。
2、对该事务的其他更新操作做同样的处理，直到读到该事务的开始标记时事务故障恢复完成。

系统故障是指造成系统停止运转的任何事件，使得系统要重新启动。如：CPU故障、操作系统故障、DBMS代码错误、系统断电等。
恢复策略：
1、从头扫描日志文件找出在故障发生前已经提交的事务将其记入重做队列，同时找出故障发生时未完成的事务将其事务标记记入撤销队列。
2、对撤销队列的各个事务进行撤销处理。
3、对重做事务中的各个事务进行重做处理

介质故障：磁盘损坏、磁头碰撞等。
恢复策略：重装数据库，重做已完成的事务。

### 10.5 恢复的实现技术

恢复的基本原理：冗余。
恢复机制的两个关键问题：如何建立冗余数据、如何利用这些冗余数据实施数据库恢复。
建立冗余数据最常用的技术是数据转储和登记日志文件。
数据转储：数据库管理员定期地将整个数据库复制到磁带、磁盘或其他存储介质上保存起来的过程。后备副本，这些副本只能将数据库恢复到转储时的状态。
静态转储：转储时不能有任何操作；动态转储: 转储和用户事务可以并发执行。
海量转储：全部数据库；增量转储：只转储更新部分

### 10.6 日志文件

日志文件是用来记录事务对数据库的更新操作的文件。事务故障恢复和系统故障恢复必须使用日志文件。
在动态转储方式中必须建立日志文件，后备副本和日志文件结合起来才能有效地恢复数据库。
为保证数据库是可恢复的，登记日志文件时必须遵循两条原则。
（1） 登记的次序严格按照并发事务执行地时间次序
（2） 必须先写日志文件再写数据库。

### 10.7 数据库镜像

相当于复制。镜像和源数据库一致。出现故障，就可以利用镜像恢复。

## 第十一章 并发控制

### 11.1 并发事务概述

并发控制机制：当多个用户并发存取数据库时就会产生多个事务同时存取同一数据的情况，可能会存取不正确的数据。为了保证事务的隔离性和一致性，数据库管理系统需要对并发操作进行正确调度。
事务是并发控制的基本单位。
并发控制带来的数据不一致性：丢失修改、不可重复读、读“脏”数据
（1）丢失修改：T1,T2同时读（这时读到的数据是一样的），T1对 其操作后，T2对其操作导致T1的操作丢失。
（2）不可重复读：（两个事务先后读取同一条记录，但两次读取的数据不同）①T1读后，T2修改数据，T1再读发现值不同
②T1读后，T2删除
③T1读后，T2插入（后两种有时也叫幻影现象）
（3）幻读：一个事务按照条件查询数据时，没有对应的数据行，但是再插入数据时，又发现这行数据已经存在
（4）读脏数据：（一个事务读到另一个事务还没提交的数据）T1操作后，T2读，T1由于某种原因撤销操作。导致T2读到的数据和数据库内容不一样。

### 11.2 并发控制技术

并发控制技术：封锁（locking）,时间戳（timestamp）,乐观控制法（optimistic scheduler），多版本并发控制（MVCC）。（ps:并发控制concurrency control）
（1） 封锁（大重点）
排他锁X锁(exclusive lock)，写锁：事务T对对象A上写锁，T可对A进行读写，但其他事务都不能对A上任何类型的锁。直到T释放A为止。
共享锁S锁（share lock），读锁：事务T对对象A上读锁，则T可以读A,但不能写，其他事务只能对A上读锁不能加写锁，直到T释放A上的S锁为止。
（2）三级封锁协议（规定一些规则，避免并发操作出现问题）
一级封锁协议：事务T在修改数据R之前必须先对其加X锁，直到事务结束才释放。（在写之前加X锁，防止丢失修改问题）
二级封锁协议：在一级基础上增加事务T在读R之前对其加S锁，读完之后释放S锁。（在读之前加S锁，读完就可以释放。防止丢失修改，脏数据问题）
三级封锁协议：在一级基础上增加事务T在读R之前对其加S锁，直到事务结束后才释放。（在读之前加S锁，事务结束才能释放。防止丢失修改，脏数据，不可重复读的问题）
（3） 活锁和死锁
活锁：某一事务永远处于等待状态。解决方法：先来先服务。
死锁：两个事务相互等待。
死锁预防：
（1）一次封锁法：要求每个事务必须一次对所有要使用的数据全部加锁。
（2）顺序封锁法：预先规定一个不会发生死锁的顺序，然后按照这个顺序严格加锁。
数据库解决死锁问题：诊断并解除死锁。
（1） 超时法
（2）等待图法。
通常采用的方法：选择一个处理代价最小的事务，将其撤销，释放此事务上所有的锁。使得其他事务能够继续运行下去。

### 11.3 并发控制的可串行性

可串行化调度：多个事务并发执行是正确的，当且仅当其结果与按某一次序结果相同。则称为是可串行化调度。
可串行性是并发事务正确调度的准则。
冲突操作是指不同事务对同一个数据的读写操作和写写操作。
一个操作在保证冲突操作次序不变的情况下，交换两个事务的不冲突操作次序得到另一个调度，如果此调度是串行的。则原调度是冲突可串行化的调度。冲突可串行化是可串行化的充分条件，不是必要条件。

### 11.4 两段锁协议

两段锁协议：所有事务必须分成两个阶段对数据加锁和解锁。
扩展阶段和收缩阶段：扩展阶段：只允许申请但是不能释放；收缩阶段：只能释放不能申请
是可串行化调度的充分条件，但可能会发生死锁。

### 11.5 多粒度封锁

显示搜索适应事物的要求直接加到数据对象上的资源。隐式封锁是该数据对象没有被独立加锁，是由其上级节点加锁而使该数据对象加上了锁。
意向锁的含义是如果一个节点加意向锁，则说明该节点的下层节点正在被加锁。
意向锁分为三种，意向共享锁IS锁（后裔加点拟加S锁），意向排他锁IX锁（后裔加点拟加X锁），共享意向排他锁SIX锁（先加S锁，后加IX锁，表示要读整张表并且更新个别元组）。

### 11.6 其他并发控制机制

时间戳方法给每个事务盖上一个时间戳，每个事务具有唯一的时间戳，并且按照这个时间戳来解决事务的冲突操作。
乐观控制法又被称为验证方法：认为事务中执行时很少发生冲突，让他自由执行，如果检查该事务发生冲突并影响可串行性则拒绝提交并回滚该事务。乐观锁用于读操作多的场景，悲观锁适用于写操作多的场景。
意向锁：如果一个结点加意向锁，则说明该节点的下层结点正在被加锁。
（节点的层次是数据库——>关系——>元组）
三种意向锁：意向共享锁（IS锁Intent share lock）,意向排他锁（IX锁），共享意向排他锁（SIX锁）
IS: 事务要对数据对象加S锁，首先要对其上层加IS锁
IX：（上面那条换成X锁）
SIX锁：对一个数据对象加SIX锁，表示对他加S锁，再加IX锁。
意向锁中：任意事务要对一个数据对象加锁，必须先对他的上层结点加意向锁。

### 11.7 MVCC（背八股文的时候顺便加上了，我觉得这不会考）

MVCC多版本并发控制，指维护一个数据的多个版本使得读写操作没有冲突。快照读为mysql实现mvcc提供了一个非阻塞读功能。MVCC的具体实现依赖于数据库记录的三个隐藏字段、undo log日志、readview

#### 11.7.1 当前读和快照读

当前读：读取的记录是最新版本读取时还要保证其他并发事务不能修改当前进入会对读取的记录进行加锁。
快照读简单的select就是快照读，快照读读取的版本是可见版本，也有可能是历史版本不加锁是非阻塞读。

#### 11.7.2 隔离级别

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6cddd46622b77e5c5b08624ed6cdf5a2.png)

不同的隔离级别会对于快照读有不同的处理方法。Read committed每一次select都会生成一个快照读，Repeatable read开启事务后第一个select语句才是快照读的，Serializable快照都会退化为当前读。

#### 11.7.3 redolog和undolog

redo log重做日志记录的是事务提交时数据页的物理修改，有两部分构成重做日志缓冲和重做日志文件。输入每次提交时会将事务刷新到Redo log而不是直接将buffer pool中的数据刷到磁盘ibd文件中。redolog是顺序写，速度快，后台线程伺机采用一定机制再将数据刷新到ibd文件中。
undo log回滚日志用于记录数据被修改前的信息，属于逻辑日志 。

#### 11.7.4 MVCC实现原理

三个隐藏字段：
DB_TRX_ID（最近修改事务ID，记录插入这条记录或最后一次修改该记录的事务ID ）、DB_ROLL_PTR（undolog版本链：回滚指针指向这条记录的上一个版本用于配合Undo log指向上一版本，不同事务或相同事务对同一条记录进行修改会导致该记录的Undo log形成1条记录版本链表 链表的头部是最新的旧记录 链表的尾部是最早的旧记录）、
DB_ROW_ID（隐藏主键如果表结构没有指定主键则生成该隐藏字段）。
由read view 来记录并维护系统当前活跃的未提交的事务的ID。readview中包含着当前活跃事务ID的集合、最小活跃事务ID、预分配事务ID 和readview创建者的事务ID。根据版本链数据访问规则来确定是否可以访问该版本。
在RC隔离级别下，在事务中每执行一次快照读生成readview，在RR隔离级别下在事务中第一次执行快照读时生成read view后续会重复使用。
MVCC靠隐藏字段Undo log版本链read view实现的。原子性由Undo log实现，持久性由Redo log实现一致性由Undo log和redo log实现，隔离性由锁和MVCC实现。

### 11.8 数据库管理系统层次结构

应用层——语言处理层——数据存取层——数据存储层
语言处理层：对数据库语言SQL进行语法分析、视图转换、完整性、安全性检查等，生成可执行代码（编译……）
数据存取层：处理元组，对元组实现增删改查这种操作
数据存储层：数据页和缓冲区，物理层面的管理

### 11.9 分布式数据库

分布式数据库是由一组数据组成，这组数据分布在计算机网络的不同计算机上，网络中的每个结点具有独立处理的能力，可以执行局部应用。同时，每个结点也能通过网络通信系统执行全局应用。（高度的自治性和整体性）

## 附录 习题

### 1 第一章-面向对象基础-练手题

传送门：
[第一章 数据库绪论](https://blog.csdn.net/qq_45400167/article/details/136383925?spm=1001.2014.3001.5501)

这一章是没有练手题的，都是概念，大家都要了解

### 2 第二/三章 [关系数据库](https://so.csdn.net/so/search?q=关系数据库&spm=1001.2101.3001.7020)和标准语言SQL

传送门：
[第二/三章 关系数据库和标准语言SQL](https://blog.csdn.net/qq_45400167/article/details/136386996)

#### 2.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/3862b9606cddcc2573f23aabc4b0fead.png)

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1cc011554d29859d364621cbb4b24cbf.png)

### 3 第四/五章 数据库安全性和完整性

传送门：
[第四/五章 数据库安全性和完整性](https://blog.csdn.net/qq_45400167/article/details/136423264)
都是一些概念，知道都得知道

### 4 第六/七章 关系数据理论和数据库设计

传送门：
[第六/七章 关系数据理论和数据库设计](https://blog.csdn.net/qq_45400167/article/details/136526047)

#### 4.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/2df41de2ff39fd6190d7006ec7935cc8.png)
这里答案仅作参考，我自己随便写的，照着书好好好好做做，这个题很重要的
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/272bcbec14fb7e2ad87e1c930d2aba2a.png)

#### 4.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6b8b7ea1c5ba02b2266a1401272170fa.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/e6fd65dd8996faa00de0440ed610252c.png)

#### 4.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/123e701a1be6d6181b904290c5747928.png)

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/87ef84432f0ee4f88c143bfd3592f00e.png)

#### 4.4

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/f79c3a530b7296d873d365d3afa7c32b.png)

1. **简述实体完整性规则，并举例说明其目的意义**。

- 实体完整性规则： 若属性 A 是基本关系 R 的主属性，则 A 不能取空值 (NULL)。 简单来说，主键的任何属性都不能为空。
- 目的和意义：
  - 唯一标识： 实体完整性规则保证了表中的每一行（实体）都有一个唯一的标识符（主键），从而能够区分不同的实体。
  - 保证数据准确性： 主键不能为空，确保每条记录都能够被有效关联，防止出现数据混乱或丢失。
  - 关系模型的基础： 关系模型依赖于主键建立表与表之间的关系，如果主键可以为空，就会破坏关系的完整性。
  - 便于数据检索和管理： 主键的唯一性简化了数据检索、更新和删除等操作，提高了数据库的效率。
- 例子：
  - 假设有一个学生表，主键是“学号”。 实体完整性规则要求每个学生的学号都不能为空。 如果学号为空，就无法确定该学生是谁，也无法将该学生的信息与其他表（如选课表）关联起来。
  - 假设一个员工表，主键是“员工ID”。 如果员工ID为空，公司就无法区分不同的员工，无法发放工资，也无法进行员工绩效评估等操作。

1. **并发操作可能会导致哪些不一致性**？

   并发操作如果不加以控制，可能会导致以下几种常见的不一致性问题：

- 丢失修改（Lost Update）：
  - 描述： 多个事务并发地读取同一数据，然后都进行修改并写回。 由于没有适当的控制，后提交的事务的修改会覆盖先提交的事务的修改，导致数据丢失。
  - 例子： 假设账户 A 的余额为 100 元。 事务 1 从 A 取款 50 元，事务 2 也从 A 取款 30 元。 如果两个事务并发执行，可能出现事务 1 读取了 100 元，计算出 50 元，但还未写回； 此时事务 2 也读取了 100 元，计算出 70 元并写回； 最后事务 1 写回 50 元，覆盖了事务 2 的修改，导致账户 A 的余额错误地变为 50 元。
- 脏读（Dirty Read）：
  - 描述： 事务 A 修改了数据，但尚未提交，此时事务 B 读取了事务 A 修改后的数据。 如果事务 A 随后回滚，那么事务 B 读取到的就是无效的、未提交的数据，即“脏数据”。
  - 例子： 假设账户 A 的余额为 100 元。 事务 1 从 A 取款 50 元，余额变为 50 元，但事务 1 尚未提交。 事务 2 读取了 A 的余额，发现是 50 元。 如果事务 1 随后因为某些原因回滚，A 的余额恢复为 100 元，但事务 2 已经基于错误的余额进行了后续操作，导致数据不一致。
- 不可重复读（Non-Repeatable Read）：
  - 描述： 事务 A 多次读取同一数据，但在事务执行过程中，事务 B 修改了该数据并提交。 导致事务 A 每次读取到的数据都不一样。
  - 例子： 事务 1 第一次读取账户 A 的余额为 100 元。 在事务 1 尚未结束时，事务 2 从 A 取款 20 元，余额变为 80 元并提交。 事务 1 再次读取 A 的余额，发现是 80 元，与第一次读取到的结果不一致。
- 幻读（Phantom Read）：
  - 描述： 事务 A 执行两次查询操作，第二次查询结果的数据行的数量比第一次多或者少，原因是事务 A 执行过程中，事务 B 插入或删除了满足查询条件的数据行。 事务 A 就好像产生了幻觉一样。
  - 例子： 假设有一个商品表，事务 1 第一次查询价格小于 100 元的商品数量为 10 个。 在事务 1 尚未结束时，事务 2 插入了 3 个价格小于 100 元的商品。 事务 1 再次查询价格小于 100 元的商品数量，发现变成了 13 个，好像出现了幻觉一样。

#### 4.5

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6079e457ffb67cbb2b55507ce79f4865.png)

#### 4.6

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/a0d0c9816403f80c6f474063a3a5ec77.png)

#### 4.7 语法树

我不知道会不会考，期末考过反正是
弄两个做做吧
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/00fc32910776ba79feaf468e3bac490d.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/0492cd15619f7f96e64dab28b052cfa1.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/bbd5314b0d8cbdb81a8dcc87e6f1bdbe.png)

#### 4.8

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/7cf31ddf1f4081b65a394d0ee5a64030.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/407d8ca1406ddd73a74b09c83491b095.png)

### 5 第八/九章 数据库编程和优化

传送门：
[第八/九章 数据库编程和优化](https://blog.csdn.net/qq_45400167/article/details/136526607)
这里的练手题与第十一章并发控制一起做。

### 6 第十章 数据库恢复技术

传送门：
[第十章 数据库恢复技术](https://blog.csdn.net/qq_45400167/article/details/136577870)

#### 6.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/f29666a27f89ec8e7d2588d6994445c1.png)

#### 6.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/80dd0f6226cfa07148edb24ba4eff610.png)

### 7 第十一章 并发控制

传送门：
[第十一章 并发控制](https://blog.csdn.net/qq_45400167/article/details/136578138)

#### 7.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/977f306e2538a81bac1c3517b8706060.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/b9b8a588bb75b25cd70296327f928e94.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/eb2e218835e2ffb8b2b9b8e52a88e1da.png)

#### 7.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8ed3d72ed8c2195ee33a66e823930aa9.png)
上一道题是10，这一道题是5，不同年份的练练手，套路都是一样的。





# 计算机组成原理

## 第一章 计算机组成原理概述及各种码

### 1.1 计算机硬件的基本组成

计算机硬件系统由运算器、存储器、控制器、输入、输出设备五大部件构成。

#### 1.1.1 存储器

存储器分为主存储器和辅助存储器，CPU能够直接访问主存储器。主存储器中MAR（Memory Address Register）地址寄存器存放访存地址，MDR（Memory Data Register）数据寄存器存放从存储器中读或写的信息。现代CPU中MAR和MDR也在CPU中。

#### 1.1.2 运算器

运算器用于进行算术运算和逻辑运算。最重要的部件为ALU(Arithmetic and Logical Unit)算数逻辑单元。运算器包括累加器ACC、乘商计算器MQ、操作数寄存器X、基址寄存器BR、程序状态寄存器PSW等。

#### 1.1.3 控制器

控制器由程序计数器PC、指令寄存器IR和控制单元CU组成。PC用来存放当前欲执行指令的地址，IR用来存放当前的指令其内容来自于主存的MDR。
CPU包含ALU、通用寄存器组GPRs、标志寄存器PSW、控制器、指令寄存器IR、程序计数器PC、存储器地址寄存器MAR和存储器数据寄存器MDR。

### 1.2 计算机的工作过程

hello.c->hello.i通过预处理器cpp，包括展开头文件、条件编译、删除注释等 gcc-e
hello.i->hello.s通过编译器ccl，检查语法规范，完成编译等 gcc-s
hello.s->hello.o通过汇编器as，将汇编指令翻译成机器指令 gcc-c
hello.o->hello.out通过链接器ld，将数据段合并，地址回填生成可执行文件
取数指令：
1、取指令 PC->MAR->M->MDR->IR
2、OP(IR)->CU
3、Ad(IR)->MAR->M->MDR->ACC

### 1.3 计算机的性能指标

CPU时钟周期：
即主频的倒数，它是CPU中最小的时间单位
主频（CPU时钟频率） ：主频的倒数是CPU时钟周期，对于同一个型号的计算机，其主频越高，完成指令的一个执行步骤所用的时间越短，执行指令的速度越快。

CPI ：执行一条指令所需的时钟周期数
CPU执行时间=CPU时钟周期数/主频 =（指令条数 *CPI ）/主频

CPU 的性能（CPU 执行时间）取决于三个要素：主频，每条指令执行所用的时钟周期数（ CPI ）、指令条数

### 1.4 各个字长区别与联系

机器字长：计算机能直接处理的二进制数据的位数，一般等于内部寄存器的大小，决定了计算机的运算精度。与ALU直接相关。
指令字长：一个指令字中包含的二进制代码的位数。
存储字长：一个存储单元存储的二进制代码的长度。等于MDR的位数， 它们都必须是字节的整数倍。
数据字长：数据总线一次能传送信息的位数，它可以不等于MDR的位数。

## 第二章 数据的表示与运算

### 2.1 ASCII码

48=’0’ 65=’A’ 97=’a’ 大小写差32

### 2.2 各种码

补码、反码各种码知道怎么算就行，一两个例子练练手

### 2.3 浮点数

这里**极其重要**，公式我就不列举了，**公式也是极其重要的**，直接在练习题里练习

## 第三章 存储系统

### 3.1 存储器的分类

按层次分：
1、主存储器（主存），CPU可以直接随机地对其进行访问，也可以和高速缓存器及辅助存储器交换数据。
2、辅助存储器（辅存），不能与CPU直接相连，用来存放当前暂时不用的程序和数据。
3、高速缓冲存储器，位于主存和CPU之间，用来存放正在执行的程序段和数据。

按存取方式分：
1、随机存储器（RAM）。存储器的任何一个存储单元的内容都可以随机存取，而且存取时间与存取单元的物理位置无关，主要用作主存或高速缓冲存储器。
2、只读存储器（ROM）。存储器的内容只能随机读出而不能写入。即使断电，内容也不会丢失。3、 串行访问存储器。

按信息的可保存性分：
1、易失性存储器，如RAM。
2、非易失性存储器，如ROM，磁表面存储器和光存储器。

### 3.2 存储层次化结构

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/578824c9f863f7855b11ff2b0d3534f7.png)
主存-辅存：实现虚拟存储，解决主存容量不够。
Cache-主存：解决CPU和主存速度不匹配的问题。

### 3.3 半导体随机存储器

#### 3.3.1 SRAM和DRAM（非非非非常重要）

主存由DRAM实现，Cache由SRAM实现
SRAM（静态随机存储器）的存储元是双稳态触发器，非破坏性读出，存取速度快，但集成度低，功耗较大。

DRAM（动态随机存储器）的存储元是栅极电容的电荷，采用地址复用技术，地址线为原来的一半，地址信息分行和列两次传送。具有容易集成，价位低，容量大，功耗低的特点。
DRAM的刷新时间为2ms，以行为单位，需要行地址。
刷新方式分为：
1、分散刷新，前一半读写，后一半刷新。优点：没有死区；缺点：加长了系统的存取周期。
2、集中刷新：专门有一段时间用于刷新。优点：读写不受刷新影响；缺点：有死区。3、异步刷新：隔一段集中读写时间刷新一次。既可以缩短死区，又可以充分利用刷新时间。
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4af0745cdbe2e06d7b50985eb04bd003.png)

#### 3.3.2 只读存储器

ROM具有结构简单，可靠性高的特点。
容量1024×8bit 前1024是地址线，后8bit是数据线。
引脚数目由地址线、片选线、数据线、读写控制线组成。

### 3.4 主存储器的扩展（非非非非常重要）

扩展方式跟后面的CPU那块一样重要
主存容量的扩展：
1、位扩展法：8片8K×1bit的chip连接八根数据线组成一个8K×8bit的存储器。
2、 字扩展法：用2/4或3/8译码器来区分个芯片的地址位置。
3、字位同时扩展法：两者结合。

片选信号的产生分为 ：
1、线选法 ：简单，不需要译码器。
2、译码片选法 。

### 3.5 双口RAM和多模块存储器

#### 3.5.1 双端口RAM

双端口RAM：指同一个存储器有左、右两个独立的端口，分别具有两组相互独立的地址线、数据线和读写控制线，允许两个独立的控制器同时异步地访问存储单元。采用忙信号来从逻辑上判断暂时关闭哪个端口。

#### 3.5.2 多模块存储器

单体多字存储器 ：存储器中只有一个存储体，每个存储单元存储m个字，总线宽度也为m个字。一次并行读出m个字，地址必须顺序排列并处于同一存储单元。缺点：指令和数据必须连续存放。

多体并行存储器 ：由多体模块组成，每个模块都有相同的容量和存取速度，各模块都有独立的读写控制电路、地址寄存器和数据寄存器，既能并行工作，又能交叉工作。

多体并行存储器由分为高位交叉编址（顺序方式）和低位交叉编址（交叉方式）
高位交叉编址 ：高位地址表示体号，低位地址为体内地址。
低位交叉编址 ：低位地址为体号，高位地址为体内地址。地位交叉编址更适合顺序连续访问，存取模块可以并行访问。二高位交叉编址只能串行访问。

流水线方式，存储器交叉模块数应大于等于 m=T/r （T为存取周期，r为总线传送周期），连续读取m个字所需时间为t1=T+(m-1)r，而顺序方式连续读取m个字所需时间为t2=mT

低位交叉的多体存储器，俗称双通道。

### 3.6 高速缓冲存储器

高速缓冲技术就是利用程序访问的局部性原理 。
当CPU发出读请求时，如果访存地址在Cache中命中，就将此地址转换成Cache地址，直接对Cache进行读操作，与主存无关；如果Cache不命中，则仍需访问主存，并把此字所在的块一次从主存调入Cache内。若此时Cache已满，则需根据某种替换算法，用这个块替换掉Cache中原来的某块信息。CPU与Cache之间的数据交换以字位单位，而Cache与主存之间的数据交换以Cache块为单位。

#### 3.6.1 Cache和主存的映射方式

直接映射（对号入座）：
主存中的每一块只能装入Cache中的唯一位置，若位置已有内容，原来块将无条件被替换出去。直接映射关系可表示为：j = i mod 2^c（j是Cache的块号，又称行号，i是主存的块号，2 ^c是Cache的总块数）。
直接映射地址结构：标记、Cache行号、块内地址。
访问过程：先访问行号c位，找到在Cache中的位置，然后再对标记位进行对比，若相等且有效位为1，则命中，反之不命中，CPU从主存读出该地址送到相应Cache并且有效位置为1，标记设为地址中高t位，且将内容送到CPU。优点：实现简单。缺点：不够灵活，冲突概率最高，空间利用率最低。

全相联映射（随意放）：
优点：比较灵活，冲突概率低，空间利用率高，命中率也高。缺点：标记比较较慢，通常采用昂贵的按内容寻址的相联存储器进行地址映射。

组相联映射（按号分组，组内随意放）：将Cache分为大小相同的组，组间采用直接映射，组内采用全相联映射。

#### 3.6.2 Cache中主存块的替换算法

1、随机算法RAND：没有依据程序访问的局部性原理。
2、先进先出算法FIFO：没有依据程序访问的局部性原理。
3、近期最少使用算法LRU。
4、最不经常使用算法LFU

#### 3.6.3 Cache写策略

对于Cache写命中 ：
1） 全写法 （写直通法write-through）：必须把数据同时写入Cache和主存
2） 写回法 （write-back）：只修改Cache的内容，而不立即写入主存，只有当此块被换出时才写回主存

对于Cache写不命中 ：
1） 写分配法 （write-allocate）：加载主存中的块到Cache中，然后更新这个Cache块
2） 非写分配法 （not-write-allocate）：只写入主存，不进行调块
非写分配法通常与全写法合用，写分配法通常和写回法合用。各级Cache之间采用全写法+非写分配法。Cache和主存之间采用写回法和写分配法。

### 3.7 虚拟存储器

同时具有虚拟页式存储器（有TLB）和Cache的系统中，访问顺序为TLB->页表->Cache->主存。
虚拟存储器和Cache的不同之处 ：1）Cache主要解决系统速度，而虚拟存储器是为了解决主存容量。2）Cache全由硬件实现，是硬件存储器，对所有程序员透明；而虚拟存储器由OS和硬件共同实现，是逻辑上的存储器，对系统程序员不透明，但对应用程序员透明。3）虚拟存储器系统不命中时对系统性能影响更大。4）Cache不命中时主存能和CPU直接通信，同时将数据调入Cache中；而虚拟存储器系统不命中时，只能先由硬盘调入内存中，而不能直接和CPU通信。

## 第四章 指令系统

### 4.1 指令格式

零地址指令 ：空操作指令、停机指令、关中断指令。

一地址指令 ：
1、只有目的操作数的单操作数指令：OP(A1)->A1，三次访存：取指-》读A1-》写A1。
2、隐含约定目的地址的双操作数指令：(ACC)OP(A1)->ACC，两次访存：取指-》读A1

二地址指令 ：
给出目的操作数和源操作数的地址，其中目的操作数地址还用于保存本次的运算结果，即(A1)OP(A2)->A1,四次访存：取指-》读A1-》读A2-》写A1

三地址指令 ：(A1)OP(A2)->A3，四次访存：取指-》读A1-》读A2-》写A3

四地址指令 ：(A1)OP(A2)->A3，A4=下一条将要执行指令的地址，四次访存：取指-》读A1-》读A2-》写A3

扩展操作码的指令格式：
若指令字长位16位，地址码为4位，则4位基本操作码用于三指令有16条。若用于多指令，1111留作拓展码，三指令为15条，二指令开头全为1111，1111|1111留作一指令，则有15条二指令，一指令开头全为1111|1111，1111|1111|1111用作零地址指令，则有15条一指令，16条零指令。

### 4.2 CISC和RISC的区别

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4778fa951422bd70cff1f478699f7de3.png)
CISC（复杂指令系统计算机）的代表：x86架构
RISC（精简指令系统计算机）的代表：ARM架构

- CISC 追求功能强大、对编程友好，适合复杂任务。
- RISC 追求指令执行效率和硬件实现的简单性，更适合资源受限的场景。

## 第五章 中央处理器及指令

### 5.1 CPU的功能和基本结构

#### 5.1.1 CPU功能

CPU由运算器和控制器组成。
运算器由ACC累加器，MQ乘商寄存器，X通用寄存器，ALU算术逻辑单元构成。
控制器由PC程序计数器，IR指令寄存器，CU控制单元构成

### 5.2 指令执行过程

CPU从主存中取出并执行一条指令所需的全部时间称为指令周期 ，也就是CPU完成一条指令的时间。
一个指令周期通常包含若干机器周期，一个机器周期又包含若干时钟周期。一个完整的指令周期应包括取指、间址、执行和中断四个机器周期。
中断周期中进栈操作是将SP减1，和传统意义上的进栈操作相反，因为计算机的堆栈中都是向低地址增加，所以进栈操作是减1，不是加1。

#### 5.2.1 取指周期

是根据PC中的内容从主存中取出指令代码并存放在指令寄存器IR中，取指令的同时，PC加1。
数据流向：
1、PC->MAR->地址总线->主存。
2、CU发出控制信号->控制总线->主存。
3、主存->数据总线->MDR->IR（存放指令）。
4、CU发出读命令->PC+1

#### 5.2.2 间址周期

在有些需要间接寻址的情况下，取操作数有效地址。
数据流向：
1、Ad(IR)->MAR->地址总线->主存。
2、CU发出读命令->控制总线->主存。
3、主存->数据总线->MDR（存放有效地址）。

#### 5.2.3 执行周期

是根据IR中的指令字的操作码和操作数通过ALU操作产生执行结果。

#### 5.2.4 中断周期

在有处理中断请求的时候，假设程序断点存入堆栈中，并用SP表示栈顶地址，而且进栈操作是先修改栈顶指针，后存入数据。
数据流向：
1、CU发送控制信号->SP-1->SP->MAR->地址总线->主存。
2、CU发送读命令->控制总线->主存。
3、PC->MDR->数据总线->主存（程序断点存入主存）。
4、CU发送读命令（中断服务程序的入口地址）->PC

### 5.3 指令执行方案

1、单指令周期 ：对所有指令都选用相同的时间来完成，指令之间串行执行，指令周期取决于执行时间最长的指令的执行时间。
2、多指令周期 ：指令之间串行执行，不再要求所有指令占用相同的执行时间。
3、流水线方案 ：指令之间并行执行，尽量让多条指令同时执行，但各自处在不同的执行步骤中。

### 5.4 数据通路

数据在功能部件之间传送的路径称为 数据通路 ，其功能是 实现CPU内部的运算器和寄存器以及寄存器之间的数据交换 。
寄存器之间：PC->Bus->MAR
主存与CPU之间：
1、PC->Bus->MAR
2、CU发出读命令 1->R
3、MEM(MAR)->MDR
4、MDR->Bus->IR
执行算术或逻辑运算：（ALU必须在两个输入端同时有效的情况下才可以工作）：
1、Ad(IR)->Bus->MAR
2、1->R
3、MEM(MAR)->数据线->MDR
4 、MDR->Bus->Y
5、(ACC)+(Y)->Z
6、Z->ACC

#### 5.4.1 数据通路基本结构

1） CPU内部单总线方式：所有寄存器的输入端和输出端都连接到一条公共的通路上。结构简单，但数据传输中存在较多冲突现象，性能较低。
2）CPU内部三总线方式：所有寄存器的输入端和输出端都连接到多条公共的通路上。效率CPU内部单总线方式相对有所提高。
3）专用数据通路方式：根据指令执行过程中的数据和地址的流动方向安排连接线路，避免使用共享的总线。性能较高，但硬件量大。前两者为内部总线，第三个为专用数据总线

### 5.5 控制器

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/488dd182599d85d3c528c067914c5b3e.png)

#### 5.5.1 CPU控制方式和功能

CPU的控制方式
控制器的主要功能有：
1）从主存中取出一条指令，并指出下一条指令在主存中的位置。
2）对指令进行译码或测试，产生相应的操作控制信号，以便启动规定的动作。
3）指挥并控制CPU、主存、输入和输出设备之间的数据流动方向。

#### 5.5.2 硬布线控制器

硬布线控制器由复杂的组合逻辑门电路和一些触发器构成，因此又称为组合逻辑控制器。

#### 5.5.3 微程序控制器

微程序控制器采用存储逻辑实现 ，也就是把微操作信号代码化，使每条机器指令转化成为一段微程序并存入一个专门的存储器（控制存储器）中， 微操作控制信号由微指令产生。微指令是若干微命令的集合，存放微指令的控制存储器的单元地址称为微地址。若指令系统中具有n种机器指令，则控制存储器中的微程序数至少是n+1个（1为公共的取指微程序）。微命令（控制序列的最小单位），微操作（最小操作,微命令和微操作一一对应），微指令（若干微命令的集合），微周期（读取并执行微指令的时间），微程序（微指令的有序集合）

#### 5.5.4 微指令的编码方式

微指令的编码方式又称为微指令的控制方式 ，指如何对微指令的控制字段进行编码，以形成控制信号。目标是在保证速度的情况下，尽量缩短微指令字长。
1）直接编码（直接控制）方式。
2）字段直接编码方式：将微指令的微命令字段分成若干个小字段，把互斥性微命令组合在同一字段中，把相容性微命令组合在不同的字段中。
3）字段间接编码方式，又称隐式编码。

#### 5.5.5 微指令的格式

1）水平型微指令：指令字中的一位对应一个控制信号，有输出时为1，否则为0。一条水平型微指令定义并执行几种并行的基本操作。优点是微程序短，执行速度快；缺点是微指令长，编写微程序较麻烦。
2）垂直型微指令：类似机器指令操作码的方式，设置微操作码字段，由微操作码规定微指令的功能。一条垂直型微指令只能定义并执行一种基本操作。优点是微指令短、简单、规整，便于编写微程序；缺点是微程序长，执行速度慢，工作效率低。
3）混合型微指令

#### 5.5.6 取指操作不同控制器流程

硬布线控制器：
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d2efe2f10bf3b885a3ebad95cd8db613.png)
微程序控制器：
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6fbd3d0a39f49291acbc97cc61456ec1.png)

#### 5.5.7 微程序控制器和硬布线控制器

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/0150c00df2b670b14fd4dea533541dd6.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d59ecbd333b2dd1c3c436d2a0d1328b2.png)

### 5.6 指令流水线

一条指令的执行过程：
1、取指 ：根据PC内容访问主存储器，取出一条指令送到IR中。
2、分析 ：对指令操作码进行译码，按照给定的寻址方式和地址字段中的内容形成操作数的有效地址EA，并从有效地址EA中取出操作数。
3、执行 ：根据操作码字段，完成指令规定的功能，即把运算结果写到通用寄存器或主存中。
指令处理方式：
1、顺序执行方式：前一条指令执行完后，才启动下一条指令，T=3nt。
2、流水线执行方式：把取第k+1条指令提前到分析第k条指令的期间完成，而将分析第k+1条指令与执行第k条指令同时进行，T=(2+n)t

#### 5.6.1 流水线

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/45d16a05ad20b4004b86692370d7d032.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/223a648908307ee0d4f80f3881b57cfc.png)

#### 5.6.2 影响流水线的因素

1）结构相关（资源冲突）：由于多条指令在同一时刻争用同一资源而形成的冲突
解决办法：
a. 前一指令访存时，使后一条相关指令（以及其后续指令）暂停一个时钟周期。
b. 单独设置数据存储器和指令存储器，使两项操作各自在不同的存储器中进行，这属于资源重复配置。
2）数据相关（数据冲突，数据冒险）：必须等前一条指令执行完才能执行后一条指令的情况---->主要情况
a. 把遇到数据相关的指令及其后续指令都暂停一至几个时钟周期，可分为硬件阻塞（stall）和软件插入"NOP"指令两种方法。
b. 设置相关专用通路，即不等前一条指令把计算结果返回寄存器组，下一条指令也不再读寄存器组，而是直接把前一条指令的ALU的计算结果作为自己的输入数据开始计算过程，使本来需要暂停的操作可以继续执行，称为数据旁路技术。
c. 通过编译器对数据相关的指令编译优化的方法，调整指令顺序来解决数据相关。
3）控制相关（控制冲突，控制冒险）：遇到转移指令和其他改变PC值得指令而造成断流
a. 对转移指令进行分支预测，分为简单（静态）预测和动态预测，静态预测总是预测条件不满足，即继续执行后续指令；动态预测根据程序执行的历史情况，进行动态预测调整。
b. 预取转移成功和不成功两个控制流方向上的目标指令。
c. 加快和提前形成条件码。
d. 提高转移方向的猜准率。

## 第六章 总线

### 6.1 总线概述

总线是一组能为多个部件分时共享的公共信息传送线路。分时和共享是总线的两个特点。
总线特性：
机械特性（尺寸）、电气特性（有效D电平）、功能特性（传输线的功能）、时间特性（时序）。

#### 6.1.1 总线的分类

1） 片内总线 ：CPU芯片内部寄存器与寄存器之间、寄存器与ALU之间的公共连接线。
2） 系统总线 ：计算机系统内各功能部件（CPU、主存、I/O接口）之间相互连接的总线。
a. 数据总线 ：用来传输各功能部件之间的数据信息，双向传输，其位数与机器字长、存储字长有关。
b. 地址总线 ：用来指出数据总线上的源数据或目的数据所在的主存单元或I/O接口的地址，单向传输，其位数与主存地址空间有关。
c. 控制总线 ：传输控制信息，包括CPU送出的控制命令和主存（或外设）返回CPU的反馈信号。
3） 通信总线 ：用于计算机系统之间或计算机系统与其他系统（如远程通信设备、测试设备）之间信息传送的总线，也称为 外部总线。

#### 6.1.2 系统总线的结构

1）单总线结构：CPU、主存、I/O设备（通过I/O接口）都挂在一组总线上。
优点：简单，成本低，易拓展。缺点：带宽低，负载重，不支持并行操作。
2）双总线结构：主存总线、I/O总线。
优点：将低速I/O设备从总线分离出来。缺点：需要增加通道等硬件设备。
3）三总线结构：主存总线、I/O总线、DMA总线。
主存总线用于在CPU和内存之间传送地址数据和控制信息。I/O总线用于在CPU和各类外设之间通信。DMA总线用于在内存和高速外设之间直接传送数据。
优点：提高了I/O设备的性能，提高系统吞吐量。缺点：系统工作效率较低。

#### 6.1.3 总线的性能指标

总线宽度 ，又称总线位宽，是总线上同时能够传输的数据位数，通常是指数据总线的根数
总线带宽 ，总线的数据传输率，即单位时间内总线上可传输数据的位数，总线带宽=总线工作频率 * (总线宽度/8)

### 6.2 总线操作和定时

一个总线周期可分为4个阶段：
1）申请分配阶段：由需要使用总线的主模块（或主设备）提出申请，经总线仲裁机构决定将下一传输周期的总线使用权授予某一申请者。
2）寻址阶段：主模块通过总线发出本次要访问的从模块（或从设备）的地址及有关命令，启动参与本次传输的从模块。
3）传输阶段：主模块和从模块进行数据交换，可单向或双向进行数据传送。
4）结束阶段：主模块的有关信息均从系统总线上撤除，让出总线使用权。

#### 6.2.1 同步定时方式

同步定时方式：系统采用一个统一的时钟信号 来协调发送和接收双方的传递定时关系。适用于总线长度较短及总线所接部件的存取时间比较接近的系统。
优点：传送速度快，具有较高的传输速率；总线控制逻辑简单。缺点：主从设备属于强制性同步；不能及时进行数据通信的有效性检验，可靠性较差。

#### 6.2.2 异步定时方式

异步定时方式：没有统一的时钟，也没有固定的时间间隔， 完全依靠传送双方相互制约的"握手"信号 来实现定时控制。
优点：总线周期长度可变，能保证两个工作速度相差很大的部件或设备之间可靠地进行信息交换，自动适应时间的配合。缺点：比同步控制方式稍复杂一些，速度比同步定时方式慢。
1）不互锁方式：主设备自动撤销"请求"信号，从设备自动撤销"回答"信号，双方不存在互锁关系
2）半互锁方式：主设备发出"请求"信号后，必须待接到从设备的"回答"信号后，才撤销"请求"信号，有互锁关系；而从设备在接到"请求"信号后，发出"回答"信号，但不必等待获知主设备的"请求"信号已经撤销，而是隔一段时间后自动撤销"回答"信号，不存在互锁关系。
3）全互锁关系：主设备发出"请求"信号后，必须待从设备"回答"后，才撤销"请求"信号；从设备发出"回答"信号，必须待获知主设备"请求"信号已撤销后，再撤销其"回答"信号。双方存在互锁关系。

### 6.3 总线标准

系统总线：
ISA总线 ：最早出现的微型计算机的 系统总线标准，应用在IBM的AT机上
EISA总线：为配合32位CPU而设计的总线扩展标准，EISA对ISA完全兼容
局部总线：
VESA总线：32位标准的计算机局部总线 ，是针对多媒体PC要求高速传送活动图像的大量数据应运而生的
PCI总线：高性能的32位或64位总线，专为高度集成的外围部件、扩充插板和处理器/存储器系统而设计的互联机制。目前常用的PCI适配器有 显卡、声卡、网卡 等。PCI总线支持即插即用，与处理器时钟频率无关，属于局部总线，可以通过桥连接实现多层PCI总线。
AGP：是一种视频接口标准，专用于连接主存和图形存储器，属于局部总线。AGP技术为传输视频和三维图形数据提供了切实可行的解决方案。
PCI-Express（PCI-E ）是最新的总线和接口标准，将全面取代现行的PCI和AGP，最终实现总线标准的统一。
设备总线：
RS-232C总线是由美国电子工业协会（EIA）推荐的一种串行通信总线标准 ，是应用于串行二进制交换的数据。终端设备（DTE）和数据通信设备（DCE）之间的标准接口。
USB总线：连接外部设备的I/O总线标准，属于设备总线 ，具有即插即用、热插拔等优点，有很强的连接能力。串行
PCMCIA：广泛应用于笔记本电脑中的一种接口标准，是一个小型的用于扩展功能的插槽，可即插即用。
连接硬盘：
IDE总线：ATA，是一种IDE接口磁盘驱动器接口类型，硬盘和光驱通过IDE接口与主板连接。
SCSI：用于计算机和智能设备之间（硬盘、软驱、光驱、打印机等）系统级接口的独立处理器标准。
SATA：基于行业标准的串行硬件驱动器接，硬盘接口规范。串行

## 第七章 输入/输出系统

### 7.1 I/O系统基本概念

I/O控制方式主要有一下4种：
1）程序查询方式：由CPU通过程序不断查询I/O设备是否已做好准备，从而控制I/O设备与主机交换信息
2）程序中断方式：只在I/O设备准备就绪并向CPU发出中断请求时才予以响应
3）DMA方式：主存和I/O设备间有一条直接数据通路，当主存和I/O设备交换信息时无需调用中断服务程序.
4）通道方式：在系统中设有通道控制部件，每个通道都挂接若干外设，主机在执行I/O命令时，只需启动有关通道，通道将执行通道程序，从而完成I/O操作。

### 7.2 I/O接口

I/O接口的功能：
1）实现主机和外设的通信联络控制。
2）进行地址译码和设备选择。
3）实现数据缓冲。
4）信号格式的转换。
5）传送控制命令和状态信息。

#### 7.2.1 I/O接口的基本结构

内部接口 ：内部接口与系统总线相连，实质上是与内存、CPU相连，数据的传输方式只能是并行传输
外部接口：外部接口通过接口电缆与外设相连，外部接口的数据传输可能是串行方式，因此I/O接口需具有串/并转换功能。

#### 7.2.2 I/O端口的编址方式：

1） 统一编址，又称存储器映射方式，是指把I/O端口当做存储器的单元进行地址分配，这种方式CPU不需要设置专门的I/O指令，用统一的访存指令 就可以访问I/O端口。
优点：需要设置专门的I/O指令，更灵活。缺点：占用存储器地址，内存容量变小，执行速度慢
2） 独立编址，又称I/O映射方式，是指I/O端口地址与存储器地址无关，独立编址CPU需要设置专门的输入/ 输出指令访问端口
优点：I/O指令和存储器指令有明显区别，程序更清晰。缺点：I/O指令少，一般只能对端口进行传送操作。增减控制的复杂性。

### 7.3 DMA方式和中断方式的区别：

1）中断方式是程序的切换，需要保护和恢复现场；而DMA方式除了预处理和后处理，其他时候不占用CPU的任何资源。

2）对中断请求的响应只能发生在每条指令执行完毕时（即指令的执行周期之后）；而对DMA请求的响应可以发生在每个机器周期结束时（在取指、间址、执行周期之后均可），只要CPU不占用总线就可以被响应。

3）中断传送过程需要CPU的干预；而DMA传送过程不需要CPU的干预，故数据传输速率非常高，适合于高速外设的成组数据传送。

4）DMA请求的优先级高于中断请求。

5）中断方式具有对异常事件的处理能力，而DMA方式仅限于传送数据块的I/O操作。

6）从数据传送来看，中断方式靠程序传送，DMA方式靠硬件传送

## 附录 习题

### 1 第一、二章-概述和数据的表示和运算-练手题

传送门：
[第一/二章 概述和数据的表示和运算](https://blog.csdn.net/qq_45400167/article/details/136382445?spm=1001.2014.3001.5501)

#### 1.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/c022879f707ac30443d437ba498bfa0b.png)

#### 1.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/295e115015f0e66391d6c1cdf1ca2545.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8280646226c076f98d642b063980d726.png)

#### 1.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/3a3ad1b78cb02076524a26d50edf53fd.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4c351154b16fe0438a8216785d96442b.png)

#### 1.4

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/3f51c590190ccfda82485862f8199ed0.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/34d73a2517835ff692f5cd8c49438214.png)

#### 1.5

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/140080c86b2284780689fcc18f097f48.png)

#### 1.6

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d0c00a58b0a634474e197fd36c7aeedd.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/12af2eaff2d964f9d57529756eb13edd.png)

#### 1.7

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/71350c00e2c3552e76cc38057c3649b9.png)

#### 1.8

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/c30e8b61ba1d0ab7756eb827523a32e9.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4ca2f87335175cff45d288fda3cdc27d.png)

#### 1.9

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/2af030c4228c8d32157e61738df936a6.png)

#### 1.10

不仅要知道浮点数怎么来的更要知道浮点是的具体操作，**与整数相比，浮点数是如何加减的，对比记忆**
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8dd32553a58c7b343fd7211dcba633df.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/e0ea16a173cdd7fe9709b967f9e7c06c.png)

#### 1.11

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/05d488db72f9436a61991c12561930b3.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/2c8805dcc693d801c4b997f0b9beee0f.png)

### 2 第三章-存储系统-练手题

传送门：
[第三章 存储系统](https://blog.csdn.net/qq_45400167/article/details/136383036?spm=1001.2014.3001.5501)

#### 2.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/42877e5c4abef076e7d04d0a01141a6a.png)
题目补充：连续读出四个字
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/b3cc02153285427a769cad6b9db05153.png)

#### 2.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1efce15d30758e38ebb146a81df3571b.png)

#### 2.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8a68a5af1f22da22b064dda66f955573.png)

#### 2.4

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6c73f66ce4c47eebca9116c0eb9b538c.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/973cf6be58bd079d42b9639b0356748c.png)

#### 2.5

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/57df34839e5b220ca1c3ccfcd83bd6e0.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/cbb4b2aff4e6fa37bafcebcba870011e.png)

#### 2.6

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/5ea22c0161a9098099bebe5d093ba3dd.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/a413582031fde2b1f8cf2732ba5e84da.jpeg)

#### 2.7

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/9407f485113d21756f46d275bb6bff40.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/50e75ccab46226fc102ea217e32c5570.png)

#### 2.8

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/c85a49d8025483e372bf5b0fcf1541a0.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/52a8d6cbb2b61735459fc50a0122d549.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1b84863cfee6d37b51762f7b275f5566.png)

#### 2.9

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/68aeadfffe4ad51e9645a7bac3592912.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/5517241408c390ec854d60db4c851f0f.png)

#### 2.10

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/aa723f675514b0a28c683bdcc0993453.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/5622611bdcb5d6a50eeb8116b12efa86.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/f6314e15a02a7612bb5cafcbba9fcec0.png)

#### 2.11

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/b1fc0d171d50a486abda6abfe6b50236.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/aa84a1b270cfc52aea9cc0f7f5be00e1.jpeg)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1f61fedacedeb0a8d7f04ce814062601.jpeg)
附上74139的真值表，低电平有效
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/cff9d8b23f31864414aad05c47b1a95a.jpeg)

#### 2.12

流水线相比于上面几道不是那么重要（我也不敢说不考），但也要做几个维持手感
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1ed97685835b4c0dfaf208d02bdc4c1a.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/9c45d6a790ee25bb13a3827dcbd8cc58.png)

#### 2.13

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/842939ff8459629c000be3c9bfb040f6.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/849969dfd669d6f0f89b24fbb9cc0893.png)

### 3 第四章-指令系统-练手题

传送门：
[第四章 指令系统](https://blog.csdn.net/qq_45400167/article/details/136386625)

#### 3.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d14e557f71d5d33fc9fa94708c267b49.png)
A

#### 3.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/55fc43e9fd2d75e8eaa7e8652960daf1.png)
A

#### 3.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/a2f99e6f5876f62e11142b287026981d.png)
数据处理、数据存储、数据传送、数据控制

#### 3.4

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/aabbdeaa6ef577fa2ef59aece49e4e24.png)

> 感谢[－TNT－](https://blog.csdn.net/qq_45630718?type=blog)同学的指正！

二地址最多28 -1
一地址最多220-212-1
零地址指令最多是232-224-212

#### 3.5

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/844ad1eae4a6925777684343aaabc38a.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/726d0fc43ed0c372b81c2676ee68f0f9.png)

#### 3.6

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/29294ca284481a3efa5cc31e49e9b908.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8ca560969d1a669d8d265b2b127584a1.png)

#### 3.7

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/baa1a9626c3132f237a247e3746021df.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4c69b11fa6958c33cb444db675b3d205.png)

#### 3.8

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/ff90e27cf1fac2a21bf7463490d9260b.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/777fd2b71ff210c881fac8c8ca79737a.png)

### 4 第五章-中央处理器-练手题

传送门：
[第五章 中央处理器](https://blog.csdn.net/qq_45400167/article/details/136421310)

#### 4.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/e5c3d1a76c2d39250a8be3819b45ae41.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/deee6bf60156881f7be47ba966bf7221.png)

#### 4.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/8b3dd551485bfa718e6768e4e6a45c1d.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/488fd32adf3296454490257a295ec775.png)

#### 4.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d23d63b63cfd5fe4bb7a18d4fc24b717.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/c53a2b26e4616f2c7a6937181bc47e9d.png)

#### 4.4

微指令也来上几道练练手感
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/2b1bbe946a41d8c8fd84fe8f85fe420c.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/43b87bd8bbf4457c3f1037d16dd9bb1b.png)

#### 4.5

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/668a3630871599da84e8340ab726fa7c.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/b977c640adcb40e8963e63bf75670f51.png)

#### 4.6

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/7dfc5f0020a59a84725b8f879a62aa16.png)

#### 4.7

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d7a152cab006a5eb403d7dacaf32dbe5.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/217feb3fa50f1c3ee9d551e0f8f3377a.png)

### 5 第六章-总线-练手题

传送门：
[第六章 总线](https://blog.csdn.net/qq_45400167/article/details/136447115)

#### 5.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6017134e2e14ac160d06aa595c1c6762.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/0f8e98e669d30cc8ace94b29c26f9b3d.png)

#### 5.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/10565641eb6a084556c8c9ecbab680ce.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1fb02b311f2db9de77a7647a6bac7d81.png)

#### 5.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/7c8be5b2521cb6d4589b1226c6f28683.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/609d90c4e5cb9b21126e4dbde37a0039.png)

#### 5.4

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/4a27c3d965170c780c793cae5d997f7b.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/dd82c72267de5c520d87c5968a9cead1.png)

#### 5.5

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/3e8fecee7d9dd2f3acc3d7351f7407cf.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/6a36f9bac80f58cf8107f09b9ace1278.png)

### 6 第七章-输入/输出系统-练手题

传送门：
[第七章 输入/输出系统](https://blog.csdn.net/qq_45400167/article/details/136447515)

#### 6.1

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/a1740d67289b5bc197b857433aa5a583.png)

#### 6.2

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/d02641c15110ec8c571a4d4ca11acf03.png)

#### 6.3

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/87e56ea087e1166cb6054675bd6cca83.png)

#### 6.4

![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/1c3e0e3de469210e033d556697eecc56.png)
![在这里插入图片描述](https://i-blog.csdnimg.cn/blog_migrate/686ae2d4bc56cf6c6366577261b6c49e.png)