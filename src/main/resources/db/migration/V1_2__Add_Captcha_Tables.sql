-- 创建系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value VARCHAR(500) NOT NULL,
    description VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建验证码记录表
CREATE TABLE IF NOT EXISTS captcha_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(100) NOT NULL,
    captcha_code VARCHAR(10) NOT NULL,
    captcha_image LONGTEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    used TINYINT DEFAULT 0,
    INDEX idx_session_id (session_id),
    INDEX idx_expires_at (expires_at)
);

-- 插入默认的系统设置
INSERT INTO system_settings (setting_key, setting_value, description) VALUES 
('captcha_enabled', '1', '验证码功能开关：1-开启，0-关闭'),
('captcha_expire_minutes', '5', '验证码过期时间（分钟）'),
('captcha_length', '4', '验证码长度')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);