server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-http-form-post-size: 10MB
    max-swallow-size: 10MB
    max-http-header-size: 10MB

spring:
  application:
    name: AiEduForge
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: not_null
      insert-strategy: not_null
      select-strategy: not_empty
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.example.aieduforge.entity

jwt:
  secret: AiEduForgeSecretKeyForJWTTokenGeneration2024
  expiration: 86400000
  header: Authorization
  prefix: Bearer 

ai:
  # AI模型配置现在存储在数据库中（ai_model表）
  # 下面的配置是为了向后兼容
  # 如果数据库中没有配置模型，将作为后备使用
  openRouter:
    base-url: https://openrouter.ai/api/v1
    api-key: 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
    model-name: google/gemma-3n-e2b-it:free
    timeout: 30000
    max-tokens: 2048
    temperature: 0.7
  prompts:
    question-generation: "Generate practice questions based on the following teaching content:"
    answer-analysis: "Analyze the following student answer and provide feedback:"
    content-generation: "Generate detailed teaching content based on the following course outline:"
    error-analysis: "Analyze student errors and provide improvement suggestions:"

logging:
  level:
    com.example.aieduforge: debug
    org.springframework.security: info
    org.springframework.web: info
    com.baomidou.mybatisplus: debug
    org.springframework.boot.autoconfigure: warn
    org.apache.ibatis: debug
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/aieduforge.log
  logback:
    rollingpolicy:
      max-history: 30
      max-file-size: 10MB

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
