<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.AiAnswerMapper">

    <!-- Find answer by question ID -->
    <select id="findByQuestionId" resultType="com.example.aieduforge.entity.AiAnswer">
        SELECT * FROM ai_answer WHERE question_id = #{questionId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- Find answers with high confidence -->
    <select id="findHighConfidenceAnswers" resultType="com.example.aieduforge.entity.AiAnswer">
        SELECT * FROM ai_answer WHERE confidence_score >= #{minConfidence}
        ORDER BY confidence_score DESC, create_time DESC
    </select>

    <!-- Find answers by feedback score -->
    <select id="findByFeedbackScore" resultType="com.example.aieduforge.entity.AiAnswer">
        SELECT * FROM ai_answer WHERE feedback_score = #{feedbackScore}
        ORDER BY create_time DESC
    </select>

    <!-- Update feedback -->
    <update id="updateFeedback">
        UPDATE ai_answer 
        SET feedback_score = #{feedbackScore}, feedback_comment = #{feedbackComment}
        WHERE id = #{id}
    </update>

</mapper>