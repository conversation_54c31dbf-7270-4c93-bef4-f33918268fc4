<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.KnowledgeBaseMapper">

    <!-- Find by course ID -->
    <select id="findByCourseId" resultType="com.example.aieduforge.entity.KnowledgeBase">
        SELECT * FROM knowledge_base 
        WHERE course_id = #{courseId} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- Find by subject -->
    <select id="findBySubject" resultType="com.example.aieduforge.entity.KnowledgeBase">
        SELECT * FROM knowledge_base 
        WHERE subject = #{subject} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- Find by upload user -->
    <select id="findByUploadUser" resultType="com.example.aieduforge.entity.KnowledgeBase">
        SELECT * FROM knowledge_base 
        WHERE upload_user_id = #{uploadUserId} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- Search by title or content -->
    <select id="searchByKeyword" resultType="com.example.aieduforge.entity.KnowledgeBase">
        SELECT * FROM knowledge_base 
        WHERE (title LIKE CONCAT('%', #{keyword}, '%') 
               OR content LIKE CONCAT('%', #{keyword}, '%'))
        AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- Find by tags -->
    <select id="findByTags" resultType="com.example.aieduforge.entity.KnowledgeBase">
        SELECT * FROM knowledge_base 
        WHERE FIND_IN_SET(#{tags}, tags) > 0 AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- Get file type statistics -->
    <select id="getFileTypeStats" resultType="map">
        SELECT 
            file_type as fileType,
            COUNT(*) as count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeCount
        FROM knowledge_base 
        WHERE status = 1
        GROUP BY file_type
        ORDER BY count DESC
    </select>

    <!-- Get resource usage statistics -->
    <select id="getResourceUsageStats" resultType="map">
        SELECT 
            COALESCE(subject, 'Unknown') as subject,
            COUNT(*) as fileCount,
            SUM(COALESCE(download_count, 0)) as downloadCount,
            SUM(COALESCE(file_size, 0)) as totalSize,
            ROUND(AVG(COALESCE(download_count, 0)), 2) as avgDownload
        FROM knowledge_base 
        WHERE status = 1
        GROUP BY subject
        ORDER BY fileCount DESC
    </select>

    <!-- Get AI knowledge data by knowledge base ID -->
    <select id="selectAIKnowledgeData" resultType="com.example.aieduforge.entity.AIKnowledgeData">
        SELECT * FROM ai_knowledge_data 
        WHERE knowledge_base_id = #{knowledgeBaseId} AND status = 1
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- Get all AI knowledge data -->
    <select id="selectAllAIKnowledgeData" resultType="com.example.aieduforge.entity.AIKnowledgeData">
        SELECT * FROM ai_knowledge_data 
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <!-- Search knowledge base with multiple filters -->
    <select id="searchWithFilters" resultType="com.example.aieduforge.entity.KnowledgeBase">
        SELECT * FROM knowledge_base 
        WHERE status = 1
        <if test="keyword != null and keyword != ''">
            AND (title LIKE CONCAT('%', #{keyword}, '%') 
                 OR content LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%')
                 OR tags LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="subject != null and subject != ''">
            AND subject = #{subject}
        </if>
        <if test="fileType != null and fileType != ''">
            AND file_type = #{fileType}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>