<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.PracticeRecordMapper">

    <!-- 获取练习总次数 -->
    <select id="getTotalPractices" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM practice_record
        WHERE student_id = #{studentId}
        AND completion_status = 2  <!-- 已完成的练习 -->
    </select>

    <!-- 获取平均正确率 -->
    <select id="getAverageAccuracy" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG((correct_count * 100.0) / NULLIF(total_questions, 0)), 0)
        FROM practice_record
        WHERE student_id = #{studentId}
        AND completion_status = 2  <!-- 已完成的练习 -->
        AND total_questions > 0
    </select>

    <!-- 获取学生练习统计数据 -->
    <select id="getStudentStatistics" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT id) as total_practices,
            SUM(total_questions) as total_questions,
            SUM(correct_count) as total_correct,
            COALESCE(AVG(score), 0) as avg_score,
            SUM(time_spent) as total_time,
            COUNT(CASE WHEN completion_status = 2 THEN 1 END) as completed_count
        FROM practice_record
        WHERE student_id = #{studentId}
        <if test="courseId != null">
            AND course_id = #{courseId}
        </if>
        AND completion_status = 2
    </select>

    <!-- 根据学生ID和课程ID查询练习记录 -->
    <select id="findByStudentAndCourse" resultType="com.example.aieduforge.entity.PracticeRecord">
        SELECT
            id, student_id, course_id, practice_type, question_ids,
            total_questions, correct_count, score, time_spent,
            completion_status, start_time, end_time, create_time
        FROM practice_record
        WHERE student_id = #{studentId}
        AND course_id = #{courseId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据学生ID查询练习记录 -->
    <select id="findByStudentId" resultType="com.example.aieduforge.entity.PracticeRecord">
        SELECT
            id, student_id, course_id, practice_type, question_ids,
            total_questions, correct_count, score, time_spent,
            completion_status, start_time, end_time, create_time
        FROM practice_record
        WHERE student_id = #{studentId}
        ORDER BY create_time DESC
    </select>

    <!-- Get practice activities by date -->
    <select id="getPracticeActivitiesByDate" resultType="map">
        SELECT 
            student_id,
            SUM(total_questions) as total_questions,
            SUM(time_spent) as time_spent,
            COUNT(*) as practice_count
        FROM practice_record 
        WHERE DATE(start_time) = #{date}
        GROUP BY student_id
    </select>

</mapper>
