<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.LearningAnalyticsMapper">

    <!-- 获取学生总学习时长 -->
    <select id="getTotalStudyTime" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(total_time_spent), 0)
        FROM learning_analytics
        WHERE student_id = #{studentId}
    </select>

    <!-- 获取已完成课程数 -->
    <select id="getCompletedCourses" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT course_id)
        FROM learning_analytics
        WHERE student_id = #{studentId}
        AND mastery_level >= 2  <!-- 至少熟练掌握 -->
    </select>

    <!-- 获取每日学习统计 -->
    <select id="getDailyStats" resultType="java.util.Map">
        SELECT
            DATE(stat_date) as date,
            SUM(total_time_spent) as study_time,
            COUNT(*) as study_count
        FROM learning_analytics
        WHERE student_id = #{studentId}
        AND stat_date >= #{startDate}
        GROUP BY DATE(stat_date)
        ORDER BY date ASC
    </select>

    <!-- 获取学科分布 -->
    <select id="getSubjectDistribution" resultType="java.util.Map">
        SELECT
            c.subject as name,
            COUNT(*) as value
        FROM learning_analytics la
        JOIN course c ON la.course_id = c.id
        WHERE la.student_id = #{studentId}
        GROUP BY c.subject
        ORDER BY value DESC
    </select>

    <!-- 获取学生学习进度 -->
    <select id="getStudentLearningProgress" resultType="java.util.Map">
        SELECT
            la.knowledge_point as knowledgePoint,
            la.total_questions as totalQuestions,
            la.correct_questions as correctQuestions,
            la.accuracy_rate as accuracyRate,
            la.mastery_level as masteryLevel,
            CASE 
                WHEN la.mastery_level >= 3 THEN '精通'
                WHEN la.mastery_level >= 2 THEN '熟练掌握'
                WHEN la.mastery_level >= 1 THEN '初步掌握'
                ELSE '未掌握'
            END as masteryDescription
        FROM learning_analytics la
        WHERE la.student_id = #{studentId}
        AND la.course_id = #{courseId}
        ORDER BY la.accuracy_rate DESC
    </select>

    <!-- 获取学习趋势 -->
    <select id="getLearningTrend" resultType="java.util.Map">
        SELECT
            DATE(stat_date) as date,
            SUM(total_time_spent) as study_time,
            AVG(accuracy_rate) as accuracy,
            AVG(mastery_level) as mastery
        FROM learning_analytics
        WHERE student_id = #{studentId}
        AND stat_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(stat_date)
        ORDER BY date ASC
    </select>

    <!-- 获取班级平均表现 -->
    <select id="getClassAveragePerformance" resultType="java.util.Map">
        SELECT
            AVG(accuracy_rate) as avg_accuracy,
            AVG(mastery_level) as avg_mastery,
            COUNT(DISTINCT student_id) as student_count
        FROM learning_analytics
        WHERE course_id = #{courseId}
        AND stat_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <!-- 获取知识点掌握情况 -->
    <select id="getKnowledgePointMastery" resultType="java.util.Map">
        SELECT
            knowledge_point,
            AVG(mastery_level) as avg_mastery,
            AVG(accuracy_rate) as avg_accuracy
        FROM learning_analytics
        WHERE course_id = #{courseId}
        GROUP BY knowledge_point
        ORDER BY avg_mastery DESC
    </select>

    <!-- 获取困难知识点 -->
    <select id="getDifficultKnowledgePoints" resultType="java.util.Map">
        SELECT
            knowledge_point,
            AVG(mastery_level) as avg_mastery,
            AVG(accuracy_rate) as avg_accuracy,
            COUNT(*) as attempt_count
        FROM learning_analytics
        WHERE course_id = #{courseId}
        GROUP BY knowledge_point
        HAVING avg_mastery &lt; 2
        ORDER BY avg_mastery ASC
        LIMIT #{limit}
    </select>

</mapper>
