<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.SysRoleMapper">

    <!-- Find roles by user ID -->
    <select id="findRolesByUserId" resultType="com.example.aieduforge.entity.SysRole">
        SELECT r.* FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.status = 1
    </select>

    <!-- Find role by code -->
    <select id="findByRoleCode" resultType="com.example.aieduforge.entity.SysRole">
        SELECT * FROM sys_role WHERE role_code = #{roleCode} AND status = 1
    </select>

</mapper>