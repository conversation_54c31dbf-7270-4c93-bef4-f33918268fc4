<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.CourseMapper">

    <!-- Result map with teacher name -->
    <resultMap id="CourseWithTeacherMap" type="com.example.aieduforge.entity.Course">
        <id column="id" property="id"/>
        <result column="course_name" property="courseName"/>
        <result column="course_code" property="courseCode"/>
        <result column="description" property="description"/>
        <result column="subject" property="subject"/>
        <result column="teacher_id" property="teacherId"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="teacher_name" property="teacherName"/>
    </resultMap>

    <!-- Find all courses with teacher names -->
    <select id="selectList" resultMap="CourseWithTeacherMap">
        SELECT c.*, u.real_name as teacher_name 
        FROM course c 
        LEFT JOIN sys_user u ON c.teacher_id = u.id 
        ORDER BY c.create_time DESC
    </select>

    <!-- Find courses by teacher ID -->
    <select id="findByTeacherId" resultMap="CourseWithTeacherMap">
        SELECT c.*, u.real_name as teacher_name 
        FROM course c 
        LEFT JOIN sys_user u ON c.teacher_id = u.id 
        WHERE c.teacher_id = #{teacherId}
        <if test="onlyEnabled != null and onlyEnabled">
            AND c.status = 1
        </if>
        ORDER BY c.create_time DESC
    </select>

    <!-- Find courses by subject -->
    <select id="findBySubject" resultMap="CourseWithTeacherMap">
        SELECT c.*, u.real_name as teacher_name 
        FROM course c 
        LEFT JOIN sys_user u ON c.teacher_id = u.id 
        WHERE c.subject = #{subject}
        <if test="onlyEnabled != null and onlyEnabled">
            AND c.status = 1
        </if>
        ORDER BY c.course_name
    </select>

    <!-- Find course by code -->
    <select id="findByCourseCode" resultType="com.example.aieduforge.entity.Course">
        SELECT * FROM course WHERE course_code = #{courseCode}
    </select>
    
    <!-- Find courses with filters -->
    <select id="findWithFilters" resultMap="CourseWithTeacherMap">
        SELECT c.*, u.real_name as teacher_name 
        FROM course c 
        LEFT JOIN sys_user u ON c.teacher_id = u.id 
        <where>
            <if test="courseName != null and courseName != ''">
                AND c.course_name LIKE CONCAT('%', #{courseName}, '%')
            </if>
            <if test="subject != null and subject != ''">
                AND c.subject = #{subject}
            </if>
            <if test="teacherName != null and teacherName != ''">
                AND u.real_name LIKE CONCAT('%', #{teacherName}, '%')
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

</mapper>
