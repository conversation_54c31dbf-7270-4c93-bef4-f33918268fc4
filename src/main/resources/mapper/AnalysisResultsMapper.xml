<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.AnalysisResultsMapper">

    <!-- 根据分析类型、目标ID和课程ID查询最新的分析结果 -->
    <select id="getLatestAnalysisResult" resultType="com.example.aieduforge.entity.AnalysisResults">
        SELECT * FROM analysis_results 
        WHERE analysis_type = #{analysisType} AND target_id = #{targetId} AND course_id = #{courseId}
        ORDER BY create_time DESC 
        LIMIT 1
    </select>
    
    <!-- 根据教师ID查询分析历史 -->
    <select id="getAnalysisHistoryByTeacher" resultType="com.example.aieduforge.entity.AnalysisResults">
        SELECT ar.*, su.real_name as target_name, c.course_name
        FROM analysis_results ar
        LEFT JOIN sys_user su ON ar.target_id = su.id AND ar.analysis_type LIKE '%student%'
        LEFT JOIN course c ON ar.course_id = c.id
        WHERE ar.teacher_id = #{teacherId}
        ORDER BY ar.create_time DESC
        LIMIT 50
    </select>
    
    <!-- 根据分析类型查询分析结果列表 -->
    <select id="getAnalysisResultsByType" resultType="com.example.aieduforge.entity.AnalysisResults">
        SELECT * FROM analysis_results 
        WHERE analysis_type = #{analysisType}
        ORDER BY create_time DESC
        LIMIT 100
    </select>

</mapper>