<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.CourseOutlineMapper">

    <!-- Find outlines by course ID -->
    <select id="findByCourseId" resultType="com.example.aieduforge.entity.CourseOutline">
        SELECT * FROM course_outline WHERE course_id = #{courseId}
        ORDER BY chapter_order
    </select>

    <!-- Find outlines by course ID ordered by chapter order -->
    <select id="findByCourseIdOrderByChapterOrder" resultType="com.example.aieduforge.entity.CourseOutline">
        SELECT * FROM course_outline WHERE course_id = #{courseId}
        ORDER BY chapter_order ASC
    </select>

</mapper>