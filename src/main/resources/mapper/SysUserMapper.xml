<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.SysUserMapper">

    <!-- Find user by username (do not filter by status; status is checked in service layer) -->
    <select id="findByUsername" resultType="com.example.aieduforge.entity.SysUser">
        SELECT * FROM sys_user WHERE username = #{username}
    </select>

    <!-- Update last login time -->
    <update id="updateLastLoginTime">
        UPDATE sys_user SET last_login_time = NOW() WHERE id = #{id}
    </update>

    <!-- Delete user role -->
    <delete id="deleteUserRole">
        DELETE FROM sys_user_role WHERE user_id = #{userId}
    </delete>

    <!-- Update user role -->
    <insert id="updateUserRole">
        INSERT INTO sys_user_role (user_id, role_id) VALUES (#{userId}, #{roleId})
    </insert>

    <!-- Get user growth trends -->
    <select id="getUserGrowthTrends" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as newUsers
        FROM sys_user 
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(create_time)
        ORDER BY date
    </select>

    <!-- Get user role distribution -->
    <select id="getUserRoleDistribution" resultType="java.util.Map">
        SELECT 
            SUM(CASE WHEN r.role_code = 'ADMIN' THEN 1 ELSE 0 END) as admins,
            SUM(CASE WHEN r.role_code = 'TEACHER' THEN 1 ELSE 0 END) as teachers,
            SUM(CASE WHEN r.role_code = 'STUDENT' THEN 1 ELSE 0 END) as students
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        WHERE u.status = 1
    </select>

    <!-- Get users by role code -->
    <select id="getUsersByRole" resultType="com.example.aieduforge.entity.SysUser">
        SELECT u.* 
        FROM sys_user u
        INNER JOIN sys_user_role ur ON u.id = ur.user_id
        INNER JOIN sys_role r ON ur.role_id = r.id
        WHERE r.role_code = #{roleCode} AND u.status = 1
        ORDER BY u.create_time DESC
    </select>

</mapper>
