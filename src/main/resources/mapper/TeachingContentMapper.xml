<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.TeachingContentMapper">

    <!-- Find content by course ID -->
    <select id="findByCourseId" resultType="com.example.aieduforge.entity.TeachingContent">
        SELECT * FROM teaching_content WHERE course_id = #{courseId}
        ORDER BY create_time DESC
    </select>

    <!-- Find content by outline ID -->
    <select id="findByOutlineId" resultType="com.example.aieduforge.entity.TeachingContent">
        SELECT * FROM teaching_content WHERE outline_id = #{outlineId}
        ORDER BY content_type, create_time
    </select>

    <!-- Find content by type -->
    <select id="findByContentType" resultType="com.example.aieduforge.entity.TeachingContent">
        SELECT * FROM teaching_content 
        WHERE course_id = #{courseId} AND content_type = #{contentType}
        ORDER BY create_time DESC
    </select>

    <!-- Find AI generated content -->
    <select id="findAiGeneratedContent" resultType="com.example.aieduforge.entity.TeachingContent">
        SELECT * FROM teaching_content 
        WHERE course_id = #{courseId} AND ai_generated = 1
        ORDER BY create_time DESC
    </select>

</mapper>