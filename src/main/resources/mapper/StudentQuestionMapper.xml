<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.StudentQuestionMapper">

    <!-- Find questions by student ID -->
    <select id="findByStudentId" resultType="com.example.aieduforge.entity.StudentQuestion">
        SELECT * FROM student_question WHERE student_id = #{studentId}
        ORDER BY create_time DESC
    </select>

    <!-- Find questions by course ID -->
    <select id="findByCourseId" resultType="com.example.aieduforge.entity.StudentQuestion">
        SELECT * FROM student_question WHERE course_id = #{courseId}
        ORDER BY create_time DESC
    </select>

    <!-- Find questions by student and course -->
    <select id="findByStudentAndCourse" resultType="com.example.aieduforge.entity.StudentQuestion">
        SELECT * FROM student_question 
        WHERE student_id = #{studentId} AND course_id = #{courseId}
        ORDER BY create_time DESC
    </select>

    <!-- Find questions by status -->
    <select id="findByStatus" resultType="com.example.aieduforge.entity.StudentQuestion">
        SELECT * FROM student_question WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- Find recent questions by student -->
    <select id="findRecentQuestionsByStudent" resultType="com.example.aieduforge.entity.StudentQuestion">
        SELECT * FROM student_question WHERE student_id = #{studentId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- Get question activities by date -->
    <select id="getQuestionActivitiesByDate" resultType="map">
        SELECT 
            student_id,
            COUNT(*) as question_count
        FROM student_question 
        WHERE DATE(create_time) = #{date}
        GROUP BY student_id
    </select>

</mapper>