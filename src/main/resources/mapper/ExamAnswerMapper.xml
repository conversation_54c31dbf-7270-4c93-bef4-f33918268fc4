<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.ExamAnswerMapper">

    <!-- Find answers by student ID -->
    <select id="findByStudentId" resultType="com.example.aieduforge.entity.ExamAnswer">
        SELECT * FROM exam_answer WHERE student_id = #{studentId}
        ORDER BY create_time DESC
    </select>

    <!-- Find answers by question ID -->
    <select id="findByQuestionId" resultType="com.example.aieduforge.entity.ExamAnswer">
        SELECT * FROM exam_answer WHERE question_id = #{questionId}
        ORDER BY create_time DESC
    </select>

    <!-- Find student answer for specific question -->
    <select id="findByStudentAndQuestion" resultType="com.example.aieduforge.entity.ExamAnswer">
        SELECT * FROM exam_answer 
        WHERE student_id = #{studentId} AND question_id = #{questionId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- Find incorrect answers by student -->
    <select id="findIncorrectAnswers" resultType="com.example.aieduforge.entity.ExamAnswer">
        SELECT * FROM exam_answer 
        WHERE student_id = #{studentId} AND is_correct = 0
        ORDER BY create_time DESC
    </select>

    <!-- Find answers by correctness -->
    <select id="findByCorrectness" resultType="com.example.aieduforge.entity.ExamAnswer">
        SELECT * FROM exam_answer 
        WHERE student_id = #{studentId} AND is_correct = #{isCorrect}
        ORDER BY create_time DESC
    </select>

    <!-- Get student answer statistics -->
    <select id="getStudentAnswerStatistics" resultType="map">
        SELECT 
            ea.id,
            ea.question_id,
            ea.student_id,
            ea.is_correct,
            ea.score,
            ea.answer_time,
            eq.course_id,
            eq.question_type,
            eq.difficulty_level
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE ea.student_id = #{studentId}
        <if test="courseId != null">
            AND eq.course_id = #{courseId}
        </if>
        ORDER BY ea.create_time DESC
    </select>

    <!-- Get pending grading answers with details -->
    <select id="getPendingGradingAnswersWithDetails" resultType="com.example.aieduforge.entity.ExamAnswer">
        SELECT 
            ea.*,
            eq.title as questionTitle,
            eq.content as questionContent,
            eq.correct_answer as correctAnswer,
            eq.question_type as questionType,
            eq.difficulty_level as difficultyLevel,
            eq.score as questionScore,
            c.course_name as courseName,
            su.real_name as studentName
        FROM exam_answer ea
        LEFT JOIN exam_question eq ON ea.question_id = eq.id
        LEFT JOIN course c ON eq.course_id = c.id
        LEFT JOIN sys_user su ON ea.student_id = su.id
        WHERE ea.needs_manual_review = 1
        <if test="courseId != null">
            AND eq.course_id = #{courseId}
        </if>
        <if test="questionType != null">
            AND eq.question_type = #{questionType}
        </if>
        ORDER BY ea.create_time DESC
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- Get answer detail with question and student info -->
    <resultMap id="AnswerDetailResultMap" type="com.example.aieduforge.entity.ExamAnswer">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="student_id" property="studentId" jdbcType="BIGINT"/>
        <result column="student_answer" property="studentAnswer" jdbcType="VARCHAR"/>
        <result column="is_correct" property="isCorrect" jdbcType="TINYINT"/>
        <result column="score" property="score" jdbcType="DECIMAL"/>
        <result column="ai_feedback" property="aiFeedback" jdbcType="VARCHAR"/>
        <result column="error_analysis" property="errorAnalysis" jdbcType="VARCHAR"/>
        <result column="improvement_suggestion" property="improvementSuggestion" jdbcType="VARCHAR"/>
        <result column="answer_time" property="answerTime" jdbcType="TIMESTAMP"/>
        <result column="evaluation_time" property="evaluationTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="needs_manual_review" property="needsManualReview" jdbcType="TINYINT"/>
        <result column="practice_id" property="practiceId" jdbcType="BIGINT"/>
        <result column="ai_score" property="aiScore" jdbcType="DECIMAL"/>
        <result column="teacher_score" property="teacherScore" jdbcType="DECIMAL"/>
        <result column="teacher_feedback" property="teacherFeedback" jdbcType="VARCHAR"/>
        <result column="grading_status" property="gradingStatus" jdbcType="TINYINT"/>
        <result column="teacher_id" property="teacherId" jdbcType="BIGINT"/>
        <result column="teacher_grading_time" property="teacherGradingTime" jdbcType="TIMESTAMP"/>
        <result column="questionTitle" property="questionTitle" jdbcType="VARCHAR"/>
        <result column="questionContent" property="questionContent" jdbcType="VARCHAR"/>
        <result column="correctAnswer" property="correctAnswer" jdbcType="VARCHAR"/>
        <result column="questionType" property="questionType" jdbcType="TINYINT"/>
        <result column="difficultyLevel" property="difficultyLevel" jdbcType="TINYINT"/>
        <result column="questionScore" property="questionScore" jdbcType="DECIMAL"/>
        <result column="answerAnalysis" property="answerAnalysis" jdbcType="VARCHAR"/>
        <result column="courseName" property="courseName" jdbcType="VARCHAR"/>
        <result column="studentName" property="studentName" jdbcType="VARCHAR"/>
        <result column="studentUsername" property="studentUsername" jdbcType="VARCHAR"/>
    </resultMap>
    
    <select id="getAnswerDetailWithInfo" resultType="java.util.HashMap">
        SELECT 
            ea.id,
            ea.question_id as questionId,
            ea.student_id as studentId,
            ea.student_answer as studentAnswer,
            ea.is_correct as isCorrect,
            ea.score,
            ea.ai_feedback as aiFeedback,
            ea.error_analysis as errorAnalysis,
            ea.improvement_suggestion as improvementSuggestion,
            ea.answer_time as answerTime,
            ea.evaluation_time as evaluationTime,
            ea.create_time as createTime,
            ea.needs_manual_review as needsManualReview,
            ea.practice_id as practiceId,
            ea.ai_score as aiScore,
            ea.teacher_score as teacherScore,
            ea.teacher_feedback as teacherFeedback,
            ea.grading_status as gradingStatus,
            ea.teacher_id as teacherId,
            ea.teacher_grading_time as teacherGradingTime,
            eq.title as questionTitle,
            eq.content as questionContent,
            eq.correct_answer as correctAnswer,
            eq.question_type as questionType,
            eq.difficulty_level as difficultyLevel,
            eq.score as questionScore,
            eq.answer_analysis as answerAnalysis,
            c.course_name as courseName,
            su.real_name as studentName,
            su.username as studentUsername
        FROM exam_answer ea
        LEFT JOIN exam_question eq ON ea.question_id = eq.id
        LEFT JOIN course c ON eq.course_id = c.id
        LEFT JOIN sys_user su ON ea.student_id = su.id
        WHERE ea.id = #{answerId}
    </select>

    <!-- Result map for grading answers with details -->
    <resultMap id="GradingAnswerResultMap" type="com.example.aieduforge.entity.ExamAnswer">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="student_id" property="studentId" jdbcType="BIGINT"/>
        <result column="student_answer" property="studentAnswer" jdbcType="VARCHAR"/>
        <result column="is_correct" property="isCorrect" jdbcType="TINYINT"/>
        <result column="score" property="score" jdbcType="DECIMAL"/>
        <result column="ai_feedback" property="aiFeedback" jdbcType="VARCHAR"/>
        <result column="error_analysis" property="errorAnalysis" jdbcType="VARCHAR"/>
        <result column="improvement_suggestion" property="improvementSuggestion" jdbcType="VARCHAR"/>
        <result column="answer_time" property="answerTime" jdbcType="TIMESTAMP"/>
        <result column="evaluation_time" property="evaluationTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="needs_manual_review" property="needsManualReview" jdbcType="TINYINT"/>
        <result column="practice_id" property="practiceId" jdbcType="BIGINT"/>
        <result column="ai_score" property="aiScore" jdbcType="DECIMAL"/>
        <result column="teacher_score" property="teacherScore" jdbcType="DECIMAL"/>
        <result column="teacher_feedback" property="teacherFeedback" jdbcType="VARCHAR"/>
        <result column="grading_status" property="gradingStatus" jdbcType="TINYINT"/>
        <result column="teacher_id" property="teacherId" jdbcType="BIGINT"/>
        <result column="teacher_grading_time" property="teacherGradingTime" jdbcType="TIMESTAMP"/>
        <result column="questionTitle" property="questionTitle" jdbcType="VARCHAR"/>
        <result column="questionContent" property="questionContent" jdbcType="VARCHAR"/>
        <result column="correctAnswer" property="correctAnswer" jdbcType="VARCHAR"/>
        <result column="questionType" property="questionType" jdbcType="TINYINT"/>
        <result column="difficultyLevel" property="difficultyLevel" jdbcType="TINYINT"/>
        <result column="questionScore" property="questionScore" jdbcType="DECIMAL"/>
        <result column="courseName" property="courseName" jdbcType="VARCHAR"/>
        <result column="studentName" property="studentName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- Get answers for grading with details (supports grading status filter) -->
    <select id="getAnswersForGradingWithDetails" resultMap="GradingAnswerResultMap">
        SELECT 
            ea.id,
            ea.question_id,
            ea.student_id,
            ea.student_answer,
            ea.is_correct,
            ea.score,
            ea.ai_feedback,
            ea.error_analysis,
            ea.improvement_suggestion,
            ea.answer_time,
            ea.evaluation_time,
            ea.create_time,
            ea.needs_manual_review,
            ea.practice_id,
            ea.ai_score,
            ea.teacher_score,
            ea.teacher_feedback,
            ea.grading_status,
            ea.teacher_id,
            ea.teacher_grading_time,
            eq.title as questionTitle,
            eq.content as questionContent,
            eq.correct_answer as correctAnswer,
            eq.question_type as questionType,
            eq.difficulty_level as difficultyLevel,
            eq.score as questionScore,
            c.course_name as courseName,
            su.real_name as studentName
        FROM exam_answer ea
        LEFT JOIN exam_question eq ON ea.question_id = eq.id
        LEFT JOIN course c ON eq.course_id = c.id
        LEFT JOIN sys_user su ON ea.student_id = su.id
        WHERE 1=1
        <if test="gradingStatus != null">
            <choose>
                <when test="gradingStatus == 1">
                    AND ea.needs_manual_review = 1 AND (ea.grading_status = 0 OR ea.grading_status = 1)
                </when>
                <when test="gradingStatus == 2">
                    AND ea.grading_status = 2
                </when>
                <otherwise>
                    <!-- gradingStatus == null 或其他值，显示所有 -->
                </otherwise>
            </choose>
        </if>
        <if test="courseId != null">
            AND eq.course_id = #{courseId}
        </if>
        <if test="questionType != null">
            AND eq.question_type = #{questionType}
        </if>
        ORDER BY ea.create_time DESC
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- Update practice record score -->
    <update id="updatePracticeRecordScore">
        UPDATE practice_record 
        SET score = #{totalScore}, correct_count = #{correctCount}
        WHERE id = #{practiceId}
    </update>

    <!-- Get course overall accuracy -->
    <select id="getCourseOverallAccuracy" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
            END as accuracy
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE eq.course_id = #{courseId}
    </select>

    <!-- Get course average score -->
    <select id="getCourseAverageScore" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(AVG(COALESCE(ea.teacher_score, ea.ai_score, ea.score)), 2)
            END as avgScore
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE eq.course_id = #{courseId}
    </select>

    <!-- Get course answer count -->
    <select id="getCourseAnswerCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE eq.course_id = #{courseId}
    </select>

    <!-- Get student answer count for a course -->
    <select id="getStudentAnswerCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE ea.student_id = #{studentId} AND eq.course_id = #{courseId}
    </select>

    <!-- Get student accuracy for a course -->
    <select id="getStudentAccuracy" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
            END as accuracy
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE ea.student_id = #{studentId} AND eq.course_id = #{courseId}
    </select>

    <!-- Get student average score for a course -->
    <select id="getStudentAverageScore" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(AVG(COALESCE(ea.teacher_score, ea.ai_score, ea.score)), 2)
            END as avgScore
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE ea.student_id = #{studentId} AND eq.course_id = #{courseId}
    </select>

    <!-- Get course student count (distinct students who answered questions) -->
    <select id="getCourseStudentCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT ea.student_id)
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE eq.course_id = #{courseId}
    </select>

    <!-- Get student rankings for a course -->
    <select id="getStudentRankings" resultType="map">
        SELECT 
            ROW_NUMBER() OVER (ORDER BY avg_score DESC, accuracy DESC) as ranking,
            student_id as studentId,
            student_name as studentName,
            ROUND(avg_score, 1) as avgScore,
            ROUND(accuracy, 1) as accuracy,
            CASE 
                WHEN accuracy >= 90 THEN '优秀'
                WHEN accuracy >= 80 THEN '良好'
                WHEN accuracy >= 70 THEN '中等'
                ELSE '待提高'
            END as performanceLevel
        FROM (
            SELECT 
                ea.student_id,
                su.real_name as student_name,
                AVG(COALESCE(ea.teacher_score, ea.ai_score, ea.score)) as avg_score,
                SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as accuracy
            FROM exam_answer ea
            INNER JOIN exam_question eq ON ea.question_id = eq.id
            INNER JOIN sys_user su ON ea.student_id = su.id
            WHERE eq.course_id = #{courseId}
            GROUP BY ea.student_id, su.real_name
            HAVING COUNT(*) > 0
        ) student_stats
        ORDER BY avg_score DESC, accuracy DESC
        LIMIT 10
    </select>

    <!-- Get knowledge point statistics from questions -->
    <select id="getKnowledgePointStatsFromQuestions" resultType="map">
        SELECT 
            COALESCE(NULLIF(TRIM(eq.knowledge_points), ''), '基础知识') as knowledgePoint,
            COUNT(DISTINCT eq.id) as totalQuestions,
            ROUND(
                CASE 
                    WHEN COUNT(ea.id) = 0 THEN 0.0
                    ELSE SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(ea.id)
                END, 2
            ) as avgAccuracy,
            ROUND(AVG(eq.difficulty_level), 0) as avgDifficulty
        FROM exam_question eq
        LEFT JOIN exam_answer ea ON eq.id = ea.question_id
        WHERE eq.course_id = #{courseId} AND eq.status = 1
        GROUP BY COALESCE(NULLIF(TRIM(eq.knowledge_points), ''), '基础知识')
        HAVING COUNT(DISTINCT eq.id) > 0
        ORDER BY avgAccuracy DESC, totalQuestions DESC
    </select>

    <!-- Get performance distribution for a course -->
    <select id="getPerformanceDistribution" resultType="map">
        <![CDATA[
        SELECT 
            SUM(CASE WHEN accuracy >= 90 THEN 1 ELSE 0 END) as excellent,
            SUM(CASE WHEN accuracy >= 80 AND accuracy < 90 THEN 1 ELSE 0 END) as good,
            SUM(CASE WHEN accuracy >= 70 AND accuracy < 80 THEN 1 ELSE 0 END) as average,
            SUM(CASE WHEN accuracy < 70 THEN 1 ELSE 0 END) as needsImprovement,
            ROUND(SUM(CASE WHEN accuracy >= 90 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as excellentPercent,
            ROUND(SUM(CASE WHEN accuracy >= 80 AND accuracy < 90 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as goodPercent,
            ROUND(SUM(CASE WHEN accuracy >= 70 AND accuracy < 80 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as averagePercent,
            ROUND(SUM(CASE WHEN accuracy < 70 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as needsImprovementPercent,
            COUNT(*) as totalStudents
        FROM (
            SELECT 
                ea.student_id,
                SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as accuracy
            FROM exam_answer ea
            INNER JOIN exam_question eq ON ea.question_id = eq.id
            WHERE eq.course_id = #{courseId}
            GROUP BY ea.student_id
            HAVING COUNT(*) > 0
        ) student_performance
        ]]>
    </select>

    <!-- Get student answers with details for analysis -->
    <select id="getStudentAnswersWithDetails" resultType="map">
        SELECT 
            ea.id,
            ea.question_id as questionId,
            ea.student_id as studentId,
            ea.student_answer as studentAnswer,
            ea.is_correct as isCorrect,
            ea.score,
            ea.answer_time as answerTime,
            eq.title as questionTitle,
            eq.content as questionContent,
            eq.correct_answer as correctAnswer,
            eq.question_type as questionType,
            eq.difficulty_level as difficultyLevel,
            eq.score as questionScore,
            c.course_name as courseName,
            su.real_name as studentName
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        INNER JOIN course c ON eq.course_id = c.id
        INNER JOIN sys_user su ON ea.student_id = su.id
        WHERE eq.course_id = #{courseId}
        ORDER BY ea.answer_time DESC, ea.id DESC
        LIMIT 200
    </select>

    <!-- Get student knowledge point statistics -->
    <select id="getStudentKnowledgePointStats" resultType="map">
        SELECT 
            COALESCE(NULLIF(TRIM(eq.knowledge_points), ''), '基础知识') as knowledgePoint,
            COUNT(*) as totalQuestions,
            SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) as correctCount,
            ROUND(
                CASE 
                    WHEN COUNT(*) = 0 THEN 0.0
                    ELSE SUM(CASE WHEN ea.is_correct = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                END, 2
            ) as accuracy
        FROM exam_answer ea
        INNER JOIN exam_question eq ON ea.question_id = eq.id
        WHERE ea.student_id = #{studentId} AND eq.course_id = #{courseId}
        GROUP BY COALESCE(NULLIF(TRIM(eq.knowledge_points), ''), '基础知识')
        HAVING COUNT(*) > 0
        ORDER BY accuracy DESC, totalQuestions DESC
    </select>

</mapper>