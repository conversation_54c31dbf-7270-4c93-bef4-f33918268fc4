<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.ExamQuestionMapper">

    <!-- 随机取指定数量的题目 -->
    <select id="findRandomQuestions" resultType="com.example.aieduforge.entity.ExamQuestion">
        SELECT *
        FROM exam_question
        WHERE course_id = #{courseId}
          AND status = 1
        ORDER BY RAND()
        LIMIT #{count}
    </select>

    <!-- 根据难度等级获取题目 -->
    <select id="findByDifficultyLevel" resultType="com.example.aieduforge.entity.ExamQuestion">
        SELECT *
        FROM exam_question
        WHERE course_id = #{courseId}
          AND difficulty_level = #{difficultyLevel}
          AND status = 1
    </select>

    <!-- 根据题目类型随机获取指定数量的题目 -->
    <select id="findRandomQuestionsByType" resultType="com.example.aieduforge.entity.ExamQuestion">
        SELECT *
        FROM exam_question
        WHERE course_id = #{courseId}
          AND question_type = #{questionType}
          AND status = 1
        ORDER BY RAND()
        LIMIT #{count}
    </select>

</mapper>
