<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.AIKnowledgeDataMapper">

    <!-- 根据知识库ID查找AI数据 -->
    <select id="findByKnowledgeBaseId" resultType="com.example.aieduforge.entity.AIKnowledgeData">
        SELECT * FROM ai_knowledge_data 
        WHERE knowledge_base_id = #{knowledgeBaseId} AND status = 1
    </select>

    <!-- 搜索相似内容 -->
    <select id="searchSimilarContent" resultType="map">
        SELECT 
            akd.*,
            kb.title,
            kb.subject,
            kb.file_type
        FROM ai_knowledge_data akd
        LEFT JOIN knowledge_base kb ON akd.knowledge_base_id = kb.id
        WHERE akd.status = 1 AND kb.status = 1
        AND (
            akd.extracted_text LIKE CONCAT('%', #{keyword}, '%')
            OR akd.summary LIKE CONCAT('%', #{keyword}, '%')
            OR akd.key_points LIKE CONCAT('%', #{keyword}, '%')
            OR akd.smart_tags LIKE CONCAT('%', #{keyword}, '%')
            OR kb.title LIKE CONCAT('%', #{keyword}, '%')
        )
        ORDER BY akd.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取知识点统计 -->
    <select id="getKnowledgeStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalDocuments,
            COALESCE(SUM(akd.word_count), 0) as totalWords,
            COALESCE(AVG(akd.word_count), 0) as avgWords,
            COUNT(DISTINCT kb.subject) as subjectCount,
            COUNT(DISTINCT kb.course_id) as courseCount
        FROM ai_knowledge_data akd
        LEFT JOIN knowledge_base kb ON akd.knowledge_base_id = kb.id
        WHERE akd.status = 1 AND kb.status = 1
    </select>

</mapper>