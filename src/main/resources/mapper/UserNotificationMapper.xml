<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.UserNotificationMapper">

    <!-- Get user notification record -->
    <select id="getUserNotificationRecord" resultType="map">
        SELECT * FROM user_notification 
        WHERE user_id = #{userId} AND notification_id = #{notificationId}
    </select>

    <!-- Update read status -->
    <update id="updateReadStatus">
        UPDATE user_notification 
        SET is_read = 1, read_time = #{readTime}
        WHERE user_id = #{userId} AND notification_id = #{notificationId}
    </update>

    <!-- Insert user notification record -->
    <insert id="insertUserNotification">
        INSERT INTO user_notification (user_id, notification_id, is_read, read_time, create_time)
        VALUES (#{userId}, #{notificationId}, #{isRead}, #{readTime}, NOW())
    </insert>

</mapper>