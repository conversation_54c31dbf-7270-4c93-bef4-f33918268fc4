<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.SysNotificationMapper">

    <!-- 获取用户的通知列表 -->
    <select id="getUserNotifications" resultType="map">
        SELECT n.id, n.title, n.content, n.type, n.target_role, n.target_user_id, 
               n.priority, n.status, n.create_user_id, n.create_time, n.update_time, n.expire_time,
               COALESCE(un.is_read, 0) as is_read, 
               un.read_time 
        FROM sys_notification n 
        LEFT JOIN user_notification un ON n.id = un.notification_id AND un.user_id = #{userId} 
        WHERE n.status = 1 
        AND (n.target_role = 'ALL' OR n.target_role = #{userRole} OR n.target_user_id = #{userId}) 
        AND (n.expire_time IS NULL OR n.expire_time > NOW()) 
        ORDER BY n.priority DESC, n.create_time DESC 
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 获取用户通知总数 -->
    <select id="getUserNotificationCount" resultType="int">
        SELECT COUNT(*) FROM sys_notification n 
        WHERE n.status = 1 
        AND (n.target_role = 'ALL' OR n.target_role = #{userRole} OR n.target_user_id = #{userId}) 
        AND (n.expire_time IS NULL OR n.expire_time > NOW())
    </select>

    <!-- 获取用户未读通知数量 -->
    <select id="getUnreadNotificationCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM sys_notification n 
        LEFT JOIN user_notification un ON n.id = un.notification_id AND un.user_id = #{userId} 
        WHERE n.status = 1 
        AND (n.target_role = 'ALL' OR n.target_role = #{userRole} OR n.target_user_id = #{userId}) 
        AND (n.expire_time IS NULL OR n.expire_time > NOW()) 
        AND (un.is_read IS NULL OR un.is_read = 0)
    </select>

    <!-- 获取用户未读通知列表 -->
    <select id="getUnreadNotifications" resultType="map">
        SELECT n.id, n.title, n.content, n.type, n.target_role, n.target_user_id, 
               n.priority, n.status, n.create_user_id, n.create_time, n.update_time, n.expire_time,
               COALESCE(un.is_read, 0) as is_read, 
               un.read_time 
        FROM sys_notification n 
        LEFT JOIN user_notification un ON n.id = un.notification_id AND un.user_id = #{userId} 
        WHERE n.status = 1 
        AND (n.target_role = 'ALL' OR n.target_role = #{userRole} OR n.target_user_id = #{userId}) 
        AND (n.expire_time IS NULL OR n.expire_time > NOW()) 
        AND (un.is_read IS NULL OR un.is_read = 0)
        ORDER BY n.priority DESC, n.create_time DESC
    </select>

    <!-- 插入新通知 -->
    <insert id="insertNotification">
        INSERT INTO sys_notification (
            title, content, type, target_role, target_user_id, 
            priority, status, create_user_id, create_time, update_time
        ) VALUES (
            #{title}, #{content}, #{type}, #{targetRole}, #{targetUserId}, 
            #{priority}, 1, #{createUserId}, #{createTime}, #{createTime}
        )
    </insert>

    <!-- 获取最新的系统通知 -->
    <select id="getLatestSystemNotifications" resultType="map">
        SELECT id, title, content, type, priority, create_time, update_time
        FROM sys_notification 
        WHERE status = 1 AND type = 1 
        ORDER BY create_time DESC 
        LIMIT #{limit}
    </select>

    <!-- 获取通知详情 -->
    <select id="getNotificationDetail" resultType="map">
        SELECT * FROM sys_notification 
        WHERE id = #{notificationId} AND status = 1
    </select>

    <!-- 删除通知（软删除） -->
    <update id="deleteNotification">
        UPDATE sys_notification 
        SET status = 0, update_time = NOW() 
        WHERE id = #{notificationId}
    </update>

    <!-- 清理过期通知 -->
    <update id="cleanExpiredNotifications">
        UPDATE sys_notification 
        SET status = 0, update_time = NOW() 
        WHERE status = 1 AND expire_time IS NOT NULL AND expire_time &lt; NOW()
    </update>

    <!-- 清理旧的用户通知记录 -->
    <delete id="cleanOldUserNotifications">
        DELETE un FROM user_notification un
        INNER JOIN sys_notification sn ON un.notification_id = sn.id
        WHERE un.is_read = 1 
        AND sn.create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 获取指定日期的通知发送数量 -->
    <select id="getNotificationCountByDate" resultType="int">
        SELECT COUNT(*) FROM sys_notification 
        WHERE status = 1 
        <if test="date == 'yesterday'">
            AND DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        </if>
        <if test="date == 'today'">
            AND DATE(create_time) = CURDATE()
        </if>
        <if test="date != 'yesterday' and date != 'today'">
            AND DATE(create_time) = #{date}
        </if>
    </select>

    <!-- 获取指定日期的通知阅读数量 -->
    <select id="getReadNotificationCountByDate" resultType="int">
        SELECT COUNT(DISTINCT un.notification_id) 
        FROM user_notification un
        INNER JOIN sys_notification sn ON un.notification_id = sn.id
        WHERE un.is_read = 1 AND sn.status = 1
        <if test="date == 'yesterday'">
            AND DATE(sn.create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        </if>
        <if test="date == 'today'">
            AND DATE(sn.create_time) = CURDATE()
        </if>
        <if test="date != 'yesterday' and date != 'today'">
            AND DATE(sn.create_time) = #{date}
        </if>
    </select>

    <!-- 获取通知总数 -->
    <select id="getTotalNotificationCount" resultType="int">
        SELECT COUNT(*) FROM sys_notification WHERE status = 1
    </select>

    <!-- 获取通知类型统计 -->
    <select id="getNotificationTypeStats" resultType="map">
        SELECT 
            type,
            CASE 
                WHEN type = 1 THEN '系统通知'
                WHEN type = 2 THEN '课程通知'
                WHEN type = 3 THEN '作业通知'
                ELSE '其他'
            END as typeName,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sys_notification WHERE status = 1), 2) as percentage
        FROM sys_notification 
        WHERE status = 1 
        GROUP BY type
        ORDER BY count DESC
    </select>

    <!-- 按角色获取阅读统计 -->
    <select id="getReadStatsByRole" resultType="map">
        SELECT 
            target_role as role,
            CASE 
                WHEN target_role = 'ALL' THEN '所有用户'
                WHEN target_role = 'TEACHER' THEN '教师'
                WHEN target_role = 'STUDENT' THEN '学生'
                WHEN target_role = 'ADMIN' THEN '管理员'
                ELSE target_role
            END as roleName,
            COUNT(*) as totalSent,
            COUNT(CASE WHEN un.is_read = 1 THEN 1 END) as totalRead,
            ROUND(COUNT(CASE WHEN un.is_read = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as readRate
        FROM sys_notification sn
        LEFT JOIN user_notification un ON sn.id = un.notification_id
        WHERE sn.status = 1
        GROUP BY target_role
        ORDER BY totalSent DESC
    </select>

    <!-- 按优先级获取阅读统计 -->
    <select id="getReadStatsByPriority" resultType="map">
        SELECT 
            priority,
            CASE 
                WHEN priority = 1 THEN '普通'
                WHEN priority = 2 THEN '重要'
                WHEN priority = 3 THEN '紧急'
                ELSE '其他'
            END as priorityName,
            COUNT(*) as totalSent,
            COUNT(CASE WHEN un.is_read = 1 THEN 1 END) as totalRead,
            ROUND(COUNT(CASE WHEN un.is_read = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as readRate
        FROM sys_notification sn
        LEFT JOIN user_notification un ON sn.id = un.notification_id
        WHERE sn.status = 1
        GROUP BY priority
        ORDER BY priority
    </select>

    <!-- 获取所有通知（管理员用） -->
    <select id="getAllNotifications" resultType="map">
        SELECT 
            id, title, content, type, target_role, target_user_id, 
            priority, status, create_user_id, create_time, update_time, expire_time
        FROM sys_notification 
        WHERE status = 1
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="targetRole != null and targetRole != ''">
            AND target_role = #{targetRole}
        </if>
        <if test="priority != null">
            AND priority = #{priority}
        </if>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 获取所有通知总数（管理员用） -->
    <select id="getAllNotificationCount" resultType="int">
        SELECT COUNT(*) FROM sys_notification 
        WHERE status = 1
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="targetRole != null and targetRole != ''">
            AND target_role = #{targetRole}
        </if>
        <if test="priority != null">
            AND priority = #{priority}
        </if>
    </select>

</mapper>