<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.TeachingEfficiencyMapper">

    <!-- Find by teacher ID -->
    <select id="findByTeacherId" resultType="com.example.aieduforge.entity.TeachingEfficiency">
        SELECT * FROM teaching_efficiency 
        WHERE teacher_id = #{teacherId}
        ORDER BY stat_date DESC
    </select>

    <!-- Find by course ID -->
    <select id="findByCourseId" resultType="com.example.aieduforge.entity.TeachingEfficiency">
        SELECT * FROM teaching_efficiency 
        WHERE course_id = #{courseId}
        ORDER BY stat_date DESC
    </select>

    <!-- Get teacher efficiency ranking -->
    <select id="getTeacherEfficiencyRanking" resultType="map">
        SELECT 
            te.teacher_id,
            su.real_name as teacher_name,
            AVG(te.efficiency_index) as avg_efficiency,
            SUM(te.student_count) as total_students,
            AVG(te.avg_student_score) as avg_score,
            AVG(te.pass_rate) as avg_pass_rate
        FROM teaching_efficiency te
        LEFT JOIN sys_user su ON te.teacher_id = su.id
        WHERE te.stat_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY te.teacher_id, su.real_name
        ORDER BY avg_efficiency DESC
        LIMIT #{limit}
    </select>

    <!-- Get course efficiency statistics -->
    <select id="getCourseEfficiencyStats" resultType="map">
        SELECT 
            te.course_id,
            c.course_name,
            AVG(te.efficiency_index) as avg_efficiency,
            AVG(te.preparation_time) as avg_prep_time,
            AVG(te.correction_time) as avg_correction_time,
            AVG(te.pass_rate) as avg_pass_rate
        FROM teaching_efficiency te
        LEFT JOIN course c ON te.course_id = c.id
        WHERE te.teacher_id = #{teacherId}
        AND te.stat_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY te.course_id, c.course_name
        ORDER BY avg_efficiency DESC
    </select>

    <!-- Get efficiency trend -->
    <select id="getEfficiencyTrend" resultType="map">
        SELECT 
            stat_date,
            AVG(efficiency_index) as efficiency,
            AVG(preparation_time) as prep_time,
            AVG(correction_time) as correction_time,
            AVG(pass_rate) as pass_rate
        FROM teaching_efficiency 
        WHERE teacher_id = #{teacherId}
        AND stat_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY stat_date
        ORDER BY stat_date ASC
    </select>

    <!-- Get average efficiency times -->
    <select id="getAverageEfficiencyTimes" resultType="map">
        SELECT 
            ROUND(AVG(preparation_time), 1) as avgPreparationTime,
            ROUND(AVG(correction_time), 1) as avgExerciseTime,
            ROUND(AVG(pass_rate), 1) as avgPassRate
        FROM teaching_efficiency 
        WHERE stat_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    </select>

    <!-- Get efficiency trends -->
    <select id="getEfficiencyTrends" resultType="map">
        SELECT 
            0.0 as preparationTrend,
            0.0 as exerciseTrend,
            0.0 as passRateTrend
    </select>

    <!-- Insert efficiency record -->
    <insert id="insertEfficiencyRecord">
        INSERT INTO teaching_efficiency (
            teacher_id, course_id, preparation_time, correction_time, design_time,
            student_count, avg_student_score, pass_rate, efficiency_index,
            optimization_suggestion, stat_date, create_time, update_time
        ) VALUES (
            #{teacherId}, #{courseId}, #{preparationTime}, #{correctionTime}, #{designTime},
            #{studentCount}, #{avgStudentScore}, #{passRate}, #{efficiencyIndex},
            #{optimizationSuggestion}, #{statDate}, NOW(), NOW()
        )
    </insert>

    <!-- Get latest efficiency data -->
    <select id="getLatestEfficiencyData" resultType="map">
        SELECT 
            preparation_time,
            correction_time,
            design_time,
            student_count,
            avg_student_score,
            efficiency_index,
            optimization_suggestion,
            stat_date,
            create_time
        FROM teaching_efficiency 
        ORDER BY create_time DESC 
        LIMIT 1
    </select>

    <!-- Get previous efficiency data for trend calculation -->
    <select id="getPreviousEfficiencyData" resultType="map">
        SELECT 
            preparation_time,
            correction_time,
            design_time,
            efficiency_index
        FROM teaching_efficiency 
        ORDER BY create_time DESC 
        LIMIT 1 OFFSET 1
    </select>

</mapper>