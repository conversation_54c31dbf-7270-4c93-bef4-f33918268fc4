<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.aieduforge.mapper.UsageStatisticsMapper">

    <!-- Get daily usage statistics by user type -->
    <select id="getDailyUsageByUserType" resultType="map">
        SELECT 
            user_type,
            stat_date,
            SUM(access_count) as total_access,
            COUNT(DISTINCT user_id) as active_users,
            SUM(duration_seconds) as total_duration
        FROM usage_statistics 
        WHERE stat_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY user_type, stat_date
        ORDER BY stat_date DESC
    </select>

    <!-- Get module usage statistics -->
    <select id="getModuleUsageStats" resultType="map">
        SELECT 
            module_name,
            SUM(access_count) as total_access,
            COUNT(DISTINCT user_id) as unique_users,
            AVG(duration_seconds) as avg_duration
        FROM usage_statistics 
        WHERE user_type = #{userType}
        AND stat_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY module_name
        ORDER BY total_access DESC
    </select>

    <!-- Get active users count -->
    <select id="getActiveUsersCount" resultType="map">
        SELECT 
            COUNT(DISTINCT CASE WHEN user_type = 1 THEN user_id END) as active_teachers,
            COUNT(DISTINCT CASE WHEN user_type = 2 THEN user_id END) as active_students,
            COUNT(DISTINCT user_id) as total_active_users
        FROM usage_statistics 
        WHERE stat_date = #{date}
    </select>

    <!-- Get user activity ranking -->
    <select id="getUserActivityRanking" resultType="map">
        SELECT 
            u.user_id,
            su.username,
            su.real_name,
            SUM(u.access_count) as total_access,
            SUM(u.duration_seconds) as total_duration
        FROM usage_statistics u
        LEFT JOIN sys_user su ON u.user_id = su.id
        WHERE u.user_type = #{userType}
        AND u.stat_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY u.user_id, su.username, su.real_name
        ORDER BY total_access DESC
        LIMIT #{limit}
    </select>

    <!-- Get usage trend data -->
    <select id="getUsageTrend" resultType="map">
        SELECT 
            stat_date,
            SUM(access_count) as daily_access,
            COUNT(DISTINCT user_id) as daily_users,
            SUM(duration_seconds) as daily_duration
        FROM usage_statistics 
        WHERE user_type = #{userType}
        AND stat_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY stat_date
        ORDER BY stat_date ASC
    </select>

</mapper>