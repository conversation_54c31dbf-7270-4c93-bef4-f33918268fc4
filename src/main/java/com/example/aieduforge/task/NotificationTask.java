package com.example.aieduforge.task;

import com.example.aieduforge.mapper.SysNotificationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 通知相关定时任务
 */
@Slf4j
@Component
public class NotificationTask {

    @Autowired
    private SysNotificationMapper sysNotificationMapper;

    /**
     * 清理过期通知
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredNotifications() {
        try {
            log.info("开始清理过期通知");
            int count = sysNotificationMapper.cleanExpiredNotifications();
            log.info("清理过期通知完成，共清理{}条记录", count);
        } catch (Exception e) {
            log.error("清理过期通知失败", e);
        }
    }

    /**
     * 清理旧的用户通知记录
     * 每周日凌晨3点执行，清理30天前的已读通知记录
     */
    @Scheduled(cron = "0 0 3 * * 0")
    public void cleanOldUserNotifications() {
        try {
            log.info("开始清理旧的用户通知记录");
            int count = sysNotificationMapper.cleanOldUserNotifications(30);
            log.info("清理旧的用户通知记录完成，共清理{}条记录", count);
        } catch (Exception e) {
            log.error("清理旧的用户通知记录失败", e);
        }
    }

    /**
     * 统计通知发送情况
     * 每天早上8点执行
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void generateNotificationStats() {
        try {
            log.info("开始生成通知统计报告");
            
            // 获取昨天的通知统计
            int totalSent = sysNotificationMapper.getNotificationCountByDate("yesterday");
            int totalRead = sysNotificationMapper.getReadNotificationCountByDate("yesterday");
            double readRate = totalSent > 0 ? (double) totalRead / totalSent * 100 : 0;
            
            log.info("昨日通知统计 - 发送总数: {}, 已读总数: {}, 阅读率: {:.2f}%", 
                    totalSent, totalRead, readRate);
            
            // 这里可以将统计数据保存到数据库或发送报告邮件
            
        } catch (Exception e) {
            log.error("生成通知统计报告失败", e);
        }
    }
}