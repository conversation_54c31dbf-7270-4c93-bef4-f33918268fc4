package com.example.aieduforge.task;

import com.example.aieduforge.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 验证码清理定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CaptchaCleanupTask {
    
    private final CaptchaService captchaService;
    
    /**
     * 每10分钟清理一次过期的验证码
     */
    @Scheduled(fixedRate = 10 * 60 * 1000)
    public void cleanExpiredCaptcha() {
        try {
            log.debug("开始清理过期验证码");
            captchaService.cleanExpiredCaptcha();
            log.debug("过期验证码清理完成");
        } catch (Exception e) {
            log.error("清理过期验证码失败", e);
        }
    }
}