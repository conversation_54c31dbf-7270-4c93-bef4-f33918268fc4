package com.example.aieduforge.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * Practice Request DTO
 */
@Data
public class PracticeRequest {
    
    @NotNull(message = "Course ID cannot be null")
    private Long courseId;
    
    @NotNull(message = "Practice type cannot be null")
    private Integer practiceType; // 1-随机练习，2-专项练习，3-错题重做
    
    @Min(value = 1, message = "Question count must be at least 1")
    @Max(value = 50, message = "Question count cannot exceed 50")
    private Integer questionCount = 10;
    
    private Integer difficultyLevel; // 1-简单，2-中等，3-困难
    
    private String knowledgePoints; // 知识点筛选
    
    private Integer questionType; // 题目类型：1-单选题，2-多选题，3-填空题，4-简答题，5-编程题，6-案例分析题
    
    private java.util.List<Long> questionIds; // 题目ID列表（可选，如果提供则使用指定题目）
}