package com.example.aieduforge.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * Student Question Request DTO
 */
@Data
public class QuestionRequest {
    
    private Long courseId;
    
    @NotBlank(message = "Question content cannot be empty")
    private String questionContent;
    
    private Integer questionType; // 1-知识询问，2-练习求助，3-其他
    
    private String contextInfo;
}