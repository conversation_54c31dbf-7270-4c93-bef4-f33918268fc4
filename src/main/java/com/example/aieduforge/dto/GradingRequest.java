package com.example.aieduforge.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import java.math.BigDecimal;

/**
 * Grading Request DTO
 */
@Data
public class GradingRequest {
    
    @NotNull(message = "答案ID不能为空")
    private Long answerId;
    
    @NotNull(message = "分数不能为空")
    @DecimalMin(value = "0.0", message = "分数不能小于0")
    @DecimalMax(value = "100.0", message = "分数不能大于100")
    private BigDecimal score;
    
    @NotNull(message = "正确性判断不能为空")
    private Integer isCorrect; // 0-错误，1-正确，2-部分正确
    
    private String feedback; // 教师反馈
    
    private String improvementSuggestion; // 改进建议
}