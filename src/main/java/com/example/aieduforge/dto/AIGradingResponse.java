package com.example.aieduforge.dto;

import lombok.Data;

/**
 * AI评分响应DTO
 */
@Data
public class AIGradingResponse {
    
    /**
     * AI评分 (0-总分)
     */
    private Double score;
    
    /**
     * 是否正确 (1-正确, 0-错误, 2-部分正确)
     */
    private Integer isCorrect;
    
    /**
     * AI反馈
     */
    private String feedback;
    
    /**
     * 改进建议
     */
    private String improvementSuggestion;
    
    /**
     * 详细分析
     */
    private String detailedAnalysis;
    
    /**
     * 相似度 (0-1)
     */
    private Double similarity;
    
    /**
     * 是否需要人工复核
     */
    private Boolean needsManualReview;
    
    /**
     * AI评分的置信度 (0-1)
     */
    private Double confidence;
    
    /**
     * 错误信息（如果评分失败）
     */
    private String errorMessage;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    public AIGradingResponse() {
        this.success = true;
    }
    
    public static AIGradingResponse error(String errorMessage) {
        AIGradingResponse response = new AIGradingResponse();
        response.setSuccess(false);
        response.setErrorMessage(errorMessage);
        return response;
    }
    
    public static AIGradingResponse success(Double score, Integer isCorrect, String feedback, String improvementSuggestion) {
        AIGradingResponse response = new AIGradingResponse();
        response.setScore(score);
        response.setIsCorrect(isCorrect);
        response.setFeedback(feedback);
        response.setImprovementSuggestion(improvementSuggestion);
        response.setSuccess(true);
        return response;
    }
}