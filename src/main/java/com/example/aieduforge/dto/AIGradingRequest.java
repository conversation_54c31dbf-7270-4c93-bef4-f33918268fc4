package com.example.aieduforge.dto;

import lombok.Data;

/**
 * AI评分请求DTO
 */
@Data
public class AIGradingRequest {
    
    /**
     * 题目标题
     */
    private String questionTitle;
    
    /**
     * 题目内容
     */
    private String questionContent;
    
    /**
     * 题目类型 (1-单选题, 2-多选题, 3-填空题, 4-简答题, 5-编程题, 6-案例分析题)
     */
    private Integer questionType;
    
    /**
     * 标准答案
     */
    private String correctAnswer;
    
    /**
     * 学生答案
     */
    private String studentAnswer;
    
    /**
     * 题目总分
     */
    private Double totalScore;
    
    /**
     * 课程信息（可选）
     */
    private String courseInfo;
    
    /**
     * 难度等级 (1-简单, 2-中等, 3-困难)
     */
    private Integer difficultyLevel;
}