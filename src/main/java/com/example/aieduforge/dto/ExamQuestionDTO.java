package com.example.aieduforge.dto;

import lombok.Data;

import java.util.Map;

@Data
public class ExamQuestionDTO {

    private Long id;

    private Long courseId;

    private String questionType;  // 使用字符串类型来匹配前端的题目类型

    private String title;

    private String content;

    private Map<String, String> options;  // 选项以Map形式存储，如：{"A": "选项A", "B": "选项B"}

    private String correctAnswer;

    private String answerAnalysis;

    private Integer difficulty;

    private Double score;

    private String knowledgePoints;

    private Integer aiGenerated;

    private String generationPrompt;
}
