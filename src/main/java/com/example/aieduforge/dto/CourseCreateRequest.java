package com.example.aieduforge.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * Course creation request DTO
 */
@Data
public class CourseCreateRequest {
    
    @NotBlank(message = "Course name is required")
    @Size(max = 100, message = "Course name cannot exceed 100 characters")
    private String courseName;
    
    @NotBlank(message = "Course code is required")
    @Size(max = 50, message = "Course code cannot exceed 50 characters")
    private String courseCode;
    
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;
    
    @NotBlank(message = "Subject is required")
    @Size(max = 50, message = "Subject cannot exceed 50 characters")
    private String subject;
    
    @NotNull(message = "Teacher ID is required")
    private Long teacherId;
    
    private Long subjectId; // Optional subject ID for linking to subjects table
}
