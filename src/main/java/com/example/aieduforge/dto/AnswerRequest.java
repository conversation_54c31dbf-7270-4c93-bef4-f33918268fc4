package com.example.aieduforge.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Answer Submission Request DTO
 */
@Data
public class AnswerRequest {
    
    @NotNull(message = "Practice ID cannot be null")
    private Long practiceId;
    
    @NotNull(message = "Question ID cannot be null")
    private Long questionId;
    
    @NotBlank(message = "Student answer cannot be empty")
    private String studentAnswer;
}