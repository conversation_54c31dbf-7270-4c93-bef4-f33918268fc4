package com.example.aieduforge.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * Question Generation Request DTO
 */
@Data
public class QuestionGenerationRequest {
    
    @NotNull(message = "Course ID cannot be null")
    private Long courseId;
    
    private Long outlineId;
    
    @NotBlank(message = "Teaching content cannot be empty")
    private String teachingContent;
    
    @NotNull(message = "Question type cannot be null")
    private String questionType; // SINGLE_CHOICE, MULTIPLE_CHOICE, FILL_BLANK, SHORT_ANSWER, PROGRAMMING, CASE_ANALYSIS
    
    @NotNull(message = "Difficulty level cannot be null")
    @Min(value = 1, message = "Difficulty level must be between 1 and 3")
    @Max(value = 3, message = "Difficulty level must be between 1 and 3")
    @JsonProperty("difficulty")
    private Integer difficultyLevel;
    
    @NotNull(message = "Question count cannot be null")
    @Min(value = 1, message = "Question count must be at least 1")
    @Max(value = 20, message = "Question count cannot exceed 20")
    @JsonProperty("count")
    private Integer questionCount;
    
    private String knowledgePoints;
    
    private String customPrompt;
}