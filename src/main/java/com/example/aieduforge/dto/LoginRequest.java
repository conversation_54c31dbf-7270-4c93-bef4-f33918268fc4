package com.example.aieduforge.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * Login Request DTO
 */
@Data
public class LoginRequest {
    
    @NotBlank(message = "Username cannot be empty")
    private String username;
    
    @NotBlank(message = "Password cannot be empty")
    private String password;
    
    /**
     * 验证码
     */
    private String captcha;
    
    /**
     * 会话ID
     */
    private String sessionId;
}
