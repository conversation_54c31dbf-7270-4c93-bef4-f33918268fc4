package com.example.aieduforge.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 错误响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponseDto {
    
    /**
     * 错误基本信息
     */
    private ErrorInfo errorInfo;
    
    /**
     * 错误详细信息
     */
    private ErrorDetails errorDetails;
    
    /**
     * 故障排除信息
     */
    private TroubleshootingInfo troubleshooting;
    
    /**
     * 格式化的JSON字符串
     */
    private String formattedJson;

    /**
     * 错误基本信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorInfo {
        /**
         * 错误标题
         */
        private String title;
        
        /**
         * 错误消息
         */
        private String message;
        
        /**
         * 错误代码
         */
        private String errorCode;
        
        /**
         * HTTP状态码
         */
        private Integer httpStatus;
        
        /**
         * 错误类型
         */
        private String errorType;
        
        /**
         * 时间戳
         */
        private LocalDateTime timestamp;
        
        /**
         * 模型名称
         */
        private String modelName;
        
        /**
         * 提供商
         */
        private String provider;
    }

    /**
     * 错误详细信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorDetails {
        /**
         * 原始错误消息
         */
        private String originalMessage;
        
        /**
         * 元数据
         */
        private Map<String, Object> metadata;
        
        /**
         * API端点
         */
        private String endpoint;
        
        /**
         * 用户ID
         */
        private String userId;
    }

    /**
     * 故障排除信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TroubleshootingInfo {
        /**
         * 解决建议
         */
        private List<String> suggestions;
        
        /**
         * 文档链接
         */
        private List<String> documentationLinks;
        
        /**
         * 替代模型建议
         */
        private List<String> alternativeModels;
        
        /**
         * 频率限制信息
         */
        private RateLimitInfo rateLimitInfo;
    }

    /**
     * 频率限制信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RateLimitInfo {
        /**
         * 限制数量
         */
        private String limit;
        
        /**
         * 剩余数量
         */
        private String remaining;
        
        /**
         * 重置时间
         */
        private String resetTime;
        
        /**
         * 重置时间戳
         */
        private Long resetTimestamp;
        
        /**
         * 升级消息
         */
        private String upgradeMessage;
    }
}