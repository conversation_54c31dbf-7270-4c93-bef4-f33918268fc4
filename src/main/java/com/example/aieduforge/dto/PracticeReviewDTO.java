package com.example.aieduforge.dto;

import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.entity.PracticeRecord;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Practice Review DTO - contains complete data for practice review
 */
@Data
public class PracticeReviewDTO {
    
    private PracticeRecord record;
    private List<ExamQuestion> questions;
    private Map<Long, String> answers; // questionId -> studentAnswer
    
    public PracticeReviewDTO() {}
    
    public PracticeReviewDTO(PracticeRecord record, List<ExamQuestion> questions, Map<Long, String> answers) {
        this.record = record;
        this.questions = questions;
        this.answers = answers;
    }
}