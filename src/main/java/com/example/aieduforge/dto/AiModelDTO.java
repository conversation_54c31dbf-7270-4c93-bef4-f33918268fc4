package com.example.aieduforge.dto;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI Model DTO for API requests and responses
 */
@Data
public class AiModelDTO {

    private Long id;

    @NotBlank(message = "Model name cannot be empty")
    @Size(max = 100, message = "Model name cannot exceed 100 characters")
    private String modelName;

    @NotBlank(message = "Provider cannot be empty")
    @Size(max = 50, message = "Provider cannot exceed 50 characters")
    private String provider;

    @NotBlank(message = "API endpoint cannot be empty")
    @Size(max = 255, message = "API endpoint cannot exceed 255 characters")
    private String apiEndpoint;

    @Size(max = 500, message = "API key cannot exceed 500 characters")
    private String apiKey;

    @NotBlank(message = "Model ID cannot be empty")
    @Size(max = 100, message = "Model ID cannot exceed 100 characters")
    private String modelId;

    private String description;

    @Min(value = 1, message = "Max tokens must be at least 1")
    @Max(value = 32000, message = "Max tokens cannot exceed 32000")
    private Integer maxTokens;

    @DecimalMin(value = "0.0", message = "Temperature must be at least 0.0")
    @DecimalMax(value = "2.0", message = "Temperature cannot exceed 2.0")
    private BigDecimal temperature;

    @Min(value = 1, message = "Timeout must be at least 1 second")
    @Max(value = 300, message = "Timeout cannot exceed 300 seconds")
    private Integer timeoutSeconds;

    private Boolean isActive;

    private Boolean isDefault;

    private Integer connectionStatus;

    private LocalDateTime lastTestTime;

    private String errorMessage;

    private Long createdBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    // Connection status display text
    public String getConnectionStatusText() {
        if (connectionStatus == null) {
            return "Unknown";
        }
        switch (connectionStatus) {
            case 1:
                return "Connected";
            case 2:
                return "Failed";
            default:
                return "Unknown";
        }
    }
}