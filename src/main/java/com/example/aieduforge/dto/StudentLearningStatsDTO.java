package com.example.aieduforge.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class StudentLearningStatsDTO {
    private Integer totalStudyTime; // 总学习时长(分钟)
    private Integer totalPractices; // 练习总次数
    private BigDecimal averageAccuracy; // 平均正确率
    private Integer completedCourses; // 完成课程数

    private List<Map<String, Object>> dailyStats; // 每日统计数据
    private List<Map<String, Object>> subjectDistribution; // 学科分布数据
}
