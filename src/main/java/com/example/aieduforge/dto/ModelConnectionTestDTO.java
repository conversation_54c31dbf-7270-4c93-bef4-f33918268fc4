package com.example.aieduforge.dto;

import com.example.aieduforge.dto.ErrorResponseDto;
import lombok.Data;

/**
 * DTO for model connection testing
 */
@Data
public class ModelConnectionTestDTO {

    private Long modelId;
    private Boolean success;
    private String message;
    private Long responseTime; // in milliseconds
    private String testPrompt;
    private String testResponse;
    private ErrorResponseDto formattedError; // New field for formatted error information

    public ModelConnectionTestDTO() {}

    public ModelConnectionTestDTO(Long modelId, Boolean success, String message) {
        this.modelId = modelId;
        this.success = success;
        this.message = message;
    }

    public ModelConnectionTestDTO(Long modelId, Boolean success, String message, Long responseTime) {
        this.modelId = modelId;
        this.success = success;
        this.message = message;
        this.responseTime = responseTime;
    }

    public ModelConnectionTestDTO(Long modelId, Boolean success, String message, Long responseTime, ErrorResponseDto formattedError) {
        this.modelId = modelId;
        this.success = success;
        this.message = message;
        this.responseTime = responseTime;
        this.formattedError = formattedError;
    }
}
