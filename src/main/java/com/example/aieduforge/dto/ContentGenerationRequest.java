package com.example.aieduforge.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Content Generation Request DTO
 */
@Data
public class ContentGenerationRequest {
    
    @NotNull(message = "Course ID cannot be null")
    private Long courseId;
    
    private Long outlineId;
    
    @NotBlank(message = "Title cannot be empty")
    private String title;
    
    @NotNull(message = "Content type cannot be null")
    private Integer contentType; // 1-知识讲解，2-实训练习，3-指导说明
    
    private String courseOutline;
    
    private String chapterName;
    
    private Integer timeAllocation;
    
    private Integer difficultyLevel;
    
    private String customPrompt;
}