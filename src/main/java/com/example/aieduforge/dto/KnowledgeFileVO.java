package com.example.aieduforge.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * Knowledge File View Object for frontend display
 */
@Data
public class KnowledgeFileVO {
    
    private Long id;
    
    /**
     * 文件名 (对应前端的fileName)
     */
    private String fileName;
    
    /**
     * 文件大小 (对应前端的fileSize)
     */
    private Long fileSize;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 学科
     */
    private String subject;
    
    /**
     * 课程ID
     */
    private Long courseId;
    
    /**
     * 课程名称 (对应前端的courseName)
     */
    private String courseName;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 上传时间 (对应前端的uploadTime)
     */
    private String uploadTime;
    
    /**
     * 下载次数 (对应前端的downloadCount)
     */
    private Integer downloadCount;
    
    /**
     * AI处理状态 (对应前端的aiProcessed)
     */
    private Boolean aiProcessed;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 上传用户ID
     */
    private Long uploadUserId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}