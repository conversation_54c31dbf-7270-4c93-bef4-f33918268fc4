package com.example.aieduforge.interceptor;

import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.StatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * User Activity Recording Interceptor
 */
@Slf4j
@Component
public class UserActivityInterceptor implements HandlerInterceptor {

    @Autowired
    private StatisticsService statisticsService;

    // Module mapping for different API endpoints
    private static final Map<String, String> MODULE_MAPPING = new HashMap<>();
    
    static {
        // 教师相关模块
        MODULE_MAPPING.put("/teacher/content", "备课模块");
        MODULE_MAPPING.put("/teacher/courses", "课程管理");
        MODULE_MAPPING.put("/teacher/grading", "批改模块");
        MODULE_MAPPING.put("/teacher/ai", "AI助手");
        
        // 学生相关模块
        MODULE_MAPPING.put("/student/practice", "练习模块");
        MODULE_MAPPING.put("/student/courses", "课程学习");
        MODULE_MAPPING.put("/student/learning", "学习模块");
        MODULE_MAPPING.put("/student/records", "学习记录");
        MODULE_MAPPING.put("/student/ai", "AI助手");
        
        // AI相关
        MODULE_MAPPING.put("/ai/", "AI助手");
        MODULE_MAPPING.put("/enhanced-ai/", "AI助手");
        
        // 题目和练习相关
        MODULE_MAPPING.put("/exam/questions", "出题模块");
        MODULE_MAPPING.put("/practice", "练习模块");
        MODULE_MAPPING.put("/questions", "题目管理");
        
        // 管理员相关
        MODULE_MAPPING.put("/admin/dashboard", "系统管理");
        MODULE_MAPPING.put("/admin/users", "用户管理");
        MODULE_MAPPING.put("/admin/courses", "课程管理");
        MODULE_MAPPING.put("/admin/resources", "资源管理");
        MODULE_MAPPING.put("/admin/statistics", "统计管理");
        
        // 通用模块
        MODULE_MAPPING.put("/courses", "课程模块");
        MODULE_MAPPING.put("/notifications", "通知模块");
        MODULE_MAPPING.put("/auth", "认证模块");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // Record request start time
        request.setAttribute("startTime", System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated() || 
                authentication.getPrincipal().equals("anonymousUser")) {
                return;
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            // Determine user type based on roles
            Integer userType = getUserType(userPrincipal);
            if (userType == null) {
                return;
            }

            // Get request info
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            
            // Skip certain requests
            if (shouldSkipRequest(requestURI, method)) {
                return;
            }

            // Determine module and action
            String moduleName = getModuleName(requestURI);
            String actionType = getActionType(requestURI, method);
            
            // Calculate duration in milliseconds, then convert to seconds
            Long startTime = (Long) request.getAttribute("startTime");
            Integer duration = 0;
            if (startTime != null) {
                long durationMs = System.currentTimeMillis() - startTime;
                // Convert to seconds, minimum 1 second for any request
                duration = Math.max(1, (int) (durationMs / 1000));
                // If less than 1 second but more than 0, still count as 1 second
                if (durationMs > 0 && duration == 0) {
                    duration = 1;
                }
            }

            // Debug log for "其他模块" requests
            if ("其他模块".equals(moduleName)) {
                log.debug("Unclassified request: {} {} -> {}", method, requestURI, moduleName);
            }

            // Record activity
            statisticsService.recordUserActivity(userId, userType, moduleName, actionType, duration);
            
        } catch (Exception e) {
            log.error("Failed to record user activity", e);
        }
    }

    private Integer getUserType(UserPrincipal userPrincipal) {
        String role = userPrincipal.getRole();
        if ("ADMIN".equals(role)) {
            return 0; // Admin
        } else if ("TEACHER".equals(role)) {
            return 1; // Teacher
        } else if ("STUDENT".equals(role)) {
            return 2; // Student
        }
        return null;
    }

    private boolean shouldSkipRequest(String requestURI, String method) {
        // Skip static resources, health checks, etc.
        return requestURI.contains("/static/") ||
               requestURI.contains("/css/") ||
               requestURI.contains("/js/") ||
               requestURI.contains("/images/") ||
               requestURI.contains("/favicon.ico") ||
               requestURI.contains("/health") ||
               requestURI.contains("/actuator") ||
               requestURI.contains("/error") ||
               requestURI.endsWith(".css") ||
               requestURI.endsWith(".js") ||
               requestURI.endsWith(".png") ||
               requestURI.endsWith(".jpg") ||
               requestURI.endsWith(".ico") ||
               "OPTIONS".equals(method) ||
               // 跳过一些频繁的系统调用
               requestURI.contains("/dashboard/overview") ||
               requestURI.contains("/dashboard/comprehensive-stats");
    }

    private String getModuleName(String requestURI) {
        // 按照最长匹配原则，优先匹配更具体的路径
        String bestMatch = "其他模块";
        int maxLength = 0;
        
        for (Map.Entry<String, String> entry : MODULE_MAPPING.entrySet()) {
            String pattern = entry.getKey();
            if (requestURI.startsWith(pattern) && pattern.length() > maxLength) {
                bestMatch = entry.getValue();
                maxLength = pattern.length();
            }
        }
        
        // 特殊处理一些常见的API路径
        if (bestMatch.equals("其他模块")) {
            if (requestURI.contains("/practice")) {
                return "练习模块";
            } else if (requestURI.contains("/question")) {
                return "知识问答";
            } else if (requestURI.contains("/course")) {
                return "课程模块";
            } else if (requestURI.contains("/ai")) {
                return "AI助手";
            } else if (requestURI.contains("/grading")) {
                return "批改模块";
            } else if (requestURI.contains("/content")) {
                return "备课模块";
            } else if (requestURI.contains("/analytics")) {
                return "学情分析";
            }
        }
        
        return bestMatch;
    }

    private String getActionType(String requestURI, String method) {
        if ("GET".equals(method)) {
            if (requestURI.contains("/list") || requestURI.contains("/query")) {
                return "数据查看";
            } else if (requestURI.contains("/detail")) {
                return "详情查看";
            } else {
                return "页面访问";
            }
        } else if ("POST".equals(method)) {
            if (requestURI.contains("/create") || requestURI.contains("/add")) {
                return "创建操作";
            } else if (requestURI.contains("/practice")) {
                return "练习答题";
            } else if (requestURI.contains("/question")) {
                return "AI提问";
            } else {
                return "提交操作";
            }
        } else if ("PUT".equals(method)) {
            return "更新操作";
        } else if ("DELETE".equals(method)) {
            return "删除操作";
        }
        return "其他操作";
    }
}