package com.example.aieduforge.service;

import com.example.aieduforge.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 定时任务服务
 */
@Slf4j
@Service
public class ScheduledTaskService {

    @Autowired
    private NotificationService notificationService;

    /**
     * 每天凌晨2点清理过期通知
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredNotifications() {
        log.info("开始清理过期通知...");
        try {
            notificationService.deleteExpiredNotifications();
            log.info("过期通知清理完成");
        } catch (Exception e) {
            log.error("清理过期通知失败", e);
        }
    }

    /**
     * 每小时更新系统统计数据
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void updateSystemStatistics() {
        log.info("开始更新系统统计数据...");
        try {
            // 这里可以添加统计数据更新逻辑
            log.info("系统统计数据更新完成");
        } catch (Exception e) {
            log.error("更新系统统计数据失败", e);
        }
    }

    /**
     * 每天凌晨1点备份重要数据
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void backupImportantData() {
        log.info("开始备份重要数据...");
        try {
            // 这里可以添加数据备份逻辑
            log.info("重要数据备份完成");
        } catch (Exception e) {
            log.error("备份重要数据失败", e);
        }
    }
}