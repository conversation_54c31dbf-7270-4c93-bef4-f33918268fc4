package com.example.aieduforge.service;

import com.example.aieduforge.entity.AnalysisResults;

import java.util.List;
import java.util.Map;

/**
 * 分析结果服务接口
 */
public interface AnalysisResultsService {
    
    /**
     * 保存分析结果
     */
    Long saveAnalysisResult(String analysisType, Long targetId, Long courseId, Map<String, Object> analysisData, Long teacherId);
    
    /**
     * 获取最新的分析结果
     */
    AnalysisResults getLatestAnalysisResult(String analysisType, Long targetId, Long courseId);
    
    /**
     * 获取教师的分析历史
     */
    List<AnalysisResults> getAnalysisHistoryByTeacher(Long teacherId);
    
    /**
     * 生成分析报告文件
     */
    String generateAnalysisReport(Long analysisResultId, String format);
    
    /**
     * 导出分析结果为Excel
     */
    byte[] exportAnalysisToExcel(Long analysisResultId);
    
    /**
     * 导出分析结果为PDF
     */
    byte[] exportAnalysisToPdf(Long analysisResultId);
    
    /**
     * 根据ID获取分析结果
     */
    AnalysisResults getById(Long id);
}