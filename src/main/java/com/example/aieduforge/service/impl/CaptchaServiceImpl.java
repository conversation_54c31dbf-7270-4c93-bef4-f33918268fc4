package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.aieduforge.entity.CaptchaRecord;
import com.example.aieduforge.mapper.CaptchaRecordMapper;
import com.example.aieduforge.service.CaptchaService;
import com.example.aieduforge.service.SystemSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {
    
    private final CaptchaRecordMapper captchaRecordMapper;
    private final SystemSettingsService systemSettingsService;
    
    private static final String CAPTCHA_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final Random RANDOM = new Random();
    
    @Override
    public Map<String, Object> generateCaptcha(String sessionId) {
        try {
            // 获取验证码配置
            int length = Integer.parseInt(systemSettingsService.getSettingValue("captcha_length", "4"));
            int expireMinutes = Integer.parseInt(systemSettingsService.getSettingValue("captcha_expire_minutes", "5"));
            
            // 生成验证码
            String captchaCode = generateRandomCode(length);
            
            // 生成验证码图片
            String captchaImage = generateCaptchaImage(captchaCode);
            
            // 删除该会话的旧验证码
            captchaRecordMapper.delete(new LambdaQueryWrapper<CaptchaRecord>()
                    .eq(CaptchaRecord::getSessionId, sessionId));
            
            // 保存验证码记录
            CaptchaRecord record = new CaptchaRecord();
            record.setSessionId(sessionId);
            record.setCaptchaCode(captchaCode.toUpperCase());
            record.setCaptchaImage(captchaImage);
            record.setCreatedAt(LocalDateTime.now());
            record.setExpiresAt(LocalDateTime.now().plusMinutes(expireMinutes));
            record.setUsed(0);
            
            captchaRecordMapper.insert(record);
            
            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("image", "data:image/png;base64," + captchaImage);
            result.put("length", length);

            return result;
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            throw new RuntimeException("生成验证码失败");
        }
    }
    
    @Override
    public boolean verifyCaptcha(String sessionId, String captcha) {
        if (!StringUtils.hasText(sessionId) || !StringUtils.hasText(captcha)) {
            return false;
        }
        
        try {
            // 查找验证码记录
            CaptchaRecord record = captchaRecordMapper.selectOne(
                    new LambdaQueryWrapper<CaptchaRecord>()
                            .eq(CaptchaRecord::getSessionId, sessionId)
                            .eq(CaptchaRecord::getUsed, 0)
                            .gt(CaptchaRecord::getExpiresAt, LocalDateTime.now())
            );
            
            if (record == null) {
                return false;
            }
            
            // 验证验证码
            boolean isValid = captcha.toUpperCase().equals(record.getCaptchaCode());
            
            if (isValid) {
                // 标记为已使用
                record.setUsed(1);
                captchaRecordMapper.updateById(record);
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("验证验证码失败", e);
            return false;
        }
    }
    
    @Override
    public void cleanExpiredCaptcha() {
        try {
            captchaRecordMapper.delete(new LambdaQueryWrapper<CaptchaRecord>()
                    .lt(CaptchaRecord::getExpiresAt, LocalDateTime.now()));
        } catch (Exception e) {
            log.error("清理过期验证码失败", e);
        }
    }
    
    @Override
    public boolean isCaptchaEnabled() {
        String enabled = systemSettingsService.getSettingValue("captcha_enabled", "1");
        return "1".equals(enabled);
    }
    
    /**
     * 生成随机验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(CAPTCHA_CHARS.charAt(RANDOM.nextInt(CAPTCHA_CHARS.length())));
        }
        return code.toString();
    }
    
    /**
     * 生成验证码图片
     */
    private String generateCaptchaImage(String code) throws IOException {
        int width = 120;
        int height = 50;

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 设置抗锯齿
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置背景色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);

        // 绘制干扰线
        for (int i = 0; i < 5; i++) {
            g.setColor(getRandomColor(150, 200));
            int x1 = RANDOM.nextInt(width);
            int y1 = RANDOM.nextInt(height);
            int x2 = RANDOM.nextInt(width);
            int y2 = RANDOM.nextInt(height);
            g.drawLine(x1, y1, x2, y2);
        }

        // 绘制验证码字符（根据长度动态布局，避免最后一个字符被裁剪）
        int fontSize = 24;
        if (code.length() >= 6) {
            fontSize = 22;
        }
        if (code.length() >= 7) {
            fontSize = 20;
        }
        g.setFont(new Font("Arial", Font.BOLD, fontSize));
        int margin = 15;
        int available = Math.max(10, width - margin * 2);
        int step = available / Math.max(1, code.length());
        for (int i = 0; i < code.length(); i++) {
            g.setColor(getRandomColor(50, 150));
            char c = code.charAt(i);
            int x = margin + i * step;
            int y = (height / 2) + RANDOM.nextInt(15) - 2;
            g.drawString(String.valueOf(c), x, y);
        }

        // 绘制干扰点
        for (int i = 0; i < 50; i++) {
            g.setColor(getRandomColor(100, 200));
            int x = RANDOM.nextInt(width);
            int y = RANDOM.nextInt(height);
            g.fillOval(x, y, 2, 2);
        }

        g.dispose();
        
        // 转换为Base64
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }
    
    /**
     * 获取随机颜色
     */
    private Color getRandomColor(int min, int max) {
        int r = min + RANDOM.nextInt(max - min);
        int g = min + RANDOM.nextInt(max - min);
        int b = min + RANDOM.nextInt(max - min);
        return new Color(r, g, b);
    }
}