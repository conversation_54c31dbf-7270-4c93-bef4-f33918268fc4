package com.example.aieduforge.service.impl;

import com.example.aieduforge.entity.AiModel;
import com.example.aieduforge.service.AiModelService;
import com.example.aieduforge.service.DynamicAIService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dynamic AI Service Implementation that uses database-configured models
 */
@Slf4j
@Service
public class DynamicAIServiceImpl implements DynamicAIService {

    @Autowired
    private AiModelService aiModelService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public String generateResponse(String prompt) {
        AiModel currentModel = getCurrentModel();
        if (currentModel == null) {
            throw new RuntimeException("No active AI model configured");
        }
        
        return generateResponseWithModel(prompt, currentModel);
    }

    @Override
    public String generateResponse(String prompt, Long modelId) {
        AiModel model = aiModelService.getModelForAIService();
        if (model == null || !model.getId().equals(modelId)) {
            // Get specific model by ID
            model = aiModelService.getModelById(modelId) != null ? 
                    convertDTOToEntity(aiModelService.getModelById(modelId)) : null;
        }
        
        if (model == null) {
            throw new RuntimeException("AI model not found with ID: " + modelId);
        }
        
        return generateResponseWithModel(prompt, model);
    }

    @Override
    public AiModel getCurrentModel() {
        return aiModelService.getModelForAIService();
    }

    @Override
    public boolean isCurrentModelAvailable() {
        AiModel currentModel = getCurrentModel();
        if (currentModel == null) {
            return false;
        }
        
        try {
            // Simple test to check if model is available
            generateResponseWithModel("Hello", currentModel);
            return true;
        } catch (Exception e) {
            log.warn("Current AI model is not available: {}", e.getMessage());
            return false;
        }
    }

    private String generateResponseWithModel(String prompt, AiModel model) {
        try {
            // Create HTTP headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            if (StringUtils.hasText(model.getApiKey())) {
                String apiKey = model.getApiKey();
                if (!apiKey.startsWith("Bearer ")) {
                    apiKey = "Bearer " + apiKey;
                }
                headers.set("Authorization", apiKey);
            }

            // Create request body based on provider
            Map<String, Object> requestBody = createRequestBody(prompt, model);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // Determine the correct endpoint
            String endpoint = getEndpointForProvider(model);

            log.debug("Calling AI model: {} at endpoint: {}", model.getModelName(), endpoint);

            // Make the API call
            ResponseEntity<Map> response = restTemplate.exchange(
                endpoint,
                HttpMethod.POST,
                entity,
                Map.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return extractResponseContent(response.getBody(), model.getProvider());
            } else {
                throw new RuntimeException("AI API call failed with status: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Failed to generate AI response with model {}: {}", model.getModelName(), e.getMessage());
            throw new RuntimeException("AI generation failed: " + e.getMessage(), e);
        }
    }

    private Map<String, Object> createRequestBody(String prompt, AiModel model) {
        Map<String, Object> requestBody = new HashMap<>();
        
        switch (model.getProvider().toLowerCase()) {
            case "openrouter":
            case "openai":
            case "anthropic":
                requestBody.put("model", model.getModelId());
                requestBody.put("messages", List.of(
                    Map.of("role", "user", "content", prompt)
                ));
                requestBody.put("max_tokens", model.getMaxTokens());
                requestBody.put("temperature", model.getTemperature());
                break;
                
            case "ollama":
                requestBody.put("model", model.getModelId());
                requestBody.put("prompt", prompt);
                requestBody.put("stream", false);
                if (model.getMaxTokens() != null) {
                    Map<String, Object> options = new HashMap<>();
                    options.put("num_predict", model.getMaxTokens());
                    options.put("temperature", model.getTemperature());
                    requestBody.put("options", options);
                }
                break;
                
            case "google":
                requestBody.put("model", model.getModelId());
                requestBody.put("contents", List.of(
                    Map.of("parts", List.of(Map.of("text", prompt)))
                ));
                Map<String, Object> generationConfig = new HashMap<>();
                generationConfig.put("maxOutputTokens", model.getMaxTokens());
                generationConfig.put("temperature", model.getTemperature());
                requestBody.put("generationConfig", generationConfig);
                break;
                
            default:
                // Default to OpenAI-compatible format
                requestBody.put("model", model.getModelId());
                requestBody.put("messages", List.of(
                    Map.of("role", "user", "content", prompt)
                ));
                requestBody.put("max_tokens", model.getMaxTokens());
                requestBody.put("temperature", model.getTemperature());
                break;
        }
        
        return requestBody;
    }

    private String getEndpointForProvider(AiModel model) {
        String baseUrl = model.getApiEndpoint();
        
        switch (model.getProvider().toLowerCase()) {
            case "openrouter":
            case "openai":
            case "anthropic":
                if (!baseUrl.endsWith("/")) {
                    baseUrl += "/";
                }
                return baseUrl + "chat/completions";
                
            case "ollama":
                if (!baseUrl.endsWith("/")) {
                    baseUrl += "/";
                }
                return baseUrl + "api/generate";
                
            case "google":
                if (!baseUrl.endsWith("/")) {
                    baseUrl += "/";
                }
                return baseUrl + "v1/models/" + model.getModelId() + ":generateContent";
                
            default:
                if (!baseUrl.endsWith("/")) {
                    baseUrl += "/";
                }
                return baseUrl + "chat/completions";
        }
    }

    private String extractResponseContent(Map<String, Object> responseBody, String provider) {
        try {
            switch (provider.toLowerCase()) {
                case "openrouter":
                case "openai":
                case "anthropic":
                    List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                    if (choices != null && !choices.isEmpty()) {
                        Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                        if (message != null) {
                            return (String) message.get("content");
                        }
                    }
                    break;
                    
                case "ollama":
                    return (String) responseBody.get("response");
                    
                case "google":
                    List<Map<String, Object>> candidates = (List<Map<String, Object>>) responseBody.get("candidates");
                    if (candidates != null && !candidates.isEmpty()) {
                        Map<String, Object> content = (Map<String, Object>) candidates.get(0).get("content");
                        if (content != null) {
                            List<Map<String, Object>> parts = (List<Map<String, Object>>) content.get("parts");
                            if (parts != null && !parts.isEmpty()) {
                                return (String) parts.get(0).get("text");
                            }
                        }
                    }
                    break;
                    
                default:
                    // Try OpenAI format first
                    List<Map<String, Object>> defaultChoices = (List<Map<String, Object>>) responseBody.get("choices");
                    if (defaultChoices != null && !defaultChoices.isEmpty()) {
                        Map<String, Object> message = (Map<String, Object>) defaultChoices.get(0).get("message");
                        if (message != null) {
                            return (String) message.get("content");
                        }
                    }
                    break;
            }
            
            // Fallback: return the entire response as JSON string
            return objectMapper.writeValueAsString(responseBody);
            
        } catch (Exception e) {
            log.error("Failed to extract response content: {}", e.getMessage());
            return "Error extracting AI response";
        }
    }

    private AiModel convertDTOToEntity(com.example.aieduforge.dto.AiModelDTO dto) {
        if (dto == null) return null;
        
        AiModel model = new AiModel();
        model.setId(dto.getId());
        model.setModelName(dto.getModelName());
        model.setProvider(dto.getProvider());
        model.setApiEndpoint(dto.getApiEndpoint());
        model.setApiKey(dto.getApiKey());
        model.setModelId(dto.getModelId());
        model.setDescription(dto.getDescription());
        model.setMaxTokens(dto.getMaxTokens());
        model.setTemperature(dto.getTemperature());
        model.setTimeoutSeconds(dto.getTimeoutSeconds());
        model.setIsActive(dto.getIsActive());
        model.setIsDefault(dto.getIsDefault());
        model.setConnectionStatus(dto.getConnectionStatus());
        model.setLastTestTime(dto.getLastTestTime());
        model.setErrorMessage(dto.getErrorMessage());
        model.setCreatedBy(dto.getCreatedBy());
        model.setCreateTime(dto.getCreateTime());
        model.setUpdateTime(dto.getUpdateTime());
        
        return model;
    }
}