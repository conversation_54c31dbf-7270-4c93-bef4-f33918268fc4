package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.KnowledgeBase;
import com.example.aieduforge.entity.TeachingContent;
import com.example.aieduforge.exception.BusinessException;
import com.example.aieduforge.mapper.KnowledgeBaseMapper;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.ResourceManagementService;
import com.example.aieduforge.service.TeachingContentService;
import com.example.aieduforge.service.AIFileProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Resource Management Service Implementation
 */
@Slf4j
@Service
public class ResourceManagementServiceImpl extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBase> 
        implements ResourceManagementService {

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;
    
    @Autowired
    private CourseService courseService;
    
    @Autowired
    private TeachingContentService teachingContentService;
    
    @Autowired
    private AIFileProcessingService aiFileProcessingService;
    
    @Autowired
    private com.example.aieduforge.service.SysUserService sysUserService;
    
    @Value("${file.upload.path:./uploads}")
    private String uploadPath;

    @Override
    @Transactional
    public KnowledgeBase uploadKnowledgeFile(MultipartFile file, Long courseId, String subject, String tags, String description, Long uploadUserId) {
        if (file.isEmpty()) {
            throw new BusinessException("File cannot be empty");
        }
        
        // Validate file type
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        if (!isValidFileType(fileExtension)) {
            throw new BusinessException("Unsupported file type: " + fileExtension);
        }
        
        // Create upload directory if not exists
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        // Generate unique filename
        String filename = UUID.randomUUID().toString() + "." + fileExtension;
        String filePath = uploadPath + File.separator + filename;
        
        try {
            // Save file to disk
            Path path = Paths.get(filePath);
            Files.write(path, file.getBytes());
            
            // Read file content for text files
            String content = "";
            if (isTextFile(fileExtension)) {
                content = new String(file.getBytes(), "UTF-8");
            }
            
            // Create knowledge base record
            KnowledgeBase knowledgeBase = new KnowledgeBase();
            knowledgeBase.setTitle(originalFilename);
            knowledgeBase.setContent(content);
            knowledgeBase.setFilePath(filePath);
            knowledgeBase.setFileType(fileExtension);
            long fileSize = file.getSize();
            log.info("File size: {} bytes for file: {}", fileSize, originalFilename);
            knowledgeBase.setFileSize(fileSize); // 设置文件大小
            knowledgeBase.setDownloadCount(0); // 初始下载次数为0
            knowledgeBase.setDescription(description != null ? description : ""); // 设置描述
            knowledgeBase.setCourseId(courseId);
            knowledgeBase.setSubject(subject);
            knowledgeBase.setTags(tags);
            knowledgeBase.setUploadUserId(uploadUserId);
            
            log.info("Setting knowledge base - courseId: {}, subject: {}, uploadUserId: {}", 
                    courseId, subject, uploadUserId);
            knowledgeBase.setStatus(1);
            knowledgeBase.setCreateTime(LocalDateTime.now());
            knowledgeBase.setUpdateTime(LocalDateTime.now());
            
            // 使用AI处理文件内容
            try {
                Map<String, Object> processedData = aiFileProcessingService.processUploadedFile(file, knowledgeBase);
                
                // 更新知识库记录
                if (processedData.containsKey("textContent")) {
                    knowledgeBase.setContent((String) processedData.get("textContent"));
                }
                
                // 合并AI生成的标签
                if (processedData.containsKey("smartTags")) {
                    String smartTags = (String) processedData.get("smartTags");
                    if (smartTags != null && !smartTags.trim().isEmpty()) {
                        String combinedTags = tags != null && !tags.trim().isEmpty() 
                            ? tags + "," + smartTags 
                            : smartTags;
                        knowledgeBase.setTags(combinedTags);
                    }
                }
                
                log.info("AI processing completed for file: {}", originalFilename);
            } catch (Exception e) {
                log.warn("AI processing failed for file: {}, continuing with basic upload", originalFilename, e);
            }
            
            if (save(knowledgeBase)) {
                // 异步更新AI知识库
                try {
                    Map<String, Object> processedData = aiFileProcessingService.processUploadedFile(file, knowledgeBase);
                    aiFileProcessingService.updateAIKnowledgeBase(knowledgeBase, processedData);
                } catch (Exception e) {
                    log.warn("Failed to update AI knowledge base for file: {}", originalFilename, e);
                }
                
                log.info("Knowledge file uploaded successfully: {}", originalFilename);
                return knowledgeBase;
            }
            
            throw new BusinessException("Failed to save knowledge base record");
            
        } catch (IOException e) {
            log.error("Failed to upload file", e);
            throw new BusinessException("Failed to upload file: " + e.getMessage());
        }
    }

    @Override
    public List<KnowledgeBase> searchKnowledge(String keyword) {
        return knowledgeBaseMapper.searchByKeyword(keyword);
    }

    @Override
    public List<KnowledgeBase> getKnowledgeBySubject(String subject) {
        return knowledgeBaseMapper.findBySubject(subject);
    }

    @Override
    public List<KnowledgeBase> getKnowledgeByCourse(Long courseId) {
        return knowledgeBaseMapper.findByCourseId(courseId);
    }

    @Override
    public List<KnowledgeBase> getKnowledgeByTags(String tags) {
        return knowledgeBaseMapper.findByTags(tags);
    }

    @Override
    public Map<String, Object> exportCourseResources(Long courseId) {
        Map<String, Object> resources = new HashMap<>();
        
        // Get course info
        Course course = courseService.getById(courseId);
        resources.put("course", course);
        
        // Get teaching content
        List<TeachingContent> teachingContents = teachingContentService.findByCourseId(courseId);
        resources.put("teachingContents", teachingContents);
        
        // Get knowledge base files
        List<KnowledgeBase> knowledgeFiles = knowledgeBaseMapper.findByCourseId(courseId);
        resources.put("knowledgeFiles", knowledgeFiles);
        
        // Generate export summary
        Map<String, Object> summary = new HashMap<>();
        summary.put("courseId", courseId);
        summary.put("courseName", course != null ? course.getCourseName() : "Unknown");
        summary.put("teachingContentCount", teachingContents.size());
        summary.put("knowledgeFileCount", knowledgeFiles.size());
        summary.put("exportTime", LocalDateTime.now());
        
        resources.put("summary", summary);
        
        return resources;
    }

    @Override
    public List<Map<String, Object>> getFileTypeStatistics() {
        return knowledgeBaseMapper.getFileTypeStats();
    }

    @Override
    @Transactional
    public boolean deleteKnowledgeFile(Long id, Long userId) {
        KnowledgeBase knowledgeBase = getById(id);
        if (knowledgeBase == null) {
            throw new BusinessException("Knowledge file not found");
        }
        
        // Check permission (only uploader or admin can delete)
        if (knowledgeBase.getUploadUserId() != null && !knowledgeBase.getUploadUserId().equals(userId)) {
            throw new BusinessException("You don't have permission to delete this file");
        }
        
        // Delete physical file
        try {
            File file = new File(knowledgeBase.getFilePath());
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            log.warn("Failed to delete physical file: {}", knowledgeBase.getFilePath(), e);
        }
        
        // Delete database record
        boolean result = removeById(id);
        if (result) {
            log.info("Knowledge file deleted successfully: {}", knowledgeBase.getTitle());
        }
        
        return result;
    }

    @Override
    @Transactional
    public KnowledgeBase updateKnowledgeInfo(Long id, KnowledgeBase knowledgeBase, Long userId) {
        KnowledgeBase existing = getById(id);
        if (existing == null) {
            throw new BusinessException("Knowledge file not found");
        }
        
        // Check permission
        if (existing.getUploadUserId() != null && !existing.getUploadUserId().equals(userId)) {
            throw new BusinessException("You don't have permission to update this file");
        }
        
        // Update allowed fields
        existing.setTitle(knowledgeBase.getTitle());
        existing.setSubject(knowledgeBase.getSubject());
        existing.setTags(knowledgeBase.getTags());
        existing.setDescription(knowledgeBase.getDescription()); // 添加描述字段更新
        existing.setUpdateTime(LocalDateTime.now());
        
        if (updateById(existing)) {
            log.info("Knowledge file info updated successfully: {}", existing.getTitle());
            return existing;
        }
        
        throw new BusinessException("Failed to update knowledge file info");
    }

    @Override
    public Map<String, Object> getResourceUsageStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // Get detailed statistics from mapper
        List<Map<String, Object>> detailedStats = knowledgeBaseMapper.getResourceUsageStats();
        
        // Process data for charts
        List<String> subjects = new ArrayList<>();
        List<Integer> downloads = new ArrayList<>();
        List<Integer> counts = new ArrayList<>();
        
        for (Map<String, Object> stat : detailedStats) {
            subjects.add((String) stat.get("subject"));
            // 安全地获取数值，避免空指针异常
            Object downloadObj = stat.get("downloadCount");
            Object countObj = stat.get("fileCount");
            
            downloads.add(downloadObj != null ? ((Number) downloadObj).intValue() : 0);
            counts.add(countObj != null ? ((Number) countObj).intValue() : 0);
        }
        
        stats.put("subjects", subjects);
        stats.put("downloads", downloads);
        stats.put("counts", counts);
        stats.put("details", detailedStats);
        
        return stats;
    }
    
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
    }
    
    private boolean isValidFileType(String extension) {
        String[] allowedTypes = {"txt", "doc", "docx", "pdf", "ppt", "pptx", "xls", "xlsx", "md", "html"};
        for (String type : allowedTypes) {
            if (type.equals(extension)) {
                return true;
            }
        }
        return false;
    }
    
    private boolean isTextFile(String extension) {
        String[] textTypes = {"txt", "md", "html", "xml", "json", "csv"};
        for (String type : textTypes) {
            if (type.equals(extension)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void downloadKnowledgeFile(Long id, Long userId, jakarta.servlet.http.HttpServletResponse response) throws Exception {
        KnowledgeBase knowledgeBase = getById(id);
        if (knowledgeBase == null) {
            throw new BusinessException("Knowledge file not found");
        }

        // 增加下载次数
        knowledgeBase.setDownloadCount((knowledgeBase.getDownloadCount() != null ? knowledgeBase.getDownloadCount() : 0) + 1);
        updateById(knowledgeBase);

        // 设置响应头
        String fileName = knowledgeBase.getTitle();
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + 
            java.net.URLEncoder.encode(fileName, "UTF-8") + "\"");

        // 读取文件并写入响应
        File file = new File(knowledgeBase.getFilePath());
        if (!file.exists()) {
            throw new BusinessException("Physical file not found");
        }

        try (java.io.FileInputStream fis = new java.io.FileInputStream(file);
             java.io.OutputStream os = response.getOutputStream()) {
            
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.flush();
        }

        log.info("File downloaded successfully: {} by user: {}", fileName, userId);
    }

    @Override
    public Map<String, Object> getKnowledgeFileDetails(Long id) {
        KnowledgeBase knowledgeBase = getById(id);
        if (knowledgeBase == null) {
            return null;
        }

        Map<String, Object> details = new HashMap<>();
        
        // 基本信息
        details.put("id", knowledgeBase.getId());
        details.put("fileName", knowledgeBase.getTitle());
        details.put("fileSize", knowledgeBase.getFileSize());
        details.put("fileType", knowledgeBase.getFileType());
        details.put("subject", knowledgeBase.getSubject());
        details.put("courseId", knowledgeBase.getCourseId());
        details.put("tags", knowledgeBase.getTags());
        details.put("description", knowledgeBase.getDescription());
        details.put("uploadTime", knowledgeBase.getCreateTime() != null ? 
            knowledgeBase.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        details.put("downloadCount", knowledgeBase.getDownloadCount() != null ? knowledgeBase.getDownloadCount() : 0);
        details.put("status", knowledgeBase.getStatus());

        // 获取课程名称
        if (knowledgeBase.getCourseId() != null) {
            try {
                Course course = courseService.getById(knowledgeBase.getCourseId());
                details.put("courseName", course != null ? course.getCourseName() : null);
            } catch (Exception e) {
                log.warn("Failed to get course name for courseId: {}", knowledgeBase.getCourseId(), e);
                details.put("courseName", null);
            }
        }

        // 获取AI处理数据
        try {
            com.example.aieduforge.entity.AIKnowledgeData aiData = 
                knowledgeBaseMapper.selectAIKnowledgeData(id);
            
            if (aiData != null) {
                details.put("aiProcessed", true);
                details.put("summary", aiData.getSummary());
                details.put("keyPoints", aiData.getKeyPoints());
                details.put("smartTags", aiData.getSmartTags());
                details.put("extractedText", aiData.getExtractedText()); // 添加提取的文本
                details.put("structuredData", aiData.getStructuredData()); // 添加结构化数据
                details.put("difficulty", aiData.getDifficulty());
                details.put("wordCount", aiData.getWordCount());
                details.put("characterCount", aiData.getCharacterCount());
                details.put("aiModel", aiData.getAiModel());
                details.put("processingTime", aiData.getProcessingTime());
            } else {
                details.put("aiProcessed", false);
            }
        } catch (Exception e) {
            log.warn("Failed to get AI processed data for knowledgeBaseId: {}", id, e);
            details.put("aiProcessed", false);
        }

        return details;
    }

    @Override
    public List<Map<String, Object>> getCourseResources(Long courseId) {
        List<Map<String, Object>> resources = new ArrayList<>();
        
        try {
            // Get course info including teacher
            Course course = courseService.getById(courseId);
            String teacherName = "未知";
            if (course != null && course.getTeacherId() != null) {
                try {
                    // 获取教师信息
                    com.example.aieduforge.entity.SysUser teacher = 
                        sysUserService.getById(course.getTeacherId());
                    if (teacher != null) {
                        teacherName = teacher.getRealName() != null ? teacher.getRealName() : teacher.getUsername();
                    }
                } catch (Exception e) {
                    log.warn("Failed to get teacher info for teacherId: {}", course.getTeacherId(), e);
                }
            }
            
            // Get knowledge base files for the course
            List<KnowledgeBase> knowledgeFiles = knowledgeBaseMapper.findByCourseId(courseId);
            for (KnowledgeBase kb : knowledgeFiles) {
                Map<String, Object> resource = new HashMap<>();
                resource.put("id", kb.getId());
                resource.put("title", kb.getTitle());
                resource.put("type", "knowledge");
                resource.put("fileType", kb.getFileType());
                resource.put("fileSize", kb.getFileSize());
                resource.put("downloadCount", kb.getDownloadCount());
                resource.put("content", kb.getContent()); // 添加文件内容
                resource.put("description", kb.getDescription());
                resource.put("tags", kb.getTags());
                resource.put("teacherName", teacherName);
                resource.put("createTime", kb.getCreateTime() != null ? 
                    kb.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                resource.put("updateTime", kb.getUpdateTime() != null ? 
                    kb.getUpdateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                resources.add(resource);
            }
            
            // Get teaching content for the course
            List<TeachingContent> teachingContents = teachingContentService.findByCourseId(courseId);
            for (TeachingContent tc : teachingContents) {
                Map<String, Object> resource = new HashMap<>();
                resource.put("id", tc.getId());
                resource.put("title", tc.getTitle());
                resource.put("type", "teaching_content");
                resource.put("contentType", tc.getContentType());
                resource.put("content", tc.getContent()); // 添加教学内容
                resource.put("description", ""); // TeachingContent doesn't have description field
                resource.put("teacherName", teacherName);
                resource.put("createTime", tc.getCreateTime() != null ? 
                    tc.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                resource.put("updateTime", tc.getUpdateTime() != null ? 
                    tc.getUpdateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
                resources.add(resource);
            }
            
        } catch (Exception e) {
            log.error("Failed to get course resources for courseId: {}", courseId, e);
        }
        
        return resources;
    }

    @Override
    public void exportCourseResourcesAsDocx(Long courseId, jakarta.servlet.http.HttpServletResponse response) throws Exception {
        // Get course info
        Course course = courseService.getById(courseId);
        if (course == null) {
            throw new BusinessException("Course not found");
        }

        // Get teacher info
        String teacherName = "未知教师";
        if (course.getTeacherId() != null) {
            try {
                com.example.aieduforge.entity.SysUser teacher = 
                    sysUserService.getById(course.getTeacherId());
                if (teacher != null) {
                    teacherName = teacher.getRealName() != null ? teacher.getRealName() : teacher.getUsername();
                }
            } catch (Exception e) {
                log.warn("Failed to get teacher info", e);
            }
        }

        // Create Word document
        org.apache.poi.xwpf.usermodel.XWPFDocument document = new org.apache.poi.xwpf.usermodel.XWPFDocument();
        
        // Title
        org.apache.poi.xwpf.usermodel.XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(org.apache.poi.xwpf.usermodel.ParagraphAlignment.CENTER);
        org.apache.poi.xwpf.usermodel.XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("课程资源导出报告");
        titleRun.setBold(true);
        titleRun.setFontSize(18);
        titleRun.setFontFamily("宋体");

        // Course info
        org.apache.poi.xwpf.usermodel.XWPFParagraph coursePara = document.createParagraph();
        org.apache.poi.xwpf.usermodel.XWPFRun courseRun = coursePara.createRun();
        courseRun.setText("课程名称：" + course.getCourseName());
        courseRun.setBold(true);
        courseRun.setFontSize(14);
        courseRun.setFontFamily("宋体");
        courseRun.addBreak();
        courseRun.setText("授课教师：" + teacherName);
        courseRun.addBreak();
        courseRun.setText("课程描述：" + (course.getDescription() != null ? course.getDescription() : "无"));
        courseRun.addBreak();
        courseRun.setText("导出时间：" + LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // Get teaching content
        List<TeachingContent> teachingContents = teachingContentService.findByCourseId(courseId);
        if (!teachingContents.isEmpty()) {
            org.apache.poi.xwpf.usermodel.XWPFParagraph teachingPara = document.createParagraph();
            org.apache.poi.xwpf.usermodel.XWPFRun teachingRun = teachingPara.createRun();
            teachingRun.addBreak();
            teachingRun.setText("教学内容");
            teachingRun.setBold(true);
            teachingRun.setFontSize(16);
            teachingRun.setFontFamily("宋体");

            for (TeachingContent tc : teachingContents) {
                org.apache.poi.xwpf.usermodel.XWPFParagraph contentPara = document.createParagraph();
                org.apache.poi.xwpf.usermodel.XWPFRun contentRun = contentPara.createRun();
                contentRun.addBreak();
                contentRun.setText("标题：" + tc.getTitle());
                contentRun.setBold(true);
                contentRun.setFontFamily("宋体");
                contentRun.addBreak();
                contentRun.setText("类型：" + (tc.getContentType() != null ? tc.getContentType() : "未知"));
                contentRun.addBreak();
                contentRun.setText("内容：");
                contentRun.addBreak();
                contentRun.setText(tc.getContent() != null ? tc.getContent() : "无内容");
                contentRun.addBreak();
                contentRun.setText("创建时间：" + (tc.getCreateTime() != null ? 
                    tc.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : ""));
            }
        }

        // Get knowledge base files
        List<KnowledgeBase> knowledgeFiles = knowledgeBaseMapper.findByCourseId(courseId);
        if (!knowledgeFiles.isEmpty()) {
            org.apache.poi.xwpf.usermodel.XWPFParagraph knowledgePara = document.createParagraph();
            org.apache.poi.xwpf.usermodel.XWPFRun knowledgeRun = knowledgePara.createRun();
            knowledgeRun.addBreak();
            knowledgeRun.setText("知识库文件");
            knowledgeRun.setBold(true);
            knowledgeRun.setFontSize(16);
            knowledgeRun.setFontFamily("宋体");

            for (KnowledgeBase kb : knowledgeFiles) {
                org.apache.poi.xwpf.usermodel.XWPFParagraph filePara = document.createParagraph();
                org.apache.poi.xwpf.usermodel.XWPFRun fileRun = filePara.createRun();
                fileRun.addBreak();
                fileRun.setText("文件名：" + kb.getTitle());
                fileRun.setBold(true);
                fileRun.setFontFamily("宋体");
                fileRun.addBreak();
                fileRun.setText("文件类型：" + kb.getFileType());
                fileRun.addBreak();
                fileRun.setText("文件大小：" + formatFileSize(kb.getFileSize()));
                fileRun.addBreak();
                fileRun.setText("标签：" + (kb.getTags() != null ? kb.getTags() : "无"));
                fileRun.addBreak();
                fileRun.setText("描述：" + (kb.getDescription() != null ? kb.getDescription() : "无"));
                fileRun.addBreak();
                if (kb.getContent() != null && !kb.getContent().trim().isEmpty()) {
                    fileRun.setText("文件内容：");
                    fileRun.addBreak();
                    fileRun.setText(kb.getContent().length() > 1000 ? 
                        kb.getContent().substring(0, 1000) + "..." : kb.getContent());
                }
                fileRun.addBreak();
                fileRun.setText("上传时间：" + (kb.getCreateTime() != null ? 
                    kb.getCreateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : ""));
            }
        }

        // Set response headers
        String fileName = course.getCourseName() + "_课程资源_" + 
            LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".docx";
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + 
            java.net.URLEncoder.encode(fileName, "UTF-8") + "\"");

        // Write document to response
        document.write(response.getOutputStream());
        document.close();
        
        log.info("Course resources exported successfully for courseId: {}", courseId);
    }

    @Override
    public void exportKnowledgeBaseAsDocx(jakarta.servlet.http.HttpServletResponse response) throws Exception {
        // Get all AI processed knowledge data
        List<com.example.aieduforge.entity.AIKnowledgeData> aiDataList = 
            knowledgeBaseMapper.selectAllAIKnowledgeData();

        // Create Word document
        org.apache.poi.xwpf.usermodel.XWPFDocument document = new org.apache.poi.xwpf.usermodel.XWPFDocument();
        
        // Title
        org.apache.poi.xwpf.usermodel.XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(org.apache.poi.xwpf.usermodel.ParagraphAlignment.CENTER);
        org.apache.poi.xwpf.usermodel.XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("AI知识库整理报告");
        titleRun.setBold(true);
        titleRun.setFontSize(18);
        titleRun.setFontFamily("宋体");

        // Export info
        org.apache.poi.xwpf.usermodel.XWPFParagraph infoPara = document.createParagraph();
        org.apache.poi.xwpf.usermodel.XWPFRun infoRun = infoPara.createRun();
        infoRun.setText("导出时间：" + LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        infoRun.setFontFamily("宋体");
        infoRun.addBreak();
        infoRun.setText("总计条目：" + aiDataList.size());

        // Process each AI knowledge data
        for (com.example.aieduforge.entity.AIKnowledgeData aiData : aiDataList) {
            // Get corresponding knowledge base
            KnowledgeBase kb = getById(aiData.getKnowledgeBaseId());
            if (kb == null) continue;

            org.apache.poi.xwpf.usermodel.XWPFParagraph itemPara = document.createParagraph();
            org.apache.poi.xwpf.usermodel.XWPFRun itemRun = itemPara.createRun();
            itemRun.addBreak();
            itemRun.setText("文件：" + kb.getTitle());
            itemRun.setBold(true);
            itemRun.setFontSize(14);
            itemRun.setFontFamily("宋体");
            itemRun.addBreak();
            
            if (aiData.getSummary() != null) {
                itemRun.setText("AI摘要：");
                itemRun.setBold(true);
                itemRun.addBreak();
                itemRun.setText(aiData.getSummary());
                itemRun.setBold(false);
                itemRun.addBreak();
            }
            
            if (aiData.getKeyPoints() != null) {
                itemRun.setText("关键知识点：");
                itemRun.setBold(true);
                itemRun.addBreak();
                itemRun.setText(aiData.getKeyPoints());
                itemRun.setBold(false);
                itemRun.addBreak();
            }
            
            if (aiData.getSmartTags() != null) {
                itemRun.setText("智能标签：" + aiData.getSmartTags());
                itemRun.addBreak();
            }
            
            if (aiData.getDifficulty() != null) {
                itemRun.setText("难度等级：" + aiData.getDifficulty());
                itemRun.addBreak();
            }
            
            if (aiData.getExtractedText() != null && !aiData.getExtractedText().trim().isEmpty()) {
                itemRun.setText("提取文本：");
                itemRun.setBold(true);
                itemRun.addBreak();
                String text = aiData.getExtractedText();
                itemRun.setText(text.length() > 2000 ? text.substring(0, 2000) + "..." : text);
                itemRun.setBold(false);
                itemRun.addBreak();
            }
        }

        // Set response headers
        String fileName = "AI知识库整理_" + 
            LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".docx";
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + 
            java.net.URLEncoder.encode(fileName, "UTF-8") + "\"");

        // Write document to response
        document.write(response.getOutputStream());
        document.close();
        
        log.info("Knowledge base exported successfully");
    }

    @Override
    public void exportSingleResourceAsDocx(Long resourceId, String type, jakarta.servlet.http.HttpServletResponse response) throws Exception {
        org.apache.poi.xwpf.usermodel.XWPFDocument document = new org.apache.poi.xwpf.usermodel.XWPFDocument();
        String fileName = "";

        if ("knowledge".equals(type)) {
            KnowledgeBase kb = getById(resourceId);
            if (kb == null) {
                throw new BusinessException("Knowledge file not found");
            }

            fileName = kb.getTitle() + "_详情_" + 
                LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".docx";

            // Title
            org.apache.poi.xwpf.usermodel.XWPFParagraph titlePara = document.createParagraph();
            titlePara.setAlignment(org.apache.poi.xwpf.usermodel.ParagraphAlignment.CENTER);
            org.apache.poi.xwpf.usermodel.XWPFRun titleRun = titlePara.createRun();
            titleRun.setText(kb.getTitle());
            titleRun.setBold(true);
            titleRun.setFontSize(18);
            titleRun.setFontFamily("宋体");

            // File info
            org.apache.poi.xwpf.usermodel.XWPFParagraph infoPara = document.createParagraph();
            org.apache.poi.xwpf.usermodel.XWPFRun infoRun = infoPara.createRun();
            infoRun.setText("文件信息");
            infoRun.setBold(true);
            infoRun.setFontSize(14);
            infoRun.setFontFamily("宋体");
            infoRun.addBreak();
            infoRun.setText("文件类型：" + kb.getFileType());
            infoRun.setBold(false);
            infoRun.addBreak();
            infoRun.setText("文件大小：" + formatFileSize(kb.getFileSize()));
            infoRun.addBreak();
            infoRun.setText("学科：" + (kb.getSubject() != null ? kb.getSubject() : "未分类"));
            infoRun.addBreak();
            infoRun.setText("标签：" + (kb.getTags() != null ? kb.getTags() : "无"));
            infoRun.addBreak();
            infoRun.setText("描述：" + (kb.getDescription() != null ? kb.getDescription() : "无"));

            // File content
            if (kb.getContent() != null && !kb.getContent().trim().isEmpty()) {
                org.apache.poi.xwpf.usermodel.XWPFParagraph contentPara = document.createParagraph();
                org.apache.poi.xwpf.usermodel.XWPFRun contentRun = contentPara.createRun();
                contentRun.addBreak();
                contentRun.setText("文件内容");
                contentRun.setBold(true);
                contentRun.setFontSize(14);
                contentRun.setFontFamily("宋体");
                contentRun.addBreak();
                contentRun.setText(kb.getContent());
                contentRun.setBold(false);
            }

            // AI analysis if available
            try {
                com.example.aieduforge.entity.AIKnowledgeData aiData = 
                    knowledgeBaseMapper.selectAIKnowledgeData(resourceId);
                
                if (aiData != null) {
                    org.apache.poi.xwpf.usermodel.XWPFParagraph aiPara = document.createParagraph();
                    org.apache.poi.xwpf.usermodel.XWPFRun aiRun = aiPara.createRun();
                    aiRun.addBreak();
                    aiRun.setText("AI分析结果");
                    aiRun.setBold(true);
                    aiRun.setFontSize(14);
                    aiRun.setFontFamily("宋体");
                    aiRun.addBreak();
                    
                    if (aiData.getSummary() != null) {
                        aiRun.setText("摘要：" + aiData.getSummary());
                        aiRun.setBold(false);
                        aiRun.addBreak();
                    }
                    
                    if (aiData.getKeyPoints() != null) {
                        aiRun.setText("关键知识点：" + aiData.getKeyPoints());
                        aiRun.addBreak();
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to get AI analysis for knowledge file: {}", resourceId, e);
            }

        } else if ("teaching_content".equals(type)) {
            TeachingContent tc = teachingContentService.getById(resourceId);
            if (tc == null) {
                throw new BusinessException("Teaching content not found");
            }

            fileName = tc.getTitle() + "_教学内容_" + 
                LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".docx";

            // Title
            org.apache.poi.xwpf.usermodel.XWPFParagraph titlePara = document.createParagraph();
            titlePara.setAlignment(org.apache.poi.xwpf.usermodel.ParagraphAlignment.CENTER);
            org.apache.poi.xwpf.usermodel.XWPFRun titleRun = titlePara.createRun();
            titleRun.setText(tc.getTitle());
            titleRun.setBold(true);
            titleRun.setFontSize(18);
            titleRun.setFontFamily("宋体");

            // Content info
            org.apache.poi.xwpf.usermodel.XWPFParagraph infoPara = document.createParagraph();
            org.apache.poi.xwpf.usermodel.XWPFRun infoRun = infoPara.createRun();
            infoRun.setText("内容类型：" + (tc.getContentType() != null ? tc.getContentType() : "未知"));
            infoRun.setFontFamily("宋体");
            infoRun.addBreak();
            infoRun.setText("描述：无"); // TeachingContent doesn't have description field

            // Teaching content
            if (tc.getContent() != null && !tc.getContent().trim().isEmpty()) {
                org.apache.poi.xwpf.usermodel.XWPFParagraph contentPara = document.createParagraph();
                org.apache.poi.xwpf.usermodel.XWPFRun contentRun = contentPara.createRun();
                contentRun.addBreak();
                contentRun.setText("教学内容");
                contentRun.setBold(true);
                contentRun.setFontSize(14);
                contentRun.setFontFamily("宋体");
                contentRun.addBreak();
                contentRun.setText(tc.getContent());
                contentRun.setBold(false);
            }
        }

        // Set response headers
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + 
            java.net.URLEncoder.encode(fileName, "UTF-8") + "\"");

        // Write document to response
        document.write(response.getOutputStream());
        document.close();
        
        log.info("Single resource exported successfully: {} (type: {})", resourceId, type);
    }

    @Override
    public Map<String, Object> searchKnowledgeWithFilters(Map<String, Object> searchParams) {
        String keyword = (String) searchParams.get("keyword");
        String subject = (String) searchParams.get("subject");
        String fileType = (String) searchParams.get("fileType");
        Integer current = (Integer) searchParams.get("current");
        Integer size = (Integer) searchParams.get("size");
        
        log.info("Searching knowledge with filters - keyword: {}, subject: {}, fileType: {}", 
                keyword, subject, fileType);
        
        // 使用Mapper进行多条件查询
        List<KnowledgeBase> allFiles = knowledgeBaseMapper.searchWithFilters(keyword, subject, fileType);
        
        // 分页处理
        int total = allFiles.size();
        int start = (current - 1) * size;
        int end = Math.min(start + size, total);
        List<KnowledgeBase> pagedFiles = allFiles.subList(start, end);
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", pagedFiles);
        result.put("total", total);
        result.put("current", current);
        result.put("size", size);
        
        log.info("Found {} knowledge files matching filters", total);
        return result;
    }

    private String formatFileSize(Long size) {
        if (size == null || size == 0) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double fileSize = size.doubleValue();
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", fileSize, units[unitIndex]);
    }
}