package com.example.aieduforge.service.impl;

import com.example.aieduforge.dto.StudentLearningStatsDTO;
import com.example.aieduforge.mapper.LearningAnalyticsMapper;
import com.example.aieduforge.mapper.PracticeRecordMapper;
import com.example.aieduforge.service.StudentStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
public class StudentStatsServiceImpl implements StudentStatsService {

    @Autowired
    private LearningAnalyticsMapper learningAnalyticsMapper;

    @Autowired
    private PracticeRecordMapper practiceRecordMapper;

    @Override
    public StudentLearningStatsDTO getStudentStats(Long studentId, Integer days) {
        StudentLearningStatsDTO stats = new StudentLearningStatsDTO();

        // 获取总体统计数据
        stats.setTotalStudyTime(learningAnalyticsMapper.getTotalStudyTime(studentId));
        stats.setTotalPractices(practiceRecordMapper.getTotalPractices(studentId));
        stats.setAverageAccuracy(practiceRecordMapper.getAverageAccuracy(studentId));
        stats.setCompletedCourses(learningAnalyticsMapper.getCompletedCourses(studentId));

        // 获取每日统计数据
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        stats.setDailyStats(learningAnalyticsMapper.getDailyStats(studentId, startDate));

        // 获取学科分布数据
        stats.setSubjectDistribution(learningAnalyticsMapper.getSubjectDistribution(studentId));

        return stats;
    }
}
