package com.example.aieduforge.service.impl;

import com.example.aieduforge.dto.ErrorResponseDto;
import com.example.aieduforge.service.ErrorFormattingService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.aieduforge.dto.AiModelDTO;
import com.example.aieduforge.dto.ModelConnectionTestDTO;
import com.example.aieduforge.entity.AiModel;
import com.example.aieduforge.mapper.AiModelMapper;
import com.example.aieduforge.service.AiModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI Model Service Implementation
 */
@Slf4j
@Service
public class AiModelServiceImpl implements AiModelService {

    @Autowired
    private AiModelMapper aiModelMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ErrorFormattingService errorFormattingService;

    @Override
    public IPage<AiModelDTO> getModelList(Page<AiModel> page, String provider, Boolean isActive) {
        QueryWrapper<AiModel> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(provider)) {
            queryWrapper.eq("provider", provider);
        }
        
        if (isActive != null) {
            queryWrapper.eq("is_active", isActive);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<AiModel> modelPage = aiModelMapper.selectPage(page, queryWrapper);
        
        // Convert to DTO
        IPage<AiModelDTO> dtoPage = modelPage.convert(this::convertToDTO);
        
        return dtoPage;
    }

    @Override
    public List<AiModelDTO> getActiveModels() {
        QueryWrapper<AiModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_active", true);
        queryWrapper.orderByDesc("is_default").orderByAsc("model_name");
        
        List<AiModel> models = aiModelMapper.selectList(queryWrapper);
        return models.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public AiModelDTO getModelById(Long id) {
        AiModel model = aiModelMapper.selectById(id);
        return model != null ? convertToDTO(model) : null;
    }

    @Override
    public AiModelDTO getActiveModel() {
        AiModel model = aiModelMapper.getActiveModel();
        return model != null ? convertToDTO(model) : null;
    }

    @Override
    public AiModelDTO getDefaultModel() {
        AiModel model = aiModelMapper.getDefaultModel();
        return model != null ? convertToDTO(model) : null;
    }

    @Override
    @Transactional
    public AiModelDTO createModel(AiModelDTO modelDTO, Long userId) {
        AiModel model = new AiModel();
        BeanUtils.copyProperties(modelDTO, model);
        model.setCreatedBy(userId);
        model.setCreateTime(LocalDateTime.now());
        model.setUpdateTime(LocalDateTime.now());
        
        // Ensure only one default model
        if (Boolean.TRUE.equals(model.getIsDefault())) {
            aiModelMapper.clearAllDefaults();
        }
        
        // Ensure only one active model
        if (Boolean.TRUE.equals(model.getIsActive())) {
            aiModelMapper.deactivateAllModels();
        }
        
        aiModelMapper.insert(model);
        return convertToDTO(model);
    }

    @Override
    @Transactional
    public AiModelDTO updateModel(Long id, AiModelDTO modelDTO, Long userId) {
        AiModel existingModel = aiModelMapper.selectById(id);
        if (existingModel == null) {
            throw new RuntimeException("Model not found with id: " + id);
        }
        
        BeanUtils.copyProperties(modelDTO, existingModel, "id", "createTime", "createdBy");
        existingModel.setUpdateTime(LocalDateTime.now());
        
        // Ensure only one default model
        if (Boolean.TRUE.equals(existingModel.getIsDefault())) {
            aiModelMapper.clearAllDefaults();
        }
        
        // Ensure only one active model
        if (Boolean.TRUE.equals(existingModel.getIsActive())) {
            aiModelMapper.deactivateAllModels();
        }
        
        aiModelMapper.updateById(existingModel);
        return convertToDTO(existingModel);
    }

    @Override
    public boolean deleteModel(Long id) {
        AiModel model = aiModelMapper.selectById(id);
        if (model == null) {
            return false;
        }
        
        // Cannot delete active or default model
        if (Boolean.TRUE.equals(model.getIsActive()) || Boolean.TRUE.equals(model.getIsDefault())) {
            throw new RuntimeException("Cannot delete active or default model");
        }
        
        return aiModelMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean setActiveModel(Long id) {
        AiModel model = aiModelMapper.selectById(id);
        if (model == null) {
            return false;
        }
        
        // Deactivate all models first
        aiModelMapper.deactivateAllModels();
        
        // Set this model as active
        model.setIsActive(true);
        model.setUpdateTime(LocalDateTime.now());
        
        return aiModelMapper.updateById(model) > 0;
    }

    @Override
    @Transactional
    public boolean setDefaultModel(Long id) {
        AiModel model = aiModelMapper.selectById(id);
        if (model == null) {
            return false;
        }
        
        // Clear all defaults first
        aiModelMapper.clearAllDefaults();
        
        // Set this model as default
        model.setIsDefault(true);
        model.setUpdateTime(LocalDateTime.now());
        
        return aiModelMapper.updateById(model) > 0;
    }

    @Override
    public ModelConnectionTestDTO testModelConnection(Long id) {
        AiModel model = aiModelMapper.selectById(id);
        if (model == null) {
            return new ModelConnectionTestDTO(id, false, "Model not found");
        }
        
        AiModelDTO modelDTO = convertToDTO(model);
        return testModelConnection(modelDTO);
    }

    @Override
    public ModelConnectionTestDTO testModelConnection(AiModelDTO modelDTO) {
        long startTime = System.currentTimeMillis();
        
        try {
            // Create test request
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            if (StringUtils.hasText(modelDTO.getApiKey())) {
                headers.setBearerAuth(modelDTO.getApiKey().replace("Bearer ", ""));
            }
            
            Map<String, Object> requestBody = new HashMap<>();
            String url = modelDTO.getApiEndpoint();
            if (!url.endsWith("/")) {
                url += "/";
            }
            
            // Configure request based on provider
            if ("ollama".equalsIgnoreCase(modelDTO.getProvider())) {
                // Ollama API format
                requestBody.put("model", modelDTO.getModelId());
                requestBody.put("prompt", "Hello, this is a connection test.");
                requestBody.put("stream", false);
                url += "api/generate";
            } else {
                // OpenAI-compatible API format
                requestBody.put("model", modelDTO.getModelId());
                requestBody.put("messages", List.of(
                    Map.of("role", "user", "content", "Hello, this is a connection test.")
                ));
                requestBody.put("max_tokens", Math.min(modelDTO.getMaxTokens(), 50));
                requestBody.put("temperature", modelDTO.getTemperature());
                url += "chat/completions";
            }
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                Map.class
            );
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // Update connection status in database
                if (modelDTO.getId() != null) {
                    aiModelMapper.updateConnectionStatus(modelDTO.getId(), AiModel.CONNECTION_SUCCESS, null);
                }
                
                ModelConnectionTestDTO result = new ModelConnectionTestDTO(
                    modelDTO.getId(), true, "Connection successful", responseTime
                );
                result.setTestPrompt("Hello, this is a connection test.");
                
                // Extract response content if available
                // Extract response content if available
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null) {
                    if ("ollama".equalsIgnoreCase(modelDTO.getProvider())) {
                        // Ollama response format
                        if (responseBody.containsKey("response")) {
                            result.setTestResponse((String) responseBody.get("response"));
                        }
                    } else {
                        // OpenAI-compatible response format
                        if (responseBody.containsKey("choices")) {
                            List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                            if (!choices.isEmpty()) {
                                Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                                if (message != null && message.containsKey("content")) {
                                    result.setTestResponse((String) message.get("content"));
                                }
                            }
                        }
                    }
                }
                
                return result;
            } else {
                String errorMsg = "HTTP " + response.getStatusCode() + ": " + response.getBody();
                if (modelDTO.getId() != null) {
                    aiModelMapper.updateConnectionStatus(modelDTO.getId(), AiModel.CONNECTION_FAILED, errorMsg);
                }
                
                // Format error using ErrorFormattingService
                ErrorResponseDto formattedError = errorFormattingService.formatModelConnectionError(
                    errorMsg, modelDTO.getModelName()
                );
                
                return new ModelConnectionTestDTO(modelDTO.getId(), false, errorMsg, responseTime, formattedError);
            }
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            String errorMsg = "Connection failed: " + e.getMessage();
            
            if (modelDTO.getId() != null) {
                aiModelMapper.updateConnectionStatus(modelDTO.getId(), AiModel.CONNECTION_FAILED, errorMsg);
            }
            
            log.error("Model connection test failed for model {}: {}", modelDTO.getModelName(), e.getMessage());
            
            // Format error using ErrorFormattingService
            ErrorResponseDto formattedError = errorFormattingService.formatModelConnectionError(
                errorMsg, modelDTO.getModelName()
            );
            
            return new ModelConnectionTestDTO(modelDTO.getId(), false, errorMsg, responseTime, formattedError);
        }
    }

    @Override
    public List<ModelConnectionTestDTO> testAllModels() {
        List<AiModel> models = aiModelMapper.selectList(null);
        return models.stream()
                .map(model -> testModelConnection(model.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public AiModel getModelForAIService() {
        // First try to get active model
        AiModel activeModel = aiModelMapper.getActiveModel();
        if (activeModel != null) {
            return activeModel;
        }
        
        // Fallback to default model
        AiModel defaultModel = aiModelMapper.getDefaultModel();
        if (defaultModel != null) {
            return defaultModel;
        }
        
        // If no active or default model, get the first available model
        QueryWrapper<AiModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time").last("LIMIT 1");
        return aiModelMapper.selectOne(queryWrapper);
    }

    private AiModelDTO convertToDTO(AiModel model) {
        AiModelDTO dto = new AiModelDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }
}