package com.example.aieduforge.service.impl;

import com.example.aieduforge.entity.UsageStatistics;
import com.example.aieduforge.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Statistics Data Generator Service
 * Generates usage statistics based on actual user activities
 */
@Slf4j
@Service
public class StatisticsDataGeneratorService {

    @Autowired
    private UsageStatisticsMapper usageStatisticsMapper;
    
    @Autowired
    private PracticeRecordMapper practiceRecordMapper;
    
    @Autowired
    private StudentQuestionMapper studentQuestionMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * Generate daily statistics based on actual user activities
     * Runs every day at 1:00 AM
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateDailyStatistics() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("Generating daily statistics for date: {}", yesterday);
        
        try {
            generatePracticeStatistics(yesterday);
            generateQuestionStatistics(yesterday);
            log.info("Daily statistics generation completed for date: {}", yesterday);
        } catch (Exception e) {
            log.error("Failed to generate daily statistics for date: {}", yesterday, e);
        }
    }

    /**
     * Generate statistics from practice records
     */
    private void generatePracticeStatistics(LocalDate date) {
        try {
            // Get practice activities for the date
            List<Map<String, Object>> practiceActivities = practiceRecordMapper.getPracticeActivitiesByDate(date);
            
            for (Map<String, Object> activity : practiceActivities) {
                Long studentId = (Long) activity.get("student_id");
                Integer totalQuestions = (Integer) activity.get("total_questions");
                Integer timeSpent = (Integer) activity.get("time_spent");
                
                // Check if record already exists
                UsageStatistics existingRecord = usageStatisticsMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UsageStatistics>()
                        .eq("user_id", studentId)
                        .eq("user_type", 2)
                        .eq("module_name", "练习模块")
                        .eq("stat_date", date)
                );
                
                if (existingRecord == null) {
                    UsageStatistics stats = new UsageStatistics();
                    stats.setUserId(studentId);
                    stats.setUserType(2); // Student
                    stats.setModuleName("练习模块");
                    stats.setActionType("练习答题");
                    stats.setAccessCount(totalQuestions);
                    stats.setDurationSeconds(timeSpent);
                    stats.setStatDate(date);
                    stats.setCreateTime(LocalDateTime.now());
                    stats.setUpdateTime(LocalDateTime.now());
                    
                    usageStatisticsMapper.insert(stats);
                } else {
                    // Update existing record
                    existingRecord.setAccessCount(existingRecord.getAccessCount() + totalQuestions);
                    existingRecord.setDurationSeconds(existingRecord.getDurationSeconds() + timeSpent);
                    existingRecord.setUpdateTime(LocalDateTime.now());
                    
                    usageStatisticsMapper.updateById(existingRecord);
                }
            }
        } catch (Exception e) {
            log.error("Failed to generate practice statistics for date: {}", date, e);
        }
    }

    /**
     * Generate statistics from student questions
     */
    private void generateQuestionStatistics(LocalDate date) {
        try {
            // Get question activities for the date
            List<Map<String, Object>> questionActivities = studentQuestionMapper.getQuestionActivitiesByDate(date);
            
            for (Map<String, Object> activity : questionActivities) {
                Long studentId = (Long) activity.get("student_id");
                Integer questionCount = (Integer) activity.get("question_count");
                
                // Check if record already exists
                UsageStatistics existingRecord = usageStatisticsMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UsageStatistics>()
                        .eq("user_id", studentId)
                        .eq("user_type", 2)
                        .eq("module_name", "知识问答")
                        .eq("stat_date", date)
                );
                
                if (existingRecord == null) {
                    UsageStatistics stats = new UsageStatistics();
                    stats.setUserId(studentId);
                    stats.setUserType(2); // Student
                    stats.setModuleName("知识问答");
                    stats.setActionType("AI提问");
                    stats.setAccessCount(questionCount);
                    stats.setDurationSeconds(questionCount * 60); // Estimate 1 minute per question
                    stats.setStatDate(date);
                    stats.setCreateTime(LocalDateTime.now());
                    stats.setUpdateTime(LocalDateTime.now());
                    
                    usageStatisticsMapper.insert(stats);
                } else {
                    // Update existing record
                    existingRecord.setAccessCount(existingRecord.getAccessCount() + questionCount);
                    existingRecord.setDurationSeconds(existingRecord.getDurationSeconds() + questionCount * 60);
                    existingRecord.setUpdateTime(LocalDateTime.now());
                    
                    usageStatisticsMapper.updateById(existingRecord);
                }
            }
        } catch (Exception e) {
            log.error("Failed to generate question statistics for date: {}", date, e);
        }
    }

    /**
     * Manual trigger for generating statistics (for testing)
     */
    public void generateStatisticsForDate(LocalDate date) {
        log.info("Manually generating statistics for date: {}", date);
        generatePracticeStatistics(date);
        generateQuestionStatistics(date);
        log.info("Manual statistics generation completed for date: {}", date);
    }
}