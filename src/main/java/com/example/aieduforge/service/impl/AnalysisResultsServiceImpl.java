package com.example.aieduforge.service.impl;

import com.example.aieduforge.entity.AnalysisResults;
import com.example.aieduforge.mapper.AnalysisResultsMapper;
import com.example.aieduforge.service.AnalysisResultsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 分析结果服务实现类
 */
@Slf4j
@Service
public class AnalysisResultsServiceImpl implements AnalysisResultsService {
    
    @Autowired
    private AnalysisResultsMapper analysisResultsMapper;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public Long saveAnalysisResult(String analysisType, Long targetId, Long courseId, Map<String, Object> analysisData, Long teacherId) {
        try {
            // 先查询是否已存在相同的分析记录（学生+课程+分析类型）
            AnalysisResults existingResult = analysisResultsMapper.getLatestAnalysisResult(analysisType, targetId, courseId);
            
            if (existingResult != null) {
                // 更新现有记录
                existingResult.setAnalysisResult(objectMapper.writeValueAsString(analysisData));
                existingResult.setTeacherId(teacherId);
                analysisResultsMapper.updateById(existingResult);
                log.info("更新分析结果成功，学生ID: {}, 课程ID: {}, 分析ID: {}", targetId, courseId, existingResult.getId());
                return existingResult.getId();
            } else {
                // 插入新记录
                AnalysisResults analysisResults = new AnalysisResults();
                analysisResults.setAnalysisType(analysisType);
                analysisResults.setTargetId(targetId);
                analysisResults.setCourseId(courseId);
                analysisResults.setAnalysisResult(objectMapper.writeValueAsString(analysisData));
                analysisResults.setTeacherId(teacherId);
                
                analysisResultsMapper.insert(analysisResults);
                log.info("保存分析结果成功，学生ID: {}, 课程ID: {}, 分析ID: {}", targetId, courseId, analysisResults.getId());
                return analysisResults.getId();
            }
        } catch (Exception e) {
            log.error("保存分析结果失败", e);
            throw new RuntimeException("保存分析结果失败: " + e.getMessage());
        }
    }
    
    @Override
    public AnalysisResults getLatestAnalysisResult(String analysisType, Long targetId, Long courseId) {
        return analysisResultsMapper.getLatestAnalysisResult(analysisType, targetId, courseId);
    }
    
    @Override
    public List<AnalysisResults> getAnalysisHistoryByTeacher(Long teacherId) {
        return analysisResultsMapper.getAnalysisHistoryByTeacher(teacherId);
    }
    
    @Override
    public String generateAnalysisReport(Long analysisResultId, String format) {
        // 这里可以根据format生成不同格式的报告
        // 暂时返回文件路径，实际实现时需要生成真实文件
        return "/reports/analysis_" + analysisResultId + "." + format;
    }
    
    @Override
    public byte[] exportAnalysisToExcel(Long analysisResultId) {
        try {
            AnalysisResults analysisResults = analysisResultsMapper.selectById(analysisResultId);
            if (analysisResults == null) {
                throw new RuntimeException("分析结果不存在");
            }
            
            Map<String, Object> analysisData = objectMapper.readValue(analysisResults.getAnalysisResult(), Map.class);
            
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生分析报告");
            
            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("学生学习分析报告");
            
            // 创建样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 16);
            titleStyle.setFont(titleFont);
            titleCell.setCellStyle(titleStyle);
            
            int rowNum = 2;
            
            // 基本信息
            if (analysisData.containsKey("basicInfo")) {
                Map<String, Object> basicInfo = (Map<String, Object>) analysisData.get("basicInfo");
                Row infoHeaderRow = sheet.createRow(rowNum++);
                infoHeaderRow.createCell(0).setCellValue("基本信息");
                
                if (basicInfo.containsKey("studentName")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("学生姓名");
                    row.createCell(1).setCellValue(basicInfo.get("studentName").toString());
                }
                
                if (basicInfo.containsKey("totalAnswers")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("总答题数");
                    row.createCell(1).setCellValue(basicInfo.get("totalAnswers").toString());
                }
                
                if (basicInfo.containsKey("totalPractices")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("总练习数");
                    row.createCell(1).setCellValue(basicInfo.get("totalPractices").toString());
                }
                rowNum++;
            }
            
            // 学习表现
            if (analysisData.containsKey("performance")) {
                Map<String, Object> performance = (Map<String, Object>) analysisData.get("performance");
                Row perfHeaderRow = sheet.createRow(rowNum++);
                perfHeaderRow.createCell(0).setCellValue("学习表现");
                
                if (performance.containsKey("overallAccuracy")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("整体正确率");
                    row.createCell(1).setCellValue(performance.get("overallAccuracy").toString() + "%");
                }
                
                if (performance.containsKey("avgTeacherScore")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("平均分");
                    row.createCell(1).setCellValue(performance.get("avgTeacherScore").toString());
                }
                
                if (performance.containsKey("performanceLevel")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("表现等级");
                    row.createCell(1).setCellValue(performance.get("performanceLevel").toString());
                }
                rowNum++;
            }
            
            // 知识点掌握情况
            if (analysisData.containsKey("knowledgeMastery")) {
                List<Map<String, Object>> knowledgeMastery = (List<Map<String, Object>>) analysisData.get("knowledgeMastery");
                Row kmHeaderRow = sheet.createRow(rowNum++);
                kmHeaderRow.createCell(0).setCellValue("知识点掌握情况");
                
                // 表头
                Row headerRow = sheet.createRow(rowNum++);
                headerRow.createCell(0).setCellValue("知识点");
                headerRow.createCell(1).setCellValue("总题数");
                headerRow.createCell(2).setCellValue("正确数");
                headerRow.createCell(3).setCellValue("正确率");
                headerRow.createCell(4).setCellValue("掌握程度");
                
                for (Map<String, Object> mastery : knowledgeMastery) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(mastery.get("knowledgePoint").toString());
                    row.createCell(1).setCellValue(mastery.get("totalQuestions").toString());
                    row.createCell(2).setCellValue(mastery.get("correctQuestions").toString());
                    row.createCell(3).setCellValue(mastery.get("accuracyRate").toString() + "%");
                    row.createCell(4).setCellValue(mastery.get("masteryDescription").toString());
                }
                rowNum++;
            }
            
            // AI诊断
            if (analysisData.containsKey("aiDiagnosis")) {
                Map<String, Object> aiDiagnosis = (Map<String, Object>) analysisData.get("aiDiagnosis");
                Row aiHeaderRow = sheet.createRow(rowNum++);
                aiHeaderRow.createCell(0).setCellValue("AI智能诊断");
                
                if (aiDiagnosis.containsKey("overallAssessment")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("整体评估");
                    row.createCell(1).setCellValue(aiDiagnosis.get("overallAssessment").toString());
                }
                
                if (aiDiagnosis.containsKey("nextStepPlan")) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue("下一步学习计划");
                    row.createCell(1).setCellValue(aiDiagnosis.get("nextStepPlan").toString());
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < 5; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();
            
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage());
        }
    }
    
    @Override
    public byte[] exportAnalysisToPdf(Long analysisResultId) {
        // PDF导出功能，需要引入PDF库如iText
        // 这里先返回空，后续可以实现
        throw new RuntimeException("PDF导出功能暂未实现");
    }
    
    @Override
    public AnalysisResults getById(Long id) {
        return analysisResultsMapper.selectById(id);
    }
}