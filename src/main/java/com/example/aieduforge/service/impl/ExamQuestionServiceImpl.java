package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.mapper.ExamQuestionMapper;
import com.example.aieduforge.service.ExamQuestionService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements ExamQuestionService {

    @Override
    public List<ExamQuestion> getQuestionsByCourse(Long courseId) {
        return lambdaQuery()
                .eq(ExamQuestion::getCourseId, courseId)
                .eq(ExamQuestion::getStatus, 1)
                .orderByDesc(ExamQuestion::getCreateTime)
                .list();
    }

    @Override
    public List<ExamQuestion> findRandomQuestions(Long courseId, Integer count) {
        return baseMapper.findRandomQuestions(courseId, count);
    }

    @Override
    public List<ExamQuestion> findByDifficultyLevel(Long courseId, int difficulty) {
        return lambdaQuery()
                .eq(ExamQuestion::getCourseId, courseId)
                .eq(ExamQuestion::getDifficultyLevel, difficulty)
                .eq(ExamQuestion::getStatus, 1)
                .list();
    }

    @Override
    public ExamQuestion getById(Long id) {
        return super.getById(id);
    }

    @Override
    public List<ExamQuestion> findRandomQuestionsByType(Long courseId, Integer questionType, Integer count) {
        return baseMapper.findRandomQuestionsByType(courseId, questionType, count);
    }
}
