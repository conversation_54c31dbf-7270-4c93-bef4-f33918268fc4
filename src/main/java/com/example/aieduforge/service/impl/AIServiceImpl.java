package com.example.aieduforge.service.impl;

import com.example.aieduforge.config.AIProperties;
import com.example.aieduforge.service.DynamicAIService;
import com.example.aieduforge.dto.QuestionDTO;
import com.example.aieduforge.entity.AiModel;
import com.example.aieduforge.service.AiModelService;
import com.example.aieduforge.service.AIService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class AIServiceImpl implements AIService {

    private final AIProperties aiProperties;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final DynamicAIService dynamicAIService;

    @Autowired
    public AIServiceImpl(AIProperties aiProperties, RestTemplate restTemplate, ObjectMapper objectMapper, DynamicAIService dynamicAIService) {
        this.aiProperties = aiProperties;
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.dynamicAIService = dynamicAIService;
    }

    @Override
    public boolean testConnection() {
        try {
            // First try the dynamic AI service
            if (dynamicAIService.isCurrentModelAvailable()) {
                return true;
            }
            
            // Fallback to the original implementation
            String response = callOpenRouter("Hi");
            return response != null && !response.isEmpty();
        } catch (org.springframework.web.client.HttpClientErrorException.TooManyRequests e) {
            log.warn("OpenRouter rate limit exceeded, but connection is valid");
            return true; // 速率限制错误表示连接是有效的，只是请求太频繁
        } catch (Exception e) {
            log.error("Failed to test OpenRouter connection", e);
            return false;
        }
    }

    @Override
    public String generateTeachingContent(String courseOutline, String chapterName, String contentType) {
        String prompt = String.format("""
            作为一名教育专家，请根据以下课程大纲和章节名称生成详细的教学内容。
            
            课程大纲：
            %s
            
            章节名称：%s
            内容类型：%s
            
            请生成结构化的教学内容，包含知识点讲解、案例分析和练习建议。
            """, courseOutline, chapterName, contentType);

        return callOpenRouter(prompt);
    }

    @Override
    public List<QuestionDTO> generateExamQuestions(String teachingContent, String questionType, Integer difficulty, Integer count) {
        log.info("开始生成题目 - 类型: {}, 难度: {}, 数量: {}", questionType, difficulty, count);
        
        // 验证输入参数
        if (count == null || count <= 0) {
            throw new IllegalArgumentException("题目数量必须大于0");
        }
        
        if (count > 20) {
            log.warn("请求生成题目数量过多: {}，限制为20道", count);
            count = 20;
        }
        
        // 验证题目类型的合法性
        Set<String> validTypes = new HashSet<>(Arrays.asList(
                "SINGLE_CHOICE", "MULTIPLE_CHOICE", "FILL_BLANK",
                "SHORT_ANSWER", "PROGRAMMING", "CASE_ANALYSIS"
        ));

        if (!validTypes.contains(questionType)) {
            log.error("不支持的题目类型: {}", questionType);
            throw new IllegalArgumentException("不支持的题目类型: " + questionType);
        }

        List<QuestionDTO> result;
        
        // 编程题使用特殊处理
        if ("PROGRAMMING".equals(questionType)) {
            result = generateProgrammingQuestions(teachingContent, difficulty, count);
        } else {
            // 其他题型使用JSON格式
            result = generateNonProgrammingQuestions(teachingContent, questionType, difficulty, count);
        }
        
        // 最终验证和调整
        if (result.size() != count) {
            log.warn("生成题目数量不匹配 - 期望: {}, 实际: {}, 类型: {}", count, result.size(), questionType);
            
            // 如果数量不足，尝试最后一次补充
            if (result.size() < count) {
                List<QuestionDTO> finalSupplement = generateSupplementaryQuestions(
                        teachingContent, questionType, difficulty, count - result.size()
                );
                result.addAll(finalSupplement);
            }
            
            // 如果数量过多，截取到指定数量
            if (result.size() > count) {
                result = result.subList(0, count);
            }
        }
        
        log.info("题目生成完成 - 类型: {}, 最终数量: {}/{}", questionType, result.size(), count);
        return result;
    }

    /**
     * 生成编程题 - 使用分隔符格式避免JSON解析问题，带数量保证
     */
    private List<QuestionDTO> generateProgrammingQuestions(String teachingContent, Integer difficulty, Integer count) {
        List<QuestionDTO> allQuestions = new ArrayList<>();
        int maxRetries = 3;
        int currentRetry = 0;
        
        while (allQuestions.size() < count && currentRetry < maxRetries) {
            try {
                int remainingCount = count - allQuestions.size();
                String prompt = buildProgrammingPrompt(teachingContent, difficulty, remainingCount);
                String response = callOpenRouter(prompt);
                
                log.debug("编程题第{}次尝试，原始响应长度: {}", currentRetry + 1, response.length());
                
                List<QuestionDTO> newQuestions = parseProgrammingResponse(response);
                
                // 去重并添加新题目
                for (QuestionDTO newQuestion : newQuestions) {
                    if (!isDuplicateQuestion(allQuestions, newQuestion)) {
                        allQuestions.add(newQuestion);
                        if (allQuestions.size() >= count) {
                            break;
                        }
                    }
                }
                
                log.info("第{}次尝试后，已生成编程题数量: {}/{}", currentRetry + 1, allQuestions.size(), count);
                
                if (allQuestions.size() >= count) {
                    break;
                }
                
                currentRetry++;
                
            } catch (Exception e) {
                log.error("生成编程题第{}次尝试失败: {}", currentRetry + 1, e.getMessage());
                currentRetry++;
            }
        }
        
        // 只有在主要生成完全失败时才使用补充函数
        if (allQuestions.size() < count) {
            int remainingCount = count - allQuestions.size();
            log.warn("主要生成不足，需要补充{}道编程题", remainingCount);
            
            // 先尝试简单提示词生成
            List<QuestionDTO> simpleQuestions = generateSimpleProgrammingQuestions(
                    teachingContent, difficulty, remainingCount
            );
            allQuestions.addAll(simpleQuestions);
            
            // 如果还是不够，才使用模板补充
            if (allQuestions.size() < count) {
                List<QuestionDTO> supplementQuestions = generateSupplementaryProgrammingQuestions(
                    teachingContent, difficulty, count - allQuestions.size()
                );
                allQuestions.addAll(supplementQuestions);
            }
        }
        
        // 确保不超过请求数量
        if (allQuestions.size() > count) {
            allQuestions = allQuestions.subList(0, count);
        }
        
        log.info("编程题生成完成，最终数量: {}/{}", allQuestions.size(), count);
        return allQuestions;
    }

    /**
     * 生成简单的编程题（当复杂提示词失败时使用）
     */
    private List<QuestionDTO> generateSimpleProgrammingQuestions(String teachingContent, Integer difficulty, int count) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            // 使用更简单的提示词，避免复杂的代码格式
            String simplePrompt = String.format("""
                生成%d道编程题，与"%s"相关，难度%d。
                
                输出JSON格式：
                [
                  {
                    "questionType": "PROGRAMMING",
                    "content": "编写函数实现功能",
                    "correctAnswer": "def func(): pass",
                    "answerAnalysis": "简单解析",
                    "difficulty": %d
                  }
                ]
                
                要求：代码必须简单，一行或用\\n连接。
                """, count, teachingContent, difficulty, difficulty);
            
            String response = callOpenRouter(simplePrompt);
            
            // 尝试多种解析方法
            List<QuestionDTO> parsedQuestions = tryMultipleParsingStrategies(response);
            
            if (!parsedQuestions.isEmpty()) {
                questions.addAll(parsedQuestions);
                log.info("简单提示词生成{}道编程题", parsedQuestions.size());
            }
            
        } catch (Exception e) {
            log.error("简单编程题生成失败: {}", e.getMessage());
        }
        
        return questions;
    }
    
    /**
     * 尝试多种解析策略
     */
    private List<QuestionDTO> tryMultipleParsingStrategies(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        // 策略1：标准JSON解析
        try {
            questions = tryJsonParsing(response);
            if (!questions.isEmpty()) {
                log.debug("标准JSON解析成功");
                return questions;
            }
        } catch (Exception e) {
            log.debug("标准JSON解析失败: {}", e.getMessage());
        }
        
        // 策略2：手动提取和构建
        try {
            questions = manuallyExtractQuestions(response);
            if (!questions.isEmpty()) {
                log.debug("手动提取成功");
                return questions;
            }
        } catch (Exception e) {
            log.debug("手动提取失败: {}", e.getMessage());
        }
        
        return questions;
    }
    
    /**
     * 手动提取题目信息
     */
    private List<QuestionDTO> manuallyExtractQuestions(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            // 查找所有可能的题目内容
            Pattern contentPattern = Pattern.compile("\"content\"\\s*:\\s*\"([^\"]+)\"");
            Pattern answerPattern = Pattern.compile("\"correctAnswer\"\\s*:\\s*\"([^\"]+)\"");
            Pattern analysisPattern = Pattern.compile("\"answerAnalysis\"\\s*:\\s*\"([^\"]+)\"");
            Pattern difficultyPattern = Pattern.compile("\"difficulty\"\\s*:\\s*(\\d+)");
            
            Matcher contentMatcher = contentPattern.matcher(response);
            Matcher answerMatcher = answerPattern.matcher(response);
            Matcher analysisMatcher = analysisPattern.matcher(response);
            Matcher difficultyMatcher = difficultyPattern.matcher(response);
            
            while (contentMatcher.find() && answerMatcher.find()) {
                QuestionDTO question = new QuestionDTO();
                question.setQuestionType("PROGRAMMING");
                question.setContent(contentMatcher.group(1));
                question.setCorrectAnswer(answerMatcher.group(1).replaceAll("\\\\n", "\n"));
                
                if (analysisMatcher.find()) {
                    question.setAnswerAnalysis(analysisMatcher.group(1));
                } else {
                    question.setAnswerAnalysis("请根据题目要求实现相应功能");
                }
                
                if (difficultyMatcher.find()) {
                    question.setDifficulty(Integer.parseInt(difficultyMatcher.group(1)));
                } else {
                    question.setDifficulty(1);
                }
                
                question.setScore(getDefaultScoreByType("PROGRAMMING", question.getDifficulty()));
                questions.add(question);
            }
            
        } catch (Exception e) {
            log.error("手动提取题目失败: {}", e.getMessage());
        }
        
        return questions;
    }

    /**
     * 检查是否是重复题目
     */
    private boolean isDuplicateQuestion(List<QuestionDTO> existingQuestions, QuestionDTO newQuestion) {
        for (QuestionDTO existing : existingQuestions) {
            if (existing.getContent() != null && newQuestion.getContent() != null) {
                // 简单的相似度检查
                String existingContent = existing.getContent().toLowerCase().replaceAll("\\s+", " ");
                String newContent = newQuestion.getContent().toLowerCase().replaceAll("\\s+", " ");
                
                if (existingContent.equals(newContent) || 
                    calculateSimilarity(existingContent, newContent) > 0.8) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 计算字符串相似度
     */
    private double calculateSimilarity(String s1, String s2) {
        if (s1 == null || s2 == null) return 0.0;
        if (s1.equals(s2)) return 1.0;
        
        String[] words1 = s1.split("\\s+");
        String[] words2 = s2.split("\\s+");
        
        int commonWords = 0;
        for (String word1 : words1) {
            for (String word2 : words2) {
                if (word1.equals(word2) && word1.length() > 2) {
                    commonWords++;
                    break;
                }
            }
        }
        
        return (double) commonWords / Math.max(words1.length, words2.length);
    }

    /**
     * 生成补充编程题 - 基于教学内容生成相关题目
     */
    private List<QuestionDTO> generateSupplementaryProgrammingQuestions(String teachingContent, Integer difficulty, int count) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            log.info("基于教学内容生成{}道补充编程题", count);
            
            // 根据教学内容生成相关的编程题模板
            List<ProgrammingTemplate> templates = generateContextualTemplates(teachingContent, difficulty);
            
            for (int i = 0; i < Math.min(count, templates.size()); i++) {
                ProgrammingTemplate template = templates.get(i);
                
                QuestionDTO question = new QuestionDTO();
                question.setQuestionType("PROGRAMMING");
                question.setContent(template.content);
                question.setCorrectAnswer(template.code);
                question.setAnswerAnalysis(template.analysis);
                question.setDifficulty(difficulty);
                question.setScore(getDefaultScoreByType("PROGRAMMING", difficulty));
                
                questions.add(question);
            }
            
            // 如果模板不够，生成通用但相关的题目
            while (questions.size() < count) {
                QuestionDTO question = generateGenericProgrammingQuestion(teachingContent, difficulty);
                questions.add(question);
            }
            
            log.info("生成了{}道补充编程题", questions.size());
            
        } catch (Exception e) {
            log.error("生成补充编程题失败: {}", e.getMessage());
        }
        
        return questions;
    }
    
    /**
     * 编程题模板类
     */
    private static class ProgrammingTemplate {
        String content;
        String code;
        String analysis;
        
        ProgrammingTemplate(String content, String code, String analysis) {
            this.content = content;
            this.code = code;
            this.analysis = analysis;
        }
    }
    
    /**
     * 根据教学内容生成相关的编程题模板
     */
    private List<ProgrammingTemplate> generateContextualTemplates(String teachingContent, Integer difficulty) {
        List<ProgrammingTemplate> templates = new ArrayList<>();
        String content = teachingContent.toLowerCase();
        
        // 数据结构相关模板
        if (content.contains("数组") || content.contains("array")) {
            templates.add(new ProgrammingTemplate(
                "实现一个函数，在数组中查找指定元素的位置",
                "def find_element(arr, target):\n    for i in range(len(arr)):\n        if arr[i] == target:\n            return i\n    return -1",
                "线性查找算法，时间复杂度O(n)，适用于未排序数组。"
            ));
        }
        
        if (content.contains("链表") || content.contains("linked list")) {
            templates.add(new ProgrammingTemplate(
                "实现一个简单的单链表节点类和插入操作",
                "class ListNode:\n    def __init__(self, val=0):\n        self.val = val\n        self.next = None\n\ndef insert_node(head, val):\n    new_node = ListNode(val)\n    new_node.next = head\n    return new_node",
                "单链表的基本操作，头插法插入新节点，时间复杂度O(1)。"
            ));
        }
        
        if (content.contains("栈") || content.contains("stack")) {
            templates.add(new ProgrammingTemplate(
                "使用数组实现一个简单的栈数据结构",
                "class Stack:\n    def __init__(self):\n        self.items = []\n    \n    def push(self, item):\n        self.items.append(item)\n    \n    def pop(self):\n        return self.items.pop() if self.items else None",
                "栈的基本实现，支持push和pop操作，遵循LIFO原则。"
            ));
        }
        
        if (content.contains("队列") || content.contains("queue")) {
            templates.add(new ProgrammingTemplate(
                "实现一个简单的队列数据结构",
                "from collections import deque\n\nclass Queue:\n    def __init__(self):\n        self.items = deque()\n    \n    def enqueue(self, item):\n        self.items.append(item)\n    \n    def dequeue(self):\n        return self.items.popleft() if self.items else None",
                "队列的基本实现，支持入队和出队操作，遵循FIFO原则。"
            ));
        }
        
        // 算法相关模板
        if (content.contains("排序") || content.contains("sort")) {
            templates.add(new ProgrammingTemplate(
                "实现冒泡排序算法",
                "def bubble_sort(arr):\n    n = len(arr)\n    for i in range(n):\n        for j in range(0, n-i-1):\n            if arr[j] > arr[j+1]:\n                arr[j], arr[j+1] = arr[j+1], arr[j]\n    return arr",
                "冒泡排序算法，时间复杂度O(n²)，空间复杂度O(1)，是稳定排序算法。"
            ));
        }
        
        if (content.contains("查找") || content.contains("search")) {
            templates.add(new ProgrammingTemplate(
                "实现二分查找算法",
                "def binary_search(arr, target):\n    left, right = 0, len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    return -1",
                "二分查找算法，时间复杂度O(log n)，要求数组已排序。"
            ));
        }
        
        // 面向对象相关模板
        if (content.contains("类") || content.contains("class") || content.contains("对象")) {
            templates.add(new ProgrammingTemplate(
                "设计一个学生类，包含基本属性和方法",
                "class Student:\n    def __init__(self, name, age, student_id):\n        self.name = name\n        self.age = age\n        self.student_id = student_id\n        self.grades = []\n    \n    def add_grade(self, grade):\n        self.grades.append(grade)\n    \n    def get_average(self):\n        return sum(self.grades) / len(self.grades) if self.grades else 0",
                "面向对象编程基础，类的定义、构造函数、属性和方法的实现。"
            ));
        }
        
        return templates;
    }
    
    /**
     * 生成通用但与教学内容相关的编程题
     */
    private QuestionDTO generateGenericProgrammingQuestion(String teachingContent, Integer difficulty) {
        QuestionDTO question = new QuestionDTO();
        question.setQuestionType("PROGRAMMING");
        question.setDifficulty(difficulty);
        question.setScore(getDefaultScoreByType("PROGRAMMING", difficulty));
        
        // 根据教学内容的关键词生成相关题目
        String subject = getSubjectFromContent(teachingContent);
        
        question.setContent(String.format("编写一个与%s相关的程序，实现基本功能", subject));
        question.setCorrectAnswer(String.format(
            "# %s相关程序实现\ndef solve_problem():\n    \"\"\"\n    根据具体需求实现相关功能\n    \"\"\"\n    # TODO: 实现具体逻辑\n    pass\n\n# 示例调用\nif __name__ == '__main__':\n    solve_problem()",
            subject
        ));
        question.setAnswerAnalysis(String.format("这是一道与%s相关的编程题，请根据具体需求实现相应的功能。注意代码的可读性和效率。", subject));
        
        return question;
    }

    /**
     * 生成非编程题 - 使用JSON格式，带数量保证和重试机制
     */
    private List<QuestionDTO> generateNonProgrammingQuestions(String teachingContent, String questionType, Integer difficulty, Integer count) {
        List<QuestionDTO> allQuestions = new ArrayList<>();
        int maxRetries = 3;
        int currentRetry = 0;
        
        while (allQuestions.size() < count && currentRetry < maxRetries) {
            try {
                int remainingCount = count - allQuestions.size();
                
                String promptTemplate = getPromptTemplateForType(questionType);
                String specificOptions = getOptionsForType(questionType);
                String typeInstructions = getInstructionsForType(questionType);

                String prompt = String.format(promptTemplate,
                        remainingCount, teachingContent, questionType, difficulty, remainingCount,
                        questionType, questionType, specificOptions, difficulty,
                        typeInstructions, remainingCount, questionType, questionType
                );

                String response = callOpenRouter(prompt);
                response = cleanJsonResponse(response);
                
                if (response.contains("[") && response.contains("]")) {
                    response = response.substring(response.indexOf("["), response.lastIndexOf("]") + 1);
                    response = response.replaceAll("\\r\\n|\\r|\\n", " ")
                            .replaceAll("\\s+", " ")
                            .replaceAll("'", "\"")
                            .replaceAll("(?<!\")(\\w+)(?=:)", "\"$1\"");

                    log.debug("第{}次尝试，清理的 JSON 响应: {}", currentRetry + 1, response);
                    List<QuestionDTO> questions = objectMapper.readValue(response, new TypeReference<List<QuestionDTO>>() {});
                    List<QuestionDTO> validQuestions = validateQuestionTypes(questions, questionType);

                    // 为题目设置默认分数（如果没有设置的话）
                    for (QuestionDTO question : validQuestions) {
                        if (question.getScore() == null || question.getScore() == 0.0) {
                            question.setScore(getDefaultScoreByType(questionType, question.getDifficulty()));
                        }
                    }

                    // 去重并添加新题目
                    for (QuestionDTO newQuestion : validQuestions) {
                        if (!isDuplicateQuestion(allQuestions, newQuestion)) {
                            allQuestions.add(newQuestion);
                            if (allQuestions.size() >= count) {
                                break;
                            }
                        }
                    }
                    
                    log.info("第{}次尝试后，已生成{}题数量: {}/{}", currentRetry + 1, questionType, allQuestions.size(), count);
                    
                    if (allQuestions.size() >= count) {
                        break;
                    }
                } else {
                    log.warn("第{}次尝试，无法提取有效JSON: {}", currentRetry + 1, response);
                }
                
                currentRetry++;
                
            } catch (JsonProcessingException e) {
                log.error("第{}次尝试，JSON解析失败: {}", currentRetry + 1, e.getMessage());
                currentRetry++;
            } catch (Exception e) {
                log.error("第{}次尝试，生成题目异常: {}", currentRetry + 1, e.getMessage());
                currentRetry++;
            }
        }
        
        // 如果还是不够，尝试生成补充题目
        if (allQuestions.size() < count) {
            List<QuestionDTO> supplementQuestions = generateSupplementaryQuestions(
                    teachingContent, questionType, difficulty, count - allQuestions.size()
            );
            allQuestions.addAll(supplementQuestions);
        }
        
        // 确保不超过请求数量
        if (allQuestions.size() > count) {
            allQuestions = allQuestions.subList(0, count);
        }
        
        log.info("最终生成{}题数量: {}/{}", questionType, allQuestions.size(), count);
        return allQuestions;
    }

    /**
     * 构建编程题提示词 - 使用分隔符格式
     */
    private String buildProgrammingPrompt(String teachingContent, Integer difficulty, Integer count) {
        // 分析教学内容，提取关键编程概念
        String programmingContext = extractProgrammingContext(teachingContent);
        String difficultyDescription = getDifficultyDescription(difficulty);
        String languageHint = suggestProgrammingLanguage(teachingContent);
        
        return String.format("""
            请生成%d道编程题，要求与"%s"相关。
            
            重点：%s
            难度：%s
            语言：%s
            
            严格按照以下JSON格式输出，代码必须在一行内：
            
            [
                {
                    "questionType": "PROGRAMMING",
                    "content": "题目描述",
                    "correctAnswer": "def function(): return result",
                    "answerAnalysis": "解题思路",
                    "difficulty": %d
                }
            ]
            
            要求：
            1. 与"%s"紧密相关
            2. 代码答案必须是单行或用\\n连接的字符串
            3. 不要使用三引号或多行字符串
            4. 确保JSON格式正确
            5. 生成恰好%d道题目
            
            示例正确格式：
            [
                {
                    "questionType": "PROGRAMMING",
                    "content": "编写函数计算数组和",
                    "correctAnswer": "def sum_array(arr):\\n    return sum(arr)",
                    "answerAnalysis": "使用内置sum函数计算数组元素总和",
                    "difficulty": %d
                }
            ]
            """, 
            count, teachingContent, programmingContext, difficultyDescription, languageHint,
            difficulty, teachingContent, count, difficulty);
    }
    
    /**
     * 从教学内容中提取编程相关的上下文
     */
    private String extractProgrammingContext(String teachingContent) {
        if (teachingContent == null || teachingContent.trim().isEmpty()) {
            return "基础编程概念和算法实现";
        }
        
        String content = teachingContent.toLowerCase();
        StringBuilder context = new StringBuilder();
        
        // 检测数据结构相关内容
        if (content.contains("数组") || content.contains("array")) {
            context.append("数组操作和遍历、");
        }
        if (content.contains("链表") || content.contains("linked list")) {
            context.append("链表的创建、插入、删除操作、");
        }
        if (content.contains("栈") || content.contains("stack")) {
            context.append("栈的实现和应用、");
        }
        if (content.contains("队列") || content.contains("queue")) {
            context.append("队列的实现和应用、");
        }
        if (content.contains("树") || content.contains("tree")) {
            context.append("二叉树的遍历和操作、");
        }
        if (content.contains("图") || content.contains("graph")) {
            context.append("图的表示和遍历算法、");
        }
        
        // 检测算法相关内容
        if (content.contains("排序") || content.contains("sort")) {
            context.append("排序算法的实现、");
        }
        if (content.contains("查找") || content.contains("search")) {
            context.append("查找算法的实现、");
        }
        if (content.contains("递归") || content.contains("recursion")) {
            context.append("递归算法的设计、");
        }
        if (content.contains("动态规划") || content.contains("dynamic programming")) {
            context.append("动态规划问题的解决、");
        }
        
        // 检测面向对象相关内容
        if (content.contains("类") || content.contains("class") || content.contains("对象")) {
            context.append("类的设计和对象的使用、");
        }
        if (content.contains("继承") || content.contains("inheritance")) {
            context.append("继承关系的实现、");
        }
        if (content.contains("多态") || content.contains("polymorphism")) {
            context.append("多态性的应用、");
        }
        
        String result = context.toString();
        if (result.isEmpty()) {
            return "基础编程逻辑和算法思维";
        }
        
        // 移除最后的逗号
        if (result.endsWith("、")) {
            result = result.substring(0, result.length() - 1);
        }
        
        return result;
    }
    
    /**
     * 获取难度描述
     */
    private String getDifficultyDescription(Integer difficulty) {
        return switch (difficulty) {
            case 1 -> "简单（基础语法和简单逻辑，适合初学者）";
            case 2 -> "中等（需要一定算法思维和数据结构知识）";
            case 3 -> "困难（复杂算法实现，需要深入理解和优化）";
            default -> "中等";
        };
    }
    
    /**
     * 根据教学内容推荐编程语言
     */
    private String suggestProgrammingLanguage(String teachingContent) {
        if (teachingContent == null) {
            return "Python（推荐，语法简洁）";
        }
        
        String content = teachingContent.toLowerCase();
        
        if (content.contains("java") || content.contains("面向对象")) {
            return "Java（面向对象特性丰富）";
        } else if (content.contains("python") || content.contains("数据分析") || content.contains("机器学习")) {
            return "Python（语法简洁，适合算法实现）";
        } else if (content.contains("c++") || content.contains("性能") || content.contains("算法竞赛")) {
            return "C++（高性能，适合复杂算法）";
        } else if (content.contains("javascript") || content.contains("web") || content.contains("前端")) {
            return "JavaScript（适合Web开发相关题目）";
        } else {
            return "Python（推荐，语法简洁易懂）";
        }
    }

    /**
     * 解析编程题响应 - 优先使用JSON格式，增强容错能力
     */
    private List<QuestionDTO> parseProgrammingResponse(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            log.debug("开始解析编程题响应，原始长度: {}", response.length());
            
            // 首先尝试JSON格式解析
            questions = tryJsonParsing(response);
            
            // 如果JSON解析失败，尝试传统的分隔符格式
            if (questions.isEmpty()) {
                log.debug("JSON解析失败，尝试分隔符格式解析");
                questions = tryMultipleParseMethods(response);
            }
            
            log.info("成功解析编程题数量: {}", questions.size());
            return questions;
            
        } catch (Exception e) {
            log.error("解析编程题响应失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
    
    /**
     * 尝试JSON格式解析编程题 - 增强版本
     */
    private List<QuestionDTO> tryJsonParsing(String response) {
        try {
            // 首先尝试提取和清理JSON
            String jsonPart = extractJsonFromResponse(response);
            if (jsonPart == null) {
                return Collections.emptyList();
            }
            
            log.debug("原始JSON部分: {}", jsonPart);
            
            // 专门处理编程题的JSON清理
            String cleanedJson = cleanProgrammingJson(jsonPart);
            
            log.debug("清理后的编程题JSON: {}", cleanedJson);
            
            List<QuestionDTO> questions = objectMapper.readValue(
                cleanedJson, 
                new TypeReference<List<QuestionDTO>>() {}
            );
            
            // 验证和设置编程题特有属性
            List<QuestionDTO> validQuestions = new ArrayList<>();
            for (QuestionDTO question : questions) {
                if (isValidProgrammingQuestion(question)) {
                    // 确保题目类型正确
                    question.setQuestionType("PROGRAMMING");
                    // 设置默认分数
                    if (question.getScore() == null || question.getScore() == 0.0) {
                        question.setScore(getDefaultScoreByType("PROGRAMMING", question.getDifficulty()));
                    }
                    validQuestions.add(question);
                }
            }
            
            log.info("JSON格式解析编程题成功，有效题目数量: {}", validQuestions.size());
            return validQuestions;
            
        } catch (Exception e) {
            log.debug("JSON格式解析编程题失败: {}", e.getMessage());
        }
        
        return Collections.emptyList();
    }
    
    /**
     * 从响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }
        
        // 查找JSON数组的开始和结束
        int start = response.indexOf("[");
        int end = response.lastIndexOf("]");
        
        if (start != -1 && end != -1 && end > start) {
            return response.substring(start, end + 1);
        }
        
        return null;
    }
    
    /**
     * 专门清理编程题的JSON格式
     */
    private String cleanProgrammingJson(String json) {
        try {
            log.debug("开始处理编程题JSON");
            
            // 第一步：处理多行代码字符串
            json = handleMultilineCodeStrings(json);
            
            // 第二步：修复常见的JSON格式问题
            json = fixCommonJsonIssues(json);
            
            // 第三步：处理编程题特有的问题
            json = handleProgrammingSpecificIssues(json);
            
            return json;
            
        } catch (Exception e) {
            log.error("清理编程题JSON失败: {}", e.getMessage());
            throw e;
        }
    }
    
    /**
     * 处理多行代码字符串
     */
    private String handleMultilineCodeStrings(String json) {
        // 处理三引号字符串
        json = json.replaceAll("\"\"\"([^\"]*?)\"\"\"", "\"$1\"");
        
        // 处理代码中的换行符
        json = json.replaceAll("\\\\n", "\\n");
        json = json.replaceAll("\\n", "\\\\n");
        
        // 处理代码中的制表符
        json = json.replaceAll("\\t", "\\\\t");
        
        return json;
    }
    
    /**
     * 修复常见的JSON格式问题
     */
    private String fixCommonJsonIssues(String json) {
        // 修复单引号为双引号
        json = json.replaceAll("'", "\"");
        
        // 修复字段名没有引号的问题
        json = json.replaceAll("(?<!\")(\\w+)(?=\\s*:)", "\"$1\"");
        
        // 修复多余的逗号
        json = json.replaceAll(",\\s*}", "}");
        json = json.replaceAll(",\\s*]", "]");
        
        // 修复缺少逗号的问题
        json = json.replaceAll("}\\s*{", "},{");
        
        return json;
    }
    
    /**
     * 处理编程题特有的问题
     */
    private String handleProgrammingSpecificIssues(String json) {
        try {
            // 使用正则表达式修复correctAnswer字段中的代码问题
            json = fixCorrectAnswerField(json);
            
            // 处理代码字符串中的特殊字符
            json = json.replaceAll("\\\\\"", "\\\"");
            
            // 修复未闭合的字符串
            json = fixUnclosedStrings(json);
            
            return json;
            
        } catch (Exception e) {
            log.error("处理编程题特有问题时出错: {}", e.getMessage());
            return json;
        }
    }
    
    /**
     * 修复correctAnswer字段中的代码问题
     */
    private String fixCorrectAnswerField(String json) {
        // 查找所有correctAnswer字段并修复其中的代码
        Pattern pattern = Pattern.compile("\"correctAnswer\"\\s*:\\s*\"([^\"]*?)\"", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(json);
        
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String codeContent = matcher.group(1);
            // 清理代码内容
            codeContent = cleanCodeContent(codeContent);
            matcher.appendReplacement(sb, "\"correctAnswer\": \"" + codeContent + "\"");
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    

    /**
     * 修复未闭合的字符串
     */
    private String fixUnclosedStrings(String json) {
        // 简单的修复策略：确保每个字段都有正确的引号
        json = json.replaceAll("\"\\s*:\\s*([^\"\\{\\[\\d][^,\\}\\]]*?)([,\\}\\]])", "\": \"$1\"$2");
        
        return json;
    }
    
    /**
     * 验证编程题是否有效
     */
    private boolean isValidProgrammingQuestion(QuestionDTO question) {
        if (question == null) return false;
        
        // 检查基本字段
        if (question.getContent() == null || question.getContent().trim().isEmpty()) {
            return false;
        }
        
        if (question.getCorrectAnswer() == null || question.getCorrectAnswer().trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含代码特征
        String answer = question.getCorrectAnswer().toLowerCase();
        boolean hasCodeFeatures = answer.contains("public") || answer.contains("class") || 
                                 answer.contains("function") || answer.contains("def") || 
                                 answer.contains("var") || answer.contains("let") ||
                                 answer.contains("{") || answer.contains("}") || 
                                 answer.contains("//") || answer.contains("#") ||
                                 answer.contains("import") || answer.contains("include") ||
                                 answer.contains("return") || answer.contains("if") ||
                                 answer.contains("for") || answer.contains("while");
        
        if (!hasCodeFeatures) {
            log.debug("编程题答案缺少代码特征: {}", question.getCorrectAnswer().substring(0, Math.min(100, question.getCorrectAnswer().length())));
            return false;
        }
        
        return true;
    }

    /**
     * 尝试多种解析方法
     */
    private List<QuestionDTO> tryMultipleParseMethods(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        // 方法1：标准分隔符解析
        questions = parseWithStandardSeparator(response);
        if (!questions.isEmpty()) {
            log.debug("标准分隔符解析成功，题目数量: {}", questions.size());
            return questions;
        }
        
        // 方法2：QUESTION_START/END 标记解析
        questions = parseWithQuestionMarkers(response);
        if (!questions.isEmpty()) {
            log.debug("问题标记解析成功，题目数量: {}", questions.size());
            return questions;
        }
        
        // 方法3：智能模式解析（基于关键字识别）
        questions = parseWithIntelligentMode(response);
        if (!questions.isEmpty()) {
            log.debug("智能模式解析成功，题目数量: {}", questions.size());
            return questions;
        }
        
        log.warn("所有解析方法都失败了");
        return Collections.emptyList();
    }

    /**
     * 标准分隔符解析
     */
    private List<QuestionDTO> parseWithStandardSeparator(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            String[] questionBlocks = response.split("===QUESTION_SEPARATOR===");
            
            for (String block : questionBlocks) {
                block = block.trim();
                if (block.isEmpty()) continue;
                
                QuestionDTO question = parseSingleProgrammingQuestion(block);
                if (question != null) {
                    questions.add(question);
                }
            }
            
            return questions;
            
        } catch (Exception e) {
            log.debug("标准分隔符解析失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 基于QUESTION_START/END标记解析
     */
    private List<QuestionDTO> parseWithQuestionMarkers(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            // 使用正则表达式找到所有QUESTION_START到QUESTION_END的块
            Pattern questionPattern = Pattern.compile(
                "QUESTION_START([\\s\\S]*?)QUESTION_END", 
                Pattern.CASE_INSENSITIVE
            );
            
            Matcher matcher = questionPattern.matcher(response);
            
            while (matcher.find()) {
                String questionBlock = matcher.group(1).trim();
                QuestionDTO question = parseSingleProgrammingQuestion("QUESTION_START\n" + questionBlock + "\nQUESTION_END");
                if (question != null) {
                    questions.add(question);
                }
            }
            
            return questions;
            
        } catch (Exception e) {
            log.debug("问题标记解析失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 智能模式解析 - 基于关键字识别
     */
    private List<QuestionDTO> parseWithIntelligentMode(String response) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            // 查找所有可能的题目内容
            List<String> contentBlocks = findContentBlocks(response);
            List<String> codeBlocks = findCodeBlocks(response);
            List<String> analysisBlocks = findAnalysisBlocks(response);
            
            // 尝试匹配内容、代码和分析
            int maxQuestions = Math.min(Math.min(contentBlocks.size(), codeBlocks.size()), analysisBlocks.size());
            
            for (int i = 0; i < maxQuestions; i++) {
                QuestionDTO question = new QuestionDTO();
                question.setQuestionType("PROGRAMMING");
                question.setContent(contentBlocks.get(i));
                question.setCorrectAnswer(codeBlocks.get(i));
                question.setAnswerAnalysis(analysisBlocks.get(i));
                question.setDifficulty(1);
                // 设置默认分数
                question.setScore(getDefaultScoreByType("PROGRAMMING", 1));
                
                questions.add(question);
            }
            
            return questions;
            
        } catch (Exception e) {
            log.debug("智能模式解析失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 查找内容块
     */
    private List<String> findContentBlocks(String response) {
        List<String> blocks = new ArrayList<>();
        
        // 查找CONTENT:后面的内容
        Pattern pattern = Pattern.compile("CONTENT:\\s*([^\\n]+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(response);
        
        while (matcher.find()) {
            blocks.add(matcher.group(1).trim());
        }
        
        return blocks;
    }

    /**
     * 查找代码块
     */
    private List<String> findCodeBlocks(String response) {
        List<String> blocks = new ArrayList<>();
        
        // 查找CODE_START到CODE_END之间的内容
        Pattern pattern = Pattern.compile("CODE_START([\\s\\S]*?)CODE_END", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(response);
        
        while (matcher.find()) {
            blocks.add(matcher.group(1).trim());
        }
        
        // 如果没有找到，尝试查找代码块标记
        if (blocks.isEmpty()) {
            Pattern codePattern = Pattern.compile("```[\\w]*\\n([\\s\\S]*?)```");
            Matcher codeMatcher = codePattern.matcher(response);
            
            while (codeMatcher.find()) {
                blocks.add(codeMatcher.group(1).trim());
            }
        }
        
        return blocks;
    }

    /**
     * 查找分析块
     */
    private List<String> findAnalysisBlocks(String response) {
        List<String> blocks = new ArrayList<>();
        
        // 查找ANALYSIS:后面的内容
        Pattern pattern = Pattern.compile("ANALYSIS:\\s*([^\\n]+(?:\\n[^A-Z][^\\n]*)*)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(response);
        
        while (matcher.find()) {
            blocks.add(matcher.group(1).trim());
        }
        
        return blocks;
    }

    /**
     * 解析单个编程题
     */
    private QuestionDTO parseSingleProgrammingQuestion(String questionBlock) {
        try {
            QuestionDTO question = new QuestionDTO();
            question.setQuestionType("PROGRAMMING");
            
            // 提取题目内容
            String content = extractField(questionBlock, "CONTENT:", "CODE_START");
            if (content != null) {
                question.setContent(content.trim());
            }
            
            // 提取代码
            String code = extractField(questionBlock, "CODE_START", "CODE_END");
            if (code != null) {
                question.setCorrectAnswer(code.trim());
            }
            
            // 提取解析
            String analysis = extractField(questionBlock, "ANALYSIS:", "DIFFICULTY:");
            if (analysis != null) {
                question.setAnswerAnalysis(analysis.trim());
            }
            
            // 提取难度
            String difficultyStr = extractField(questionBlock, "DIFFICULTY:", "QUESTION_END");
            if (difficultyStr != null) {
                try {
                    question.setDifficulty(Integer.parseInt(difficultyStr.trim()));
                } catch (NumberFormatException e) {
                    question.setDifficulty(1);
                }
            }
            
            // 设置默认分数 - 根据题型和难度
            question.setScore(getDefaultScoreByType("PROGRAMMING", question.getDifficulty()));
            
            // 验证必要字段
            if (question.getContent() != null && question.getCorrectAnswer() != null) {
                return question;
            } else {
                log.warn("编程题解析不完整，跳过: content={}, code={}", 
                    question.getContent(), question.getCorrectAnswer());
                return null;
            }
            
        } catch (Exception e) {
            log.error("解析单个编程题失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 生成补充题目（当AI生成数量不足时使用）
     */
    private List<QuestionDTO> generateSupplementaryQuestions(String teachingContent, String questionType, Integer difficulty, int count) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        try {
            log.info("生成{}道补充{}题", count, questionType);
            
            // 根据题型生成简单的模板题目
            for (int i = 0; i < count; i++) {
                QuestionDTO question = new QuestionDTO();
                question.setQuestionType(questionType);
                question.setDifficulty(difficulty != null ? difficulty : 1);
                question.setScore(getDefaultScoreByType(questionType, question.getDifficulty()));
                
                switch (questionType) {
                    case "SINGLE_CHOICE":
                        question.setContent(String.format("关于%s的基础知识，以下说法正确的是？", getSubjectFromContent(teachingContent)));
                        question.setCorrectAnswer("A");
                        question.setAnswerAnalysis("这是一道基础选择题，请根据教学内容选择正确答案。");
                        break;
                        
                    case "MULTIPLE_CHOICE":
                        question.setContent(String.format("关于%s，以下哪些说法是正确的？（多选）", getSubjectFromContent(teachingContent)));
                        question.setCorrectAnswer("AB");
                        question.setAnswerAnalysis("这是一道多选题，请仔细分析各个选项。");
                        break;
                        
                    case "FILL_BLANK":
                        question.setContent(String.format("在%s中，______是一个重要概念。", getSubjectFromContent(teachingContent)));
                        question.setCorrectAnswer("请根据教学内容填写");
                        question.setAnswerAnalysis("请根据教学内容填写正确答案。");
                        break;
                        
                    case "SHORT_ANSWER":
                        question.setContent(String.format("请简述%s的基本概念和特点。", getSubjectFromContent(teachingContent)));
                        question.setCorrectAnswer("请根据教学内容回答");
                        question.setAnswerAnalysis("这是一道简答题，请结合教学内容进行回答。");
                        break;
                        
                    case "CASE_ANALYSIS":
                        question.setContent(String.format("请分析一个关于%s的实际应用案例。", getSubjectFromContent(teachingContent)));
                        question.setCorrectAnswer("请根据教学内容分析");
                        question.setAnswerAnalysis("这是一道案例分析题，请结合实际情况进行分析。");
                        break;
                        
                    default:
                        question.setContent("请根据教学内容回答相关问题。");
                        question.setCorrectAnswer("请根据教学内容回答");
                        question.setAnswerAnalysis("请仔细阅读教学内容后回答。");
                        break;
                }
                
                questions.add(question);
            }
            
            log.info("生成了{}道补充{}题", questions.size(), questionType);
            
        } catch (Exception e) {
            log.error("生成补充题目失败: {}", e.getMessage());
        }
        
        return questions;
    }

    /**
     * 从教学内容中提取主题
     */
    private String getSubjectFromContent(String teachingContent) {
        if (teachingContent == null || teachingContent.trim().isEmpty()) {
            return "相关知识";
        }
        
        // 简单提取前20个字符作为主题
        String subject = teachingContent.trim();
        if (subject.length() > 20) {
            subject = subject.substring(0, 20) + "...";
        }
        
        return subject;
    }

    /**
     * 根据题型和难度获取默认分数
     */
    private Double getDefaultScoreByType(String questionType, Integer difficulty) {
        if (difficulty == null) difficulty = 1;
        
        switch (questionType) {
            case "SINGLE_CHOICE":
                return 5.0;
            case "MULTIPLE_CHOICE":
                return difficulty >= 3 ? 10.0 : 5.0;
            case "FILL_BLANK":
                return 5.0;
            case "SHORT_ANSWER":
                return 15.0;
            case "PROGRAMMING":
                // 编程题根据难度设置分数
                switch (difficulty) {
                    case 1: return 15.0;  // 简单
                    case 2: return 20.0;  // 中等
                    case 3: return 30.0;  // 困难
                    default: return 20.0;
                }
            case "CASE_ANALYSIS":
                return 20.0;
            default:
                return 10.0;
        }
    }

    /**
     * 从文本块中提取指定字段
     */
    private String extractField(String text, String startMarker, String endMarker) {
        try {
            int startIndex = text.indexOf(startMarker);
            if (startIndex == -1) return null;
            
            startIndex += startMarker.length();
            
            int endIndex = text.indexOf(endMarker, startIndex);
            if (endIndex == -1) {
                // 如果没有结束标记，取到文本末尾
                return text.substring(startIndex);
            }
            
            return text.substring(startIndex, endIndex);
            
        } catch (Exception e) {
            log.error("提取字段失败: startMarker={}, endMarker={}", startMarker, endMarker);
            return null;
        }
    }

    /**
     * 验证题目类型一致性，过滤掉不符合要求的题目，并进行更严格的验证
     */
    private List<QuestionDTO> validateQuestionTypes(List<QuestionDTO> questions, String expectedType) {
        List<QuestionDTO> validQuestions = new ArrayList<>();

        for (QuestionDTO question : questions) {
            if (isValidQuestion(question, expectedType)) {
                validQuestions.add(question);
            } else {
                log.warn("题目验证失败 - 期望类型: {}, 实际类型: {}, 内容: {}", 
                        expectedType, question.getQuestionType(), 
                        question.getContent() != null ? question.getContent().substring(0, Math.min(50, question.getContent().length())) : "null");
            }
        }

        log.info("题目类型验证完成，原始数量: {}, 有效数量: {}, 期望类型: {}",
                questions.size(), validQuestions.size(), expectedType);

        return validQuestions;
    }

    /**
     * 验证单个题目是否有效
     */
    private boolean isValidQuestion(QuestionDTO question, String expectedType) {
        // 检查基本字段
        if (question == null) {
            return false;
        }
        
        // 检查题目类型
        if (question.getQuestionType() == null || !question.getQuestionType().equals(expectedType)) {
            return false;
        }
        
        // 检查题目内容
        if (question.getContent() == null || question.getContent().trim().isEmpty()) {
            return false;
        }
        
        // 检查正确答案
        if (question.getCorrectAnswer() == null || question.getCorrectAnswer().trim().isEmpty()) {
            return false;
        }
        
        // 根据题型进行特殊验证
        switch (expectedType) {
            case "SINGLE_CHOICE":
            case "MULTIPLE_CHOICE":
                // 选择题应该有选项
                return question.getOptions() != null && !question.getOptions().isEmpty();
                
            case "PROGRAMMING":
                // 编程题的答案应该包含代码特征
                String answer = question.getCorrectAnswer().toLowerCase();
                return answer.contains("public") || answer.contains("class") || answer.contains("function") || 
                       answer.contains("def") || answer.contains("var") || answer.contains("let") ||
                       answer.contains("{") || answer.contains("}") || answer.contains("//");
                
            default:
                return true;
        }
    }

    /**
     * 专门处理编程题的代码内容 - 高级版本
     */
    private String handleProgrammingQuestion(String json) {
        try {
            log.debug("开始处理编程题JSON");
            
            // 使用更精确的正则表达式匹配编程题对象
            Pattern programmingPattern = Pattern.compile(
                "\\{[^{}]*\"questionType\":\\s*\"PROGRAMMING\"[^{}]*\\}",
                Pattern.DOTALL
            );
            
            Matcher matcher = programmingPattern.matcher(json);
            StringBuffer result = new StringBuffer();
            
            while (matcher.find()) {
                String programmingObject = matcher.group();
                String processedObject = processProgrammingObject(programmingObject);
                matcher.appendReplacement(result, Matcher.quoteReplacement(processedObject));
            }
            matcher.appendTail(result);
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("处理编程题内容时出错: {}", e.getMessage());
            return json;
        }
    }

    /**
     * 处理单个编程题对象
     */
    private String processProgrammingObject(String programmingObject) {
        try {
            // 分别处理 correctAnswer 和 answerAnalysis 字段
            programmingObject = processCodeField(programmingObject, "correctAnswer");
            programmingObject = processCodeField(programmingObject, "answerAnalysis");
            
            return programmingObject;
            
        } catch (Exception e) {
            log.error("处理编程题对象时出错: {}", e.getMessage());
            return programmingObject;
        }
    }

    /**
     * 处理包含代码的字段
     */
    private String processCodeField(String json, String fieldName) {
        try {
            // 匹配字段值，支持跨行和复杂内容
            Pattern fieldPattern = Pattern.compile(
                "\"" + fieldName + "\"\\s*:\\s*\"([\\s\\S]*?)\"(?=\\s*[,}])",
                Pattern.DOTALL
            );
            
            Matcher matcher = fieldPattern.matcher(json);
            StringBuffer result = new StringBuffer();
            
            while (matcher.find()) {
                String fieldValue = matcher.group(1);
                String cleanedValue = cleanCodeContent(fieldValue);
                
                String replacement = "\"" + fieldName + "\": \"" + cleanedValue + "\"";
                matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
            }
            matcher.appendTail(result);
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("处理{}字段时出错: {}", fieldName, e.getMessage());
            return json;
        }
    }

    /**
     * 清理代码内容 - 使用先进的代码处理技术
     */
    private String cleanCodeContent(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "";
        }
        
        try {
            // 第一步：基础清理
            code = performBasicCodeCleaning(code);
            
            // 第二步：处理引号和转义字符
            code = handleQuotesInCode(code);
            
            // 第三步：处理代码结构
            code = normalizeCodeStructure(code);
            
            // 第四步：最终验证和清理
            code = finalCodeValidation(code);
            
            return code;
            
        } catch (Exception e) {
            log.error("清理代码内容时出错: {}", e.getMessage());
            // 返回安全的备用版本
            return code.replaceAll("\"", "'").replaceAll("\\\\", "\\\\\\\\");
        }
    }

    /**
     * 基础代码清理
     */
    private String performBasicCodeCleaning(String code) {
        return code
                // 移除代码块标记
                .replaceAll("```\\w*\\n?", "")
                .replaceAll("```", "")
                // 处理常见的转义序列
                .replace("\\\\n", "\\n")
                .replace("\\\\t", "\\t")
                .replace("\\\\r", "\\r")
                // 标准化换行符
                .replaceAll("\\\\n", "\n")
                .replaceAll("\\\\r\\\\n", "\n")
                .replaceAll("\\\\r", "\n")
                // 清理多余空白
                .replaceAll("[ \\t]+", " ")
                .trim();
    }

    /**
     * 处理代码中的引号 - 智能识别字符串和代码结构
     */
    private String handleQuotesInCode(String code) {
        try {
            StringBuilder result = new StringBuilder();
            boolean inString = false;
            char stringChar = '"';
            
            for (int i = 0; i < code.length(); i++) {
                char c = code.charAt(i);
                char prev = i > 0 ? code.charAt(i - 1) : '\0';
                
                if (!inString) {
                    // 不在字符串内
                    if (c == '"' || c == '\'') {
                        // 检查是否是字符串开始
                        if (isStringStart(code, i)) {
                            inString = true;
                            stringChar = c;
                            result.append('\''); // 统一使用单引号
                        } else {
                            // 可能是代码中的引号，需要转义或替换
                            result.append('\'');
                        }
                    } else {
                        result.append(c);
                    }
                } else {
                    // 在字符串内
                    if (c == stringChar && prev != '\\') {
                        // 字符串结束
                        inString = false;
                        result.append('\'');
                    } else if (c == '"' && stringChar != '"') {
                        // 字符串内的双引号，替换为单引号
                        result.append('\'');
                    } else {
                        result.append(c);
                    }
                }
            }
            
            return result.toString();
            
        } catch (Exception e) {
            log.error("处理代码引号时出错: {}", e.getMessage());
            // 简单替换作为备用方案
            return code.replaceAll("\"", "'");
        }
    }

    /**
     * 判断是否是字符串开始
     */
    private boolean isStringStart(String code, int position) {
        if (position == 0) return true;
        
        // 检查前面的字符，判断是否可能是字符串开始
        char prev = code.charAt(position - 1);
        return prev == '=' || prev == '(' || prev == ',' || prev == '[' || 
               prev == '{' || prev == ':' || prev == ' ' || prev == '\n' ||
               prev == '\t' || prev == '!';
    }

    /**
     * 标准化代码结构
     */
    private String normalizeCodeStructure(String code) {
        try {
            // 处理Python代码的特殊情况
            if (code.contains("def ") || code.contains("if ") || code.contains("for ") || code.contains("while ")) {
                code = normalizePythonCode(code);
            }
            
            // 处理Java代码
            if (code.contains("public ") || code.contains("private ") || code.contains("class ")) {
                code = normalizeJavaCode(code);
            }
            
            // 处理JavaScript代码
            if (code.contains("function ") || code.contains("const ") || code.contains("let ")) {
                code = normalizeJavaScriptCode(code);
            }
            
            return code;
            
        } catch (Exception e) {
            log.error("标准化代码结构时出错: {}", e.getMessage());
            return code;
        }
    }

    /**
     * 标准化Python代码
     */
    private String normalizePythonCode(String code) {
        return code
                // 处理Python字符串比较
                .replaceAll("==\\s*[\"']0[\"']", "== 0")
                .replaceAll("==\\s*[\"']target[\"']", "== target")
                .replaceAll("==\\s*[\"']([^\"']+)[\"']", "== '$1'")
                // 处理条件语句
                .replaceAll("if\\s+([^:]+)\\s*[\"']([^\"']*)[\"']\\s*:", "if $1 $2:")
                .replaceAll("else\\s*[\"']([^\"']*)[\"']\\s*:", "else:")
                // 标准化缩进
                .replaceAll("\\n\\s*", "\\n    ")
                // 处理函数定义
                .replaceAll("def\\s+([^(]+)\\(([^)]*)\\)\\s*:", "def $1($2):");
    }

    /**
     * 标准化Java代码
     */
    private String normalizeJavaCode(String code) {
        return code
                // 处理字符串比较
                .replaceAll("\\.equals\\([\"']([^\"']+)[\"']\\)", ".equals('$1')")
                // 处理System.out.println
                .replaceAll("System\\.out\\.println\\([\"']([^\"']+)[\"']\\)", "System.out.println('$1')")
                // 标准化大括号
                .replaceAll("\\{\\s*", "{ ")
                .replaceAll("\\s*\\}", " }");
    }

    /**
     * 标准化JavaScript代码
     */
    private String normalizeJavaScriptCode(String code) {
        return code
                // 处理console.log
                .replaceAll("console\\.log\\([\"']([^\"']+)[\"']\\)", "console.log('$1')")
                // 处理字符串比较
                .replaceAll("===\\s*[\"']([^\"']+)[\"']", "=== '$1'")
                .replaceAll("==\\s*[\"']([^\"']+)[\"']", "== '$1'")
                // 标准化函数定义
                .replaceAll("function\\s+([^(]+)\\(([^)]*)\\)\\s*\\{", "function $1($2) {");
    }

    /**
     * 最终代码验证和清理
     */
    private String finalCodeValidation(String code) {
        return code
                // 移除多余的转义字符
                .replaceAll("\\\\{2,}", "\\\\")
                // 标准化换行符为JSON安全格式
                .replaceAll("\\n", "\\\\n")
                .replaceAll("\\r", "\\\\r")
                .replaceAll("\\t", "\\\\t")
                // 确保没有未转义的双引号
                .replaceAll("(?<!\\\\)\"", "'")
                // 清理多余空格
                .replaceAll("\\s+", " ")
                .trim();
    }

    private String cleanJsonResponse(String response) {
        try {
            // 如果响应中包含了非JSON内容，只保留JSON部分
            int start = response.indexOf("[");
            int end = response.lastIndexOf("]") + 1;
            if (start >= 0 && end > start) {
                String jsonPart = response.substring(start, end);
                log.debug("原始JSON部分: {}", jsonPart);

                // 使用多层次清理策略
                jsonPart = performAdvancedJsonCleaning(jsonPart);

                // 验证并返回
                try {
                    objectMapper.readTree(jsonPart);
                    log.debug("JSON清理成功");
                    return jsonPart;
                } catch (JsonProcessingException e) {
                    log.debug("JSON验证失败，尝试智能修复: {}", e.getMessage());
                    return performIntelligentJsonRepair(jsonPart, e);
                }
            }
            return response;
        } catch (Exception e) {
            log.error("清理JSON响应时出错: {}", e.getMessage());
            return response;
        }
    }

    /**
     * 高级JSON清理 - 多层次处理策略
     */
    private String performAdvancedJsonCleaning(String json) {
        // 第一层：基础清理
        json = basicJsonCleaning(json);
        
        // 第二层：字符编码和引号处理
        json = handleCharacterEncoding(json);
        
        // 第三层：结构化字段处理
        json = processStructuredFields(json);
        
        // 第四层：格式标准化
        json = standardizeJsonFormat(json);
        
        return json;
    }

    /**
     * 基础JSON清理
     */
    private String basicJsonCleaning(String json) {
        return json
                .replaceAll("\\r\\n|\\r|\\n", " ")  // 移除换行符
                .replaceAll("\\s+", " ")           // 合并多余空格
                .replaceAll("\\t", " ")            // 替换制表符
                .trim();
    }

    /**
     * 字符编码和引号处理
     */
    private String handleCharacterEncoding(String json) {
        // 处理各种引号字符
        json = json
                .replace("“", "\"")     // 左双引号
                .replace("”", "\"")     // 右双引号
                .replace("'", "'")      // 左单引号
                .replace("'", "'")      // 右单引号
                .replace("'", "'")      // 撇号
                .replace("‚", "'")      // 下单引号
                .replace("„", "\"")     // 下双引号
                .replace("‹", "'")      // 左单角引号
                .replace("›", "'")      // 右单角引号
                .replace("«", "\"")     // 左双角引号
                .replace("»", "\"");    // 右双角引号

        // 处理其他特殊字符
        json = json
                .replace("–", "-")      // en dash
                .replace("—", "-")      // em dash
                .replace("…", "...")    // 省略号
                .replace("　", " ");     // 全角空格

        return json;
    }

    /**
     * 结构化字段处理
     */
    private String processStructuredFields(String json) {
        // 优先处理编程题 - 这是最复杂的情况
        if (json.contains("\"questionType\": \"PROGRAMMING\"")) {
            json = handleProgrammingQuestion(json);
        }
        
        // 处理字符串字段中的引号问题
        json = fixStringFieldQuotes(json);
        
        // 处理年份和数字格式
        json = handleSpecialNumberFormats(json);
        
        // 专门处理编程题中的代码字符串问题
        json = fixProgrammingCodeStrings(json);
        
        return json;
    }

    /**
     * 专门修复编程题中的代码字符串问题 - 增强版
     */
    private String fixProgrammingCodeStrings(String json) {
        try {
            log.debug("开始修复编程题代码字符串问题");
            
            // 第一步：修复代码中被错误引号包围的数字和关键字
            json = fixQuotedCodeElements(json);
            
            // 第二步：修复代码结构中的引号问题
            json = fixCodeStructureQuotes(json);
            
            // 第三步：修复特定的编程语言语法问题
            json = fixLanguageSpecificIssues(json);
            
            // 第四步：最终的代码字符串清理
            json = finalCodeStringCleaning(json);

            log.debug("编程题代码字符串修复完成");
            return json;
            
        } catch (Exception e) {
            log.error("修复编程题代码字符串时出错: {}", e.getMessage());
            return json;
        }
    }

    /**
     * 修复被错误引号包围的代码元素
     */
    private String fixQuotedCodeElements(String json) {
        return json
                // 修复数字比较
                .replaceAll("==\\s*\"(\\d+)\"", "== $1")
                .replaceAll("!=\\s*\"(\\d+)\"", "!= $1")
                .replaceAll(">\\s*\"(\\d+)\"", "> $1")
                .replaceAll("<\\s*\"(\\d+)\"", "< $1")
                .replaceAll(">=\\s*\"(\\d+)\"", ">= $1")
                .replaceAll("<=\\s*\"(\\d+)\"", "<= $1")
                
                // 修复变量比较
                .replaceAll("==\\s*\"(\\w+)\"(?!\\s*[,}])", "== $1")
                .replaceAll("!=\\s*\"(\\w+)\"(?!\\s*[,}])", "!= $1")
                
                // 修复关键字被引号包围的问题
                .replaceAll("\"(if|else|elif|for|while|def|class|return|break|continue|try|except|finally|with|import|from|as)\"", "$1")
                
                // 修复函数和方法调用
                .replaceAll("\"(\\w+)\"\\s*\\(", "$1(")
                
                // 修复变量赋值
                .replaceAll("=\\s*\"(\\w+)\"(?!\\s*[,}])", "= $1")
                
                // 修复数组/列表访问
                .replaceAll("\\[\"(\\w+)\"\\]", "[$1]")
                .replaceAll("\\[\"(\\d+)\"\\]", "[$1]");
    }

    /**
     * 修复代码结构中的引号问题
     */
    private String fixCodeStructureQuotes(String json) {
        return json
                // 修复条件语句结构
                .replaceAll("if\\s+\"([^\"]+)\"\\s*:", "if $1:")
                .replaceAll("elif\\s+\"([^\"]+)\"\\s*:", "elif $1:")
                .replaceAll("while\\s+\"([^\"]+)\"\\s*:", "while $1:")
                .replaceAll("for\\s+\"([^\"]+)\"\\s*:", "for $1:")
                
                // 修复函数定义
                .replaceAll("def\\s+\"(\\w+)\"\\s*\\(", "def $1(")
                .replaceAll("class\\s+\"(\\w+)\"\\s*:", "class $1:")
                
                // 修复return语句
                .replaceAll("return\\s+\"(\\w+)\"(?!\\s*[,}])", "return $1")
                .replaceAll("return\\s+\"(\\d+)\"", "return $1")
                
                // 修复print/console.log语句中的变量
                .replaceAll("print\\s*\\(\\s*\"(\\w+)\"\\s*\\)", "print($1)")
                .replaceAll("console\\.log\\s*\\(\\s*\"(\\w+)\"\\s*\\)", "console.log($1)")
                
                // 修复逻辑运算符
                .replaceAll("\"(and|or|not)\"", "$1")
                .replaceAll("\"(&&|\\|\\||!)\"", "$1");
    }

    /**
     * 修复特定编程语言的语法问题
     */
    private String fixLanguageSpecificIssues(String json) {
        // Python特定修复
        json = fixPythonSpecificIssues(json);
        
        // Java特定修复
        json = fixJavaSpecificIssues(json);
        
        // C++特定修复
        json = fixCppSpecificIssues(json);
        
        // JavaScript特定修复
        json = fixJavaScriptSpecificIssues(json);
        
        return json;
    }

    /**
     * Python特定问题修复
     */
    private String fixPythonSpecificIssues(String json) {
        return json
                // 修复Python中的None, True, False
                .replaceAll("\"(None|True|False)\"(?!\\s*[,}])", "$1")
                
                // 修复Python的range函数
                .replaceAll("range\\s*\\(\\s*\"(\\d+)\"", "range($1")
                .replaceAll("range\\s*\\(\\s*\"(\\w+)\"", "range($1")
                
                // 修复Python的len函数
                .replaceAll("len\\s*\\(\\s*\"(\\w+)\"\\s*\\)", "len($1)")
                
                // 修复Python的字符串方法
                .replaceAll("\\.(split|join|strip|replace|find|index)\\s*\\(\\s*\"(\\w+)\"", ".$1($2")
                
                // 修复Python的模运算
                .replaceAll("%\\s*\"(\\d+)\"", "% $1");
    }

    /**
     * Java特定问题修复
     */
    private String fixJavaSpecificIssues(String json) {
        return json
                // 修复Java的null, true, false
                .replaceAll("\"(null|true|false)\"(?!\\s*[,}])", "$1")
                
                // 修复Java的访问修饰符
                .replaceAll("\"(public|private|protected|static|final|abstract)\"\\s+", "$1 ")
                
                // 修复Java的数据类型
                .replaceAll("\"(int|long|double|float|boolean|char|String|void)\"\\s+", "$1 ")
                
                // 修复Java的System.out.println
                .replaceAll("System\\.out\\.println\\s*\\(\\s*\"(\\w+)\"\\s*\\)", "System.out.println($1)")
                
                // 修复Java的equals方法
                .replaceAll("\\.equals\\s*\\(\\s*\"(\\w+)\"\\s*\\)", ".equals($1)");
    }

    /**
     * C++特定问题修复
     */
    private String fixCppSpecificIssues(String json) {
        return json
                // 修复C++的关键字
                .replaceAll("\"(std|cout|cin|endl|namespace|using|include)\"", "$1")
                
                // 修复C++的数据类型
                .replaceAll("\"(int|long|double|float|bool|char|string|void)\"\\s+", "$1 ")
                
                // 修复C++的cout语句
                .replaceAll("cout\\s*<<\\s*\"(\\w+)\"", "cout << $1")
                
                // 修复C++的cin语句
                .replaceAll("cin\\s*>>\\s*\"(\\w+)\"", "cin >> $1");
    }

    /**
     * JavaScript特定问题修复
     */
    private String fixJavaScriptSpecificIssues(String json) {
        return json
                // 修复JavaScript的关键字
                .replaceAll("\"(var|let|const|function|undefined|null)\"(?!\\s*[,}])", "$1")
                
                // 修复JavaScript的console方法
                .replaceAll("console\\.(log|error|warn|info)\\s*\\(\\s*\"(\\w+)\"\\s*\\)", "console.$1($2)")
                
                // 修复JavaScript的typeof
                .replaceAll("typeof\\s+\"(\\w+)\"", "typeof $1")
                
                // 修复JavaScript的数组方法
                .replaceAll("\\.(push|pop|shift|unshift|slice|splice)\\s*\\(\\s*\"(\\w+)\"", ".$1($2");
    }

    /**
     * 最终的代码字符串清理
     */
    private String finalCodeStringCleaning(String json) {
        return json
                // 清理多余的转义字符
                .replaceAll("\\\\{2,}", "\\\\")
                
                // 修复可能遗漏的简单情况
                .replaceAll("\"(\\d+)\"(?=\\s*[+\\-*/])", "$1")  // 数学运算中的数字
                .replaceAll("([+\\-*/])\\s*\"(\\d+)\"", "$1 $2")  // 运算符后的数字
                
                // 修复布尔值
                .replaceAll("\"(true|false)\"(?!\\s*[,}])", "$1")
                
                // 修复空值
                .replaceAll("\"(null|undefined|None)\"(?!\\s*[,}])", "$1")
                
                // 清理可能的语法错误
                .replaceAll("\\s+", " ")  // 标准化空格
                .replaceAll("\\s*([{}\\[\\](),;])\\s*", "$1")  // 清理标点符号周围的空格
                .trim();
    }

    /**
     * 处理特殊数字格式（包括年份）
     */
    private String handleSpecialNumberFormats(String json) {
        try {
            // 处理年份范围格式：1900"1920" -> 1900-1920
            json = json.replaceAll("(\\d{4})\"(\\d{4})\"", "$1-$2");
            
            // 处理年份格式：1900"年 -> 1900年
            json = json.replaceAll("(\\d{4})\"年", "$1年");
            
            // 处理其他数字格式问题
            json = json.replaceAll("(\\d+)\"(\\d+)\"", "$1-$2");
            
            // 处理科学计数法中的问题
            json = json.replaceAll("(\\d+\\.\\d+)\"([eE][+-]?\\d+)\"", "$1$2");
            
            return json;
        } catch (Exception e) {
            log.warn("处理数字格式时出错: {}", e.getMessage());
            return json;
        }
    }

    /**
     * JSON格式标准化
     */
    private String standardizeJsonFormat(String json) {
        return json
                .replaceAll(",\\s*]", "]")         // 移除数组末尾多余逗号
                .replaceAll(",\\s*}", "}")         // 移除对象末尾多余逗号
                .replaceAll("\\}\\s*,\\s*\\{", "},{")  // 标准化对象分隔
                .replaceAll("\\[\\s*\\{", "[{")    // 标准化数组开始
                .replaceAll("\\}\\s*\\]", "}]")    // 标准化数组结束
                .replaceAll("\\{\\s*,", "{")       // 移除对象开始的逗号
                .replaceAll(",\\s*,", ",")         // 移除重复逗号
                .replaceAll(":\\s*,", ": null,")   // 处理空值
                .replaceAll("\"\"", "\"");         // 移除重复引号
    }

    /**
     * 智能JSON修复 - 基于错误信息的精准修复
     */
    private String performIntelligentJsonRepair(String json, JsonProcessingException error) {
        String errorMessage = error.getMessage();
        log.debug("JSON解析错误详情: {}", errorMessage);
        
        try {
            // 根据错误类型进行针对性修复
            if (errorMessage.contains("Unexpected character")) {
                json = repairUnexpectedCharacter(json, errorMessage);
            } else if (errorMessage.contains("was expecting comma")) {
                json = repairMissingComma(json);
            } else if (errorMessage.contains("Unexpected end-of-input")) {
                json = repairIncompleteJson(json);
            } else if (errorMessage.contains("duplicate field")) {
                json = repairDuplicateFields(json);
            } else {
                // 通用修复策略
                json = performGenericRepair(json);
            }
            
            // 验证修复结果
            objectMapper.readTree(json);
            log.debug("智能修复成功");
            return json;
            
        } catch (JsonProcessingException e) {
            log.warn("智能修复失败，使用最后的修复策略: {}", e.getMessage());
            return performLastResortRepair(json);
        }
    }

    /**
     * 修复意外字符错误
     */
    private String repairUnexpectedCharacter(String json, String errorMessage) {
        try {
            // 特殊处理转义字符错误
            if (errorMessage.contains("Unrecognized character escape")) {
                return fixEscapeCharacterIssues(json);
            }
            
            // 从错误信息中提取问题字符的位置
            Pattern pattern = Pattern.compile("column: (\\d+)");
            Matcher matcher = pattern.matcher(errorMessage);
            
            if (matcher.find()) {
                int column = Integer.parseInt(matcher.group(1));
                // 在问题位置附近查找并修复
                return repairAroundPosition(json, column);
            }
            
            // 如果无法定位，使用通用修复
            return performGenericRepair(json);
            
        } catch (Exception e) {
            log.warn("修复意外字符时出错: {}", e.getMessage());
            return performGenericRepair(json);
        }
    }

    /**
     * 修复转义字符问题
     */
    private String fixEscapeCharacterIssues(String json) {
        try {
            log.debug("开始修复转义字符问题");
            
            // 第一步：修复单引号转义问题
            json = fixSingleQuoteEscaping(json);
            
            // 第二步：修复代码块中的转义问题
            json = fixCodeBlockEscaping(json);
            
            // 第三步：修复其他转义字符问题
            json = fixOtherEscapeIssues(json);
            
            // 第四步：标准化JSON格式
            json = normalizeJsonAfterEscapeFix(json);
            
            log.debug("转义字符修复完成");
            return json;
            
        } catch (Exception e) {
            log.error("修复转义字符时出错: {}", e.getMessage());
            return performGenericRepair(json);
        }
    }

    /**
     * 修复单引号转义问题
     */
    private String fixSingleQuoteEscaping(String json) {
        return json
                // 移除错误的单引号转义
                .replaceAll("\\\\'", "'")
                // 修复字符串中的单引号
                .replaceAll("'([^']*)'", "\"$1\"")
                // 处理代码中的单引号字符串
                .replaceAll("(?<=\\s)'([^'\\s]*)'(?=\\s)", "\"$1\"")
                // 修复JSON字段名中的单引号
                .replaceAll("'(\\w+)'\\s*:", "\"$1\":")
                // 修复JSON字段值中的单引号
                .replaceAll(":\\s*'([^']*)'", ": \"$1\"");
    }

    /**
     * 修复代码块中的转义问题
     */
    private String fixCodeBlockEscaping(String json) {
        try {
            // 处理代码块标记
            json = json.replaceAll("```\\w*", "");
            
            // 修复代码中的换行符表示
            json = json.replaceAll("\\\\n", "\\n");
            
            // 修复代码中的制表符
            json = json.replaceAll("\\\\t", "\\t");
            
            // 处理代码中的特殊字符
            json = json
                    .replaceAll("\\\\\\\\", "\\\\")  // 双反斜杠
                    .replaceAll("\\\\\"", "\"")      // 转义的双引号
                    .replaceAll("\\\\r", "\\r");    // 回车符
            
            return json;
            
        } catch (Exception e) {
            log.warn("修复代码块转义时出错: {}", e.getMessage());
            return json;
        }
    }

    /**
     * 修复其他转义字符问题
     */
    private String fixOtherEscapeIssues(String json) {
        return json
                // 修复Unicode转义
                .replaceAll("\\\\u([0-9a-fA-F]{4})", "\\u$1")
                // 修复其他常见转义字符
                .replaceAll("\\\\b", "\b")
                .replaceAll("\\\\f", "\f")
                .replaceAll("\\\\v", "\u000B")
                // 清理多余的反斜杠
                .replaceAll("\\\\{3,}", "\\\\");
    }

    /**
     * 转义修复后的JSON标准化
     */
    private String normalizeJsonAfterEscapeFix(String json) {
        return json
                // 确保所有字段名都有双引号
                .replaceAll("([{,]\\s*)([a-zA-Z_]\\w*)\\s*:", "$1\"$2\":")
                // 确保字符串值都有双引号
                .replaceAll(":\\s*([^\"\\[\\{][^,}\\]]*?)([,}\\]])", ": \"$1\"$2")
                // 修复数字值被错误引号包围的问题
                .replaceAll("\"(\\d+(?:\\.\\d+)?)\"", "$1")
                // 修复布尔值被错误引号包围的问题
                .replaceAll("\"(true|false|null)\"", "$1")
                // 清理多余的空格和逗号
                .replaceAll("\\s+", " ")
                .replaceAll(",\\s*([}\\]])", "$1")
                .replaceAll("([{\\[]\\s*),", "$1")
                .trim();
    }

    /**
     * 在指定位置附近修复JSON
     */
    private String repairAroundPosition(String json, int position) {
        try {
            // 确保位置在有效范围内
            if (position < 0 || position >= json.length()) {
                return performGenericRepair(json);
            }
            
            // 获取问题区域的上下文
            int start = Math.max(0, position - 50);
            int end = Math.min(json.length(), position + 50);
            String context = json.substring(start, end);
            
            // 在上下文中查找并修复常见问题
            String repairedContext = context
                    .replaceAll("\"([^\"]*?)\"([^\"]*?)\"", "\"$1$2\"")  // 修复字符串中的引号
                    .replaceAll("([^,\\{\\[])\\s*\"", "$1,\"")          // 添加缺失的逗号
                    .replaceAll("\"\\s*([^:,\\}\\]])", "\",\"$1\"")     // 修复字符串结束
                    .replaceAll("([^\"\\s])\\s*:", "\"$1\":");          // 修复键名引号
            
            // 重新组装JSON
            return json.substring(0, start) + repairedContext + json.substring(end);
            
        } catch (Exception e) {
            log.warn("在位置{}附近修复时出错: {}", position, e.getMessage());
            return performGenericRepair(json);
        }
    }

    /**
     * 修复缺失逗号
     */
    private String repairMissingComma(String json) {
        return json
                .replaceAll("\"\\s*\"", "\",\"")           // 字符串之间添加逗号
                .replaceAll("\\}\\s*\"", "},\"")           // 对象后添加逗号
                .replaceAll("\\]\\s*\"", "],\"")           // 数组后添加逗号
                .replaceAll("([^,\\{\\[])\\s*\"\\w+\"\\s*:", "$1,\"$2\":"); // 属性前添加逗号
    }

    /**
     * 修复不完整的JSON
     */
    private String repairIncompleteJson(String json) {
        // 计算需要补充的括号
        int openBraces = json.length() - json.replace("{", "").length();
        int closeBraces = json.length() - json.replace("}", "").length();
        int openBrackets = json.length() - json.replace("[", "").length();
        int closeBrackets = json.length() - json.replace("]", "").length();
        
        StringBuilder repaired = new StringBuilder(json);
        
        // 补充缺失的括号
        for (int i = 0; i < openBraces - closeBraces; i++) {
            repaired.append("}");
        }
        for (int i = 0; i < openBrackets - closeBrackets; i++) {
            repaired.append("]");
        }
        
        return repaired.toString();
    }

    /**
     * 修复重复字段
     */
    private String repairDuplicateFields(String json) {
        try {
            // 使用正则表达式找到重复的字段并保留最后一个
            Pattern pattern = Pattern.compile("\"(\\w+)\"\\s*:\\s*([^,}]+)");
            Matcher matcher = pattern.matcher(json);
            
            Map<String, String> fields = new LinkedHashMap<>();
            StringBuffer sb = new StringBuffer();
            
            while (matcher.find()) {
                String key = matcher.group(1);
                String value = matcher.group(2);
                fields.put(key, value);
            }
            
            // 重建JSON，去除重复字段
            // 这里简化处理，实际可能需要更复杂的逻辑
            return json.replaceAll("(\"\\w+\"\\s*:\\s*[^,}]+),\\s*\\1", "$1");
            
        } catch (Exception e) {
            log.warn("修复重复字段时出错: {}", e.getMessage());
            return performGenericRepair(json);
        }
    }

    /**
     * 通用修复策略
     */
    private String performGenericRepair(String json) {
        return json
                .replaceAll("\"\"", "\"")                    // 移除重复引号
                .replaceAll("\\\\\"", "'")                   // 转义引号改为单引号
                .replaceAll("(?<!\\\\)\"(?=\\w)", "'")       // 单词前的未转义引号改为单引号
                .replaceAll("(?<=\\w)\"(?!\\s*[,:}\\]])", "'") // 单词后的引号改为单引号
                .replaceAll(",\\s*([,}\\]])", "$1")          // 移除多余逗号
                .replaceAll("\\s*,\\s*", ",")                // 标准化逗号格式
                .replaceAll("\\s*:\\s*", ":")                // 标准化冒号格式
                .replaceAll("\\{\\s*", "{")                  // 标准化大括号
                .replaceAll("\\s*\\}", "}")                  // 标准化大括号
                .replaceAll("\\[\\s*", "[")                  // 标准化中括号
                .replaceAll("\\s*\\]", "]");                 // 标准化中括号
    }

    /**
     * 最后的修复策略 - 当所有方法都失败时
     */
    private String performLastResortRepair(String json) {
        try {
            log.warn("执行最后的修复策略，可能会丢失部分数据");
            
            // 尝试提取可解析的JSON对象
            List<String> validObjects = new ArrayList<>();
            Pattern objectPattern = Pattern.compile("\\{[^{}]*\\}");
            Matcher matcher = objectPattern.matcher(json);
            
            while (matcher.find()) {
                String obj = matcher.group();
                try {
                    // 尝试解析单个对象
                    objectMapper.readTree(obj);
                    validObjects.add(obj);
                } catch (JsonProcessingException e) {
                    // 忽略无法解析的对象
                    log.debug("跳过无法解析的对象: {}", obj);
                }
            }
            
            if (!validObjects.isEmpty()) {
                return "[" + String.join(",", validObjects) + "]";
            }
            
            // 如果完全无法修复，返回空数组
            log.error("JSON完全无法修复，返回空数组");
            return "[]";
            
        } catch (Exception e) {
            log.error("最后修复策略也失败了: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * 修复字符串字段中的引号问题
     */
    private String fixStringFieldQuotes(String json) {
        try {
            // 使用正则表达式匹配字符串字段的值，并处理其中的引号
            Pattern stringFieldPattern = Pattern.compile("(\"(?:content|answerAnalysis|correctAnswer)\"\\s*:\\s*\")([^\"]*(?:\"[^\"]*)*)(\"(?:\\s*[,}]))");
            Matcher matcher = stringFieldPattern.matcher(json);
            StringBuffer sb = new StringBuffer();

            while (matcher.find()) {
                String fieldStart = matcher.group(1);  // 字段名和开始引号
                String fieldValue = matcher.group(2);  // 字段值
                String fieldEnd = matcher.group(3);    // 结束引号和后续字符

                // 处理字段值中的引号
                String cleanValue = fieldValue
                        .replace("\"", "'")           // 将双引号替换为单引号
                        .replace("“", "'")           // 处理中文引号
                    .replace("”", "'")           // 处理中文引号
                        .replace("'", "'")           // 统一单引号格式
                        .replace("\\", "\\\\");      // 转义反斜杠

                matcher.appendReplacement(sb, Matcher.quoteReplacement(fieldStart + cleanValue + fieldEnd));
            }
            matcher.appendTail(sb);

            return sb.toString();
        } catch (Exception e) {
            log.error("修复字符串字段引号时出错: {}", e.getMessage());
            return json;
        }
    }

    /**
     * 最后的修复尝试
     */
    private String attemptFinalFix(String json) {
        try {
            // 更激进的清理策略
            String fixed = json
                    .replaceAll("\"\"", "\"")                    // 移除重复引号
                    .replaceAll("\\\\\"", "'")                   // 转义引号改为单引号
                    .replaceAll("(?<!\\\\)\"(?=\\w)", "'")       // 单词前的未转义引号改为单引号
                    .replaceAll("(?<=\\w)\"(?!\\s*[,:}\\]])", "'") // 单词后的引号改为单引号
                    .replaceAll(",\\s*([,}\\]])", "$1")          // 移除多余逗号
                    .replaceAll("\\s*,\\s*", ",")                // 标准化逗号格式
                    .replaceAll("\\s*:\\s*", ":")                // 标准化冒号格式
                    .replaceAll("\\{\\s*", "{")                  // 标准化大括号
                    .replaceAll("\\s*\\}", "}")                  // 标准化大括号
                    .replaceAll("\\[\\s*", "[")                  // 标准化中括号
                    .replaceAll("\\s*\\]", "]");                 // 标准化中括号

            // 验证修复结果
            objectMapper.readTree(fixed);
            log.debug("最终修复成功");
            return fixed;
        } catch (JsonProcessingException e) {
            log.error("最终修复也失败了: {}", e.getMessage());
            log.debug("最终失败的JSON: {}", json);
            // 返回空数组，避免完全失败
            return "[]";
        }
    }

    /**
     * 处理特殊的年份格式问题
     */
    private String handleSpecialYearFormat(String json) {
        try {
            // 先处理选项中的年份范围
            Pattern optionsPattern = Pattern.compile("(?<=\"options\":\\s*\\{)[^}]+\\}");
            Matcher optionsMatcher = optionsPattern.matcher(json);
            StringBuffer optionsSb = new StringBuffer();
            while (optionsMatcher.find()) {
                String optionsContent = optionsMatcher.group();
                // 处理形如 "1900"1920" 的年份范围格式
                optionsContent = optionsContent.replaceAll("\"(\\d+)\"(\\d+)\"", "\"$1-$2\"");
                optionsMatcher.appendReplacement(optionsSb, optionsContent);
            }
            optionsMatcher.appendTail(optionsSb);
            json = optionsSb.toString();

            // 处理content和answerAnalysis中的年份格式
            Pattern[] patterns = {
                    Pattern.compile("(\\d+)\"(\\d+)\""),  // 处理形如 1940"1960" 的格式
                    Pattern.compile("\"(\\d+)\"(\\d+)"),  // 处理形如 "1940"1960 的格式
                    Pattern.compile("(\\d+)\"年"),        // 处理形如 1900"年 的格式
                    Pattern.compile("\"(\\d+)\"年"),      // 处理形如 "1900"年 的格式
                    Pattern.compile("(\\d+)[\"“”](年)")   // 处理其他年份格式
            };

            String result = json;
            for (Pattern pattern : patterns) {
                Matcher matcher = pattern.matcher(result);
                StringBuffer sb = new StringBuffer();
                while (matcher.find()) {
                    String replacement;
                    if (matcher.group().contains("年")) {
                        replacement = matcher.group(1) + "年";
                    } else {
                        replacement = matcher.group(1) + "-" + matcher.group(2);
                    }
                    matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
                }
                matcher.appendTail(sb);
                result = sb.toString();
            }

            // 清理最终结果中可能存在的问题
            result = result.replaceAll("\"\"", "\"")  // 处理连续的引号
                    .replaceAll("\\s+", " ")     // 处理多余的空格
                    .replaceAll(",\\s*,", ",")   // 处理重复的逗号
                    .replaceAll(",\\s*}", "}");  // 处理末尾的逗号

            return result;
        } catch (Exception e) {
            log.error("处理年份格式时出错: {}", e.getMessage());
            return json;
        }
    }

    @Override
    public String analyzeStudentAnswer(String question, String correctAnswer, String studentAnswer) {
        String prompt = String.format("""
            作为一名教育专家，请分析学生的答案和提供详细的反馈。
            
            题目：%s
            
            标准答案：%s
            
            学生答案：%s
            
            请提供：
            1. 答案评分（正确/部分正确/错误）
            2. 具体分析和反馈
            3. 改进建议
            """, question, correctAnswer, studentAnswer);

        return callOpenRouter(prompt);
    }

    @Override
    public String generateErrorAnalysis(String studentErrors, String knowledgePoints) {
        String prompt = String.format("""
            作为一名教育专家，请分析学生的错误并提供改进建议。
            
            学生错误：%s
            
            相关知识点：%s
            
            请提供：
            1. 错误原因分析
            2. 针对性的改进建议
            3. 推荐的练习方向
            """, studentErrors, knowledgePoints);

        return callOpenRouter(prompt);
    }

    @Override
    public com.example.aieduforge.dto.AIGradingResponse intelligentGrading(
            String questionTitle,
            String questionContent,
            Integer questionType,
            String correctAnswer,
            String studentAnswer,
            Double totalScore,
            Integer difficultyLevel) {

        log.info("开始AI智能评分 - 题目类型: {}, 总分: {}, 学生答案长度: {}", 
                questionType, totalScore, studentAnswer != null ? studentAnswer.length() : 0);

        try {
            // 构建智能评分提示词
            String prompt = buildIntelligentGradingPrompt(
                    questionTitle, questionContent, questionType,
                    correctAnswer, studentAnswer, totalScore, difficultyLevel
            );

            // 调用AI模型
            String aiResponse = callOpenRouter(prompt);
            log.debug("AI原始响应: {}", aiResponse);

            // 解析AI响应
            com.example.aieduforge.dto.AIGradingResponse response = parseGradingResponse(aiResponse, totalScore);
            
            log.info("AI评分成功 - 得分: {}, 相似度: {}, 置信度: {}", 
                    response.getScore(), response.getSimilarity(), response.getConfidence());
            
            return response;

        } catch (Exception e) {
            log.error("AI智能评分失败，使用后备算法: {}", e.getMessage(), e);
            com.example.aieduforge.dto.AIGradingResponse fallbackResponse = 
                    createFallbackResponse(questionType, correctAnswer, studentAnswer, totalScore);
            
            log.info("后备算法评分 - 得分: {}, 相似度: {}", 
                    fallbackResponse.getScore(), fallbackResponse.getSimilarity());
            
            return fallbackResponse;
        }
    }

    @Override
    public String generateResponse(String prompt) {
        return callOpenRouter(prompt);
    }

    /**
     * 构建智能评分提示词
     */
    private String buildIntelligentGradingPrompt(
            String questionTitle, String questionContent, Integer questionType,
            String correctAnswer, String studentAnswer, Double totalScore, Integer difficultyLevel) {

        StringBuilder prompt = new StringBuilder();

        prompt.append("你是一位专业的教师，需要对学生的答案进行智能评分。请仔细分析并给出公正的评分。\n\n");

        // 题目信息
        prompt.append("【题目信息】\n");
        prompt.append("题目：").append(questionTitle).append("\n");
        if (questionContent != null && !questionContent.equals(questionTitle)) {
            prompt.append("题目内容：").append(questionContent).append("\n");
        }
        prompt.append("题目类型：").append(getQuestionTypeName(questionType)).append("\n");
        prompt.append("总分：").append(totalScore).append("分\n");
        prompt.append("难度：").append(getDifficultyName(difficultyLevel)).append("\n\n");

        // 参考答案（注意：这只是参考，不是唯一标准）
        prompt.append("【参考答案】\n");
        prompt.append(correctAnswer).append("\n");
        prompt.append("注意：这只是参考答案，不是唯一正确答案。学生的答案如果有道理、逻辑清晰，即使与参考答案不同也应该得分。\n\n");

        // 学生答案
        prompt.append("【学生答案】\n");
        prompt.append(studentAnswer).append("\n\n");

        // 评分原则
        prompt.append("【评分原则】\n");
        prompt.append("重要：不要简单地与参考答案对比！要重点评估学生答案本身的质量：\n");
        prompt.append("1. 答案是否有道理、符合逻辑\n");
        prompt.append("2. 概念理解是否正确（可以有不同表达方式）\n");
        prompt.append("3. 论述是否清晰、有条理\n");
        prompt.append("4. 是否体现了对问题的深入思考\n");
        prompt.append("5. 即使角度不同，只要合理就应该给分\n\n");

        // 评分标准
        prompt.append("【具体评分标准】\n");
        switch (questionType) {
            case 1, 2, 3: // 客观题
                prompt.append("这是客观题，有标准答案：\n");
                prompt.append("- 答案正确：给满分\n");
                prompt.append("- 答案错误：给0分\n");
                break;
            case 4: // 简答题
                prompt.append("这是简答题，重点评估答案的合理性和质量：\n");
                prompt.append("- 概念理解正确性（35%）：核心概念是否理解正确（允许不同表达）\n");
                prompt.append("- 逻辑清晰度（25%）：论述是否有逻辑、条理清楚\n");
                prompt.append("- 内容充实度（25%）：是否有足够的内容支撑观点\n");
                prompt.append("- 创新思考（15%）：是否有独特见解或深入思考\n");
                prompt.append("评分提示：\n");
                prompt.append("- 90-100%：概念正确，逻辑清晰，内容充实，有独特见解\n");
                prompt.append("- 80-89%：概念正确，逻辑清晰，内容较充实\n");
                prompt.append("- 70-79%：概念基本正确，有一定逻辑，内容一般\n");
                prompt.append("- 60-69%：概念部分正确，逻辑不够清晰\n");
                prompt.append("- 50-59%：概念理解有误，但有一定思考\n");
                prompt.append("- 0-49%：概念错误，逻辑混乱\n");
                break;
            case 5: // 编程题
                prompt.append("这是编程题，重点评估代码的正确性和质量：\n");
                prompt.append("- 算法思路正确性（40%）：解决问题的思路是否正确\n");
                prompt.append("- 代码实现合理性（30%）：代码是否能实现预期功能\n");
                prompt.append("- 代码规范性（20%）：代码风格、命名是否规范\n");
                prompt.append("- 创新性和效率（10%）：是否有优化思考\n");
                prompt.append("注意：不同的实现方式都可能是正确的，重点看思路和逻辑\n");
                break;
            case 6: // 案例分析题
                prompt.append("这是案例分析题，重点评估分析的深度和合理性：\n");
                prompt.append("- 问题识别准确性（30%）：是否准确识别关键问题\n");
                prompt.append("- 分析深度和逻辑（30%）：分析是否深入、有逻辑\n");
                prompt.append("- 解决方案合理性（25%）：提出的方案是否可行\n");
                prompt.append("- 视角独特性（15%）：是否有独特的分析角度\n");
                prompt.append("注意：不同的分析角度和解决方案都可能是合理的\n");
                break;
        }

        // 输出格式要求
        prompt.append("\n【输出格式】\n");
        prompt.append("请严格按照以下JSON格式输出，不要包含其他内容：\n");
        prompt.append("{\n");
        prompt.append("  \"score\": 具体分数(数字，0到").append(totalScore).append("之间),\n");
        prompt.append("  \"isCorrect\": 正确性(1-完全正确,2-部分正确,0-错误),\n");
        prompt.append("  \"feedback\": \"详细的评价反馈，说明得分原因\",\n");
        prompt.append("  \"improvementSuggestion\": \"具体的改进建议\",\n");
        prompt.append("  \"similarity\": 相似度(0-1之间的小数),\n");
        prompt.append("  \"confidence\": 评分置信度(0-1之间的小数),\n");
        prompt.append("  \"needsManualReview\": 是否需要人工复核(true/false)\n");
        prompt.append("}\n\n");

        prompt.append("【重要提醒】\n");
        prompt.append("1. 评分要公正客观，重点看答案本身的质量，不要拘泥于参考答案\n");
        prompt.append("2. 如果学生答案逻辑清晰、有道理，即使表达方式不同也要给高分\n");
        prompt.append("3. 鼓励学生的独特见解和创新思考，不要因为与参考答案不同就扣分\n");
        prompt.append("4. 反馈要具体且建设性，既要指出优点也要指出可改进之处\n");
        prompt.append("5. 改进建议要实用，帮助学生在现有基础上提高\n");
        prompt.append("6. 对于概念理解正确但表达不够完整的答案，要给予鼓励性的分数\n");
        prompt.append("7. 严格按照JSON格式输出，确保可以被程序解析\n");

        prompt.append("\n【评分示例】\n");
        prompt.append("好的评分思路：\n");
        prompt.append("- 学生用自己的话解释概念，虽然与参考答案不同，但理解正确 → 高分\n");
        prompt.append("- 学生从不同角度分析问题，有独特见解 → 高分\n");
        prompt.append("- 学生答案简洁但抓住了核心要点 → 适当高分\n");
        prompt.append("- 学生答案冗长但逻辑清晰，要点全面 → 高分\n");
        prompt.append("\n错误的评分思路：\n");
        prompt.append("- 仅因为用词不同就扣分 → 错误\n");
        prompt.append("- 仅因为顺序不同就扣分 → 错误\n");
        prompt.append("- 仅因为详细程度不同就大幅扣分 → 错误\n");

        return prompt.toString();
    }

    /**
     * 解析AI评分响应
     */
    private com.example.aieduforge.dto.AIGradingResponse parseGradingResponse(String aiResponse, Double totalScore) {
        try {
            // 提取JSON部分
            String jsonStr = extractJSON(aiResponse);
            if (jsonStr == null) {
                throw new RuntimeException("无法从AI响应中提取JSON");
            }

            // 解析JSON
            Map<String, Object> responseMap = objectMapper.readValue(jsonStr, Map.class);

            com.example.aieduforge.dto.AIGradingResponse response = new com.example.aieduforge.dto.AIGradingResponse();

            // 提取各个字段
            Object scoreObj = responseMap.get("score");
            Double score = scoreObj instanceof Number ? ((Number) scoreObj).doubleValue() : 0.0;

            // 验证分数范围
            if (score > totalScore) score = totalScore;
            if (score < 0) score = 0.0;

            response.setScore(score);
            response.setIsCorrect(getIntegerValue(responseMap, "isCorrect", 0));
            response.setFeedback(getStringValue(responseMap, "feedback", "AI评分完成"));
            response.setImprovementSuggestion(getStringValue(responseMap, "improvementSuggestion", "继续努力"));
            response.setSimilarity(getDoubleValue(responseMap, "similarity", 0.5));
            response.setConfidence(getDoubleValue(responseMap, "confidence", 0.8));
            response.setNeedsManualReview(getBooleanValue(responseMap, "needsManualReview", true));
            response.setSuccess(true);

            return response;

        } catch (Exception e) {
            log.error("解析AI评分响应失败: {}", e.getMessage());
            log.debug("原始AI响应: {}", aiResponse);
            
            // 解析失败时，返回错误响应而不是抛出异常
            com.example.aieduforge.dto.AIGradingResponse errorResponse = new com.example.aieduforge.dto.AIGradingResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("AI评分响应解析失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJSON(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }

        // 查找JSON开始和结束位置
        int start = response.indexOf("{");
        int end = response.lastIndexOf("}");

        if (start != -1 && end != -1 && end > start) {
            return response.substring(start, end + 1);
        }

        return null;
    }

    /**
     * 创建后备评分响应
     */
    private com.example.aieduforge.dto.AIGradingResponse createFallbackResponse(
            Integer questionType, String correctAnswer, String studentAnswer, Double totalScore) {

        com.example.aieduforge.dto.AIGradingResponse response = new com.example.aieduforge.dto.AIGradingResponse();

        // 使用简单的相似度算法作为后备
        double similarity = calculateSimpleSimilarity(studentAnswer, correctAnswer);

        double score;
        int isCorrect;
        String feedback;
        String improvementSuggestion;
        boolean needsManualReview = false;

        // 根据题目类型调整评分策略
        switch (questionType) {
            case 1, 2, 3: // 客观题
                isCorrect = similarity > 0.8 ? 1 : 0;
                score = isCorrect == 1 ? totalScore : 0;
                feedback = isCorrect == 1 ? "答案正确" : "答案错误";
                improvementSuggestion = isCorrect == 1 ? "继续保持" : "建议复习相关知识点";
                break;

            default: // 主观题
                if (similarity >= 0.7) {
                    score = totalScore;
                    isCorrect = 1;
                    feedback = String.format("答案质量优秀（相似度：%.1f%%），要点覆盖全面", similarity * 100);
                    improvementSuggestion = "回答质量很高，可以尝试增加更多实例";
                } else if (similarity >= 0.5) {
                    score = totalScore * 0.8;
                    isCorrect = 2;
                    feedback = String.format("答案基本正确（相似度：%.1f%%），部分要点可以更完善", similarity * 100);
                    improvementSuggestion = "回答基本正确，建议补充更多关键要点";
                } else if (similarity >= 0.3) {
                    score = totalScore * 0.6;
                    isCorrect = 2;
                    feedback = String.format("答案部分正确（相似度：%.1f%%），需要补充关键要点", similarity * 100);
                    improvementSuggestion = "回答有一定基础，建议重新学习相关概念";
                } else if (similarity >= 0.1) {
                    score = totalScore * 0.3;
                    isCorrect = 0;
                    feedback = String.format("答案需要改进（相似度：%.1f%%），建议重新学习相关知识", similarity * 100);
                    improvementSuggestion = "建议系统复习相关知识点，可以寻求老师帮助";
                } else {
                    score = 0.0;
                    isCorrect = 0;
                    feedback = String.format("答案与题目要求差距较大（相似度：%.1f%%）", similarity * 100);
                    improvementSuggestion = "建议重新理解题目要求，系统学习相关知识点";
                }
                needsManualReview = true;
                break;
        }

        response.setScore(score);
        response.setIsCorrect(isCorrect);
        response.setFeedback(feedback);
        response.setImprovementSuggestion(improvementSuggestion);
        response.setSimilarity(similarity);
        response.setConfidence(0.6); // 后备算法置信度较低
        response.setNeedsManualReview(needsManualReview);
        response.setSuccess(true);

        return response;
    }

    /**
     * 简单相似度计算
     */
    private double calculateSimpleSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) return 0.0;

        text1 = text1.toLowerCase().replaceAll("[\\p{Punct}\\s]+", " ").trim();
        text2 = text2.toLowerCase().replaceAll("[\\p{Punct}\\s]+", " ").trim();

        if (text1.equals(text2)) return 1.0;

        String[] words1 = text1.split("\\s+");
        String[] words2 = text2.split("\\s+");

        int commonWords = 0;
        for (String word1 : words1) {
            for (String word2 : words2) {
                if (word1.equals(word2) && word1.length() > 1) {
                    commonWords++;
                    break;
                }
            }
        }

        return (double) commonWords / Math.max(words1.length, words2.length);
    }

    // 辅助方法
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private Integer getIntegerValue(Map<String, Object> map, String key, Integer defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    private Double getDoubleValue(Map<String, Object> map, String key, Double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }

    private Boolean getBooleanValue(Map<String, Object> map, String key, Boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    private String getQuestionTypeName(Integer type) {
        return switch (type) {
            case 1 -> "单选题";
            case 2 -> "多选题";
            case 3 -> "填空题";
            case 4 -> "简答题";
            case 5 -> "编程题";
            case 6 -> "案例分析题";
            default -> "未知类型";
        };
    }

    private String getDifficultyName(Integer level) {
        return switch (level) {
            case 1 -> "简单";
            case 2 -> "中等";
            case 3 -> "困难";
            default -> "未知";
        };
    }

    private String callOpenRouter(String prompt) {
        // 使用动态AI模型服务生成响应
        try {
            return dynamicAIService.generateResponse(prompt);
        } catch (Exception e) {
            log.warn("动态AI模型服务调用失败，回退到配置文件中的模型: {}", e.getMessage());
        }
        
        // 如果动态服务失败，回退到原始实现
        // 构建OpenAI兼容的请求格式
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        
        List<Map<String, Object>> messages = new ArrayList<>();
        messages.add(message);
        
        Map<String, Object> request = new HashMap<>();
        request.put("model", aiProperties.getOpenRouter().getModelName());
        request.put("messages", messages);
        request.put("max_tokens", aiProperties.getOpenRouter().getMaxTokens());
        request.put("temperature", aiProperties.getOpenRouter().getTemperature());

        try {
            String url = aiProperties.getOpenRouter().getBaseUrl() + "/chat/completions";
            
            // 设置请求头
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.set("Authorization", "Bearer " + aiProperties.getOpenRouter().getApiKey());
            headers.set("Content-Type", "application/json");
            headers.set("HTTP-Referer", "http://localhost:8080");
            headers.set("X-Title", "AiEduForge");
            
            org.springframework.http.HttpEntity<Map<String, Object>> entity = 
                new org.springframework.http.HttpEntity<>(request, headers);
            
            Map<String, Object> response = restTemplate.postForObject(url, entity, Map.class);
            
            if (response != null && response.containsKey("choices")) {
                List<Map<String, Object>> choices = (List<Map<String, Object>>) response.get("choices");
                if (!choices.isEmpty()) {
                    Map<String, Object> choice = choices.get(0);
                    Map<String, Object> message_response = (Map<String, Object>) choice.get("message");
                    return (String) message_response.get("content");
                }
            }
            return null;
        } catch (Exception e) {
            log.error("调用 OpenRouter API 失败", e);
            throw new RuntimeException("AI服务调用失败", e);
        }
    }

    private String getPromptTemplateForType(String questionType) {
        // 基础提示模板
        String template = """
            作为一名专业的教育专家，请严格按照以下要求生成考试题目。要求生成的题目必须是%d个，不能多也不能少。
            
            教学内容：
            %s
            
            【重要约束】：
            1. 题目类型：%s - 这是硬性要求，所有题目都必须是这个类型，不允许混合其他类型
            2. 难度等级：%d（1-简单，2-中等，3-困难）
            3. 必须生成数量：%d个（这是强制要求，必须准确生成这么多个题目）
            4. questionType字段值必须严格等于："%s"，不允许任何变化
            
            【JSON格式要求】：
            必须按照以下JSON数组格式返回，确保格式正确：
            [
                {
                    "questionType": "%s",
                    "content": "题目内容",
                    %s
                    "correctAnswer": "正确答案",
                    "answerAnalysis": "答案解析（包含解题思路和关键知识点）",
                    "difficulty": %d
                }
            ]
            
            %s
            
            【严格要求】：
            1. 所有%d个题目的questionType字段必须完全一致，都是"%s"
            2. 不允许生成任何其他类型的题目（如：不能在选择题中混入填空题）
            3. 严格按照JSON格式返回，所有字符串必须使用双引号
            4. 每个题目的难度必须符合指定的难度等级
            5. 内容要有教育意义和专业性
            6. 答案解析必须详尽，便于学习
            
            【再次强调】：生成的所有题目类型必须完全一致，都是%s类型，绝对不允许混合其他类型！
            """;

        // 返回处理好的模板
        return template;
    }

    private String getOptionsForType(String questionType) {
        return switch (questionType) {
            case "SINGLE_CHOICE", "MULTIPLE_CHOICE" -> """
                "options": {
                    "A": "选项A",
                    "B": "选项B",
                    "C": "选项C",
                    "D": "选项D"
                },""";
            default -> "";
        };
    }

    private String getInstructionsForType(String questionType) {
        return switch (questionType) {
            case "SINGLE_CHOICE" -> """
                单选题具体要求：
                - 必须提供4个选项(A/B/C/D)
                - 题目内容要简洁明了，便于理解
                - 答案必须是单个大写字母(A/B/C/D)
                - 选项内容要合理，有一定的干扰性""";
            case "MULTIPLE_CHOICE" -> """
                多选题具体要求：
                - 必须提供4个选项(A/B/C/D)
                - 答案必须是大写字母组合，用逗号分隔(如"A,C,D")
                - 至少选择2个选项
                - 选项内容要合理，具有一定的迷惑性""";
            case "FILL_BLANK" -> """
                填空题具体要求：
                - 填空处答案必须需用下划线"_____"替换，题目中你不得出现答案，这是硬性要求
                - 如果有多个填空，答案用|||分隔
                - 答案要简洁明确，便于评分
                - 避免使用模糊的描述性语言""";
            case "SHORT_ANSWER" -> """
                简答题具体要求：
                - 题目要明确考察目标
                - 答案要包含关键点列举
                - 答案解析要说明评分要点
                - 避免过于简单或复杂的题目""";
            case "PROGRAMMING" -> """
                编程题具体要求：
                1. 题目格式要求：
                   - 必须包含完整的问题描述
                   - 说明输入参数和返回值的类型
                   - 提供示例输入和期望输出
                   - 编程题对应的知识点和教学内容一致，例如教学内容是"线性表”，你的编程题可以出"返回单链表第K个节点"之类的代码，不的出无关的题
                
                2. 代码格式要求：
                   - 代码必须是单行文本，不要使用实际换行符
                   - 使用'\\n'代替换行
                   - 所有引号使用单引号，避免使用双引号
                   - 不要包含Markdown代码块标记(```)
                   - 注释要简洁，避免多行注释
                
                3. JSON格式要求：
                   - correctAnswer字段必须是一个合格的JSON字符串
                   - 避免在代码中使用未转义的双引号
                   - 确保代码中的特殊字符都正确转义
                
                4. 答案解析要求：
                   - 简要说明代码思路
                   - 解释关键算法步骤
                   - 分析时间和空间复杂度
                   - 不要包含代码片段
                
                示例格式：
                {
                    "questionType": "PROGRAMMING",
                    "content": "编写一个函数计算两个数的和",
                    "correctAnswer": "python代码\\n  def add(a, b):\\n    return a + b\\n",
                    "answerAnalysis": "这是一个简单的加法函数，接收两个参数并返回它们的和。时间复杂度O(1)。",
                    "difficulty": 1
                }""";
            case "CASE_ANALYSIS" -> """
                案例分析题具体要求：
                - 题目要包含完整的案例背景
                - 提供明确的分析要求
                - 答案要包含分析思路、关键点分析、结论
                - 避免主观性过强的描述""";
            default -> "";
        };
    }
}
