package com.example.aieduforge.service.impl;

import com.example.aieduforge.mapper.SysNotificationMapper;
import com.example.aieduforge.service.NotificationStatsService;
import com.example.aieduforge.websocket.NotificationWebSocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 通知统计服务实现类
 */
@Slf4j
@Service
public class NotificationStatsServiceImpl implements NotificationStatsService {

    @Autowired
    private SysNotificationMapper sysNotificationMapper;

    @Override
    public Map<String, Object> getOverviewStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 总通知数
            int totalNotifications = sysNotificationMapper.getTotalNotificationCount();
            
            // 今日发送数
            int todaySent = sysNotificationMapper.getNotificationCountByDate("today");
            
            // 昨日发送数
            int yesterdaySent = sysNotificationMapper.getNotificationCountByDate("yesterday");
            
            // 今日阅读数
            int todayRead = sysNotificationMapper.getReadNotificationCountByDate("today");
            
            // 昨日阅读数
            int yesterdayRead = sysNotificationMapper.getReadNotificationCountByDate("yesterday");
            
            // 计算增长率
            double sentGrowthRate = yesterdaySent > 0 ? 
                ((double)(todaySent - yesterdaySent) / yesterdaySent * 100) : 0;
            double readGrowthRate = yesterdayRead > 0 ? 
                ((double)(todayRead - yesterdayRead) / yesterdayRead * 100) : 0;
            
            // 今日阅读率
            double todayReadRate = todaySent > 0 ? ((double)todayRead / todaySent * 100) : 0;
            
            // 在线用户数
            int onlineUsers = NotificationWebSocket.getOnlineCount();
            
            stats.put("totalNotifications", totalNotifications);
            stats.put("todaySent", todaySent);
            stats.put("todayRead", todayRead);
            stats.put("todayReadRate", Math.round(todayReadRate * 100.0) / 100.0);
            stats.put("sentGrowthRate", Math.round(sentGrowthRate * 100.0) / 100.0);
            stats.put("readGrowthRate", Math.round(readGrowthRate * 100.0) / 100.0);
            stats.put("onlineUsers", onlineUsers);
            
        } catch (Exception e) {
            log.error("获取通知概览统计失败", e);
            stats.put("error", "获取统计数据失败");
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getTrendStats(Integer days) {
        Map<String, Object> stats = new HashMap<>();
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate today = LocalDate.now();
            
            for (int i = days - 1; i >= 0; i--) {
                LocalDate date = today.minusDays(i);
                String dateStr = date.format(formatter);
                
                int sent = sysNotificationMapper.getNotificationCountByDate(dateStr);
                int read = sysNotificationMapper.getReadNotificationCountByDate(dateStr);
                double readRate = sent > 0 ? ((double)read / sent * 100) : 0;
                
                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", dateStr);
                dayData.put("sent", sent);
                dayData.put("read", read);
                dayData.put("readRate", Math.round(readRate * 100.0) / 100.0);
                
                trendData.add(dayData);
            }
            
            stats.put("trendData", trendData);
            
        } catch (Exception e) {
            log.error("获取通知趋势统计失败", e);
            stats.put("error", "获取趋势数据失败");
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getTypeDistribution() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            List<Map<String, Object>> typeStats = sysNotificationMapper.getNotificationTypeStats();
            stats.put("typeDistribution", typeStats);
            
        } catch (Exception e) {
            log.error("获取通知类型分布失败", e);
            stats.put("error", "获取类型分布数据失败");
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getReadStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 按角色统计阅读情况
            List<Map<String, Object>> roleReadStats = sysNotificationMapper.getReadStatsByRole();
            
            // 按优先级统计阅读情况
            List<Map<String, Object>> priorityReadStats = sysNotificationMapper.getReadStatsByPriority();
            
            stats.put("roleReadStats", roleReadStats);
            stats.put("priorityReadStats", priorityReadStats);
            
        } catch (Exception e) {
            log.error("获取用户阅读统计失败", e);
            stats.put("error", "获取阅读统计数据失败");
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getWebSocketStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            int onlineCount = NotificationWebSocket.getOnlineCount();
            
            stats.put("onlineCount", onlineCount);
            stats.put("connectionStatus", "正常");
            
        } catch (Exception e) {
            log.error("获取WebSocket统计失败", e);
            stats.put("error", "获取WebSocket统计数据失败");
        }
        
        return stats;
    }
}