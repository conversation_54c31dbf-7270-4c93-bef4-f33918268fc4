package com.example.aieduforge.service.impl;

import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.ExamAnswer;
import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.entity.PracticeRecord;
import com.example.aieduforge.exception.BusinessException;
import com.example.aieduforge.mapper.ExamAnswerMapper;
import com.example.aieduforge.mapper.PracticeRecordMapper;
import com.example.aieduforge.service.AIService;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.PracticeService;
import com.example.aieduforge.service.ExamQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Practice Service Implementation
 */
@Slf4j
@Service
public class PracticeServiceImpl implements PracticeService {

    @Autowired
    private PracticeRecordMapper practiceRecordMapper;
    
    @Autowired
    private ExamAnswerMapper examAnswerMapper;
    
    @Autowired
    private AIService aiService;

    @Autowired
    private ExamQuestionService examQuestionService;
    
    @Autowired
    private CourseService courseService;

    @Override
    public List<ExamQuestion> generatePracticeQuestions(Long studentId, Long courseId, Integer practiceType, Integer count, Integer questionType) {
        List<ExamQuestion> questions;
        
        switch (practiceType) {
            case 1: // 随机练习
                if (questionType != null) {
                    questions = examQuestionService.findRandomQuestionsByType(courseId, questionType, count);
                } else {
                    questions = examQuestionService.findRandomQuestions(courseId, count);
                }
                break;
            case 2: // 专项练习 - 根据难度
                questions = examQuestionService.findByDifficultyLevel(courseId, 1); // 简单题目
                // 如果指定了题目类型，进行筛选
                if (questionType != null) {
                    questions = questions.stream()
                        .filter(q -> questionType.equals(q.getQuestionType()))
                        .collect(Collectors.toList());
                }
                if (questions.size() > count) {
                    questions = questions.subList(0, count);
                }
                break;
            case 3: // 错题重做
                questions = getWrongQuestions(studentId, courseId);
                // 如果指定了题目类型，进行筛选
                if (questionType != null) {
                    questions = questions.stream()
                        .filter(q -> questionType.equals(q.getQuestionType()))
                        .collect(Collectors.toList());
                }
                if (questions.size() > count) {
                    questions = questions.subList(0, count);
                }
                break;
            default:
                if (questionType != null) {
                    questions = examQuestionService.findRandomQuestionsByType(courseId, questionType, count);
                } else {
                    questions = examQuestionService.findRandomQuestions(courseId, count);
                }
        }
        
        if (questions.isEmpty()) {
            String typeMsg = getQuestionTypeMessage(questionType);
            String practiceTypeMsg = getPracticeTypeMessage(practiceType);
            throw new BusinessException("当前课程中没有足够的" + typeMsg + "题目用于" + practiceTypeMsg + "，请联系老师添加更多题目或选择其他练习类型");
        }
        
        return questions;
    }

    @Override
    @Transactional
    public PracticeRecord startPractice(Long studentId, Long courseId, Integer practiceType, List<Long> questionIds) {
        PracticeRecord record = new PracticeRecord();
        record.setStudentId(studentId);
        record.setCourseId(courseId);
        record.setPracticeType(practiceType);
        record.setQuestionIds(questionIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        record.setTotalQuestions(questionIds.size());
        record.setCorrectCount(0);
        record.setScore(BigDecimal.ZERO);
        record.setCompletionStatus(1); // 进行中
        record.setStartTime(LocalDateTime.now());
        record.setCreateTime(LocalDateTime.now());
        
        if (practiceRecordMapper.insert(record) <= 0) {
            throw new BusinessException("Failed to start practice session");
        }
        
        log.info("Practice session started for student: {} in course: {}", studentId, courseId);
        return record;
    }

    @Override
    @Transactional
    public ExamAnswer submitAnswer(Long practiceId, Long questionId, String studentAnswer, Long studentId) {
        // Get question details
        ExamQuestion question = examQuestionService.getById(questionId);
        if (question == null) {
            throw new BusinessException("Question not found");
        }
        
        // Check if answer already exists
        ExamAnswer existingAnswer = examAnswerMapper.findByStudentAndQuestion(studentId, questionId);
        if (existingAnswer != null) {
            // Update existing answer
            existingAnswer.setStudentAnswer(studentAnswer);
            existingAnswer.setPracticeId(practiceId); // 确保设置练习ID
            existingAnswer.setAnswerTime(LocalDateTime.now());
            
            // Evaluate answer
            evaluateAnswer(existingAnswer, question);
            
            examAnswerMapper.updateById(existingAnswer);
            return existingAnswer;
        }
        
        // Create new answer
        ExamAnswer answer = new ExamAnswer();
        answer.setQuestionId(questionId);
        answer.setStudentId(studentId);
        answer.setStudentAnswer(studentAnswer);
        answer.setPracticeId(practiceId); // 设置练习ID
        answer.setAnswerTime(LocalDateTime.now());
        answer.setCreateTime(LocalDateTime.now());
        
        // Evaluate answer
        evaluateAnswer(answer, question);
        
        if (examAnswerMapper.insert(answer) <= 0) {
            throw new BusinessException("Failed to submit answer");
        }
        
        log.info("Answer submitted for question: {} by student: {}", questionId, studentId);
        return answer;
    }

    @Override
    @Transactional
    public PracticeRecord completePractice(Long practiceId, Long studentId) {
        PracticeRecord record = practiceRecordMapper.selectById(practiceId);
        if (record == null) {
            throw new BusinessException("Practice record not found");
        }
        
        if (!record.getStudentId().equals(studentId)) {
            throw new BusinessException("You don't have permission to complete this practice");
        }
        
        // Calculate results
        String[] questionIds = record.getQuestionIds().split(",");
        int correctCount = 0;
        BigDecimal totalScore = BigDecimal.ZERO;
        
        // 收集详细评分信息
        List<Map<String, Object>> detailedScores = new ArrayList<>();
        
        for (String questionIdStr : questionIds) {
            Long questionId = Long.valueOf(questionIdStr);
            ExamAnswer answer = examAnswerMapper.findByStudentAndQuestion(studentId, questionId);
            ExamQuestion question = examQuestionService.getById(questionId);
            
            if (answer != null) {
                if (answer.getIsCorrect() == 1) {
                    correctCount++;
                }
                if (answer.getScore() != null) {
                    totalScore = totalScore.add(answer.getScore());
                }
                
                // 构建详细评分信息
                Map<String, Object> scoreDetail = new HashMap<>();
                scoreDetail.put("questionId", questionId);
                scoreDetail.put("questionTitle", question != null ? question.getTitle() : "");
                scoreDetail.put("questionType", question != null ? question.getQuestionType() : 0);
                scoreDetail.put("aiScore", answer.getAiScore());
                scoreDetail.put("teacherScore", answer.getTeacherScore());
                scoreDetail.put("finalScore", answer.getScore());
                scoreDetail.put("totalScore", question != null ? question.getScore() : BigDecimal.ZERO);
                scoreDetail.put("aiFeedback", answer.getAiFeedback());
                scoreDetail.put("teacherFeedback", answer.getTeacherFeedback());
                scoreDetail.put("improvementSuggestion", answer.getImprovementSuggestion());
                scoreDetail.put("gradingStatus", answer.getGradingStatus());
                scoreDetail.put("needsManualReview", answer.getNeedsManualReview());
                scoreDetail.put("isCorrect", answer.getIsCorrect());
                detailedScores.add(scoreDetail);
            }
        }
        
        // Update practice record
        record.setCorrectCount(correctCount);
        record.setScore(totalScore);
        record.setCompletionStatus(2); // 已完成
        record.setEndTime(LocalDateTime.now());
        
        // Calculate time spent
        if (record.getStartTime() != null) {
            long timeSpent = ChronoUnit.SECONDS.between(record.getStartTime(), record.getEndTime());
            record.setTimeSpent((int) timeSpent);
        }
        
        practiceRecordMapper.updateById(record);
        
        // 将详细评分信息添加到返回对象中（通过扩展字段）
        record.setDetailedScores(detailedScores);
        
        log.info("Practice session completed for student: {}, score: {}, detailedScores count: {}", 
            studentId, totalScore, detailedScores.size());
        
        // 调试：打印详细评分信息
        for (Map<String, Object> scoreDetail : detailedScores) {
            log.info("Score detail: questionId={}, aiScore={}, teacherScore={}, finalScore={}, aiFeedback={}", 
                scoreDetail.get("questionId"), 
                scoreDetail.get("aiScore"), 
                scoreDetail.get("teacherScore"), 
                scoreDetail.get("finalScore"),
                scoreDetail.get("aiFeedback"));
        }
        
        return record;
    }

    @Override
    public List<PracticeRecord> getPracticeHistory(Long studentId, Long courseId) {
        List<PracticeRecord> records;
        if (courseId != null) {
            records = practiceRecordMapper.findByStudentAndCourse(studentId, courseId);
        } else {
            records = practiceRecordMapper.findByStudentId(studentId);
        }
        
        // 为每个练习记录计算总可能得分和课程名称
        for (PracticeRecord record : records) {
            enrichPracticeRecord(record);
        }
        
        return records;
    }
    
    /**
     * 丰富练习记录信息
     */
    private void enrichPracticeRecord(PracticeRecord record) {
        try {
            // 获取课程名称
            if (record.getCourseId() != null) {
                Course course = courseService.getById(record.getCourseId());
                if (course != null) {
                    record.setCourseName(course.getCourseName());
                }
            }
            
            // 计算总可能得分
            if (record.getQuestionIds() != null && !record.getQuestionIds().isEmpty()) {
                String[] questionIds = record.getQuestionIds().split(",");
                BigDecimal totalScore = BigDecimal.ZERO;
                
                for (String questionIdStr : questionIds) {
                    try {
                        Long questionId = Long.parseLong(questionIdStr.trim());
                        ExamQuestion question = examQuestionService.getById(questionId);
                        if (question != null && question.getScore() != null) {
                            totalScore = totalScore.add(BigDecimal.valueOf(question.getScore()));
                        }
                    } catch (NumberFormatException e) {
                        log.warn("Invalid question ID: {}", questionIdStr);
                    }
                }
                
                record.setTotalPossibleScore(totalScore);
            }
            
            // 重新计算练习记录的分数（确保数据一致性）
            if (record.getId() != null) {
                recalculatePracticeScore(record);
            }
        } catch (Exception e) {
            log.error("Error enriching practice record {}: {}", record.getId(), e.getMessage());
        }
    }
    
    /**
     * 重新计算练习记录的分数
     */
    private void recalculatePracticeScore(PracticeRecord record) {
        try {
            // 获取该练习的所有答案
            List<ExamAnswer> answers = examAnswerMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ExamAnswer>()
                    .eq("practice_id", record.getId())
            );
            
            BigDecimal totalScore = BigDecimal.ZERO;
            int correctCount = 0;
            
            for (ExamAnswer answer : answers) {
                if (answer.getScore() != null) {
                    totalScore = totalScore.add(answer.getScore());
                }
                if (answer.getIsCorrect() != null && answer.getIsCorrect() == 1) {
                    correctCount++;
                }
            }
            
            // 如果计算出的分数与记录中的不一致，更新记录
            if (!totalScore.equals(record.getScore()) || !Integer.valueOf(correctCount).equals(record.getCorrectCount())) {
                record.setScore(totalScore);
                record.setCorrectCount(correctCount);
                practiceRecordMapper.updateById(record);
                log.info("更新练习记录 {} 的分数: {} -> {}, 正确数: {} -> {}", 
                    record.getId(), record.getScore(), totalScore, record.getCorrectCount(), correctCount);
            }
        } catch (Exception e) {
            log.error("重新计算练习记录分数失败: {}", e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getPracticeStatistics(Long studentId, Long courseId) {
        Map<String, Object> stats = practiceRecordMapper.getStudentStatistics(studentId, courseId);
        return stats != null ? stats : new HashMap<>();
    }

    @Override
    public List<ExamQuestion> getWrongQuestions(Long studentId, Long courseId) {
        List<ExamAnswer> wrongAnswers = examAnswerMapper.findIncorrectAnswers(studentId);
        
        // 修复对象转换
        List<ExamQuestion> questions = wrongAnswers.stream()
            .map(answer -> examQuestionService.getById(answer.getQuestionId()))
            .filter(q -> q != null && (courseId == null || courseId.equals(q.getCourseId())))
            .collect(Collectors.toList());

        return questions;
    }

    @Override
    public ExamAnswer getStudentAnswer(Long studentId, Long questionId) {
        return examAnswerMapper.findByStudentAndQuestion(studentId, questionId);
    }
    
    @Override
    public PracticeRecord getPracticeRecordById(Long practiceId, Long studentId) {
        // Get the practice record and verify it belongs to the student
        PracticeRecord record = practiceRecordMapper.selectById(practiceId);
        if (record != null && record.getStudentId().equals(studentId)) {
            return record;
        }
        return null;
    }
    
    @Override
    public com.example.aieduforge.dto.PracticeReviewDTO getPracticeReviewData(Long practiceId, Long studentId) {
        // Get the practice record and verify it belongs to the student
        PracticeRecord record = practiceRecordMapper.selectById(practiceId);
        if (record == null || !record.getStudentId().equals(studentId)) {
            return null;
        }
        
        // 丰富练习记录信息
        enrichPracticeRecord(record);
        
        // Parse question IDs from the stored string
        List<ExamQuestion> questions = new ArrayList<>();
        Map<Long, String> answers = new HashMap<>();
        Map<Long, ExamAnswer> detailedAnswers = new HashMap<>();
        
        if (record.getQuestionIds() != null && !record.getQuestionIds().isEmpty()) {
            try {
                // Parse question IDs (assuming they're stored as comma-separated values)
                String[] questionIdStrings = record.getQuestionIds().split(",");
                List<Long> questionIds = new ArrayList<>();
                
                for (String idStr : questionIdStrings) {
                    try {
                        questionIds.add(Long.parseLong(idStr.trim()));
                    } catch (NumberFormatException e) {
                        // Skip invalid IDs
                        continue;
                    }
                }
                
                // Fetch questions by IDs
                if (!questionIds.isEmpty()) {
                    for (Long questionId : questionIds) {
                        ExamQuestion question = examQuestionService.getById(questionId);
                        if (question != null) {
                            questions.add(question);
                        }
                        
                        // Fetch student answer for this question
                        ExamAnswer answer = examAnswerMapper.findByStudentAndQuestion(studentId, questionId);
                        if (answer != null) {
                            if (answer.getStudentAnswer() != null) {
                                answers.put(questionId, answer.getStudentAnswer());
                            }
                            detailedAnswers.put(questionId, answer);
                        }
                    }
                }
            } catch (Exception e) {
                // Log error but continue with empty data
                log.error("Error parsing question IDs: {}", e.getMessage());
            }
        }
        
        // 构建详细评分信息
        List<Map<String, Object>> detailedScores = new ArrayList<>();
        for (ExamQuestion question : questions) {
            ExamAnswer answer = detailedAnswers.get(question.getId());
            Map<String, Object> scoreDetail = new HashMap<>();
            scoreDetail.put("questionId", question.getId());
            scoreDetail.put("questionTitle", question.getTitle());
            scoreDetail.put("questionType", question.getQuestionType());
            scoreDetail.put("questionText", question.getContent());
            scoreDetail.put("correctAnswer", question.getCorrectAnswer());
            
            if (answer != null) {
                scoreDetail.put("studentAnswer", answer.getStudentAnswer());
                scoreDetail.put("aiScore", answer.getAiScore());
                scoreDetail.put("teacherScore", answer.getTeacherScore());
                scoreDetail.put("finalScore", answer.getScore());
                scoreDetail.put("totalScore", question.getScore());
                scoreDetail.put("aiFeedback", answer.getAiFeedback());
                scoreDetail.put("teacherFeedback", answer.getTeacherFeedback());
                scoreDetail.put("gradingStatus", answer.getGradingStatus());
                scoreDetail.put("needsManualReview", answer.getNeedsManualReview());
                scoreDetail.put("isCorrect", answer.getIsCorrect());
            } else {
                scoreDetail.put("studentAnswer", "");
                scoreDetail.put("aiScore", BigDecimal.ZERO);
                scoreDetail.put("teacherScore", null);
                scoreDetail.put("finalScore", BigDecimal.ZERO);
                scoreDetail.put("totalScore", question.getScore());
                scoreDetail.put("aiFeedback", "未作答");
                scoreDetail.put("teacherFeedback", null);
                scoreDetail.put("gradingStatus", 0);
                scoreDetail.put("needsManualReview", 0);
                scoreDetail.put("isCorrect", 0);
            }
            
            detailedScores.add(scoreDetail);
        }
        
        // 将详细评分信息添加到练习记录中
        record.setDetailedScores(detailedScores);
        
        log.info("Practice review data loaded for practice: {}, detailedScores count: {}", practiceId, detailedScores.size());
        
        // 调试：打印详细评分信息
        for (Map<String, Object> scoreDetail : detailedScores) {
            log.info("Review score detail: questionId={}, aiScore={}, teacherScore={}, finalScore={}, aiFeedback={}", 
                scoreDetail.get("questionId"), 
                scoreDetail.get("aiScore"), 
                scoreDetail.get("teacherScore"), 
                scoreDetail.get("finalScore"),
                scoreDetail.get("aiFeedback"));
        }
        
        return new com.example.aieduforge.dto.PracticeReviewDTO(record, questions, answers);
    }
    
    /**
     * 获取题目类型的中文描述
     */
    private String getQuestionTypeMessage(Integer questionType) {
        if (questionType == null) {
            return "";
        }
        return switch (questionType) {
            case 1 -> "单选题";
            case 2 -> "多选题";
            case 3 -> "填空题";
            case 4 -> "简答题";
            case 5 -> "编程题";
            case 6 -> "案例分析题";
            default -> "指定类型的";
        };
    }

    /**
     * 获取练习类型的中文描述
     */
    private String getPracticeTypeMessage(Integer practiceType) {
        return switch (practiceType) {
            case 1 -> "随机练习";
            case 2 -> "专项练习";
            case 3 -> "错题重做";
            default -> "练习";
        };
    }

    private void evaluateAnswer(ExamAnswer answer, ExamQuestion question) {
        String correctAnswer = question.getCorrectAnswer();
        String studentAnswer = answer.getStudentAnswer();
        
        boolean isCorrect = false;
        BigDecimal score = BigDecimal.ZERO;
        String feedback = "";
        String improvementSuggestion = "";
        
        if (studentAnswer != null && correctAnswer != null) {
            // 对于主观题，优先使用AI智能评分
            if (question.getQuestionType() >= 4) {
                try {
                    com.example.aieduforge.dto.AIGradingResponse aiResponse = callAIGrading(answer, question);
                    if (aiResponse != null && aiResponse.getSuccess()) {
                        isCorrect = aiResponse.getIsCorrect() == 1;
                        score = BigDecimal.valueOf(aiResponse.getScore() != null ? aiResponse.getScore() : 0.0);
                        feedback = aiResponse.getFeedback() != null ? aiResponse.getFeedback() : "AI评分完成";
                        improvementSuggestion = aiResponse.getImprovementSuggestion() != null ? 
                                aiResponse.getImprovementSuggestion() : "继续努力学习";
                        answer.setNeedsManualReview(aiResponse.getNeedsManualReview() != null && aiResponse.getNeedsManualReview() ? 1 : 0);
                        
                        // 添加相似度信息到反馈中
                        if (aiResponse.getSimilarity() != null) {
                            feedback += String.format(" (相似度: %.1f%%)", aiResponse.getSimilarity() * 100);
                        }
                        
                        log.info("AI智能评分成功 - 题目ID: {}, 学生ID: {}, 分数: {}, 相似度: {}, 置信度: {}", 
                            question.getId(), answer.getStudentId(), aiResponse.getScore(), 
                            aiResponse.getSimilarity(), aiResponse.getConfidence());
                    } else {
                        String errorMsg = aiResponse != null ? aiResponse.getErrorMessage() : "AI服务返回空响应";
                        log.warn("AI评分失败，使用传统算法: {}", errorMsg);
                        evaluateAnswerTraditional(answer, question);
                        return;
                    }
                } catch (Exception e) {
                    log.error("AI评分异常，使用传统算法: {}", e.getMessage(), e);
                    evaluateAnswerTraditional(answer, question);
                    return;
                }
            } else {
                // 客观题使用传统算法
                evaluateAnswerTraditional(answer, question);
                return;
            }
            switch (question.getQuestionType()) {
                case 1: // 单选题
                    isCorrect = evaluateSingleChoice(studentAnswer, correctAnswer);
                    feedback = isCorrect ? "答案正确！选择准确。" : "答案错误，正确答案是：" + correctAnswer;
                    improvementSuggestion = isCorrect ? "继续保持对基础概念的掌握。" : "建议复习相关知识点，加强对基础概念的理解。";
                    break;
                    
                case 2: // 多选题
                    isCorrect = evaluateMultipleChoice(studentAnswer, correctAnswer);
                    feedback = isCorrect ? "答案正确！多选题掌握良好。" : "答案错误，正确答案是：" + correctAnswer;
                    improvementSuggestion = isCorrect ? "多选题理解准确，继续保持。" : "多选题需要更仔细的分析，建议重点复习相关知识点的细节。";
                    break;
                    
                case 3: // 填空题
                    isCorrect = evaluateFillBlank(studentAnswer, correctAnswer);
                    feedback = isCorrect ? "答案正确！知识点掌握准确。" : "答案错误，正确答案是：" + correctAnswer;
                    improvementSuggestion = isCorrect ? "对关键概念掌握准确。" : "建议加强对关键术语和概念的记忆与理解。";
                    break;
                    
                case 4: // 简答题
                    // 使用智能评分算法，不再依赖简单的相似度对比
                    double intelligentScore = evaluateAnswerIntelligently(studentAnswer, correctAnswer, question.getQuestionType());
                    isCorrect = intelligentScore >= 0.6;
                    
                    // 生成更合理的反馈
                    if (intelligentScore >= 0.85) {
                        feedback = String.format("答案质量优秀（智能评分：%.1f%%），概念理解准确，表达清晰。", intelligentScore * 100);
                        improvementSuggestion = "回答质量很高，概念掌握准确，继续保持这种学习水平。";
                    } else if (intelligentScore >= 0.7) {
                        feedback = String.format("答案质量良好（智能评分：%.1f%%），基本概念正确，逻辑清楚。", intelligentScore * 100);
                        improvementSuggestion = "回答基本正确，概念理解到位，可以尝试补充更多细节或实例。";
                    } else if (intelligentScore >= 0.5) {
                        feedback = String.format("答案部分正确（智能评分：%.1f%%），有一定理解基础。", intelligentScore * 100);
                        improvementSuggestion = "概念理解有一定基础，建议加强表达的逻辑性和完整性。";
                    } else if (intelligentScore >= 0.3) {
                        feedback = String.format("答案需要改进（智能评分：%.1f%%），概念理解不够准确。", intelligentScore * 100);
                        improvementSuggestion = "建议重新学习相关概念，注意理解核心要点和逻辑关系。";
                    } else {
                        feedback = String.format("答案质量较低（智能评分：%.1f%%），需要系统学习。", intelligentScore * 100);
                        improvementSuggestion = "建议系统复习相关知识点，可以寻求老师帮助理解核心概念。";
                    }
                    
                    feedback += " 建议教师复核。";
                    answer.setNeedsManualReview(1);
                    break;
                    
                case 5: // 编程题
                    // 智能判断：如果学生答案不像代码，按简答题处理
                    if (isLikelyCode(studentAnswer)) {
                        // 真正的编程题
                        feedback = analyzeCodeAnswer(studentAnswer, correctAnswer);
                        improvementSuggestion = "编程题需要注意代码规范、逻辑正确性和效率。建议多练习编程基础。";
                        answer.setNeedsManualReview(1);
                    } else {
                        // 可能是题目类型设置错误，按简答题处理
                        double codeSimilarity = calculateTextSimilarity(studentAnswer, correctAnswer);
                        isCorrect = codeSimilarity >= 0.5; // 编程题相关的文字描述，降低要求
                        
                        if (codeSimilarity >= 0.7) {
                            feedback = String.format("答案相似度：%.1f%%，理解正确，表达清晰。", codeSimilarity * 100);
                            improvementSuggestion = "对编程概念理解准确，可以尝试结合实际代码示例来加深理解。";
                        } else if (codeSimilarity >= 0.5) {
                            feedback = String.format("答案相似度：%.1f%%，基本理解正确，可以更详细。", codeSimilarity * 100);
                            improvementSuggestion = "基本概念掌握良好，建议补充更多细节和实例。";
                        } else if (codeSimilarity >= 0.3) {
                            feedback = String.format("答案相似度：%.1f%%，部分正确，需要补充要点。", codeSimilarity * 100);
                            improvementSuggestion = "理解有一定基础，建议重新学习相关概念，注意关键要点。";
                        } else {
                            feedback = String.format("答案相似度：%.1f%%，需要加强学习。", codeSimilarity * 100);
                            improvementSuggestion = "建议系统学习相关知识点，可以寻求老师帮助。";
                        }
                        
                        feedback += " 建议教师复核。";
                        answer.setNeedsManualReview(1);
                    }
                    break;
                    
                case 6: // 案例分析题
                    double caseSimilarity = calculateTextSimilarity(studentAnswer, correctAnswer);
                    isCorrect = caseSimilarity >= 0.5; // 50%相似度认为基本正确
                    
                    if (caseSimilarity >= 0.7) {
                        feedback = String.format("答案相似度：%.1f%%，案例分析全面，思路清晰。", caseSimilarity * 100);
                        improvementSuggestion = "案例分析能力强，能够抓住关键问题。可以尝试从更多角度分析问题。";
                    } else if (caseSimilarity >= 0.5) {
                        feedback = String.format("答案相似度：%.1f%%，分析基本正确，可以更深入。", caseSimilarity * 100);
                        improvementSuggestion = "案例分析有一定深度，建议加强对问题本质的理解，提高分析的系统性。";
                    } else {
                        feedback = String.format("答案相似度：%.1f%%，分析不够深入，需要改进。", caseSimilarity * 100);
                        improvementSuggestion = "案例分析能力需要提升，建议多练习案例分析方法，学会从多个维度思考问题。";
                    }
                    
                    feedback += " 建议教师复核。";
                    answer.setNeedsManualReview(1);
                    break;
                    
                default:
                    feedback = "未知题目类型，需要教师评分";
                    improvementSuggestion = "请等待教师评分和反馈。";
                    answer.setNeedsManualReview(1);
            }
        } else {
            feedback = "答案为空或题目信息不完整";
            improvementSuggestion = "请确保完整回答问题，不要留空。";
        }
        
        // 设置评分结果
        answer.setIsCorrect(isCorrect ? 1 : 0);
        answer.setAiFeedback(feedback);
        answer.setImprovementSuggestion(improvementSuggestion);
        
        // 计算分数
        if (question.getScore() != null) {
            BigDecimal fullScore = BigDecimal.valueOf(question.getScore());
            
            switch (question.getQuestionType()) {
                case 1: // 单选题
                case 2: // 多选题
                case 3: // 填空题
                    // 客观题：答对得满分，答错得0分
                    score = isCorrect ? fullScore : BigDecimal.ZERO;
                    break;
                    
                case 4: // 简答题
                case 6: // 案例分析题
                    // 主观题：根据相似度给分，但更加宽松
                    double similarity = calculateTextSimilarity(studentAnswer, correctAnswer);
                    if (similarity >= 0.7) {
                        score = fullScore; // 70%以上相似度给满分
                    } else if (similarity >= 0.5) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.85)); // 50-70%给85%分数
                    } else if (similarity >= 0.3) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.7)); // 30-50%给70%分数
                    } else if (similarity >= 0.15) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.5)); // 15-30%给50%分数
                    } else if (similarity >= 0.05) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.3)); // 5-15%给30%分数
                    } else {
                        score = BigDecimal.ZERO; // 5%以下不给分
                    }
                    break;
                    
                case 5: // 编程题
                    // 编程题的评分逻辑
                    if (isLikelyCode(studentAnswer)) {
                        // 真正的编程题，暂时不给AI分数，等待教师评分
                        score = BigDecimal.ZERO;
                    } else {
                        // 可能是题目类型设置错误，按简答题处理，给予更宽松的评分
                        double codeSimilarity = calculateTextSimilarity(studentAnswer, correctAnswer);
                        if (codeSimilarity >= 0.6) {
                            score = fullScore.multiply(BigDecimal.valueOf(0.9)); // 60%以上给90%分数
                        } else if (codeSimilarity >= 0.4) {
                            score = fullScore.multiply(BigDecimal.valueOf(0.75)); // 40-60%给75%分数
                        } else if (codeSimilarity >= 0.2) {
                            score = fullScore.multiply(BigDecimal.valueOf(0.6)); // 20-40%给60%分数
                        } else if (codeSimilarity >= 0.1) {
                            score = fullScore.multiply(BigDecimal.valueOf(0.4)); // 10-20%给40%分数
                        } else {
                            score = BigDecimal.ZERO; // 10%以下不给分
                        }
                    }
                    break;
                    
                default:
                    score = BigDecimal.ZERO;
            }
        }
        
        // 设置AI分数和总分数
        answer.setAiScore(score); // 设置AI参考分数
        answer.setScore(score);   // 设置当前分数（教师评分前使用AI分数）
        answer.setEvaluationTime(LocalDateTime.now());
    }
    
    /**
     * 调用AI智能评分
     */
    private com.example.aieduforge.dto.AIGradingResponse callAIGrading(ExamAnswer answer, ExamQuestion question) {
        log.debug("调用AI评分 - 题目: {}, 学生答案: {}, 标准答案: {}", 
                question.getTitle(), 
                answer.getStudentAnswer() != null ? answer.getStudentAnswer().substring(0, Math.min(50, answer.getStudentAnswer().length())) + "..." : "null",
                question.getCorrectAnswer() != null ? question.getCorrectAnswer().substring(0, Math.min(50, question.getCorrectAnswer().length())) + "..." : "null");
        
        return aiService.intelligentGrading(
            question.getTitle(),
            question.getContent(),
            question.getQuestionType(),
            question.getCorrectAnswer(),
            answer.getStudentAnswer(),
            question.getScore() != null ? question.getScore().doubleValue() : 0.0,
            question.getDifficultyLevel()
        );
    }
    
    /**
     * 传统评分算法
     */
    private void evaluateAnswerTraditional(ExamAnswer answer, ExamQuestion question) {
        String correctAnswer = question.getCorrectAnswer();
        String studentAnswer = answer.getStudentAnswer();
        
        boolean isCorrect = false;
        BigDecimal score = BigDecimal.ZERO;
        String feedback = "";
        String improvementSuggestion = "";
        
        if (studentAnswer != null && correctAnswer != null) {
            switch (question.getQuestionType()) {
                case 1: // 单选题
                    isCorrect = evaluateSingleChoice(studentAnswer, correctAnswer);
                    feedback = isCorrect ? "答案正确！选择准确。" : "答案错误，正确答案是：" + correctAnswer;
                    improvementSuggestion = isCorrect ? "继续保持对基础概念的掌握。" : "建议复习相关知识点，加强对基础概念的理解。";
                    break;
                    
                case 2: // 多选题
                    isCorrect = evaluateMultipleChoice(studentAnswer, correctAnswer);
                    feedback = isCorrect ? "答案正确！多选题掌握良好。" : "答案错误，正确答案是：" + correctAnswer;
                    improvementSuggestion = isCorrect ? "多选题理解准确，继续保持。" : "多选题需要更仔细的分析，建议重点复习相关知识点的细节。";
                    break;
                    
                case 3: // 填空题
                    isCorrect = evaluateFillBlank(studentAnswer, correctAnswer);
                    feedback = isCorrect ? "答案正确！知识点掌握准确。" : "答案错误，正确答案是：" + correctAnswer;
                    improvementSuggestion = isCorrect ? "对关键概念掌握准确。" : "建议加强对关键术语和概念的记忆与理解。";
                    break;
                    
                default: // 主观题使用智能评分算法
                    // 使用多维度评估而不是简单的相似度对比
                    double conceptScore = evaluateConceptUnderstanding(studentAnswer, correctAnswer);
                    double logicScore = evaluateLogicClarity(studentAnswer);
                    double completenessScore = evaluateCompleteness(studentAnswer, correctAnswer);
                    
                    // 综合评分：概念理解40%，逻辑清晰30%，完整性30%
                    double overallScore = (conceptScore * 0.4 + logicScore * 0.3 + completenessScore * 0.3);
                    isCorrect = overallScore >= 0.6;
                    
                    // 生成更智能的反馈
                    if (overallScore >= 0.85) {
                        feedback = String.format("答案质量优秀（综合评分：%.1f%%），概念理解准确，逻辑清晰。", overallScore * 100);
                        improvementSuggestion = "回答质量很高，表达清晰，继续保持这种水平。";
                    } else if (overallScore >= 0.7) {
                        feedback = String.format("答案质量良好（综合评分：%.1f%%），基本概念正确。", overallScore * 100);
                        improvementSuggestion = "回答基本正确，可以尝试补充更多细节或实例。";
                    } else if (overallScore >= 0.5) {
                        feedback = String.format("答案部分正确（综合评分：%.1f%%），有一定理解基础。", overallScore * 100);
                        improvementSuggestion = "概念理解有一定基础，建议加强逻辑表达和内容完整性。";
                    } else if (overallScore >= 0.3) {
                        feedback = String.format("答案需要改进（综合评分：%.1f%%），概念理解不够准确。", overallScore * 100);
                        improvementSuggestion = "建议重新学习相关概念，注意理解核心要点。";
                    } else {
                        feedback = String.format("答案质量较低（综合评分：%.1f%%），需要系统学习。", overallScore * 100);
                        improvementSuggestion = "建议系统复习相关知识点，可以寻求老师帮助。";
                    }
                    
                    feedback += " 建议教师复核。";
                    answer.setNeedsManualReview(1);
                    break;
            }
        } else {
            feedback = "答案为空或题目信息不完整";
            improvementSuggestion = "请确保完整回答问题，不要留空。";
        }
        
        // 设置评分结果
        answer.setIsCorrect(isCorrect ? 1 : 0);
        answer.setAiFeedback(feedback);
        answer.setImprovementSuggestion(improvementSuggestion);
        
        // 计算分数
        if (question.getScore() != null) {
            BigDecimal fullScore = BigDecimal.valueOf(question.getScore());
            
            switch (question.getQuestionType()) {
                case 1, 2, 3: // 客观题
                    score = isCorrect ? fullScore : BigDecimal.ZERO;
                    break;
                default: // 主观题
                    double similarity = calculateTextSimilarity(studentAnswer, correctAnswer);
                    if (similarity >= 0.7) {
                        score = fullScore;
                    } else if (similarity >= 0.5) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.8));
                    } else if (similarity >= 0.3) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.6));
                    } else if (similarity >= 0.1) {
                        score = fullScore.multiply(BigDecimal.valueOf(0.3));
                    } else {
                        score = BigDecimal.ZERO;
                    }
                    break;
            }
        }
        
        // 设置AI分数和总分数
        answer.setAiScore(score);
        answer.setScore(score);
        answer.setEvaluationTime(LocalDateTime.now());
    }
    
    /**
     * 评估单选题答案
     */
    private boolean evaluateSingleChoice(String studentAnswer, String correctAnswer) {
        return studentAnswer.trim().equalsIgnoreCase(correctAnswer.trim());
    }
    
    /**
     * 评估多选题答案
     */
    private boolean evaluateMultipleChoice(String studentAnswer, String correctAnswer) {
        // 处理多选题答案，如 "A,B,C" 格式
        String[] studentOptions = studentAnswer.replaceAll("\\s+", "").split(",");
        String[] correctOptions = correctAnswer.replaceAll("\\s+", "").split(",");
        
        if (studentOptions.length != correctOptions.length) {
            return false;
        }
        
        Arrays.sort(studentOptions);
        Arrays.sort(correctOptions);
        
        return Arrays.equals(studentOptions, correctOptions);
    }
    
    /**
     * 评估填空题答案
     */
    private boolean evaluateFillBlank(String studentAnswer, String correctAnswer) {
        // 处理多个填空，用|||分隔
        if (correctAnswer.contains("|||")) {
            String[] studentBlanks = studentAnswer.split("\\|\\|\\|");
            String[] correctBlanks = correctAnswer.split("\\|\\|\\|");
            
            if (studentBlanks.length != correctBlanks.length) {
                return false;
            }
            
            for (int i = 0; i < correctBlanks.length; i++) {
                if (!studentBlanks[i].trim().equalsIgnoreCase(correctBlanks[i].trim())) {
                    return false;
                }
            }
            return true;
        } else {
            // 单个填空
            return studentAnswer.trim().equalsIgnoreCase(correctAnswer.trim());
        }
    }
    
    /**
     * 计算文本相似度（改进的中文支持算法）
     */
    private double calculateTextSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) {
            return 0.0;
        }
        
        // 预处理文本
        String processedText1 = preprocessText(text1);
        String processedText2 = preprocessText(text2);
        
        if (processedText1.isEmpty() || processedText2.isEmpty()) {
            return 0.0;
        }
        
        // 如果文本完全相同，返回100%相似度
        if (processedText1.equals(processedText2)) {
            return 1.0;
        }
        
        // 检查关键词匹配度（对专业术语更友好）
        double keywordSimilarity = calculateKeywordSimilarity(text1, text2);
        
        // 使用多种算法计算相似度
        double charSimilarity = calculateCharacterSimilarity(processedText1, processedText2);
        double wordSimilarity = calculateWordSimilarity(processedText1, processedText2);
        double substringSimilarity = calculateSubstringSimilarity(processedText1, processedText2);
        
        // 加权平均：关键词相似度30%，字符相似度25%，词汇相似度30%，子串相似度15%
        double finalSimilarity = (keywordSimilarity * 0.3 + charSimilarity * 0.25 + wordSimilarity * 0.3 + substringSimilarity * 0.15);
        
        // 如果关键词匹配度很高，给予额外加分
        if (keywordSimilarity > 0.7) {
            finalSimilarity = Math.min(1.0, finalSimilarity + 0.1);
        }
        
        return finalSimilarity;
    }
    
    /**
     * 计算关键词相似度（专门处理专业术语）
     */
    private double calculateKeywordSimilarity(String text1, String text2) {
        // 提取关键词（长度大于1的词汇，去除常见停用词）
        Set<String> keywords1 = extractKeywords(text1);
        Set<String> keywords2 = extractKeywords(text2);
        
        if (keywords1.isEmpty() && keywords2.isEmpty()) {
            return 1.0;
        }
        
        if (keywords1.isEmpty() || keywords2.isEmpty()) {
            return 0.0;
        }
        
        // 计算交集和并集
        Set<String> intersection = new HashSet<>(keywords1);
        intersection.retainAll(keywords2);
        
        Set<String> union = new HashSet<>(keywords1);
        union.addAll(keywords2);
        
        return (double) intersection.size() / union.size();
    }
    
    /**
     * 提取关键词
     */
    private Set<String> extractKeywords(String text) {
        Set<String> keywords = new HashSet<>();
        
        // 常见停用词
        Set<String> stopWords = Set.of("的", "是", "在", "有", "和", "与", "或", "但", "而", "了", "着", "过", 
                                     "将", "被", "把", "从", "到", "为", "由", "以", "及", "等", "如", "像", 
                                     "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "个", "种", "类");
        
        // 分词（简单的基于标点和空格的分词）
        String[] words = text.replaceAll("[\\p{Punct}\\s]+", " ").trim().split("\\s+");
        
        for (String word : words) {
            word = word.trim();
            // 保留长度大于1且不是停用词的词汇
            if (word.length() > 1 && !stopWords.contains(word)) {
                keywords.add(word.toLowerCase());
            }
        }
        
        return keywords;
    }
    
    /**
     * 预处理文本
     */
    private String preprocessText(String text) {
        if (text == null) return "";
        
        // 转换为小写，移除多余的空白字符和标点符号
        return text.toLowerCase()
                   .replaceAll("[\\s\\p{Punct}]+", " ")
                   .trim();
    }
    
    /**
     * 计算字符级相似度（适合中文）
     */
    private double calculateCharacterSimilarity(String text1, String text2) {
        Set<Character> chars1 = new HashSet<>();
        Set<Character> chars2 = new HashSet<>();
        
        for (char c : text1.toCharArray()) {
            if (!Character.isWhitespace(c)) {
                chars1.add(c);
            }
        }
        
        for (char c : text2.toCharArray()) {
            if (!Character.isWhitespace(c)) {
                chars2.add(c);
            }
        }
        
        Set<Character> intersection = new HashSet<>(chars1);
        intersection.retainAll(chars2);
        
        Set<Character> union = new HashSet<>(chars1);
        union.addAll(chars2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    /**
     * 计算词汇级相似度
     */
    private double calculateWordSimilarity(String text1, String text2) {
        String[] words1 = text1.split("\\s+");
        String[] words2 = text2.split("\\s+");
        
        Set<String> set1 = new HashSet<>(Arrays.asList(words1));
        Set<String> set2 = new HashSet<>(Arrays.asList(words2));
        
        // 移除空字符串
        set1.remove("");
        set2.remove("");
        
        if (set1.isEmpty() && set2.isEmpty()) {
            return 1.0;
        }
        
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        
        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    /**
     * 判断学生答案是否像代码
     */
    private boolean isLikelyCode(String studentAnswer) {
        if (studentAnswer == null || studentAnswer.trim().isEmpty()) {
            return false;
        }
        
        String answer = studentAnswer.trim();
        
        // 检查代码特征
        int codeIndicators = 0;
        
        // 检查编程语言关键字
        String[] keywords = {"function", "def", "class", "public", "private", "static", "void", "int", "String", 
                           "return", "if", "else", "for", "while", "import", "include", "#include", "var", "let", "const"};
        for (String keyword : keywords) {
            if (answer.contains(keyword)) {
                codeIndicators++;
                break; // 找到一个就够了
            }
        }
        
        // 检查代码符号
        if (answer.contains("{") && answer.contains("}")) codeIndicators++;
        if (answer.contains("(") && answer.contains(")")) codeIndicators++;
        if (answer.contains(";")) codeIndicators++;
        if (answer.contains("//") || answer.contains("/*") || answer.contains("#")) codeIndicators++;
        if (answer.contains("=") && !answer.contains("==")) codeIndicators++;
        
        // 检查缩进结构（代码通常有缩进）
        String[] lines = answer.split("\n");
        int indentedLines = 0;
        for (String line : lines) {
            if (line.startsWith("    ") || line.startsWith("\t")) {
                indentedLines++;
            }
        }
        if (indentedLines > 1) codeIndicators++;
        
        // 如果有2个或以上的代码特征，认为是代码
        return codeIndicators >= 2;
    }
    
    /**
     * 分析编程题答案
     */
    private String analyzeCodeAnswer(String studentAnswer, String correctAnswer) {
        if (studentAnswer == null || studentAnswer.trim().isEmpty()) {
            return "编程题未提交代码，等待教师评分";
        }
        
        // 基础代码分析
        String code = studentAnswer.trim();
        StringBuilder analysis = new StringBuilder();
        
        // 检查代码长度
        if (code.length() < 10) {
            analysis.append("代码过于简短，可能不完整。");
        } else if (code.length() > 1000) {
            analysis.append("代码较长，注意代码简洁性。");
        } else {
            analysis.append("代码长度适中。");
        }
        
        // 检查基本语法结构
        if (code.contains("{") && code.contains("}")) {
            analysis.append("包含代码块结构。");
        }
        
        if (code.contains("function") || code.contains("def") || code.contains("public") || code.contains("private")) {
            analysis.append("包含函数定义。");
        }
        
        // 检查注释
        if (code.contains("//") || code.contains("/*") || code.contains("#")) {
            analysis.append("包含代码注释，良好的编程习惯。");
        }
        
        analysis.append(" 具体逻辑和正确性需要教师人工评分。");
        
        return analysis.toString();
    }
    
    /**
     * 计算子串相似度（检查关键短语）
     */
    private double calculateSubstringSimilarity(String text1, String text2) {
        // 生成2-3字符的子串
        Set<String> substrings1 = generateSubstrings(text1, 2, 3);
        Set<String> substrings2 = generateSubstrings(text2, 2, 3);
        
        if (substrings1.isEmpty() && substrings2.isEmpty()) {
            return 1.0;
        }
        
        Set<String> intersection = new HashSet<>(substrings1);
        intersection.retainAll(substrings2);
        
        Set<String> union = new HashSet<>(substrings1);
        union.addAll(substrings2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    /**
     * 生成指定长度范围的子串
     */
    private Set<String> generateSubstrings(String text, int minLen, int maxLen) {
        Set<String> substrings = new HashSet<>();
        text = text.replaceAll("\\s+", ""); // 移除空格
        
        for (int len = minLen; len <= maxLen && len <= text.length(); len++) {
            for (int i = 0; i <= text.length() - len; i++) {
                String substring = text.substring(i, i + len);
                if (!substring.trim().isEmpty()) {
                    substrings.add(substring);
                }
            }
        }
        
        return substrings;
    }
    
    /**
     * 构建评分详情对象
     */
    private Map<String, Object> buildScoreDetail(ExamQuestion question, ExamAnswer answer) {
        Map<String, Object> scoreDetail = new HashMap<>();
        scoreDetail.put("questionId", question.getId());
        scoreDetail.put("questionTitle", question.getTitle());
        scoreDetail.put("questionType", question.getQuestionType());
        scoreDetail.put("questionText", question.getContent());
        scoreDetail.put("correctAnswer", question.getCorrectAnswer());
        
        if (answer != null) {
            scoreDetail.put("studentAnswer", answer.getStudentAnswer());
            scoreDetail.put("aiScore", answer.getAiScore());
            scoreDetail.put("teacherScore", answer.getTeacherScore());
            scoreDetail.put("finalScore", answer.getScore());
            scoreDetail.put("totalScore", question.getScore());
            scoreDetail.put("aiFeedback", answer.getAiFeedback());
            scoreDetail.put("teacherFeedback", answer.getTeacherFeedback());
            scoreDetail.put("gradingStatus", answer.getGradingStatus());
            scoreDetail.put("needsManualReview", answer.getNeedsManualReview());
            scoreDetail.put("isCorrect", answer.getIsCorrect());
        } else {
            scoreDetail.put("studentAnswer", "");
            scoreDetail.put("aiScore", BigDecimal.ZERO);
            scoreDetail.put("teacherScore", null);
            scoreDetail.put("finalScore", BigDecimal.ZERO);
            scoreDetail.put("totalScore", question.getScore());
            scoreDetail.put("aiFeedback", "未作答");
            scoreDetail.put("teacherFeedback", null);
            scoreDetail.put("gradingStatus", 0);
            scoreDetail.put("needsManualReview", 0);
            scoreDetail.put("isCorrect", 0);
        }
        
        return scoreDetail;
    }

    /**
     * 智能评分算法 - 不依赖简单的相似度对比
     */
    private double evaluateAnswerIntelligently(String studentAnswer, String correctAnswer, Integer questionType) {
        if (studentAnswer == null || studentAnswer.trim().isEmpty()) {
            return 0.0;
        }
        
        // 多维度评估
        double conceptScore = evaluateConceptUnderstanding(studentAnswer, correctAnswer);
        double logicScore = evaluateLogicClarity(studentAnswer);
        double completenessScore = evaluateCompleteness(studentAnswer, correctAnswer);
        double creativityScore = evaluateCreativity(studentAnswer, correctAnswer);
        
        // 根据题目类型调整权重
        switch (questionType) {
            case 4: // 简答题：概念40%，逻辑30%，完整性20%，创新10%
                return conceptScore * 0.4 + logicScore * 0.3 + completenessScore * 0.2 + creativityScore * 0.1;
            case 5: // 编程题：概念35%，逻辑40%，完整性20%，创新5%
                return conceptScore * 0.35 + logicScore * 0.4 + completenessScore * 0.2 + creativityScore * 0.05;
            case 6: // 案例分析题：概念30%，逻辑35%，完整性25%，创新10%
                return conceptScore * 0.3 + logicScore * 0.35 + completenessScore * 0.25 + creativityScore * 0.1;
            default:
                return conceptScore * 0.4 + logicScore * 0.3 + completenessScore * 0.3;
        }
    }
    
    /**
     * 评估概念理解程度 - 不仅看相似度，更看理解正确性
     */
    private double evaluateConceptUnderstanding(String studentAnswer, String correctAnswer) {
        if (studentAnswer == null || correctAnswer == null) return 0.0;
        
        // 提取关键概念
        Set<String> studentConcepts = extractImportantConcepts(studentAnswer);
        Set<String> correctConcepts = extractImportantConcepts(correctAnswer);
        
        // 概念匹配度
        double conceptMatch = calculateConceptMatch(studentConcepts, correctConcepts);
        
        // 概念表达质量
        double expressionQuality = evaluateConceptExpression(studentAnswer);
        
        // 综合评分：匹配度70%，表达质量30%
        return Math.min(1.0, conceptMatch * 0.7 + expressionQuality * 0.3);
    }
    
    /**
     * 评估逻辑清晰度
     */
    private double evaluateLogicClarity(String studentAnswer) {
        if (studentAnswer == null || studentAnswer.trim().isEmpty()) return 0.0;
        
        double score = 0.3; // 基础分
        String answer = studentAnswer.toLowerCase();
        
        // 检查逻辑连接词
        if (answer.contains("因为") || answer.contains("由于") || answer.contains("因此") || 
            answer.contains("所以") || answer.contains("因而")) {
            score += 0.2; // 有因果逻辑
        }
        
        // 检查结构化表达
        if (answer.contains("首先") || answer.contains("其次") || answer.contains("最后") ||
            answer.contains("1.") || answer.contains("2.") || answer.contains("3.")) {
            score += 0.2; // 有序列表达
        }
        
        // 检查举例说明
        if (answer.contains("例如") || answer.contains("比如") || answer.contains("举例") ||
            answer.contains("如") || answer.contains("像")) {
            score += 0.15; // 有实例支撑
        }
        
        // 检查对比分析
        if (answer.contains("相比") || answer.contains("对比") || answer.contains("区别") ||
            answer.contains("不同") || answer.contains("相同")) {
            score += 0.1; // 有对比分析
        }
        
        // 检查句子完整性和长度合理性
        String[] sentences = answer.split("[。！？；]");
        if (sentences.length >= 2 && sentences.length <= 10) {
            score += 0.05; // 句子数量合理
        }
        
        return Math.min(1.0, score);
    }
    
    /**
     * 评估内容完整性 - 不仅看长度，更看要点覆盖
     */
    private double evaluateCompleteness(String studentAnswer, String correctAnswer) {
        if (studentAnswer == null || correctAnswer == null) return 0.0;
        
        // 要点覆盖度
        Set<String> studentPoints = extractKeyPoints(studentAnswer);
        Set<String> correctPoints = extractKeyPoints(correctAnswer);
        
        double pointCoverage = correctPoints.isEmpty() ? 0.5 : 
            (double) studentPoints.size() / Math.max(correctPoints.size(), studentPoints.size());
        
        // 内容充实度（基于有效词汇数量）
        int effectiveWords = countEffectiveWords(studentAnswer);
        double contentRichness = Math.min(1.0, effectiveWords / 20.0); // 20个有效词汇为满分
        
        // 综合评估：要点覆盖70%，内容充实30%
        return Math.min(1.0, pointCoverage * 0.7 + contentRichness * 0.3);
    }
    
    /**
     * 评估创新性和独特见解
     */
    private double evaluateCreativity(String studentAnswer, String correctAnswer) {
        if (studentAnswer == null || correctAnswer == null) return 0.5;
        
        double score = 0.5; // 基础分
        String answer = studentAnswer.toLowerCase();
        
        // 检查独特表达
        if (!studentAnswer.equals(correctAnswer) && 
            calculateTextSimilarity(studentAnswer, correctAnswer) < 0.8) {
            score += 0.2; // 有独特表达
        }
        
        // 检查深入思考的词汇
        if (answer.contains("深入") || answer.contains("进一步") || answer.contains("更重要的是") ||
            answer.contains("值得注意") || answer.contains("需要强调")) {
            score += 0.15; // 有深入思考
        }
        
        // 检查实际应用
        if (answer.contains("实际") || answer.contains("应用") || answer.contains("实践") ||
            answer.contains("现实") || answer.contains("生活中")) {
            score += 0.15; // 联系实际
        }
        
        // 检查问题意识
        if (answer.contains("问题") || answer.contains("挑战") || answer.contains("难点") ||
            answer.contains("注意") || answer.contains("避免")) {
            score += 0.1; // 有问题意识
        }
        
        return Math.min(1.0, score);
    }
    
    /**
     * 提取重要概念
     */
    private Set<String> extractImportantConcepts(String text) {
        Set<String> concepts = new HashSet<>();
        if (text == null) return concepts;
        
        // 专业术语模式匹配
        String[] techTerms = text.split("[，。；！？\\s]+");
        for (String term : techTerms) {
            term = term.trim();
            if (term.length() >= 2 && term.length() <= 10) {
                // 过滤掉常见词汇，保留可能的专业术语
                if (!isCommonWord(term)) {
                    concepts.add(term.toLowerCase());
                }
            }
        }
        
        return concepts;
    }
    
    /**
     * 计算概念匹配度
     */
    private double calculateConceptMatch(Set<String> studentConcepts, Set<String> correctConcepts) {
        if (correctConcepts.isEmpty()) return 0.8; // 如果标准答案没有明确概念，给较高分
        
        Set<String> intersection = new HashSet<>(studentConcepts);
        intersection.retainAll(correctConcepts);
        
        // 不仅看交集，也看学生是否有合理的概念表达
        double directMatch = (double) intersection.size() / correctConcepts.size();
        double conceptRichness = Math.min(1.0, (double) studentConcepts.size() / correctConcepts.size());
        
        return Math.min(1.0, directMatch * 0.7 + conceptRichness * 0.3);
    }
    
    /**
     * 评估概念表达质量
     */
    private double evaluateConceptExpression(String studentAnswer) {
        if (studentAnswer == null) return 0.0;
        
        double score = 0.5; // 基础分
        String answer = studentAnswer.toLowerCase();
        
        // 检查定义性表达
        if (answer.contains("是指") || answer.contains("指的是") || answer.contains("定义为") ||
            answer.contains("意思是") || answer.contains("就是")) {
            score += 0.2; // 有定义表达
        }
        
        // 检查解释性表达
        if (answer.contains("也就是说") || answer.contains("换句话说") || answer.contains("即") ||
            answer.contains("具体来说") || answer.contains("简单来说")) {
            score += 0.15; // 有解释表达
        }
        
        // 检查专业性
        if (hasSpecializedTerms(answer)) {
            score += 0.15; // 使用专业术语
        }
        
        return Math.min(1.0, score);
    }
    
    /**
     * 提取关键要点
     */
    private Set<String> extractKeyPoints(String text) {
        Set<String> points = new HashSet<>();
        if (text == null) return points;
        
        // 按句子分割，每个句子可能是一个要点
        String[] sentences = text.split("[。！？；]");
        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.length() > 5) { // 过滤太短的句子
                points.add(sentence.toLowerCase());
            }
        }
        
        return points;
    }
    
    /**
     * 计算有效词汇数量
     */
    private int countEffectiveWords(String text) {
        if (text == null) return 0;
        
        String[] words = text.replaceAll("[\\p{Punct}]+", " ").trim().split("\\s+");
        int count = 0;
        
        for (String word : words) {
            if (word.length() > 1 && !isCommonWord(word)) {
                count++;
            }
        }
        
        return count;
    }
    
    /**
     * 判断是否为常见词汇
     */
    private boolean isCommonWord(String word) {
        Set<String> commonWords = Set.of(
            "的", "是", "在", "有", "和", "与", "或", "但", "而", "了", "着", "过",
            "将", "被", "把", "从", "到", "为", "由", "以", "及", "等", "如", "像",
            "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "个", "种", "类",
            "这", "那", "些", "此", "其", "他", "她", "它", "我", "你", "们", "自己",
            "可以", "能够", "应该", "需要", "必须", "可能", "也许", "或许", "当然",
            "非常", "很", "比较", "相当", "十分", "特别", "尤其", "更加", "最"
        );
        
        return commonWords.contains(word.toLowerCase());
    }
    
    /**
     * 检查是否包含专业术语
     */
    private boolean hasSpecializedTerms(String text) {
        // 这里可以根据具体学科添加专业术语检测
        // 目前使用简单的启发式方法
        return text.length() > 20 && 
               (text.contains("算法") || text.contains("数据结构") || text.contains("函数") ||
                text.contains("变量") || text.contains("对象") || text.contains("类") ||
                text.contains("方法") || text.contains("属性") || text.contains("接口") ||
                text.contains("继承") || text.contains("封装") || text.contains("多态"));
    }
}