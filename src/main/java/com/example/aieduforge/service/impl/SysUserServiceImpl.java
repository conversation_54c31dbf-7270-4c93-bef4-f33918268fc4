package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.aieduforge.config.JwtProperties;
import com.example.aieduforge.dto.LoginRequest;
import com.example.aieduforge.dto.LoginResponse;
import com.example.aieduforge.entity.SysRole;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.exception.BusinessException;
import com.example.aieduforge.mapper.SysRoleMapper;
import com.example.aieduforge.mapper.SysUserMapper;
import com.example.aieduforge.service.SysUserService;
import com.example.aieduforge.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User Service Implementation
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private SysRoleMapper sysRoleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private JwtProperties jwtProperties;

    @Override
    @Transactional
    public LoginResponse login(LoginRequest loginRequest) {
        // Find user by username
        SysUser user = findByUsername(loginRequest.getUsername());
        if (user == null) {
            throw new BusinessException("用户名或密码错误");
        }

        // Check user status first to give precise feedback
        Integer status = user.getStatus();
        if (status != null && status == 0) {
            throw new BusinessException("该账号已被禁用，请联系管理员");
        }

        // Verify password
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }
        
        // Get user roles
        List<SysRole> roles = sysRoleMapper.findRolesByUserId(user.getId());
        String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
        
        // Generate JWT token
        String token = jwtUtils.generateToken(user.getUsername(), user.getId(), roleCode);
        
        // Update last login time
        updateLastLoginTime(user.getId());
        
        return new LoginResponse(token, user.getId(), user.getUsername(), 
                user.getRealName(), roleCode, jwtProperties.getExpiration());
    }

    @Override
    public SysUser findByUsername(String username) {
        return sysUserMapper.findByUsername(username);
    }

    @Override
    @Transactional
    public SysUser register(SysUser user) {
        // Check if username exists
        if (findByUsername(user.getUsername()) != null) {
            throw new BusinessException("Username already exists");
        }
        
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setStatus(1);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        // Save user
        if (save(user)) {
            return user;
        }
        
        throw new BusinessException("Failed to register user");
    }

    @Override
    public void updateLastLoginTime(Long userId) {
        sysUserMapper.updateLastLoginTime(userId);
    }

    @Override
    @Transactional
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        SysUser user = getById(userId);
        if (user == null) {
            throw new BusinessException("User not found");
        }
        
        // Verify old password
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("Invalid old password");
        }
        
        // Update password
        user.setPassword(passwordEncoder.encode(newPassword));
        return updateById(user);
    }

    @Override
    public SysUser getCurrentUser() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        SysUser user = findByUsername(username);
        if (user == null) {
            throw new BusinessException("Current user not found");
        }
        
        // Get user roles
        List<SysRole> roles = sysRoleMapper.findRolesByUserId(user.getId());
        String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
        
        // Set role information
        user.setRole(roleCode);
        user.setPassword(null); // Don't return password
        return user;
    }

    @Override
    @Transactional
    public SysUser updateUserWithRole(Long userId, SysUser user) {
        // Update user basic info
        SysUser existingUser = getById(userId);
        if (existingUser == null) {
            throw new BusinessException("User not found");
        }
        
        // Update basic user information
        existingUser.setRealName(user.getRealName());
        existingUser.setEmail(user.getEmail());
        existingUser.setPhone(user.getPhone());
        existingUser.setUpdateTime(LocalDateTime.now());
        
        if (!updateById(existingUser)) {
            throw new BusinessException("Failed to update user");
        }
        
        // Update user role if provided
        if (user.getRole() != null && !user.getRole().isEmpty()) {
            // Find role by code
            SysRole role = sysRoleMapper.findByRoleCode(user.getRole());
            if (role != null) {
                // Delete existing role associations
                sysUserMapper.deleteUserRole(userId);
                // Add new role association
                sysUserMapper.updateUserRole(userId, role.getId());
            }
        }
        
        // Return updated user with role info
        List<SysRole> roles = sysRoleMapper.findRolesByUserId(userId);
        String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
        existingUser.setRole(roleCode);
        existingUser.setPassword(null);
        
        return existingUser;
    }

    @Override
    @Transactional
    public SysUser updateProfile(SysUser user) {
        SysUser currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("Current user not found");
        }
        
        // Update profile information
        currentUser.setRealName(user.getRealName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhone(user.getPhone());
        currentUser.setAvatar(user.getAvatar());
        currentUser.setUpdateTime(LocalDateTime.now());
        
        if (!updateById(currentUser)) {
            throw new BusinessException("Failed to update profile");
        }
        
        // Return updated user with role info
        List<SysRole> roles = sysRoleMapper.findRolesByUserId(currentUser.getId());
        String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
        currentUser.setRole(roleCode);
        currentUser.setPassword(null);
        
        return currentUser;
    }

    @Override
    @Transactional
    public void changePassword(com.example.aieduforge.dto.ChangePasswordRequest request) {
        SysUser currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new BusinessException("Current user not found");
        }
        
        // Get full user info with password
        SysUser userWithPassword = getById(currentUser.getId());
        
        // Verify old password
        if (!passwordEncoder.matches(request.getOldPassword(), userWithPassword.getPassword())) {
            throw new BusinessException("当前密码不正确");
        }
        
        // Update password
        userWithPassword.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userWithPassword.setUpdateTime(LocalDateTime.now());
        
        if (!updateById(userWithPassword)) {
            throw new BusinessException("密码修改失败");
        }
    }

    @Override
    @Transactional
    public String uploadAvatar(org.springframework.web.multipart.MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }
        
        // Check file type
        String contentType = file.getContentType();
        if (contentType == null || (!contentType.startsWith("image/"))) {
            throw new BusinessException("只能上传图片文件");
        }
        
        // Check file size (2MB)
        if (file.getSize() > 2 * 1024 * 1024) {
            throw new BusinessException("文件大小不能超过2MB");
        }
        
        try {
            // Generate unique filename
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null ? 
                originalFilename.substring(originalFilename.lastIndexOf(".")) : ".jpg";
            String filename = "avatar_" + System.currentTimeMillis() + extension;
            
            // Get project root directory
            String projectRoot = System.getProperty("user.dir");
            java.io.File uploadDir = new java.io.File(projectRoot, "uploads/avatars");
            
            // Create upload directory if not exists
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    throw new BusinessException("无法创建上传目录");
                }
            }
            
            // Save file
            java.io.File targetFile = new java.io.File(uploadDir, filename);
            file.transferTo(targetFile);
            
            // Return relative path
            String avatarUrl = "/uploads/avatars/" + filename;
            
            // Update user avatar
            SysUser currentUser = getCurrentUser();
            currentUser.setAvatar(avatarUrl);
            currentUser.setUpdateTime(LocalDateTime.now());
            updateById(currentUser);
            
            return avatarUrl;
            
        } catch (Exception e) {
            log.error("Failed to upload avatar", e);
            throw new BusinessException("头像上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<SysUser> getTeachers() {
        // Query users with TEACHER role
        List<SysUser> teachers = sysUserMapper.getUsersByRole("TEACHER");
        
        // Remove passwords from response
        teachers.forEach(teacher -> {
            teacher.setPassword(null);
            teacher.setRole("TEACHER");
        });
        
        return teachers;
    }
}
