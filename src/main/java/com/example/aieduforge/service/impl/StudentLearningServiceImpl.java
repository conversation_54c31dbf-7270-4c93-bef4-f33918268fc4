package com.example.aieduforge.service.impl;

import com.example.aieduforge.entity.AiAnswer;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.StudentQuestion;
import com.example.aieduforge.exception.BusinessException;
import com.example.aieduforge.mapper.AiAnswerMapper;
import com.example.aieduforge.mapper.StudentQuestionMapper;
import com.example.aieduforge.service.AIService;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.StudentLearningService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Student Learning Service Implementation
 */
@Slf4j
@Service
public class StudentLearningServiceImpl implements StudentLearningService {

    @Autowired
    private StudentQuestionMapper studentQuestionMapper;
    
    @Autowired
    private AiAnswerMapper aiAnswerMapper;
    
    @Autowired
    private AIService aiService;
    
    @Autowired
    private CourseService courseService;
    
    @Autowired
    private com.example.aieduforge.service.TeachingContentService teachingContentService;

    @Override
    @Transactional
    public AiAnswer askQuestion(StudentQuestion question) {
        // Validate course exists
        if (question.getCourseId() != null) {
            Course course = courseService.getById(question.getCourseId());
            if (course == null) {
                throw new BusinessException("Course not found");
            }
        }
        
        // Save student question
        question.setStatus(1); // 待回答
        question.setCreateTime(LocalDateTime.now());
        question.setUpdateTime(LocalDateTime.now());
        
        if (studentQuestionMapper.insert(question) <= 0) {
            throw new BusinessException("Failed to save question");
        }
        
        // Generate AI answer
        long startTime = System.currentTimeMillis();
        String courseContext = "";
        if (question.getCourseId() != null) {
            Course course = courseService.getById(question.getCourseId());
            if (course != null) {
                courseContext = "课程：" + course.getCourseName() + "\n课程描述：" + 
                    (course.getDescription() != null ? course.getDescription() : "无描述") + "\n";
            }
        }
        
        String contextInfo = question.getContextInfo() != null ? question.getContextInfo() : "";
        
        // 构建更智能的提示词，让AI基于教学内容回答问题而不是直接输出内容
        String intelligentPrompt = buildIntelligentPrompt(courseContext, contextInfo, question.getQuestionContent());
        
        log.debug("Generated intelligent prompt for question: {}", question.getId());
        log.debug("Prompt length: {} characters", intelligentPrompt.length());
        
        String aiResponse;
        try {
            aiResponse = aiService.generateTeachingContent(intelligentPrompt, "学生提问", "问题解答");
            if (aiResponse == null || aiResponse.trim().isEmpty()) {
                aiResponse = "抱歉，我暂时无法回答您的问题。请尝试重新表述您的问题，或者联系老师获取帮助。";
            }
        } catch (Exception e) {
            log.error("Failed to generate AI response for question: {}", question.getId(), e);
            aiResponse = "抱歉，AI服务暂时不可用。请稍后重试或联系老师获取帮助。";
        }
        
        long endTime = System.currentTimeMillis();
        
        // Save AI answer
        AiAnswer aiAnswer = new AiAnswer();
        aiAnswer.setQuestionId(question.getId());
        aiAnswer.setAnswerContent(aiResponse);
        
        // 根据回答内容调整置信度
        BigDecimal confidence;
        if (aiResponse.contains("AI服务暂时不可用") || aiResponse.contains("暂时无法回答")) {
            confidence = BigDecimal.valueOf(0.2); // 很低置信度
        } else if (aiResponse.contains("在我们当前学习的内容中没有涉及") || 
                   aiResponse.contains("这个问题在当前学习的内容中没有涉及")) {
            confidence = BigDecimal.valueOf(0.5); // 中等置信度
        } else if (aiResponse.contains("向老师请教") || aiResponse.contains("建议咨询老师")) {
            confidence = BigDecimal.valueOf(0.6); // 中等偏高置信度
        } else {
            confidence = BigDecimal.valueOf(0.8); // 高置信度
        }
        
        aiAnswer.setConfidenceScore(confidence);
        aiAnswer.setAiModel("gemma2:1b");
        aiAnswer.setGenerationTime((int) (endTime - startTime));
        aiAnswer.setCreateTime(LocalDateTime.now());
        
        if (aiAnswerMapper.insert(aiAnswer) <= 0) {
            throw new BusinessException("Failed to save AI answer");
        }
        
        // Update question status to answered
        question.setStatus(2); // 已回答
        question.setUpdateTime(LocalDateTime.now());
        studentQuestionMapper.updateById(question);
        
        log.info("AI answer generated for question: {} with confidence: {}", 
                question.getId(), confidence);
        return aiAnswer;
    }

    @Override
    public List<StudentQuestion> getQuestionHistory(Long studentId, Long courseId) {
        if (courseId != null) {
            return studentQuestionMapper.findByStudentAndCourse(studentId, courseId);
        } else {
            return studentQuestionMapper.findByStudentId(studentId);
        }
    }

    @Override
    public AiAnswer getAiAnswer(Long questionId) {
        return aiAnswerMapper.findByQuestionId(questionId);
    }

    @Override
    @Transactional
    public boolean rateAnswer(Long answerId, Integer rating, String comment) {
        if (rating < 1 || rating > 5) {
            throw new BusinessException("Rating must be between 1 and 5");
        }
        
        int result = aiAnswerMapper.updateFeedback(answerId, rating, comment);
        if (result > 0) {
            log.info("AI answer rated: {} with score: {}", answerId, rating);
            return true;
        }
        return false;
    }

    @Override
    public List<StudentQuestion> getRecentQuestions(Long studentId, Integer limit) {
        return studentQuestionMapper.findRecentQuestionsByStudent(studentId, limit);
    }

    @Override
    @Transactional
    public boolean updateQuestionStatus(Long questionId, Integer status, Long studentId) {
        StudentQuestion question = studentQuestionMapper.selectById(questionId);
        if (question == null) {
            throw new BusinessException("Question not found");
        }
        
        if (!question.getStudentId().equals(studentId)) {
            throw new BusinessException("You don't have permission to update this question");
        }
        
        question.setStatus(status);
        question.setUpdateTime(LocalDateTime.now());
        
        int result = studentQuestionMapper.updateById(question);
        return result > 0;
    }
    
    /**
     * 构建智能提示词，强制AI用对话方式回答而不是教学大纲
     */
    private String buildIntelligentPrompt(String courseContext, String teachingContent, String studentQuestion) {
        StringBuilder prompt = new StringBuilder();
        
        // 极其严格的角色设定
        prompt.append("你是学习助手，学生问你问题，你要像朋友一样回答。\n");
        prompt.append("严禁写教学大纲！严禁分章节！严禁列时间！严禁写课程计划！\n");
        prompt.append("只能用聊天的语气直接回答问题！\n\n");
        
        // 背景信息
        if (courseContext != null && !courseContext.trim().isEmpty()) {
            prompt.append("课程：").append(courseContext).append("\n");
        }
        
        // 教学内容
        boolean hasTeachingContent = teachingContent != null && !teachingContent.trim().isEmpty();
        if (hasTeachingContent) {
            String limitedContent = teachingContent.length() > 400 ? 
                teachingContent.substring(0, 400) + "..." : teachingContent;
            prompt.append("参考内容：").append(limitedContent).append("\n\n");
        }
        
        // 学生问题
        prompt.append("学生问：").append(studentQuestion).append("\n\n");
        
        // 极其严格的回答要求
        prompt.append("回答要求：\n");
        prompt.append("- 必须用对话语气，就像面对面聊天\n");
        prompt.append("- 绝对不能出现：目标、学习时长、教学内容、知识点、案例分析、练习、时间分配等词\n");
        prompt.append("- 不能分章节、分步骤、分点列举\n");
        prompt.append("- 最多60字\n");
        
        if (hasTeachingContent) {
            prompt.append("- 如果问题与参考内容相关：简单解释\n");
            prompt.append("- 如果问题与参考内容无关：说'这个跟我们学的内容不太相关，不过...'然后简单回答\n");
        }
        
        prompt.append("\n正确示例：\n");
        prompt.append("学生问：贪心算法是什么？\n");
        prompt.append("正确回答：贪心算法就是每一步都选择当前最优的选择，希望最终得到全局最优解。比如找零钱时总是先用面额最大的硬币。\n");
        prompt.append("错误回答：课程目标、学习时长、教学内容等格式\n\n");
        prompt.append("现在回答：");
        
        return prompt.toString();
    }
    
    @Override
    public int calculateLearningProgress(Long studentId, Long courseId) {
        // Get all questions asked by the student for this course
        List<StudentQuestion> questions = studentQuestionMapper.findByStudentAndCourse(studentId, courseId);
        
        if (questions.isEmpty()) {
            return 0;
        }
        
        // Get total teaching content count for the course
        Course course = courseService.getById(courseId);
        if (course == null) {
            return 0;
        }
        
        // Get teaching content count from course service
        // This assumes we have a method to get content count
        int totalContentCount = getTotalContentCount(courseId);
        
        if (totalContentCount == 0) {
            return 0;
        }
        
        // Calculate learned content based on questions
        // Strategy: Every question indicates engagement with content
        // We'll use a more conservative approach: every 2-3 questions = 1 content learned
        int learnedContentCount = Math.min((questions.size() + 1) / 2, totalContentCount);
        
        // Calculate progress percentage
        return (learnedContentCount * 100) / totalContentCount;
    }
    
    /**
     * Get total teaching content count for a course
     */
    private int getTotalContentCount(Long courseId) {
        try {
            List<com.example.aieduforge.entity.TeachingContent> contents = 
                teachingContentService.findByCourseId(courseId);
            return contents.size();
        } catch (Exception e) {
            log.warn("Failed to get content count for course: {}", courseId, e);
            return 10; // Default fallback
        }
    }
}