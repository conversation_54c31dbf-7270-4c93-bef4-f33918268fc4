package com.example.aieduforge.service.impl;

import com.example.aieduforge.entity.KnowledgeBase;
import com.example.aieduforge.entity.AIKnowledgeData;
import com.example.aieduforge.service.AIFileProcessingService;
import com.example.aieduforge.service.AIService;
import com.example.aieduforge.mapper.AIKnowledgeDataMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.Loader;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI文件处理服务实现类
 */
@Slf4j
@Service
public class AIFileProcessingServiceImpl implements AIFileProcessingService {

    @Autowired
    private AIService aiService;
    
    @Autowired
    private AIKnowledgeDataMapper aiKnowledgeDataMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Map<String, Object> processUploadedFile(MultipartFile file, KnowledgeBase knowledgeBase) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 提取文本内容
            String textContent = extractTextContent(file);
            if (textContent == null || textContent.trim().isEmpty()) {
                log.warn("No text content extracted from file: {}", file.getOriginalFilename());
                textContent = "无法提取文本内容";
            }
            
            // 2. 基本处理：生成摘要和关键点（核心功能）
            String summary = generateSummary(textContent);
            String keyPoints = extractKeyPoints(textContent, knowledgeBase.getSubject());
            
            // 3. 简单的结构化数据
            Map<String, Object> structuredData = new HashMap<>();
            structuredData.put("wordCount", countWords(textContent));
            structuredData.put("characterCount", textContent.length());
            
            // 组装结果
            result.put("textContent", textContent);
            result.put("summary", summary);
            result.put("keyPoints", keyPoints);
            result.put("structuredData", structuredData);
            result.put("processedAt", System.currentTimeMillis());
            
            log.info("File processed successfully: {}", file.getOriginalFilename());
            
        } catch (Exception e) {
            log.error("Failed to process file: {}", file.getOriginalFilename(), e);
            result.put("error", "文件处理失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public String extractTextContent(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (filename == null) return "";
        
        String extension = getFileExtension(filename).toLowerCase();
        
        try {
            switch (extension) {
                case "pdf":
                    return extractFromPDF(file);
                case "doc":
                    return extractFromDOC(file);
                case "docx":
                    return extractFromDOCX(file);
                case "txt":
                case "md":
                    return new String(file.getBytes(), "UTF-8");
                default:
                    log.warn("Unsupported file type for text extraction: {}", extension);
                    return "";
            }
        } catch (Exception e) {
            log.error("Failed to extract text from file: {}", filename, e);
            return "";
        }
    }

    @Override
    public String generateSmartTags(String content, String subject) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        try {
            String prompt = String.format(
                "请分析以下%s学科的文档内容，生成5-8个相关的标签关键词，用逗号分隔。标签应该准确反映文档的主要内容和知识点：\n\n%s",
                subject != null ? subject : "通用",
                content.length() > 2000 ? content.substring(0, 2000) + "..." : content
            );
            
            String response = aiService.generateResponse(prompt);
            
            // 清理和格式化标签
            if (response != null && !response.trim().isEmpty()) {
                return cleanTags(response);
            }
            
        } catch (Exception e) {
            log.error("Failed to generate smart tags", e);
        }
        
        // 如果AI生成失败，使用简单的关键词提取
        return extractSimpleTags(content, subject);
    }

    @Override
    public Map<String, Object> convertToStructuredData(String content, String fileType) {
        Map<String, Object> structuredData = new HashMap<>();
        
        if (content == null || content.trim().isEmpty()) {
            return structuredData;
        }
        
        try {
            // 基本信息
            structuredData.put("wordCount", countWords(content));
            structuredData.put("characterCount", content.length());
            structuredData.put("paragraphCount", countParagraphs(content));
            
            // 提取章节标题（如果有）
            structuredData.put("headings", extractHeadings(content));
            
            // 提取关键术语
            structuredData.put("keyTerms", extractKeyTerms(content));
            
            // 文档类型特定的结构化处理
            switch (fileType.toLowerCase()) {
                case "pdf":
                case "doc":
                case "docx":
                    structuredData.put("documentType", "academic");
                    break;
                case "txt":
                case "md":
                    structuredData.put("documentType", "text");
                    break;
                default:
                    structuredData.put("documentType", "unknown");
            }
            
        } catch (Exception e) {
            log.error("Failed to convert to structured data", e);
        }
        
        return structuredData;
    }

    @Override
    public String generateSummary(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "无内容摘要";
        }
        
        try {
            String prompt = String.format(
                "请为以下文档内容生成一个简洁的摘要（100-200字），突出主要内容和关键信息：\n\n%s",
                content.length() > 3000 ? content.substring(0, 3000) + "..." : content
            );
            
            String response = aiService.generateResponse(prompt);
            
            if (response != null && !response.trim().isEmpty()) {
                return response.trim();
            }
            
        } catch (Exception e) {
            log.error("Failed to generate summary", e);
        }
        
        // 如果AI生成失败，使用简单的摘要生成
        return generateSimpleSummary(content);
    }

    @Override
    public String extractKeyPoints(String content, String subject) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        try {
            String prompt = String.format(
                "请从以下%s学科的文档中提取5-10个关键知识点，每个知识点用一行表示：\n\n%s",
                subject != null ? subject : "通用",
                content.length() > 3000 ? content.substring(0, 3000) + "..." : content
            );
            
            String response = aiService.generateResponse(prompt);
            
            if (response != null && !response.trim().isEmpty()) {
                return response.trim();
            }
            
        } catch (Exception e) {
            log.error("Failed to extract key points", e);
        }
        
        // 如果AI生成失败，使用简单的关键点提取
        return extractSimpleKeyPoints(content);
    }

    @Override
    public boolean updateAIKnowledgeBase(KnowledgeBase knowledgeBase, Map<String, Object> processedData) {
        try {
            // 更新知识库记录的内容
            if (processedData.containsKey("textContent")) {
                knowledgeBase.setContent((String) processedData.get("textContent"));
            }
            
            // 更新标签
            if (processedData.containsKey("smartTags")) {
                String existingTags = knowledgeBase.getTags();
                String smartTags = (String) processedData.get("smartTags");
                
                if (existingTags != null && !existingTags.trim().isEmpty()) {
                    knowledgeBase.setTags(existingTags + "," + smartTags);
                } else {
                    knowledgeBase.setTags(smartTags);
                }
            }
            
            // 创建或更新AI知识库数据记录
            AIKnowledgeData aiData = aiKnowledgeDataMapper.findByKnowledgeBaseId(knowledgeBase.getId());
            if (aiData == null) {
                aiData = new AIKnowledgeData();
                aiData.setKnowledgeBaseId(knowledgeBase.getId());
                aiData.setCreateTime(java.time.LocalDateTime.now());
            }
            
            // 更新AI数据
            aiData.setExtractedText((String) processedData.get("textContent"));
            aiData.setSummary((String) processedData.get("summary"));
            aiData.setKeyPoints((String) processedData.get("keyPoints"));
            aiData.setSmartTags((String) processedData.get("smartTags"));
            
            // 存储结构化数据为JSON
            if (processedData.containsKey("structuredData")) {
                String structuredDataJson = objectMapper.writeValueAsString(processedData.get("structuredData"));
                aiData.setStructuredData(structuredDataJson);
                
                // 提取统计信息
                @SuppressWarnings("unchecked")
                Map<String, Object> structuredData = (Map<String, Object>) processedData.get("structuredData");
                if (structuredData.containsKey("wordCount")) {
                    aiData.setWordCount((Integer) structuredData.get("wordCount"));
                }
                if (structuredData.containsKey("characterCount")) {
                    aiData.setCharacterCount((Integer) structuredData.get("characterCount"));
                }
            }
            
            aiData.setAiModel("gemma3:1b"); // 从配置中获取
            aiData.setProcessingTime((int) (System.currentTimeMillis() - (Long) processedData.getOrDefault("processedAt", System.currentTimeMillis())));
            aiData.setStatus(1);
            aiData.setUpdateTime(java.time.LocalDateTime.now());
            
            // 保存或更新AI数据
            if (aiData.getId() == null) {
                aiKnowledgeDataMapper.insert(aiData);
            } else {
                aiKnowledgeDataMapper.updateById(aiData);
            }
            
            log.info("AI knowledge base updated for file: {}", knowledgeBase.getTitle());
            return true;
            
        } catch (Exception e) {
            log.error("Failed to update AI knowledge base", e);
            return false;
        }
    }
    
    // 私有辅助方法
    
    private String extractFromPDF(MultipartFile file) throws IOException {
        try (PDDocument document = Loader.loadPDF(file.getBytes())) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }
    
    private String extractFromDOC(MultipartFile file) throws IOException {
        try (HWPFDocument document = new HWPFDocument(file.getInputStream());
             WordExtractor extractor = new WordExtractor(document)) {
            return extractor.getText();
        }
    }
    
    private String extractFromDOCX(MultipartFile file) throws IOException {
        try (XWPFDocument document = new XWPFDocument(file.getInputStream());
             XWPFWordExtractor extractor = new XWPFWordExtractor(document)) {
            return extractor.getText();
        }
    }
    
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }
    
    private String cleanTags(String tags) {
        if (tags == null) return "";
        
        // 移除多余的空格和特殊字符，保留中英文和逗号
        return tags.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9,，\\s]", "")
                  .replaceAll("\\s*[,，]\\s*", ",")
                  .replaceAll("^,+|,+$", "")
                  .trim();
    }
    
    private String extractSimpleTags(String content, String subject) {
        // 简单的关键词提取逻辑
        StringBuilder tags = new StringBuilder();
        
        if (subject != null) {
            tags.append(subject).append(",");
        }
        
        // 提取一些常见的技术词汇
        String[] commonTerms = {"算法", "数据结构", "编程", "Java", "Python", "数据库", "网络", "系统"};
        for (String term : commonTerms) {
            if (content.contains(term)) {
                tags.append(term).append(",");
            }
        }
        
        return tags.length() > 0 ? tags.substring(0, tags.length() - 1) : "";
    }
    
    private int countWords(String content) {
        if (content == null || content.trim().isEmpty()) return 0;
        return content.trim().split("\\s+").length;
    }
    
    private int countParagraphs(String content) {
        if (content == null || content.trim().isEmpty()) return 0;
        return content.split("\n\n").length;
    }
    
    private String[] extractHeadings(String content) {
        // 简单的标题提取（基于常见的标题模式）
        Pattern headingPattern = Pattern.compile("^(第[一二三四五六七八九十\\d]+[章节]|\\d+\\.\\d*|[一二三四五六七八九十]+、)(.+)$", Pattern.MULTILINE);
        Matcher matcher = headingPattern.matcher(content);
        
        java.util.List<String> headings = new java.util.ArrayList<>();
        while (matcher.find() && headings.size() < 10) {
            headings.add(matcher.group().trim());
        }
        
        return headings.toArray(new String[0]);
    }
    
    private String[] extractKeyTerms(String content) {
        // 简单的关键术语提取
        Pattern termPattern = Pattern.compile("[\\u4e00-\\u9fa5a-zA-Z]{3,10}");
        Matcher matcher = termPattern.matcher(content);
        
        Map<String, Integer> termFreq = new HashMap<>();
        while (matcher.find()) {
            String term = matcher.group();
            termFreq.put(term, termFreq.getOrDefault(term, 0) + 1);
        }
        
        return termFreq.entrySet().stream()
                .filter(entry -> entry.getValue() > 2)
                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                .limit(10)
                .map(Map.Entry::getKey)
                .toArray(String[]::new);
    }
    
    private String generateSimpleSummary(String content) {
        if (content.length() <= 200) {
            return content;
        }
        
        // 取前200个字符作为简单摘要
        String summary = content.substring(0, 200);
        int lastSentence = Math.max(summary.lastIndexOf('。'), summary.lastIndexOf('.'));
        
        if (lastSentence > 100) {
            return summary.substring(0, lastSentence + 1);
        }
        
        return summary + "...";
    }
    
    private String extractSimpleKeyPoints(String content) {
        // 简单的关键点提取：查找包含关键词的句子
        String[] sentences = content.split("[。！？.!?]");
        StringBuilder keyPoints = new StringBuilder();
        
        String[] keywords = {"重要", "关键", "核心", "主要", "基本", "原理", "方法", "特点"};
        
        int count = 0;
        for (String sentence : sentences) {
            if (count >= 5) break;
            
            for (String keyword : keywords) {
                if (sentence.contains(keyword) && sentence.length() > 10 && sentence.length() < 100) {
                    keyPoints.append("• ").append(sentence.trim()).append("\n");
                    count++;
                    break;
                }
            }
        }
        
        return keyPoints.toString();
    }
}