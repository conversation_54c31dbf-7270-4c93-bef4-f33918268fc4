package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.aieduforge.dto.ContentGenerationRequest;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.TeachingContent;
import com.example.aieduforge.exception.BusinessException;
import com.example.aieduforge.mapper.TeachingContentMapper;
import com.example.aieduforge.service.AIService;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.TeachingContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Teaching Content Service Implementation
 */
@Slf4j
@Service
public class TeachingContentServiceImpl extends ServiceImpl<TeachingContentMapper, TeachingContent> 
        implements TeachingContentService {

    @Autowired
    private TeachingContentMapper teachingContentMapper;
    
    @Autowired
    private CourseService courseService;
    
    @Autowired
    private AIService aiService;

    @Override
    @Transactional
    public TeachingContent generateContent(ContentGenerationRequest request, Long teacherId) {
        // Verify teacher owns the course
        if (!courseService.isTeacherOwnsCourse(request.getCourseId(), teacherId)) {
            throw new BusinessException("You don't have permission to create content for this course");
        }
        
        // Get course information
        Course course = courseService.getById(request.getCourseId());
        if (course == null) {
            throw new BusinessException("Course not found");
        }
        
        // Prepare content type description
        String contentTypeDesc = getContentTypeDescription(request.getContentType());
        
        // Generate content using AI
        String generatedContent;
        String prompt = request.getCustomPrompt();
        
        if (prompt == null || prompt.trim().isEmpty()) {
            generatedContent = aiService.generateTeachingContent(
                request.getCourseOutline() != null ? request.getCourseOutline() : course.getDescription(),
                request.getChapterName() != null ? request.getChapterName() : request.getTitle(),
                contentTypeDesc
            );
            prompt = "Auto-generated using default prompt";
        } else {
            generatedContent = aiService.generateTeachingContent(prompt, request.getTitle(), contentTypeDesc);
        }
        
        // Create teaching content entity
        TeachingContent content = new TeachingContent();
        content.setCourseId(request.getCourseId());
        content.setChapterName(request.getChapterName());    // 设置章节名称
        content.setOutlineId(request.getOutlineId());
        content.setContentType(request.getContentType());
        content.setTitle(request.getTitle());
        content.setContent(generatedContent);
        content.setAiGenerated(1);                          // 标记为AI生成
        content.setGenerationPrompt(prompt);
        content.setTimeAllocation(request.getTimeAllocation());
        content.setDifficultyLevel(request.getDifficultyLevel());
        content.setCreateUserId(teacherId);
        content.setCreateTime(LocalDateTime.now());
        content.setUpdateTime(LocalDateTime.now());
        
        // Save content
        if (save(content)) {
            log.info("Teaching content generated successfully for course: {}", request.getCourseId());
            return content;
        }
        
        throw new BusinessException("Failed to save generated content");
    }

    private String getContentTypeDescription(Integer contentType) {
        switch (contentType) {
            case 1: return "知识讲解";
            case 2: return "实训练习";
            case 3: return "指导说明";
            default: return "未知类型";
        }
    }

    @Override
    public List<TeachingContent> findByCourseId(Long courseId) {
        return teachingContentMapper.findByCourseId(courseId);
    }

    @Override
    public List<TeachingContent> findByOutlineId(Long outlineId) {
        return teachingContentMapper.findByOutlineId(outlineId);
    }

    @Override
    public List<TeachingContent> findByContentType(Long courseId, Integer contentType) {
        return teachingContentMapper.findByContentType(courseId, contentType);
    }

    @Override
    @Transactional
    public TeachingContent updateContent(Long contentId, TeachingContent content, Long teacherId) {
        TeachingContent existingContent = getById(contentId);
        if (existingContent == null) {
            throw new BusinessException("Content not found");
        }
        
        // Verify teacher owns the content
        if (!isTeacherOwnsContent(contentId, teacherId)) {
            throw new BusinessException("You don't have permission to update this content");
        }
        
        content.setId(contentId);
        content.setUpdateTime(LocalDateTime.now());
        
        if (updateById(content)) {
            log.info("Teaching content updated successfully: {}", contentId);
            return content;
        }
        
        throw new BusinessException("Failed to update content");
    }

    @Override
    @Transactional
    public boolean deleteContent(Long contentId, Long teacherId) {
        TeachingContent content = getById(contentId);
        if (content == null) {
            throw new BusinessException("Content not found");
        }
        
        // Verify teacher owns the content
        if (!isTeacherOwnsContent(contentId, teacherId)) {
            throw new BusinessException("You don't have permission to delete this content");
        }
        
        boolean result = removeById(contentId);
        if (result) {
            log.info("Teaching content deleted successfully: {}", contentId);
        }
        
        return result;
    }

    @Override
    public boolean isTeacherOwnsContent(Long contentId, Long teacherId) {
        TeachingContent content = getById(contentId);
        if (content == null) {
            return false;
        }
        
        // Check if teacher owns the course that contains this content
        return courseService.isTeacherOwnsCourse(content.getCourseId(), teacherId);
    }
}