package com.example.aieduforge.service.impl;

import com.example.aieduforge.mapper.SysNotificationMapper;
import com.example.aieduforge.mapper.UserNotificationMapper;
import com.example.aieduforge.service.NotificationService;
import com.example.aieduforge.websocket.NotificationWebSocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Notification Service Implementation
 */
@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    private SysNotificationMapper sysNotificationMapper;
    
    @Autowired
    private UserNotificationMapper userNotificationMapper;

    @Override
    public Map<String, Object> getUserNotifications(Long userId, String userRole, Integer page, Integer size) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算分页参数
            int offset = (page - 1) * size;
            
            // 获取用户可见的通知
            List<Map<String, Object>> notifications = sysNotificationMapper.getUserNotifications(
                userId, userRole, size, offset);
            
            // 获取总数
            int total = sysNotificationMapper.getUserNotificationCount(userId, userRole);
            
            result.put("notifications", notifications);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
        } catch (Exception e) {
            log.error("获取用户通知失败", e);
            result.put("notifications", List.of());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public int getUnreadCount(Long userId, String userRole) {
        try {
            return sysNotificationMapper.getUnreadNotificationCount(userId, userRole);
        } catch (Exception e) {
            log.error("获取未读通知数量失败", e);
            return 0;
        }
    }

    @Override
    public boolean markAsRead(Long notificationId, Long userId) {
        try {
            // 检查是否已存在用户通知记录
            Map<String, Object> existingRecord = userNotificationMapper.getUserNotificationRecord(userId, notificationId);
            
            if (existingRecord != null) {
                // 更新已存在的记录
                return userNotificationMapper.updateReadStatus(userId, notificationId, LocalDateTime.now()) > 0;
            } else {
                // 创建新的用户通知记录
                return userNotificationMapper.insertUserNotification(userId, notificationId, 1, LocalDateTime.now()) > 0;
            }
        } catch (Exception e) {
            log.error("标记通知已读失败", e);
            return false;
        }
    }

    @Override
    public boolean markAllAsRead(Long userId, String userRole) {
        try {
            // 获取用户所有未读通知
            List<Map<String, Object>> unreadNotifications = sysNotificationMapper.getUnreadNotifications(userId, userRole);
            
            for (Map<String, Object> notification : unreadNotifications) {
                Long notificationId = ((Number) notification.get("id")).longValue();
                markAsRead(notificationId, userId);
            }
            
            return true;
        } catch (Exception e) {
            log.error("标记所有通知已读失败", e);
            return false;
        }
    }

    @Override
    public boolean publishNotification(String title, String content, Integer type, 
                                     String targetRole, Long targetUserId, Integer priority, Long createUserId) {
        try {
            int result = sysNotificationMapper.insertNotification(
                title, content, type, targetRole, targetUserId, priority, createUserId, LocalDateTime.now());
            
            if (result > 0) {
                // 通过WebSocket推送通知
                Map<String, Object> notification = new HashMap<>();
                notification.put("title", title);
                notification.put("content", content);
                notification.put("type", type);
                notification.put("priority", priority);
                notification.put("create_time", LocalDateTime.now().toString());
                
                if (targetUserId != null) {
                    // 发送给指定用户
                    NotificationWebSocket.sendNotificationToUser(targetUserId, notification);
                } else if ("ALL".equals(targetRole)) {
                    // 发送给所有用户
                    NotificationWebSocket.sendNotificationToAll(notification);
                } else {
                    // 发送给指定角色的用户
                    NotificationWebSocket.sendNotificationToRole(targetRole, notification);
                }
                
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("发布通知失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getLatestSystemNotifications(Integer limit) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> notifications = sysNotificationMapper.getLatestSystemNotifications(limit);
            result.put("notifications", notifications);
        } catch (Exception e) {
            log.error("获取最新系统通知失败", e);
            result.put("notifications", List.of());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getNotificationDetail(Long notificationId, Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> notification = sysNotificationMapper.getNotificationDetail(notificationId);
            if (notification != null) {
                // 检查用户是否有权限查看此通知
                String targetRole = (String) notification.get("target_role");
                Long targetUserId = (Long) notification.get("target_user_id");
                
                // 这里需要获取用户角色，简化处理
                boolean hasPermission = "ALL".equals(targetRole) || 
                                      (targetUserId != null && targetUserId.equals(userId));
                
                if (hasPermission) {
                    // 获取用户的阅读状态
                    Map<String, Object> userNotification = userNotificationMapper.getUserNotificationRecord(userId, notificationId);
                    notification.put("is_read", userNotification != null ? userNotification.get("is_read") : 0);
                    notification.put("read_time", userNotification != null ? userNotification.get("read_time") : null);
                    
                    result.put("notification", notification);
                } else {
                    result.put("error", "无权限查看此通知");
                }
            } else {
                result.put("error", "通知不存在");
            }
        } catch (Exception e) {
            log.error("获取通知详情失败", e);
            result.put("error", "获取通知详情失败");
        }
        
        return result;
    }

    @Override
    public boolean deleteNotification(Long notificationId) {
        try {
            return sysNotificationMapper.deleteNotification(notificationId) > 0;
        } catch (Exception e) {
            log.error("删除通知失败", e);
            return false;
        }
    }

    @Override
    public int deleteExpiredNotifications() {
        try {
            return sysNotificationMapper.cleanExpiredNotifications();
        } catch (Exception e) {
            log.error("清理过期通知失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getAllNotifications(Integer page, Integer size, Integer type, String targetRole, Integer priority) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算分页参数
            int offset = (page - 1) * size;
            
            // 获取所有通知
            List<Map<String, Object>> notifications = sysNotificationMapper.getAllNotifications(
                type, targetRole, priority, size, offset);
            
            // 获取总数
            int total = sysNotificationMapper.getAllNotificationCount(type, targetRole, priority);
            
            result.put("notifications", notifications);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            
        } catch (Exception e) {
            log.error("获取所有通知失败", e);
            result.put("notifications", List.of());
            result.put("total", 0);
        }
        
        return result;
    }
}