package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.aieduforge.entity.SystemSettings;
import com.example.aieduforge.mapper.SystemSettingsMapper;
import com.example.aieduforge.service.SystemSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统设置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemSettingsServiceImpl implements SystemSettingsService {
    
    private final SystemSettingsMapper systemSettingsMapper;
    
    // 缓存系统设置，避免频繁查询数据库
    private final Map<String, String> settingsCache = new HashMap<>();
    private long lastCacheTime = 0;
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟缓存
    
    @Override
    public String getSettingValue(String settingKey) {
        return getSettingValue(settingKey, null);
    }
    
    @Override
    public String getSettingValue(String settingKey, String defaultValue) {
        try {
            // 检查缓存是否过期
            if (System.currentTimeMillis() - lastCacheTime > CACHE_EXPIRE_TIME) {
                refreshCache();
            }
            
            String value = settingsCache.get(settingKey);
            return value != null ? value : defaultValue;
        } catch (Exception e) {
            log.error("获取系统设置失败: {}", settingKey, e);
            return defaultValue;
        }
    }
    
    @Override
    public void updateSetting(String settingKey, String settingValue) {
        try {
            SystemSettings setting = systemSettingsMapper.selectOne(
                    new LambdaQueryWrapper<SystemSettings>()
                            .eq(SystemSettings::getSettingKey, settingKey)
            );
            
            if (setting != null) {
                setting.setSettingValue(settingValue);
                setting.setUpdatedAt(LocalDateTime.now());
                systemSettingsMapper.updateById(setting);
            } else {
                setting = new SystemSettings();
                setting.setSettingKey(settingKey);
                setting.setSettingValue(settingValue);
                setting.setCreatedAt(LocalDateTime.now());
                setting.setUpdatedAt(LocalDateTime.now());
                systemSettingsMapper.insert(setting);
            }
            
            // 更新缓存
            settingsCache.put(settingKey, settingValue);
        } catch (Exception e) {
            log.error("更新系统设置失败: {} = {}", settingKey, settingValue, e);
            throw new RuntimeException("更新系统设置失败");
        }
    }
    
    @Override
    public List<SystemSettings> getAllSettings() {
        try {
            return systemSettingsMapper.selectList(null);
        } catch (Exception e) {
            log.error("获取所有系统设置失败", e);
            throw new RuntimeException("获取系统设置失败");
        }
    }
    
    @Override
    public SystemSettings getCaptchaSettings() {
        try {
            Map<String, Object> captchaSettings = new HashMap<>();
            captchaSettings.put("enabled", "1".equals(getSettingValue("captcha_enabled", "1")));
            captchaSettings.put("length", Integer.parseInt(getSettingValue("captcha_length", "4")));
            captchaSettings.put("expireMinutes", Integer.parseInt(getSettingValue("captcha_expire_minutes", "5")));
            
            // 创建一个临时的SystemSettings对象来返回验证码设置
            SystemSettings settings = new SystemSettings();
            settings.setSettingKey("captcha_settings");
            settings.setSettingValue(captchaSettings.toString());
            settings.setDescription("验证码相关设置");
            
            return settings;
        } catch (Exception e) {
            log.error("获取验证码设置失败", e);
            throw new RuntimeException("获取验证码设置失败");
        }
    }
    
    @Override
    public void toggleCaptchaEnabled(boolean enabled) {
        updateSetting("captcha_enabled", enabled ? "1" : "0");
    }
    
    @Override
    public void updateCaptchaLength(int length) {
        if (length < 3 || length > 8) {
            throw new IllegalArgumentException("验证码长度必须在3-8位之间");
        }
        updateSetting("captcha_length", String.valueOf(length));
    }
    
    @Override
    public void updateCaptchaExpireMinutes(int expireMinutes) {
        if (expireMinutes < 1 || expireMinutes > 60) {
            throw new IllegalArgumentException("验证码过期时间必须在1-60分钟之间");
        }
        updateSetting("captcha_expire_minutes", String.valueOf(expireMinutes));
    }
    
    /**
     * 刷新设置缓存
     */
    private void refreshCache() {
        try {
            List<SystemSettings> settings = systemSettingsMapper.selectList(null);
            settingsCache.clear();
            for (SystemSettings setting : settings) {
                settingsCache.put(setting.getSettingKey(), setting.getSettingValue());
            }
            lastCacheTime = System.currentTimeMillis();
        } catch (Exception e) {
            log.error("刷新系统设置缓存失败", e);
        }
    }
}