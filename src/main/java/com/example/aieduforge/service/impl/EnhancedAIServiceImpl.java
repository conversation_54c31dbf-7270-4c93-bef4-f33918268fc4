package com.example.aieduforge.service.impl;

import com.example.aieduforge.mapper.AIKnowledgeDataMapper;
import com.example.aieduforge.mapper.KnowledgeBaseMapper;
import com.example.aieduforge.mapper.LearningAnalyticsMapper;
import com.example.aieduforge.service.AIService;
import com.example.aieduforge.service.EnhancedAIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 增强AI服务实现类
 */
@Slf4j
@Service
public class EnhancedAIServiceImpl implements EnhancedAIService {

    @Autowired
    private AIService aiService;
    
    @Autowired
    private AIKnowledgeDataMapper aiKnowledgeDataMapper;
    
    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;
    
    @Autowired
    private LearningAnalyticsMapper learningAnalyticsMapper;
    
    @Autowired
    private com.example.aieduforge.mapper.StudentQuestionMapper studentQuestionMapper;
    
    @Autowired
    private com.example.aieduforge.mapper.AiAnswerMapper aiAnswerMapper;

    @Override
    public String answerQuestionWithKnowledge(String question, String subject, Long courseId) {
        try {
            // 1. 搜索相关知识库内容
            List<Map<String, Object>> relevantKnowledge = searchRelevantKnowledge(question, subject, 3);
            
            // 2. 构建简化的上下文
            StringBuilder context = new StringBuilder();
            context.append("基于以下知识库内容回答问题：\n\n");
            
            for (Map<String, Object> knowledge : relevantKnowledge) {
                if (knowledge.get("title") != null) {
                    context.append("【").append(knowledge.get("title")).append("】\n");
                }
                if (knowledge.get("summary") != null) {
                    context.append(knowledge.get("summary")).append("\n\n");
                }
            }
            
            context.append("问题：").append(question).append("\n\n");
            context.append("请基于上述内容回答问题，如果没有相关信息请说明。");
            
            // 3. 调用AI生成回答
            String answer = aiService.generateResponse(context.toString());
            
            log.info("Generated knowledge-based answer for question: {}", question);
            return answer != null ? answer : "抱歉，我无法基于当前知识库回答这个问题。";
            
        } catch (Exception e) {
            log.error("Failed to answer question with knowledge", e);
            return "抱歉，回答问题时出现错误，请稍后重试。";
        }
    }

    @Override
    public List<Map<String, Object>> searchRelevantKnowledge(String query, String subject, int limit) {
        try {
            // 使用关键词搜索相关内容
            List<Map<String, Object>> results = aiKnowledgeDataMapper.searchSimilarContent(query, limit);
            
            // 如果按学科筛选
            if (subject != null && !subject.trim().isEmpty()) {
                results = results.stream()
                    .filter(item -> subject.equals(item.get("subject")))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            return results;
            
        } catch (Exception e) {
            log.error("Failed to search relevant knowledge", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> generateQuestionsFromKnowledge(String subject, Long courseId, int count) {
        List<Map<String, Object>> questions = new ArrayList<>();
        
        try {
            // 1. 获取相关知识库内容
            List<Map<String, Object>> knowledgeList = new ArrayList<>();
            if (courseId != null) {
                // 获取特定课程的知识库
                var courseKnowledge = knowledgeBaseMapper.findByCourseId(courseId);
                for (var kb : courseKnowledge) {
                    var aiData = aiKnowledgeDataMapper.findByKnowledgeBaseId(kb.getId());
                    if (aiData != null) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("title", kb.getTitle());
                        item.put("content", aiData.getExtractedText());
                        item.put("keyPoints", aiData.getKeyPoints());
                        knowledgeList.add(item);
                    }
                }
            } else if (subject != null) {
                // 获取特定学科的知识库
                var subjectKnowledge = knowledgeBaseMapper.findBySubject(subject);
                for (var kb : subjectKnowledge) {
                    var aiData = aiKnowledgeDataMapper.findByKnowledgeBaseId(kb.getId());
                    if (aiData != null) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("title", kb.getTitle());
                        item.put("content", aiData.getExtractedText());
                        item.put("keyPoints", aiData.getKeyPoints());
                        knowledgeList.add(item);
                    }
                }
            }
            
            // 2. 为每个知识点生成题目
            for (Map<String, Object> knowledge : knowledgeList) {
                if (questions.size() >= count) break;
                
                String prompt = String.format(
                    "基于以下%s学科的知识内容，生成1道选择题，包含题目、4个选项（A、B、C、D）和正确答案：\n\n%s\n\n" +
                    "请按以下格式输出：\n" +
                    "题目：[题目内容]\n" +
                    "A. [选项A]\n" +
                    "B. [选项B]\n" +
                    "C. [选项C]\n" +
                    "D. [选项D]\n" +
                    "答案：[正确选项]\n" +
                    "解析：[答案解析]",
                    subject != null ? subject : "通用",
                    knowledge.get("keyPoints") != null ? knowledge.get("keyPoints") : knowledge.get("content")
                );
                
                String response = aiService.generateResponse(prompt);
                if (response != null && !response.trim().isEmpty()) {
                    Map<String, Object> question = parseQuestionResponse(response);
                    if (!question.isEmpty()) {
                        question.put("source", knowledge.get("title"));
                        questions.add(question);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to generate questions from knowledge", e);
        }
        
        return questions;
    }

    @Override
    public Map<String, Object> analyzeStudentAnswer(String question, String studentAnswer, String correctAnswer) {
        Map<String, Object> analysis = new HashMap<>();
        
        try {
            String prompt = String.format(
                "请分析学生的答案，并提供详细反馈：\n\n" +
                "题目：%s\n" +
                "标准答案：%s\n" +
                "学生答案：%s\n\n" +
                "请从以下几个方面进行分析：\n" +
                "1. 答案正确性（正确/部分正确/错误）\n" +
                "2. 错误原因分析\n" +
                "3. 改进建议\n" +
                "4. 相关知识点提醒",
                question, correctAnswer, studentAnswer
            );
            
            String response = aiService.generateResponse(prompt);
            
            analysis.put("feedback", response);
            analysis.put("isCorrect", determineCorrectness(studentAnswer, correctAnswer));
            analysis.put("score", calculateScore(studentAnswer, correctAnswer));
            analysis.put("analysisTime", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to analyze student answer", e);
            analysis.put("feedback", "答案分析失败，请稍后重试。");
            analysis.put("isCorrect", false);
            analysis.put("score", 0);
        }
        
        return analysis;
    }

    @Override
    public List<Map<String, Object>> recommendLearningResources(String topic, String subject, Long studentId) {
        List<Map<String, Object>> recommendations = new ArrayList<>();
        
        try {
            // 1. 搜索相关知识库资源
            List<Map<String, Object>> resources = searchRelevantKnowledge(topic, subject, 10);
            
            // 2. 根据学生学习情况进行个性化推荐
            if (studentId != null) {
                // 获取学生的学习分析数据
                // 这里可以根据学生的学习记录、错误分析等进行个性化推荐
            }
            
            // 3. 格式化推荐结果
            for (Map<String, Object> resource : resources) {
                Map<String, Object> recommendation = new HashMap<>();
                recommendation.put("title", resource.get("title"));
                recommendation.put("type", "knowledge_base");
                recommendation.put("summary", resource.get("summary"));
                recommendation.put("difficulty", "中等"); // 可以基于内容分析确定难度
                recommendation.put("relevanceScore", calculateRelevanceScore(topic, (String) resource.get("summary")));
                recommendations.add(recommendation);
            }
            
            // 按相关性排序
            recommendations.sort((a, b) -> 
                Double.compare((Double) b.get("relevanceScore"), (Double) a.get("relevanceScore")));
            
        } catch (Exception e) {
            log.error("Failed to recommend learning resources", e);
        }
        
        return recommendations;
    }

    @Override
    public Map<String, Object> generatePersonalizedStudyPlan(Long studentId, Long courseId) {
        Map<String, Object> studyPlan = new HashMap<>();
        
        try {
            // 1. 分析学生学习情况
            // 这里可以调用学习分析服务获取学生的学习数据
            
            // 2. 获取课程知识点
            List<Map<String, Object>> courseKnowledge = new ArrayList<>();
            if (courseId != null) {
                var knowledgeList = knowledgeBaseMapper.findByCourseId(courseId);
                for (var kb : knowledgeList) {
                    var aiData = aiKnowledgeDataMapper.findByKnowledgeBaseId(kb.getId());
                    if (aiData != null) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("title", kb.getTitle());
                        item.put("keyPoints", aiData.getKeyPoints());
                        courseKnowledge.add(item);
                    }
                }
            }
            
            // 3. 生成学习计划
            StringBuilder planPrompt = new StringBuilder();
            planPrompt.append("基于以下课程内容，为学生制定一个个性化的学习计划：\n\n");
            
            for (Map<String, Object> knowledge : courseKnowledge) {
                planPrompt.append("- ").append(knowledge.get("title")).append("\n");
                if (knowledge.get("keyPoints") != null) {
                    planPrompt.append("  关键点：").append(knowledge.get("keyPoints")).append("\n");
                }
            }
            
            planPrompt.append("\n请制定一个为期4周的学习计划，包括：\n");
            planPrompt.append("1. 每周学习目标\n");
            planPrompt.append("2. 具体学习任务\n");
            planPrompt.append("3. 练习建议\n");
            planPrompt.append("4. 复习安排");
            
            String planResponse = aiService.generateResponse(planPrompt.toString());
            
            studyPlan.put("plan", planResponse);
            studyPlan.put("studentId", studentId);
            studyPlan.put("courseId", courseId);
            studyPlan.put("generatedAt", System.currentTimeMillis());
            studyPlan.put("duration", "4周");
            
        } catch (Exception e) {
            log.error("Failed to generate personalized study plan", e);
            studyPlan.put("plan", "学习计划生成失败，请稍后重试。");
        }
        
        return studyPlan;
    }

    @Override
    public Map<String, Object> answerQuestionWithKnowledgeAndSave(String question, String subject, Long courseId, Long studentId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 生成AI回答
            String answer = answerQuestionWithKnowledge(question, subject, courseId);
            
            // 2. 保存问答记录
            Map<String, Object> saveResult = saveQuestionAndAnswer(question, answer, subject, courseId, studentId, "knowledge_qa");
            
            result.put("answer", answer);
            result.put("questionId", saveResult.get("questionId"));
            result.put("answerId", saveResult.get("answerId"));
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Failed to answer question with knowledge and save", e);
            throw new RuntimeException("回答问题并保存记录失败", e);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> saveQuestionAndAnswer(String question, String answer, String subject, 
                                                   Long courseId, Long studentId, String questionSource) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 保存学生问题
            com.example.aieduforge.entity.StudentQuestion studentQuestion = new com.example.aieduforge.entity.StudentQuestion();
            studentQuestion.setStudentId(studentId);
            studentQuestion.setCourseId(courseId);
            studentQuestion.setQuestionContent(question);
            studentQuestion.setQuestionType(1); // 1-知识询问
            studentQuestion.setContextInfo(subject);
            studentQuestion.setStatus(2); // 2-已回答
            studentQuestion.setQuestionSource(questionSource);
            
            studentQuestionMapper.insert(studentQuestion);
            Long questionId = studentQuestion.getId();
            
            // 2. 保存AI回答
            if (answer != null && !answer.trim().isEmpty()) {
                com.example.aieduforge.entity.AiAnswer aiAnswer = new com.example.aieduforge.entity.AiAnswer();
                aiAnswer.setQuestionId(questionId);
                aiAnswer.setAnswerContent(answer);
                aiAnswer.setAiModel("enhanced-ai");
                aiAnswer.setGenerationTime(1000); // 模拟生成时间
                
                aiAnswerMapper.insert(aiAnswer);
                result.put("answerId", aiAnswer.getId());
            }
            
            result.put("questionId", questionId);
            result.put("success", true);
            
            log.info("Saved question and answer for student: {}, question: {}", studentId, questionId);
            
        } catch (Exception e) {
            log.error("Failed to save question and answer", e);
            throw new RuntimeException("保存问答记录失败", e);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getKnowledgeFileContent(Long knowledgeId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 首先尝试直接查询knowledge_base表
            com.example.aieduforge.entity.KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeId);
            
            // 2. 如果没找到，可能传递的是ai_knowledge_data的ID，尝试通过ai_knowledge_data查找
            if (knowledgeBase == null) {
                com.example.aieduforge.entity.AIKnowledgeData aiData = aiKnowledgeDataMapper.selectById(knowledgeId);
                if (aiData != null) {
                    knowledgeBase = knowledgeBaseMapper.selectById(aiData.getKnowledgeBaseId());
                }
            }
            
            if (knowledgeBase == null) {
                throw new RuntimeException("知识库文件不存在");
            }
            
            // 2. 获取AI处理后的数据
            com.example.aieduforge.entity.AIKnowledgeData aiData = aiKnowledgeDataMapper.findByKnowledgeBaseId(knowledgeBase.getId());
            
            // 3. 构建返回结果
            result.put("id", knowledgeBase.getId());
            result.put("title", knowledgeBase.getTitle());
            result.put("fileType", knowledgeBase.getFileType());
            result.put("fileSize", knowledgeBase.getFileSize());
            result.put("description", knowledgeBase.getDescription());
            result.put("subject", knowledgeBase.getSubject());
            result.put("tags", knowledgeBase.getTags());
            result.put("createTime", knowledgeBase.getCreateTime());
            
            // 4. 添加文件内容
            if (aiData != null) {
                result.put("content", aiData.getExtractedText());
                result.put("summary", aiData.getSummary());
                result.put("keyPoints", aiData.getKeyPoints());
                result.put("contentType", determineContentType(knowledgeBase.getFileType()));
            } else {
                // 如果没有AI处理数据，返回原始内容
                result.put("content", knowledgeBase.getContent());
                result.put("contentType", "text");
            }
            
            // 5. 增加下载次数
            knowledgeBase.setDownloadCount(knowledgeBase.getDownloadCount() + 1);
            knowledgeBaseMapper.updateById(knowledgeBase);
            
        } catch (Exception e) {
            log.error("Failed to get knowledge file content for id: {}", knowledgeId, e);
            throw new RuntimeException("获取知识文件内容失败: " + e.getMessage());
        }
        
        return result;
    }
    
    // 私有辅助方法
    
    private Map<String, Object> parseQuestionResponse(String response) {
        Map<String, Object> question = new HashMap<>();
        
        try {
            String[] lines = response.split("\n");
            String title = "";
            List<String> options = new ArrayList<>();
            String answer = "";
            String explanation = "";
            
            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("题目：")) {
                    title = line.substring(3).trim();
                } else if (line.matches("^[A-D]\\..*")) {
                    options.add(line);
                } else if (line.startsWith("答案：")) {
                    answer = line.substring(3).trim();
                } else if (line.startsWith("解析：")) {
                    explanation = line.substring(3).trim();
                }
            }
            
            if (!title.isEmpty() && !options.isEmpty() && !answer.isEmpty()) {
                question.put("title", title);
                question.put("options", options);
                question.put("correctAnswer", answer);
                question.put("explanation", explanation);
                question.put("type", "choice");
            }
            
        } catch (Exception e) {
            log.error("Failed to parse question response", e);
        }
        
        return question;
    }
    
    private boolean determineCorrectness(String studentAnswer, String correctAnswer) {
        if (studentAnswer == null || correctAnswer == null) return false;
        
        // 简单的相似度比较
        String student = studentAnswer.toLowerCase().trim();
        String correct = correctAnswer.toLowerCase().trim();
        
        return student.equals(correct) || student.contains(correct) || correct.contains(student);
    }
    
    private int calculateScore(String studentAnswer, String correctAnswer) {
        if (determineCorrectness(studentAnswer, correctAnswer)) {
            return 100;
        }
        
        // 基于相似度计算部分分数
        if (studentAnswer != null && correctAnswer != null) {
            String student = studentAnswer.toLowerCase();
            String correct = correctAnswer.toLowerCase();
            
            // 简单的相似度计算
            int commonWords = 0;
            String[] studentWords = student.split("\\s+");
            String[] correctWords = correct.split("\\s+");
            
            for (String word : studentWords) {
                for (String cWord : correctWords) {
                    if (word.equals(cWord)) {
                        commonWords++;
                        break;
                    }
                }
            }
            
            if (correctWords.length > 0) {
                return Math.min(80, (commonWords * 100) / correctWords.length);
            }
        }
        
        return 0;
    }
    
    private double calculateRelevanceScore(String topic, String content) {
        if (topic == null || content == null) return 0.0;
        
        String topicLower = topic.toLowerCase();
        String contentLower = content.toLowerCase();
        
        // 简单的相关性计算
        double score = 0.0;
        
        // 直接匹配
        if (contentLower.contains(topicLower)) {
            score += 0.8;
        }
        
        // 关键词匹配
        String[] topicWords = topicLower.split("\\s+");
        for (String word : topicWords) {
            if (contentLower.contains(word)) {
                score += 0.2;
            }
        }
        
        return Math.min(1.0, score);
    }
    
    private String determineContentType(String fileType) {
        if (fileType == null) return "text";
        
        String type = fileType.toLowerCase();
        if (type.contains("md") || type.contains("markdown")) {
            return "markdown";
        } else if (type.contains("html") || type.contains("htm")) {
            return "html";
        } else if (type.contains("pdf")) {
            return "pdf";
        } else if (type.contains("doc") || type.contains("docx")) {
            return "document";
        } else {
            return "text";
        }
    }
}