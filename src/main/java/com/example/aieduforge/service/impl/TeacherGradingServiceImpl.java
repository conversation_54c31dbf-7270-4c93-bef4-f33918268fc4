package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.aieduforge.dto.GradingRequest;
import com.example.aieduforge.entity.ExamAnswer;
import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.mapper.ExamAnswerMapper;
import com.example.aieduforge.mapper.ExamQuestionMapper;
import com.example.aieduforge.mapper.SysUserMapper;
import com.example.aieduforge.service.TeacherGradingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Teacher Grading Service Implementation
 */
@Slf4j
@Service
public class TeacherGradingServiceImpl implements TeacherGradingService {

    @Autowired
    private ExamAnswerMapper examAnswerMapper;
    
    @Autowired
    private ExamQuestionMapper examQuestionMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public List<ExamAnswer> getPendingGradingAnswers(Long teacherId, Long courseId, Integer questionType, Integer current, Integer size) {
        // 计算偏移量
        int offset = (current - 1) * size;
        // 使用自定义SQL查询来获取完整的答案信息
        return examAnswerMapper.getPendingGradingAnswersWithDetails(courseId, questionType, offset, size);
    }

    @Override
    public List<ExamAnswer> getAnswersForGrading(Long teacherId, Long courseId, Integer questionType, Integer gradingStatus, Integer current, Integer size) {
        // 计算偏移量
        int offset = (current - 1) * size;
        // 使用自定义SQL查询来获取完整的答案信息，支持按评分状态筛选
        return examAnswerMapper.getAnswersForGradingWithDetails(courseId, questionType, gradingStatus, offset, size);
    }

    @Override
    public Map<String, Object> getAnswerDetail(Long answerId, Long teacherId) {
        // 使用自定义SQL查询来获取完整的答案详情
        return examAnswerMapper.getAnswerDetailWithInfo(answerId);
    }

    @Override
    @Transactional
    public boolean gradeAnswer(Long answerId, BigDecimal score, Integer isCorrect, String feedback, String improvementSuggestion, Long teacherId) {
        try {
            ExamAnswer answer = examAnswerMapper.selectById(answerId);
            if (answer == null) {
                return false;
            }
            
            // 更新教师评分信息
            answer.setTeacherScore(score);
            answer.setIsCorrect(isCorrect);
            answer.setTeacherFeedback(feedback);
            answer.setImprovementSuggestion(improvementSuggestion);
            answer.setTeacherId(teacherId);
            answer.setGradingStatus(2); // 2表示已评分
            answer.setNeedsManualReview(0); // 标记为不需要人工评分
            answer.setTeacherGradingTime(LocalDateTime.now());
            
            // 最终分数使用教师评分
            answer.setScore(score);
            
            int updated = examAnswerMapper.updateById(answer);
            
            // 确保数据库更新成功后记录日志
            if (updated > 0) {
                log.info("成功更新答案 {} 的最终分数为: {}", answerId, score);
            } else {
                log.error("更新答案 {} 的分数失败", answerId);
            }
            
            // 更新对应的练习记录总分
            if (updated > 0 && answer.getPracticeId() != null) {
                updatePracticeRecordScore(answer.getPracticeId());
            }
            
            log.info("教师 {} 完成答案 {} 的评分，分数: {}, 正确性: {}", 
                teacherId, answerId, score, isCorrect);
            
            return updated > 0;
        } catch (Exception e) {
            log.error("教师评分失败", e);
            return false;
        }
    }
    
    /**
     * 更新练习记录的总分
     */
    private void updatePracticeRecordScore(Long practiceId) {
        try {
            // 计算该练习的总分和正确题数
            QueryWrapper<ExamAnswer> wrapper = new QueryWrapper<>();
            wrapper.eq("practice_id", practiceId);
            List<ExamAnswer> answers = examAnswerMapper.selectList(wrapper);
            
            BigDecimal totalScore = BigDecimal.ZERO;
            int correctCount = 0;
            
            for (ExamAnswer answer : answers) {
                if (answer.getScore() != null) {
                    totalScore = totalScore.add(answer.getScore());
                }
                if (answer.getIsCorrect() != null && answer.getIsCorrect() == 1) {
                    correctCount++;
                }
            }
            
            // 更新练习记录
            examAnswerMapper.updatePracticeRecordScore(practiceId, totalScore, correctCount);
            
            log.info("更新练习记录 {} 的总分为: {}, 正确题数: {}", practiceId, totalScore, correctCount);
        } catch (Exception e) {
            log.error("更新练习记录总分失败: {}", e.getMessage());
        }
    }

    @Override
    @Transactional
    public int batchGradeAnswers(List<GradingRequest> requests, Long teacherId) {
        int successCount = 0;
        
        for (GradingRequest request : requests) {
            boolean success = gradeAnswer(
                request.getAnswerId(),
                request.getScore(),
                request.getIsCorrect(),
                request.getFeedback(),
                request.getImprovementSuggestion(),
                teacherId
            );
            
            if (success) {
                successCount++;
            }
        }
        
        log.info("教师 {} 批量评分完成，成功评分 {} 道题", teacherId, successCount);
        return successCount;
    }

    @Override
    public Map<String, Object> getGradingStatistics(Long teacherId, Long courseId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 主观题类型（需要人工评分）
        String subjectiveTypesCondition = "4,5,6"; // 简答题、编程题、案例分析题
        
        // 构建基础查询条件 - 只统计主观题
        String baseSql = "SELECT id FROM exam_question WHERE question_type IN (" + subjectiveTypesCondition + ")";
        if (courseId != null) {
            baseSql += " AND course_id = " + courseId;
        }
        
        // 待评分数量 (主观题且grading_status = 0 或 1)
        QueryWrapper<ExamAnswer> pendingWrapper = new QueryWrapper<>();
        pendingWrapper.inSql("question_id", baseSql);
        pendingWrapper.in("grading_status", 0, 1);
        Long pendingCount = examAnswerMapper.selectCount(pendingWrapper);
        
        // 已评分数量 (主观题且教师已评分)
        QueryWrapper<ExamAnswer> gradedWrapper = new QueryWrapper<>();
        gradedWrapper.inSql("question_id", baseSql);
        gradedWrapper.eq("grading_status", 2);
        gradedWrapper.isNotNull("teacher_grading_time");
        Long gradedCount = examAnswerMapper.selectCount(gradedWrapper);
        
        // 主观题总数量
        QueryWrapper<ExamAnswer> totalWrapper = new QueryWrapper<>();
        totalWrapper.inSql("question_id", baseSql);
        Long totalManualReviewCount = examAnswerMapper.selectCount(totalWrapper);
        
        // 各题型待评分统计（只统计主观题）
        Map<String, Long> typeStatistics = new HashMap<>();
        int[] subjectiveTypes = {4, 5, 6}; // 简答题、编程题、案例分析题
        for (int type : subjectiveTypes) {
            QueryWrapper<ExamAnswer> typeWrapper = new QueryWrapper<>();
            typeWrapper.in("grading_status", 0, 1); // 待评分
            if (courseId != null) {
                typeWrapper.inSql("question_id", 
                    String.format("SELECT id FROM exam_question WHERE course_id = %d AND question_type = %d", courseId, type));
            } else {
                typeWrapper.inSql("question_id", 
                    String.format("SELECT id FROM exam_question WHERE question_type = %d", type));
            }
            Long count = examAnswerMapper.selectCount(typeWrapper);
            if (count > 0) { // 只显示有数据的题型
                typeStatistics.put(getQuestionTypeName(type), count);
            }
        }
        
        statistics.put("pendingCount", pendingCount);
        statistics.put("gradedCount", gradedCount);
        statistics.put("totalCount", totalManualReviewCount); // 只统计主观题总数
        statistics.put("typeStatistics", typeStatistics);
        
        // 计算完成度：已评分数量 / 主观题总数量
        double gradingProgress = totalManualReviewCount > 0 ? 
            (double) gradedCount / totalManualReviewCount * 100 : 100.0;
        statistics.put("gradingProgress", gradingProgress);
        
        return statistics;
    }
    
    private String getQuestionTypeName(int type) {
        return switch (type) {
            case 1 -> "单选题";
            case 2 -> "多选题";
            case 3 -> "填空题";
            case 4 -> "简答题";
            case 5 -> "编程题";
            case 6 -> "案例分析题";
            default -> "未知类型";
        };
    }
}