package com.example.aieduforge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.aieduforge.dto.CourseCreateRequest;
import com.example.aieduforge.dto.CourseResponse;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.exception.BusinessException;
import com.example.aieduforge.mapper.CourseMapper;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Course Service Implementation
 */
@Slf4j
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {

    @Autowired
    private CourseMapper courseMapper;
    
    @Autowired
    private SysUserService sysUserService;

    @Override  
    public List<Course> findWithFilters(String courseName, String subject, String teacherName) {
        return courseMapper.findWithFilters(courseName, subject, teacherName);
    }
    
    @Override
    public List<Course> findAll() {
        // 使用 QueryWrapper 获取所有课程，包括禁用的
        return list();  // MyBatis-Plus的list()方法会返回所有记录，不会自动过滤状态
    }

    @Override
    @Transactional
    public Course createCourse(Course course) {
        // Check if course code exists
        if (courseMapper.findByCourseCode(course.getCourseCode()) != null) {
            throw new BusinessException("Course code already exists");
        }
        
        course.setStatus(1);
        course.setCreateTime(LocalDateTime.now());
        course.setUpdateTime(LocalDateTime.now());
        
        if (save(course)) {
            log.info("Course created by admin: {}", course.getCourseName());
            return course;
        }
        
        throw new BusinessException("Failed to create course");
    }

    @Override
    public List<Course> findByTeacherId(Long teacherId) {
        return courseMapper.findByTeacherId(teacherId, true); // 默认只返回启用的课程
    }

    @Override
    public List<Course> findBySubject(String subject) {
        return courseMapper.findBySubject(subject);
    }

    @Override
    @Transactional
    public Course updateCourse(Long courseId, Course course) {
        Course existingCourse = getById(courseId);
        if (existingCourse == null) {
            throw new BusinessException("Course not found");
        }

        // Update only allowed fields
        existingCourse.setCourseName(course.getCourseName());
        existingCourse.setSubject(course.getSubject());
        existingCourse.setDescription(course.getDescription());
        existingCourse.setUpdateTime(LocalDateTime.now());

        if (updateById(existingCourse)) {
            log.info("Course updated by admin: {}", existingCourse.getCourseName());
            return existingCourse;
        }

        throw new BusinessException("Failed to update course");
    }

    @Override
    @Transactional
    public boolean deleteCourse(Long courseId) {
        Course course = getById(courseId);
        if (course == null) {
            throw new BusinessException("Course not found");
        }

        if (removeById(courseId)) {
            log.info("Course deleted by admin: {}", course.getCourseName());
            return true;
        }

        return false;
    }

    @Override
    @Transactional
    public boolean updateStatus(Long courseId, Integer status) {
        Course course = getById(courseId);
        if (course == null) {
            throw new BusinessException("Course not found");
        }

        course.setStatus(status);
        course.setUpdateTime(LocalDateTime.now());

        if (updateById(course)) {
            log.info("Course status updated by admin: {} -> {}", course.getCourseName(), status);
            return true;
        }

        return false;
    }

    @Override
    @Transactional
    public Course createCourse(Course course, Long teacherId) {
        // Check if course code exists
        if (courseMapper.findByCourseCode(course.getCourseCode()) != null) {
            throw new BusinessException("Course code already exists");
        }

        course.setTeacherId(teacherId);
        course.setStatus(1);
        course.setCreateTime(LocalDateTime.now());
        course.setUpdateTime(LocalDateTime.now());

        if (save(course)) {
            log.info("Course created successfully: {}", course.getCourseName());
            return course;
        }

        throw new BusinessException("Failed to create course");
    }

    @Override
    @Transactional
    public Course updateCourse(Long courseId, Course course, Long teacherId) {
        Course existingCourse = getById(courseId);
        if (existingCourse == null) {
            throw new BusinessException("Course not found");
        }
        
        if (!existingCourse.getTeacherId().equals(teacherId)) {
            throw new BusinessException("You don't have permission to update this course");
        }
        
        // Check course code uniqueness if changed
        if (!existingCourse.getCourseCode().equals(course.getCourseCode())) {
            Course codeCheck = courseMapper.findByCourseCode(course.getCourseCode());
            if (codeCheck != null && !codeCheck.getId().equals(courseId)) {
                throw new BusinessException("Course code already exists");
            }
        }
        
        course.setId(courseId);
        course.setTeacherId(teacherId);
        course.setUpdateTime(LocalDateTime.now());
        
        if (updateById(course)) {
            log.info("Course updated successfully: {}", course.getCourseName());
            return course;
        }
        
        throw new BusinessException("Failed to update course");
    }

    @Override
    @Transactional
    public boolean deleteCourse(Long courseId, Long teacherId) {
        Course course = getById(courseId);
        if (course == null) {
            throw new BusinessException("Course not found");
        }
        
        if (!course.getTeacherId().equals(teacherId)) {
            throw new BusinessException("You don't have permission to delete this course");
        }
        
        // Soft delete by setting status to 0
        course.setStatus(0);
        course.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(course);
        if (result) {
            log.info("Course deleted successfully: {}", course.getCourseName());
        }
        
        return result;
    }

    @Override
    public boolean isTeacherOwnsCourse(Long courseId, Long teacherId) {
        Course course = getById(courseId);
        return course != null && course.getTeacherId().equals(teacherId);
    }

    @Override
    public boolean assignTeacher(Long courseId, Long teacherId) {
        Course course = getById(courseId);
        if (course == null) {
            throw new BusinessException("Course not found");
        }
        course.setTeacherId(teacherId);
        course.setUpdateTime(LocalDateTime.now());
        return updateById(course);
    }

    @Override
    public CourseResponse getCourseWithDetails(Long courseId) {
        Course course = getById(courseId);
        if (course == null) {
            throw new BusinessException("Course not found");
        }
        // Assuming sysUserService is available to fetch teacher details
        SysUser teacher = sysUserService.getById(course.getTeacherId());
        return buildCourseResponse(course, teacher);
    }

    @Override
    public CourseResponse createCourseFromRequest(CourseCreateRequest request) {
        if (courseMapper.findByCourseCode(request.getCourseCode()) != null) {
            throw new BusinessException("Course code already exists");
        }
        Course course = new Course();
        course.setCourseName(request.getCourseName());
        course.setCourseCode(request.getCourseCode());
        course.setDescription(request.getDescription());
        course.setSubject(request.getSubject());
        course.setTeacherId(request.getTeacherId());
        course.setStatus(1);
        course.setCreateTime(LocalDateTime.now());
        course.setUpdateTime(LocalDateTime.now());
        if (save(course)) {
            SysUser teacher = sysUserService.getById(course.getTeacherId());
            return buildCourseResponse(course, teacher);
        }
        throw new BusinessException("Failed to create course");
    }

    @Override
    public List<CourseResponse> getAllCoursesWithDetails() {
        List<Course> courses = list();
        List<CourseResponse> courseResponses = new ArrayList<>();
        for (Course course : courses) {
            SysUser teacher = sysUserService.getById(course.getTeacherId());
            courseResponses.add(buildCourseResponse(course, teacher));
        }
        return courseResponses;
    }

    @Override
    public boolean existsByCourseCode(String courseCode) {
        Course course = courseMapper.findByCourseCode(courseCode);
        return course != null;
    }

    private CourseResponse buildCourseResponse(Course course, SysUser teacher) {
        CourseResponse response = new CourseResponse();
        response.setId(course.getId());
        response.setCourseName(course.getCourseName());
        response.setCourseCode(course.getCourseCode());
        response.setDescription(course.getDescription());
        response.setSubject(course.getSubject());
        response.setTeacherId(course.getTeacherId());
        response.setStatus(course.getStatus());
        response.setCreateTime(course.getCreateTime());
        response.setUpdateTime(course.getUpdateTime());
        if (teacher != null) {
            response.setTeacherName(teacher.getRealName());
            response.setTeacherEmail(teacher.getEmail());
        }
        return response;
    }
}
