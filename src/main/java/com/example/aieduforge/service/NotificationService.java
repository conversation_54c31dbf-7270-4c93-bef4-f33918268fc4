package com.example.aieduforge.service;

import java.util.Map;

/**
 * Notification Service Interface
 */
public interface NotificationService {
    
    /**
     * Get user notifications with pagination
     */
    Map<String, Object> getUserNotifications(Long userId, String userRole, Integer page, Integer size);
    
    /**
     * Get unread notification count for user
     */
    int getUnreadCount(Long userId, String userRole);
    
    /**
     * Mark notification as read
     */
    boolean markAsRead(Long notificationId, Long userId);
    
    /**
     * Mark all notifications as read for user
     */
    boolean markAllAsRead(Long userId, String userRole);
    
    /**
     * Publish new notification
     */
    boolean publishNotification(String title, String content, Integer type, 
                              String targetRole, Long targetUserId, Integer priority, Long createUserId);
    
    /**
     * Get latest system notifications (for dashboard)
     */
    Map<String, Object> getLatestSystemNotifications(Integer limit);
    
    /**
     * Get notification detail
     */
    Map<String, Object> getNotificationDetail(Long notificationId, Long userId);
    
    /**
     * Delete notification
     */
    boolean deleteNotification(Long notificationId);
    
    /**
     * Delete expired notifications
     */
    int deleteExpiredNotifications();
    
    /**
     * Get all notifications for admin management
     */
    Map<String, Object> getAllNotifications(Integer page, Integer size, Integer type, String targetRole, Integer priority);
}