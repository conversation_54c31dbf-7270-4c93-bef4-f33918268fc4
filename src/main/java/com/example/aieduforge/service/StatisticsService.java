package com.example.aieduforge.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Statistics Service Interface
 */
public interface StatisticsService {
    
    /**
     * Get dashboard overview data
     */
    Map<String, Object> getDashboardOverview();
    
    /**
     * Get usage statistics by date range
     */
    Map<String, Object> getUsageStatistics(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get teacher efficiency statistics
     */
    Map<String, Object> getTeacherEfficiencyStats(Long teacherId, LocalDate startDate, LocalDate endDate);
    
    /**
     * Get student learning statistics
     */
    Map<String, Object> getStudentLearningStats(Long studentId, Long courseId);
    
    /**
     * Get course performance statistics
     */
    Map<String, Object> getCoursePerformanceStats(Long courseId, LocalDate startDate, LocalDate endDate);
    
    /**
     * Get system usage trends
     */
    List<Map<String, Object>> getUsageTrends(Integer days);
    
    /**
     * Get active users statistics
     */
    Map<String, Object> getActiveUsersStats(LocalDate date);
    
    /**
     * Get module usage ranking
     */
    List<Map<String, Object>> getModuleUsageRanking(Integer userType, LocalDate startDate, LocalDate endDate);
    
    /**
     * Get knowledge point difficulty analysis
     */
    List<Map<String, Object>> getKnowledgePointDifficulty(Long courseId);
    
    /**
     * Record user activity
     */
    void recordUserActivity(Long userId, Integer userType, String moduleName, String actionType, Integer duration);
    
    /**
     * Get teacher usage statistics for dashboard
     */
    Map<String, Object> getTeacherUsageStats();
    
    /**
     * Get student usage statistics for dashboard
     */
    Map<String, Object> getStudentUsageStats();
}