package com.example.aieduforge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.aieduforge.entity.KnowledgeBase;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Resource Management Service Interface
 */
public interface ResourceManagementService extends IService<KnowledgeBase> {
    
    /**
     * Upload knowledge base file
     */
    KnowledgeBase uploadKnowledgeFile(MultipartFile file, Long courseId, String subject, String tags, String description, Long uploadUserId);
    
    /**
     * Search knowledge base by keyword
     */
    List<KnowledgeBase> searchKnowledge(String keyword);
    
    /**
     * Get knowledge base by subject
     */
    List<KnowledgeBase> getKnowledgeBySubject(String subject);
    
    /**
     * Get knowledge base by course
     */
    List<KnowledgeBase> getKnowledgeByCourse(Long courseId);
    
    /**
     * Get knowledge base by tags
     */
    List<KnowledgeBase> getKnowledgeByTags(String tags);
    
    /**
     * Export course resources
     */
    Map<String, Object> exportCourseResources(Long courseId);
    
    /**
     * Get file type statistics
     */
    List<Map<String, Object>> getFileTypeStatistics();
    
    /**
     * Delete knowledge base file
     */
    boolean deleteKnowledgeFile(Long id, Long userId);
    
    /**
     * Update knowledge base info
     */
    KnowledgeBase updateKnowledgeInfo(Long id, KnowledgeBase knowledgeBase, Long userId);
    
    /**
     * Get resource usage statistics
     */
    Map<String, Object> getResourceUsageStats();
    
    /**
     * Download knowledge base file
     */
    void downloadKnowledgeFile(Long id, Long userId, jakarta.servlet.http.HttpServletResponse response) throws Exception;
    
    /**
     * Get knowledge base file details with AI processed data
     */
    Map<String, Object> getKnowledgeFileDetails(Long id);
    
    /**
     * Get course resources including knowledge files and teaching content
     */
    List<Map<String, Object>> getCourseResources(Long courseId);
    
    /**
     * Export course resources as DOCX
     */
    void exportCourseResourcesAsDocx(Long courseId, jakarta.servlet.http.HttpServletResponse response) throws Exception;
    
    /**
     * Export knowledge base as DOCX
     */
    void exportKnowledgeBaseAsDocx(jakarta.servlet.http.HttpServletResponse response) throws Exception;
    
    /**
     * Export single resource as DOCX
     */
    void exportSingleResourceAsDocx(Long resourceId, String type, jakarta.servlet.http.HttpServletResponse response) throws Exception;
    
    /**
     * Search knowledge base with multiple filters
     */
    Map<String, Object> searchKnowledgeWithFilters(Map<String, Object> searchParams);
}