package com.example.aieduforge.service;

import java.util.Map;

/**
 * 验证码服务接口
 */
public interface CaptchaService {
    
    /**
     * 生成验证码
     * @param sessionId 会话ID
     * @return 验证码数据（包含图片base64和会话ID）
     */
    Map<String, Object> generateCaptcha(String sessionId);
    
    /**
     * 验证验证码
     * @param sessionId 会话ID
     * @param captcha 用户输入的验证码
     * @return 验证结果
     */
    boolean verifyCaptcha(String sessionId, String captcha);
    
    /**
     * 清理过期的验证码记录
     */
    void cleanExpiredCaptcha();
    
    /**
     * 检查验证码功能是否启用
     * @return 是否启用
     */
    boolean isCaptchaEnabled();
}