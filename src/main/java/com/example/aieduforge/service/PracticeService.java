package com.example.aieduforge.service;

import com.example.aieduforge.entity.ExamAnswer;
import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.entity.PracticeRecord;

import java.util.List;
import java.util.Map;

/**
 * Practice Service Interface
 */
public interface PracticeService {
    
    /**
     * Generate practice questions for student
     */
    List<ExamQuestion> generatePracticeQuestions(Long studentId, Long courseId, Integer practiceType, Integer count, Integer questionType);
    
    /**
     * Start a practice session
     */
    PracticeRecord startPractice(Long studentId, Long courseId, Integer practiceType, List<Long> questionIds);
    
    /**
     * Submit practice answer
     */
    ExamAnswer submitAnswer(Long practiceId, Long questionId, String studentAnswer, Long studentId);
    
    /**
     * Complete practice session
     */
    PracticeRecord completePractice(Long practiceId, Long studentId);
    
    /**
     * Get practice history
     */
    List<PracticeRecord> getPracticeHistory(Long studentId, Long courseId);
    
    /**
     * Get practice statistics
     */
    Map<String, Object> getPracticeStatistics(Long studentId, Long courseId);

    /**
     * Get wrong questions for review
     */
    List<ExamQuestion> getWrongQuestions(Long studentId, Long courseId);
    
    /**
     * Get student answer details
     */
    ExamAnswer getStudentAnswer(Long studentId, Long questionId);
    
    /**
     * Get practice record details by ID
     */
    PracticeRecord getPracticeRecordById(Long practiceId, Long studentId);
    
    /**
     * Get complete practice review data including questions and answers
     */
    com.example.aieduforge.dto.PracticeReviewDTO getPracticeReviewData(Long practiceId, Long studentId);
}