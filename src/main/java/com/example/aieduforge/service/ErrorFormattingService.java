package com.example.aieduforge.service;

import com.example.aieduforge.dto.ErrorResponseDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.Instant;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI模型错误信息格式化服务
 */
@Slf4j
@Service
public class ErrorFormattingService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 错误类型映射
    private static final Map<Integer, String> ERROR_TYPE_MAP = Map.of(
        400, "BAD_REQUEST",
        401, "AUTHENTICATION_ERROR", 
        403, "AUTHORIZATION_ERROR",
        429, "RATE_LIMIT_EXCEEDED",
        500, "INTERNAL_SERVER_ERROR",
        502, "BAD_GATEWAY",
        503, "SERVICE_UNAVAILABLE",
        504, "GATEWAY_TIMEOUT"
    );

    /**
     * 格式化AI模型连接错误
     */
    public ErrorResponseDto formatModelConnectionError(String rawErrorMessage, String modelName) {
        try {
            // 解析原始错误信息
            ParsedError parsedError = parseRawError(rawErrorMessage);
            
            // 构建格式化的错误响应
            return ErrorResponseDto.builder()
                .errorInfo(buildErrorInfo(parsedError, modelName))
                .errorDetails(buildErrorDetails(parsedError))
                .troubleshooting(buildTroubleshootingInfo(parsedError))
                .formattedJson(formatAsJson(parsedError))
                .build();
                
        } catch (Exception e) {
            log.error("Error formatting model connection error", e);
            return buildFallbackErrorResponse(rawErrorMessage, modelName);
        }
    }

    /**
     * 解析原始错误信息
     */
    private ParsedError parseRawError(String rawError) {
        ParsedError parsed = new ParsedError();
        
        try {
            // 提取HTTP状态码
            Pattern statusPattern = Pattern.compile("(\\d{3})\\s+([A-Za-z\\s]+)");
            Matcher statusMatcher = statusPattern.matcher(rawError);
            if (statusMatcher.find()) {
                parsed.httpStatus = Integer.parseInt(statusMatcher.group(1));
                parsed.statusText = statusMatcher.group(2).trim();
            }

            // 提取JSON错误信息
            Pattern jsonPattern = Pattern.compile("\\{.*\\}");
            Matcher jsonMatcher = jsonPattern.matcher(rawError);
            if (jsonMatcher.find()) {
                String jsonStr = jsonMatcher.group();
                JsonNode errorJson = objectMapper.readTree(jsonStr);
                
                if (errorJson.has("error")) {
                    JsonNode error = errorJson.get("error");
                    parsed.errorMessage = error.has("message") ? error.get("message").asText() : "";
                    parsed.errorCode = error.has("code") ? error.get("code").asInt() : parsed.httpStatus;
                    
                    // 解析元数据
                    if (error.has("metadata")) {
                        JsonNode metadata = error.get("metadata");
                        parsed.metadata = objectMapper.convertValue(metadata, Map.class);
                        
                        // 提取频率限制信息
                        if (metadata.has("headers")) {
                            JsonNode headers = metadata.get("headers");
                            parsed.rateLimitInfo = extractRateLimitInfo(headers);
                        }
                    }
                }
                
                // 提取用户ID
                if (errorJson.has("user_id")) {
                    parsed.userId = errorJson.get("user_id").asText();
                }
                
                parsed.originalJson = jsonStr;
            }

            // 提取URL信息
            Pattern urlPattern = Pattern.compile("(https?://[^\\s\"]+)");
            Matcher urlMatcher = urlPattern.matcher(rawError);
            if (urlMatcher.find()) {
                parsed.endpoint = urlMatcher.group(1);
            }

        } catch (Exception e) {
            log.warn("Failed to parse error details, using raw message", e);
            parsed.errorMessage = rawError;
        }

        return parsed;
    }

    /**
     * 提取频率限制信息
     */
    private ErrorResponseDto.RateLimitInfo extractRateLimitInfo(JsonNode headers) {
        return ErrorResponseDto.RateLimitInfo.builder()
            .limit(headers.has("X-RateLimit-Limit") ? headers.get("X-RateLimit-Limit").asText() : null)
            .remaining(headers.has("X-RateLimit-Remaining") ? headers.get("X-RateLimit-Remaining").asText() : null)
            .resetTime(headers.has("X-RateLimit-Reset") ? 
                formatResetTime(headers.get("X-RateLimit-Reset").asLong()) : null)
            .resetTimestamp(headers.has("X-RateLimit-Reset") ? 
                headers.get("X-RateLimit-Reset").asLong() : null)
            .upgradeMessage("Add 10 credits to unlock 1000 free model requests per day")
            .build();
    }

    /**
     * 格式化重置时间
     */
    private String formatResetTime(long timestamp) {
        try {
            return Instant.ofEpochMilli(timestamp).toString();
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * 构建错误基本信息
     */
    private ErrorResponseDto.ErrorInfo buildErrorInfo(ParsedError parsed, String modelName) {
        String errorType = ERROR_TYPE_MAP.getOrDefault(parsed.httpStatus, "UNKNOWN_ERROR");
        String title = generateErrorTitle(parsed.httpStatus, errorType);
        
        return ErrorResponseDto.ErrorInfo.builder()
            .title(title)
            .message(parsed.errorMessage)
            .errorCode(String.valueOf(parsed.errorCode))
            .httpStatus(parsed.httpStatus)
            .errorType(errorType)
            .timestamp(LocalDateTime.now())
            .modelName(modelName)
            .provider(extractProvider(parsed.endpoint))
            .build();
    }

    /**
     * 构建错误详细信息
     */
    private ErrorResponseDto.ErrorDetails buildErrorDetails(ParsedError parsed) {
        return ErrorResponseDto.ErrorDetails.builder()
            .originalMessage(parsed.originalJson != null ? parsed.originalJson : parsed.errorMessage)
            .metadata(parsed.metadata)
            .endpoint(parsed.endpoint)
            .userId(parsed.userId)
            .build();
    }

    /**
     * 构建故障排除信息
     */
    private ErrorResponseDto.TroubleshootingInfo buildTroubleshootingInfo(ParsedError parsed) {
        List<String> suggestions = generateSuggestions(parsed.httpStatus);
        List<String> documentationLinks = generateDocumentationLinks(parsed.httpStatus);
        List<String> alternativeModels = generateAlternativeModels(parsed.httpStatus);
        
        return ErrorResponseDto.TroubleshootingInfo.builder()
            .suggestions(suggestions)
            .documentationLinks(documentationLinks)
            .alternativeModels(alternativeModels)
            .rateLimitInfo(parsed.rateLimitInfo)
            .build();
    }

    /**
     * 生成错误标题
     */
    private String generateErrorTitle(Integer httpStatus, String errorType) {
        return switch (httpStatus) {
            case 429 -> "Rate Limit Exceeded";
            case 401 -> "Authentication Failed";
            case 403 -> "Access Denied";
            case 500 -> "Internal Server Error";
            case 502 -> "Bad Gateway";
            case 503 -> "Service Unavailable";
            case 504 -> "Gateway Timeout";
            default -> "Connection Failed";
        };
    }

    /**
     * 生成解决建议
     */
    private List<String> generateSuggestions(Integer httpStatus) {
        return switch (httpStatus) {
            case 429 -> Arrays.asList(
                "Wait for the rate limit to reset",
                "Consider upgrading your plan for higher limits",
                "Implement request throttling in your application",
                "Try using a different model with higher limits"
            );
            case 401 -> Arrays.asList(
                "Check your API key is correct",
                "Verify your account is active",
                "Ensure proper authentication headers are set"
            );
            case 403 -> Arrays.asList(
                "Verify you have permission to access this model",
                "Check if your account has the required subscription",
                "Contact support if you believe this is an error"
            );
            default -> Arrays.asList(
                "Check your internet connection",
                "Verify the model endpoint is correct",
                "Try again in a few moments"
            );
        };
    }

    /**
     * 生成文档链接
     */
    private List<String> generateDocumentationLinks(Integer httpStatus) {
        return Arrays.asList(
            "https://openrouter.ai/docs/limits",
            "https://openrouter.ai/docs/errors",
            "https://openrouter.ai/docs/authentication"
        );
    }

    /**
     * 生成替代模型建议
     */
    private List<String> generateAlternativeModels(Integer httpStatus) {
        if (httpStatus == 429) {
            return Arrays.asList(
                "gpt-3.5-turbo",
                "claude-3-haiku",
                "gemma-2-9b-it"
            );
        }
        return new ArrayList<>();
    }

    /**
     * 提取提供商信息
     */
    private String extractProvider(String endpoint) {
        if (endpoint == null) return "Unknown";
        if (endpoint.contains("openrouter.ai")) return "OpenRouter";
        if (endpoint.contains("openai.com")) return "OpenAI";
        if (endpoint.contains("anthropic.com")) return "Anthropic";
        return "Unknown";
    }

    /**
     * 格式化为JSON
     */
    private String formatAsJson(ParsedError parsed) {
        try {
            Map<String, Object> formatted = new HashMap<>();
            formatted.put("httpStatus", parsed.httpStatus);
            formatted.put("errorMessage", parsed.errorMessage);
            formatted.put("endpoint", parsed.endpoint);
            formatted.put("userId", parsed.userId);
            formatted.put("metadata", parsed.metadata);
            
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(formatted);
        } catch (Exception e) {
            return parsed.originalJson != null ? parsed.originalJson : "{}";
        }
    }

    /**
     * 构建备用错误响应
     */
    private ErrorResponseDto buildFallbackErrorResponse(String rawError, String modelName) {
        return ErrorResponseDto.builder()
            .errorInfo(ErrorResponseDto.ErrorInfo.builder()
                .title("Connection Failed")
                .message("Failed to connect to AI model")
                .errorCode("UNKNOWN")
                .httpStatus(500)
                .errorType("UNKNOWN_ERROR")
                .timestamp(LocalDateTime.now())
                .modelName(modelName)
                .provider("Unknown")
                .build())
            .errorDetails(ErrorResponseDto.ErrorDetails.builder()
                .originalMessage(rawError)
                .build())
            .troubleshooting(ErrorResponseDto.TroubleshootingInfo.builder()
                .suggestions(Arrays.asList("Check your connection and try again"))
                .build())
            .formattedJson("{}")
            .build();
    }

    /**
     * 解析后的错误信息内部类
     */
    private static class ParsedError {
        Integer httpStatus;
        String statusText;
        String errorMessage;
        Integer errorCode;
        String endpoint;
        String userId;
        String originalJson;
        Map<String, Object> metadata;
        ErrorResponseDto.RateLimitInfo rateLimitInfo;
    }
}