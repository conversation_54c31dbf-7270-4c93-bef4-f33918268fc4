package com.example.aieduforge.service;

import com.example.aieduforge.entity.AiAnswer;
import com.example.aieduforge.entity.StudentQuestion;

import java.util.List;

/**
 * Student Learning Service Interface
 */
public interface StudentLearningService {
    
    /**
     * Submit a question and get AI answer
     */
    AiAnswer askQuestion(StudentQuestion question);
    
    /**
     * Get student's question history
     */
    List<StudentQuestion> getQuestionHistory(Long studentId, Long courseId);
    
    /**
     * Get AI answer for a question
     */
    AiAnswer getAiAnswer(Long questionId);
    
    /**
     * Rate AI answer
     */
    boolean rateAnswer(Long answerId, Integer rating, String comment);
    
    /**
     * Get recent questions by student
     */
    List<StudentQuestion> getRecentQuestions(Long studentId, Integer limit);
    
    /**
     * Update question status
     */
    boolean updateQuestionStatus(Long questionId, Integer status, Long studentId);
    
    /**
     * Calculate learning progress for a student in a course
     * Based on the number of teaching contents the student has asked questions about
     */
    int calculateLearningProgress(Long studentId, Long courseId);
}