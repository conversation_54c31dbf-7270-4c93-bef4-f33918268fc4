package com.example.aieduforge.service;

import com.example.aieduforge.entity.AiModel;

/**
 * Dynamic AI Service Interface that uses database-configured models
 */
public interface DynamicAIService {
    
    /**
     * Generate response using the current active AI model
     */
    String generateResponse(String prompt);
    
    /**
     * Generate response using a specific AI model
     */
    String generateResponse(String prompt, Long modelId);
    
    /**
     * Get current active model configuration
     */
    AiModel getCurrentModel();
    
    /**
     * Test if the current active model is available
     */
    boolean isCurrentModelAvailable();
}