package com.example.aieduforge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.aieduforge.entity.Course;

import java.util.List;

/**
 * Course Service Interface
 */
public interface CourseService extends IService<Course> {
    
    /**
     * Create new course
     */
    Course createCourse(Course course, Long teacherId);
    
    /**
     * Find courses by teacher
     */
    List<Course> findByTeacherId(Long teacherId);
    
    /**
     * Find courses with filters
     */
    List<Course> findWithFilters(String courseName, String subject, String teacherName);
    
    /**
     * Find courses by subject
     */
    List<Course> findBySubject(String subject);
    
    /**
     * Update course
     */
    Course updateCourse(Long courseId, Course course, Long teacherId);
    
    /**
     * Delete course
     */
    boolean deleteCourse(Long courseId, Long teacherId);
    
    /**
     * Check if teacher owns course
     */
    boolean isTeacherOwnsCourse(Long courseId, Long teacherId);

    /**
     * Create course (admin)
     */
    Course createCourse(Course course);

    /**
     * Update course (admin)
     */
    Course updateCourse(Long courseId, Course course);

    /**
     * Delete course (admin)
     */
    boolean deleteCourse(Long courseId);

    /**
     * Update course status (admin)
     */
    boolean updateStatus(Long courseId, Integer status);

    /**
     * Find all courses (admin)
     */
    List<Course> findAll();
    
    /**
     * Assign teacher to course
     */
    boolean assignTeacher(Long courseId, Long teacherId);
    
    /**
     * Get course with teacher details
     */
    com.example.aieduforge.dto.CourseResponse getCourseWithDetails(Long courseId);
    
    /**
     * Create course from request DTO
     */
    com.example.aieduforge.dto.CourseResponse createCourseFromRequest(com.example.aieduforge.dto.CourseCreateRequest request);
    
    /**
     * Get all courses with teacher details
     */
    java.util.List<com.example.aieduforge.dto.CourseResponse> getAllCoursesWithDetails();
    
    /**
     * Check if course code exists
     */
    boolean existsByCourseCode(String courseCode);
}
