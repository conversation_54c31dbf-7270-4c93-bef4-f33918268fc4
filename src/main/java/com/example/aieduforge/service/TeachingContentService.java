package com.example.aieduforge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.aieduforge.dto.ContentGenerationRequest;
import com.example.aieduforge.entity.TeachingContent;

import java.util.List;

/**
 * Teaching Content Service Interface
 */
public interface TeachingContentService extends IService<TeachingContent> {
    
    /**
     * Generate teaching content using AI
     */
    TeachingContent generateContent(ContentGenerationRequest request, Long teacherId);
    
    /**
     * Find content by course ID
     */
    List<TeachingContent> findByCourseId(Long courseId);
    
    /**
     * Find content by outline ID
     */
    List<TeachingContent> findByOutlineId(Long outlineId);
    
    /**
     * Find content by type
     */
    List<TeachingContent> findByContentType(Long courseId, Integer contentType);
    
    /**
     * Update teaching content
     */
    TeachingContent updateContent(Long contentId, TeachingContent content, Long teacherId);
    
    /**
     * Delete teaching content
     */
    boolean deleteContent(Long contentId, Long teacherId);
    
    /**
     * Check if teacher owns content
     */
    boolean isTeacherOwnsContent(Long contentId, Long teacherId);
}