package com.example.aieduforge.service;

import java.util.Map;

/**
 * 通知统计服务接口
 */
public interface NotificationStatsService {
    
    /**
     * 获取通知概览统计
     */
    Map<String, Object> getOverviewStats();
    
    /**
     * 获取通知发送趋势
     */
    Map<String, Object> getTrendStats(Integer days);
    
    /**
     * 获取通知类型分布
     */
    Map<String, Object> getTypeDistribution();
    
    /**
     * 获取用户阅读统计
     */
    Map<String, Object> getReadStats();
    
    /**
     * 获取WebSocket连接统计
     */
    Map<String, Object> getWebSocketStats();
}