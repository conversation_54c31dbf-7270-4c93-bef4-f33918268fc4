package com.example.aieduforge.service;

import com.example.aieduforge.entity.KnowledgeBase;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * AI文件处理服务接口
 */
public interface AIFileProcessingService {
    
    /**
     * 处理上传的文件，提取内容并转换为AI可学习的格式
     */
    Map<String, Object> processUploadedFile(MultipartFile file, KnowledgeBase knowledgeBase);
    
    /**
     * 从文件中提取文本内容
     */
    String extractTextContent(MultipartFile file);
    
    /**
     * 使用AI分析文件内容并生成标签
     */
    String generateSmartTags(String content, String subject);
    
    /**
     * 将文件内容转换为结构化数据
     */
    Map<String, Object> convertToStructuredData(String content, String fileType);
    
    /**
     * 生成文件摘要
     */
    String generateSummary(String content);
    
    /**
     * 提取关键知识点
     */
    String extractKeyPoints(String content, String subject);
    
    /**
     * 更新AI知识库
     */
    boolean updateAIKnowledgeBase(KnowledgeBase knowledgeBase, Map<String, Object> processedData);
}