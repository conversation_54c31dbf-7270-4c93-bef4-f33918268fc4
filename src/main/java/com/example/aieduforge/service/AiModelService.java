package com.example.aieduforge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.aieduforge.dto.AiModelDTO;
import com.example.aieduforge.dto.ModelConnectionTestDTO;
import com.example.aieduforge.entity.AiModel;

import java.util.List;

/**
 * AI Model Service Interface
 */
public interface AiModelService {

    /**
     * Get paginated list of AI models
     */
    IPage<AiModelDTO> getModelList(Page<AiModel> page, String provider, Boolean isActive);

    /**
     * Get all active models
     */
    List<AiModelDTO> getActiveModels();

    /**
     * Get model by ID
     */
    AiModelDTO getModelById(Long id);

    /**
     * Get current active model
     */
    AiModelDTO getActiveModel();

    /**
     * Get default model
     */
    AiModelDTO getDefaultModel();

    /**
     * Create new AI model
     */
    AiModelDTO createModel(AiModelDTO modelDTO, Long userId);

    /**
     * Update AI model
     */
    AiModelDTO updateModel(Long id, AiModelDTO modelDTO, Long userId);

    /**
     * Delete AI model
     */
    boolean deleteModel(Long id);

    /**
     * Set model as active (deactivate others)
     */
    boolean setActiveModel(Long id);

    /**
     * Set model as default
     */
    boolean setDefaultModel(Long id);

    /**
     * Test model connection
     */
    ModelConnectionTestDTO testModelConnection(Long id);

    /**
     * Test model connection with custom parameters
     */
    ModelConnectionTestDTO testModelConnection(AiModelDTO modelDTO);

    /**
     * Batch test all models
     */
    List<ModelConnectionTestDTO> testAllModels();

    /**
     * Get model configuration for AI service
     */
    AiModel getModelForAIService();
}