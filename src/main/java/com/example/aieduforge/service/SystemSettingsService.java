package com.example.aieduforge.service;

import com.example.aieduforge.entity.SystemSettings;

import java.util.List;

/**
 * 系统设置服务接口
 */
public interface SystemSettingsService {
    
    /**
     * 根据设置键获取设置值
     * @param settingKey 设置键
     * @return 设置值
     */
    String getSettingValue(String settingKey);
    
    /**
     * 根据设置键获取设置值，如果不存在则返回默认值
     * @param settingKey 设置键
     * @param defaultValue 默认值
     * @return 设置值
     */
    String getSettingValue(String settingKey, String defaultValue);
    
    /**
     * 更新设置值
     * @param settingKey 设置键
     * @param settingValue 设置值
     */
    void updateSetting(String settingKey, String settingValue);
    
    /**
     * 获取所有系统设置
     * @return 系统设置列表
     */
    List<SystemSettings> getAllSettings();
    
    /**
     * 获取验证码相关设置
     * @return 验证码设置
     */
    SystemSettings getCaptchaSettings();
    
    /**
     * 切换验证码开关
     * @param enabled 是否启用
     */
    void toggleCaptchaEnabled(boolean enabled);
    
    /**
     * 更新验证码长度
     * @param length 验证码长度
     */
    void updateCaptchaLength(int length);
    
    /**
     * 更新验证码过期时间
     * @param expireMinutes 过期时间（分钟）
     */
    void updateCaptchaExpireMinutes(int expireMinutes);
}