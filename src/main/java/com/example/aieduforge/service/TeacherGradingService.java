package com.example.aieduforge.service;

import com.example.aieduforge.dto.GradingRequest;
import com.example.aieduforge.entity.ExamAnswer;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Teacher Grading Service Interface
 */
public interface TeacherGradingService {
    
    /**
     * 获取需要人工评分的答案列表
     */
    List<ExamAnswer> getPendingGradingAnswers(Long teacherId, Long courseId, Integer questionType, Integer current, Integer size);
    
    /**
     * 获取答案列表（支持按评分状态筛选）
     */
    List<ExamAnswer> getAnswersForGrading(Long teacherId, Long courseId, Integer questionType, Integer gradingStatus, Integer current, Integer size);
    
    /**
     * 获取答案详情（包含题目信息和学生信息）
     */
    Map<String, Object> getAnswerDetail(Long answerId, Long teacherId);
    
    /**
     * 教师评分
     */
    boolean gradeAnswer(Long answerId, BigDecimal score, Integer isCorrect, String feedback, String improvementSuggestion, Long teacherId);
    
    /**
     * 批量评分
     */
    int batchGradeAnswers(List<GradingRequest> requests, Long teacherId);
    
    /**
     * 获取评分统计信息
     */
    Map<String, Object> getGradingStatistics(Long teacherId, Long courseId);
}