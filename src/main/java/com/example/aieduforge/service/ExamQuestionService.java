package com.example.aieduforge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.aieduforge.entity.ExamQuestion;
import java.util.List;

public interface ExamQuestionService extends IService<ExamQuestion> {
    /**
     * 获取课程题目列表
     */
    List<ExamQuestion> getQuestionsByCourse(Long courseId);

    /**
     * 随机获取指定数量的题目
     */
    List<ExamQuestion> findRandomQuestions(Long courseId, Integer count);

    /**
     * 根据难度等级获取题目
     */
    List<ExamQuestion> findByDifficultyLevel(Long courseId, int difficulty);

    /**
     * 根据ID获取题目
     */
    ExamQuestion getById(Long id);

    /**
     * 根据题目类型随机获取指定数量的题目
     */
    List<ExamQuestion> findRandomQuestionsByType(Long courseId, Integer questionType, Integer count);
}
