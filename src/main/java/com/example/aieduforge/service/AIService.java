package com.example.aieduforge.service;

import com.example.aieduforge.dto.QuestionDTO;
import java.util.List;

/**
 * AI Service Interface for Ollama Integration
 */
public interface AIService {
    
    /**
     * Generate teaching content based on course outline
     */
    String generateTeachingContent(String courseOutline, String chapterName, String contentType);
    
    /**
     * Generate exam questions based on teaching content
     */
    List<QuestionDTO> generateExamQuestions(String teachingContent, String questionType, Integer difficulty, Integer count);

    /**
     * Analyze student answer and provide feedback
     */
    String analyzeStudentAnswer(String question, String correctAnswer, String studentAnswer);
    
    /**
     * 智能评分 - 使用AI模型对学生答案进行评分
     */
    com.example.aieduforge.dto.AIGradingResponse intelligentGrading(
        String questionTitle, 
        String questionContent, 
        Integer questionType, 
        String correctAnswer, 
        String studentAnswer, 
        Double totalScore,
        Integer difficultyLevel
    );
    
    /**
     * Generate error analysis and improvement suggestions
     */
    String generateErrorAnalysis(String studentErrors, String knowledgePoints);
    
    /**
     * Generate response based on prompt
     */
    String generateResponse(String prompt);
    
    /**
     * Test Ollama connection
     */
    boolean testConnection();
}