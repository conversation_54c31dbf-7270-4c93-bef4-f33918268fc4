package com.example.aieduforge.service;

import java.util.List;
import java.util.Map;

/**
 * 增强AI服务接口 - 基于知识库的智能问答
 */
public interface EnhancedAIService {
    
    /**
     * 基于知识库回答问题
     */
    String answerQuestionWithKnowledge(String question, String subject, Long courseId);
    
    /**
     * 基于知识库回答问题并保存记录
     */
    Map<String, Object> answerQuestionWithKnowledgeAndSave(String question, String subject, Long courseId, Long studentId);
    
    /**
     * 获取知识文件内容
     */
    Map<String, Object> getKnowledgeFileContent(Long knowledgeId);
    
    /**
     * 搜索相关知识库内容
     */
    List<Map<String, Object>> searchRelevantKnowledge(String query, String subject, int limit);
    
    /**
     * 生成基于知识库的练习题
     */
    List<Map<String, Object>> generateQuestionsFromKnowledge(String subject, Long courseId, int count);
    
    /**
     * 分析学生答案并提供反馈
     */
    Map<String, Object> analyzeStudentAnswer(String question, String studentAnswer, String correctAnswer);
    
    /**
     * 推荐相关学习资源
     */
    List<Map<String, Object>> recommendLearningResources(String topic, String subject, Long studentId);
    
    /**
     * 生成个性化学习计划
     */
    Map<String, Object> generatePersonalizedStudyPlan(Long studentId, Long courseId);
    
    /**
     * 保存问答记录（不调用AI）
     */
    Map<String, Object> saveQuestionAndAnswer(String question, String answer, String subject, 
                                            Long courseId, Long studentId, String questionSource);
}