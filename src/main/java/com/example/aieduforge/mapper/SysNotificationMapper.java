package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.SysNotification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 系统通知Mapper
 */
@Mapper
public interface SysNotificationMapper extends BaseMapper<SysNotification> {
    
    /**
     * 获取用户的通知列表
     */
    List<Map<String, Object>> getUserNotifications(@Param("userId") Long userId, 
                                                  @Param("userRole") String userRole, 
                                                  @Param("limit") Integer limit,
                                                  @Param("offset") Integer offset);
    
    /**
     * 获取用户通知总数
     */
    int getUserNotificationCount(@Param("userId") Long userId, @Param("userRole") String userRole);
    
    /**
     * 获取用户未读通知数量
     */
    Integer getUnreadNotificationCount(@Param("userId") Long userId, @Param("userRole") String userRole);
    
    /**
     * 获取用户未读通知列表
     */
    List<Map<String, Object>> getUnreadNotifications(@Param("userId") Long userId, @Param("userRole") String userRole);
    
    /**
     * 插入新通知
     */
    int insertNotification(@Param("title") String title,
                          @Param("content") String content,
                          @Param("type") Integer type,
                          @Param("targetRole") String targetRole,
                          @Param("targetUserId") Long targetUserId,
                          @Param("priority") Integer priority,
                          @Param("createUserId") Long createUserId,
                          @Param("createTime") java.time.LocalDateTime createTime);
    
    /**
     * 获取最新的系统通知（用于管理员仪表板）
     */
    List<Map<String, Object>> getLatestSystemNotifications(@Param("limit") Integer limit);
    
    /**
     * 获取通知详情
     */
    Map<String, Object> getNotificationDetail(@Param("notificationId") Long notificationId);
    
    /**
     * 删除通知
     */
    int deleteNotification(@Param("notificationId") Long notificationId);
    
    /**
     * 清理过期通知
     */
    int cleanExpiredNotifications();
    
    /**
     * 清理旧的用户通知记录
     */
    int cleanOldUserNotifications(@Param("days") Integer days);
    
    /**
     * 获取指定日期的通知发送数量
     */
    int getNotificationCountByDate(@Param("date") String date);
    
    /**
     * 获取指定日期的通知阅读数量
     */
    int getReadNotificationCountByDate(@Param("date") String date);
    
    /**
     * 获取通知总数
     */
    int getTotalNotificationCount();
    
    /**
     * 获取通知类型统计
     */
    List<Map<String, Object>> getNotificationTypeStats();
    
    /**
     * 按角色获取阅读统计
     */
    List<Map<String, Object>> getReadStatsByRole();
    
    /**
     * 按优先级获取阅读统计
     */
    List<Map<String, Object>> getReadStatsByPriority();
    
    /**
     * 获取所有通知（管理员用）
     */
    List<Map<String, Object>> getAllNotifications(@Param("type") Integer type,
                                                 @Param("targetRole") String targetRole,
                                                 @Param("priority") Integer priority,
                                                 @Param("limit") Integer limit,
                                                 @Param("offset") Integer offset);
    
    /**
     * 获取所有通知总数（管理员用）
     */
    int getAllNotificationCount(@Param("type") Integer type,
                               @Param("targetRole") String targetRole,
                               @Param("priority") Integer priority);
}