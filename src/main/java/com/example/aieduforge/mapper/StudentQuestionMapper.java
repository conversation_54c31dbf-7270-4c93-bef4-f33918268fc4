package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.StudentQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 学生问题
 */
@Mapper
@Repository
public interface StudentQuestionMapper extends BaseMapper<StudentQuestion> {
    
    /**
     * Find questions by student ID
     */
    List<StudentQuestion> findByStudentId(@Param("studentId") Long studentId);
    
    /**
     * Find questions by course ID
     */
    List<StudentQuestion> findByCourseId(@Param("courseId") Long courseId);
    
    /**
     * Find questions by student and course
     */
    List<StudentQuestion> findByStudentAndCourse(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
    
    /**
     * Find questions by status
     */
    List<StudentQuestion> findByStatus(@Param("status") Integer status);
    
    /**
     * Find recent questions by student
     */
    List<StudentQuestion> findRecentQuestionsByStudent(@Param("studentId") Long studentId, @Param("limit") Integer limit);
    
    /**
     * Get question activities by date
     */
    List<java.util.Map<String, Object>> getQuestionActivitiesByDate(@Param("date") java.time.LocalDate date);
}