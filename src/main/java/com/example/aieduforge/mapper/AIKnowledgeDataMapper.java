package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.AIKnowledgeData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * AI知识库数据Mapper
 */
@Mapper
public interface AIKnowledgeDataMapper extends BaseMapper<AIKnowledgeData> {
    
    /**
     * 根据知识库ID查找AI数据
     */
    AIKnowledgeData findByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * 搜索相似内容
     */
    List<Map<String, Object>> searchSimilarContent(@Param("keyword") String keyword, @Param("limit") Integer limit);
    
    /**
     * 获取知识点统计
     */
    Map<String, Object> getKnowledgeStatistics();
}