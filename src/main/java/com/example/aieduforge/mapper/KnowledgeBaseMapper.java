package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.KnowledgeBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Knowledge Base Mapper
 */
@Mapper
@Repository
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {
    
    /**
     * Find by course ID
     */
    List<KnowledgeBase> findByCourseId(@Param("courseId") Long courseId);
    
    /**
     * Find by subject
     */
    List<KnowledgeBase> findBySubject(@Param("subject") String subject);
    
    /**
     * Find by upload user
     */
    List<KnowledgeBase> findByUploadUser(@Param("uploadUserId") Long uploadUserId);
    
    /**
     * Search by title or content
     */
    List<KnowledgeBase> searchByKeyword(@Param("keyword") String keyword);
    
    /**
     * Find by tags
     */
    List<KnowledgeBase> findByTags(@Param("tags") String tags);
    
    /**
     * Get file type statistics
     */
    List<Map<String, Object>> getFileTypeStats();
    
    /**
     * Get resource usage statistics
     */
    List<Map<String, Object>> getResourceUsageStats();
    
    /**
     * Get AI knowledge data by knowledge base ID
     */
    com.example.aieduforge.entity.AIKnowledgeData selectAIKnowledgeData(@Param("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * Get all AI knowledge data
     */
    List<com.example.aieduforge.entity.AIKnowledgeData> selectAllAIKnowledgeData();
    
    /**
     * Search knowledge base with multiple filters
     */
    List<KnowledgeBase> searchWithFilters(@Param("keyword") String keyword, 
                                         @Param("subject") String subject, 
                                         @Param("fileType") String fileType);
}