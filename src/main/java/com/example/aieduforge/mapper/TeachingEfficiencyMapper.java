package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.TeachingEfficiency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Teaching Efficiency Mapper
 */
@Mapper
@Repository
public interface TeachingEfficiencyMapper extends BaseMapper<TeachingEfficiency> {
    
    /**
     * Find by teacher ID
     */
    List<TeachingEfficiency> findByTeacherId(@Param("teacherId") Long teacherId);
    
    /**
     * Find by course ID
     */
    List<TeachingEfficiency> findByCourseId(@Param("courseId") Long courseId);
    
    /**
     * Get teacher efficiency ranking
     */
    List<Map<String, Object>> getTeacherEfficiencyRanking(@Param("startDate") LocalDate startDate,
                                                          @Param("endDate") LocalDate endDate,
                                                          @Param("limit") Integer limit);
    
    /**
     * Get course efficiency statistics
     */
    List<Map<String, Object>> getCourseEfficiencyStats(@Param("teacherId") Long teacherId,
                                                       @Param("startDate") LocalDate startDate,
                                                       @Param("endDate") LocalDate endDate);
    
    /**
     * Get efficiency trend
     */
    List<Map<String, Object>> getEfficiencyTrend(@Param("teacherId") Long teacherId,
                                                 @Param("days") Integer days);
    
    /**
     * Get average efficiency times
     */
    Map<String, Object> getAverageEfficiencyTimes();
    
    /**
     * Get efficiency trends (comparing periods)
     */
    Map<String, Object> getEfficiencyTrends();
    
    /**
     * Insert efficiency record
     */
    void insertEfficiencyRecord(@Param("teacherId") Long teacherId,
                               @Param("courseId") Long courseId,
                               @Param("preparationTime") Integer preparationTime,
                               @Param("correctionTime") Integer correctionTime,
                               @Param("designTime") Integer designTime,
                               @Param("studentCount") Integer studentCount,
                               @Param("avgStudentScore") Double avgStudentScore,
                               @Param("passRate") Double passRate,
                               @Param("efficiencyIndex") Double efficiencyIndex,
                               @Param("optimizationSuggestion") String optimizationSuggestion,
                               @Param("statDate") LocalDate statDate);
    
    /**
     * Get latest efficiency data
     */
    Map<String, Object> getLatestEfficiencyData();
    
    /**
     * Get previous efficiency data for trend calculation
     */
    Map<String, Object> getPreviousEfficiencyData();
}