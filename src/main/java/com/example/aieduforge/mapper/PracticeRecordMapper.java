package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.PracticeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface PracticeRecordMapper extends BaseMapper<PracticeRecord> {

    /**
     * 获取练习总次数
     */
    Integer getTotalPractices(@Param("studentId") Long studentId);

    /**
     * 获取平均正确率
     */
    BigDecimal getAverageAccuracy(@Param("studentId") Long studentId);

    /**
     * 根据学生ID和课程ID查询练习记录
     */
    List<PracticeRecord> findByStudentAndCourse(@Param("studentId") Long studentId, @Param("courseId") Long courseId);

    /**
     * 根据学生ID查询练习记录
     */
    List<PracticeRecord> findByStudentId(@Param("studentId") Long studentId);

    /**
     * 获取学生练习统计数据
     */
    Map<String, Object> getStudentStatistics(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
    
    /**
     * Get practice activities by date
     */
    List<Map<String, Object>> getPracticeActivitiesByDate(@Param("date") java.time.LocalDate date);
}
