package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.LearningAnalytics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface LearningAnalyticsMapper extends BaseMapper<LearningAnalytics> {

    /**
     * 获取学生总学习时长（分钟）
     */
    Integer getTotalStudyTime(@Param("studentId") Long studentId);

    /**
     * 获取已完成课程数
     */
    Integer getCompletedCourses(@Param("studentId") Long studentId);

    /**
     * 获取每日学习统计
     */
    List<Map<String, Object>> getDailyStats(@Param("studentId") Long studentId, @Param("startDate") LocalDateTime startDate);

    /**
     * 获取学科分布统计
     */
    List<Map<String, Object>> getSubjectDistribution(@Param("studentId") Long studentId);

    /**
     * 获取学生学习进度
     */
    List<Map<String, Object>> getStudentLearningProgress(@Param("studentId") Long studentId, @Param("courseId") Long courseId);

    /**
     * 获取学习趋势
     */
    List<Map<String, Object>> getLearningTrend(@Param("studentId") Long studentId, @Param("days") int days);

    /**
     * 获取班级平均表现
     */
    Map<String, Object> getClassAveragePerformance(@Param("courseId") Long courseId,
                                                  @Param("startDate") LocalDate startDate,
                                                  @Param("endDate") LocalDate endDate);

    /**
     * 获取知识点掌握情况
     */
    List<Map<String, Object>> getKnowledgePointMastery(@Param("courseId") Long courseId);

    /**
     * 获取困难知识点
     */
    List<Map<String, Object>> getDifficultKnowledgePoints(@Param("courseId") Long courseId, @Param("limit") int limit);
}
