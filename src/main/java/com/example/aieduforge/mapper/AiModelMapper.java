package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.AiModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * AI Model Mapper
 */
@Mapper
public interface AiModelMapper extends BaseMapper<AiModel> {

    /**
     * Get the current active model
     */
    @Select("SELECT * FROM ai_model WHERE is_active = 1 LIMIT 1")
    AiModel getActiveModel();

    /**
     * Get the default model
     */
    @Select("SELECT * FROM ai_model WHERE is_default = 1 LIMIT 1")
    AiModel getDefaultModel();

    /**
     * Set all models as inactive
     */
    @Update("UPDATE ai_model SET is_active = 0")
    int deactivateAllModels();

    /**
     * Set all models as non-default
     */
    @Update("UPDATE ai_model SET is_default = 0")
    int clearAllDefaults();

    /**
     * Update connection status
     */
    @Update("UPDATE ai_model SET connection_status = #{status}, last_test_time = NOW(), error_message = #{errorMessage} WHERE id = #{id}")
    int updateConnectionStatus(Long id, Integer status, String errorMessage);
}