package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.AnalysisResults;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分析结果Mapper接口
 */
@Mapper
public interface AnalysisResultsMapper extends BaseMapper<AnalysisResults> {
    
    /**
     * 根据分析类型、目标ID和课程ID查询最新的分析结果
     */
    AnalysisResults getLatestAnalysisResult(@Param("analysisType") String analysisType, 
                                          @Param("targetId") Long targetId,
                                          @Param("courseId") Long courseId);
    
    /**
     * 根据教师ID查询分析历史
     */
    List<AnalysisResults> getAnalysisHistoryByTeacher(@Param("teacherId") Long teacherId);
    
    /**
     * 根据分析类型查询分析结果列表
     */
    List<AnalysisResults> getAnalysisResultsByType(@Param("analysisType") String analysisType);
}