package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.UsageStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Usage Statistics Mapper
 */
@Mapper
@Repository
public interface UsageStatisticsMapper extends BaseMapper<UsageStatistics> {
    
    /**
     * Get daily usage statistics by user type
     */
    List<Map<String, Object>> getDailyUsageByUserType(@Param("startDate") LocalDate startDate, 
                                                      @Param("endDate") LocalDate endDate);
    
    /**
     * Get module usage statistics
     */
    List<Map<String, Object>> getModuleUsageStats(@Param("userType") Integer userType,
                                                  @Param("startDate") LocalDate startDate,
                                                  @Param("endDate") LocalDate endDate);
    
    /**
     * Get active users count
     */
    Map<String, Object> getActiveUsersCount(@Param("date") LocalDate date);
    
    /**
     * Get user activity ranking
     */
    List<Map<String, Object>> getUserActivityRanking(@Param("userType") Integer userType,
                                                     @Param("startDate") LocalDate startDate,
                                                     @Param("endDate") LocalDate endDate,
                                                     @Param("limit") Integer limit);
    
    /**
     * Get usage trend data
     */
    List<Map<String, Object>> getUsageTrend(@Param("userType") Integer userType,
                                           @Param("days") Integer days);
}