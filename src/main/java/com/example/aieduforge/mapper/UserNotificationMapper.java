package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.UserNotification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * User Notification Mapper
 */
@Mapper
@Repository
public interface UserNotificationMapper extends BaseMapper<UserNotification> {
    
    /**
     * Get user notification record
     */
    Map<String, Object> getUserNotificationRecord(@Param("userId") Long userId, 
                                                  @Param("notificationId") Long notificationId);
    
    /**
     * Update read status
     */
    int updateReadStatus(@Param("userId") Long userId, 
                        @Param("notificationId") Long notificationId,
                        @Param("readTime") LocalDateTime readTime);
    
    /**
     * Insert user notification record
     */
    int insertUserNotification(@Param("userId") Long userId,
                              @Param("notificationId") Long notificationId,
                              @Param("isRead") Integer isRead,
                              @Param("readTime") LocalDateTime readTime);
}