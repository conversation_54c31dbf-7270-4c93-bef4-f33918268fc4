package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.CourseOutline;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Course Outline Mapper
 */
@Mapper
@Repository
public interface CourseOutlineMapper extends BaseMapper<CourseOutline> {
    
    /**
     * Find outlines by course ID
     */
    List<CourseOutline> findByCourseId(@Param("courseId") Long courseId);
    
    /**
     * Find outlines by course ID ordered by chapter order
     */
    List<CourseOutline> findByCourseIdOrderByChapterOrder(@Param("courseId") Long courseId);
}