package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.AiAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AI Answer Mapper
 */
@Mapper
@Repository
public interface AiAnswerMapper extends BaseMapper<AiAnswer> {
    
    /**
     * Find answer by question ID
     */
    AiAnswer findByQuestionId(@Param("questionId") Long questionId);
    
    /**
     * Find answers with high confidence
     */
    List<AiAnswer> findHighConfidenceAnswers(@Param("minConfidence") Double minConfidence);
    
    /**
     * Find answers by feedback score
     */
    List<AiAnswer> findByFeedbackScore(@Param("feedbackScore") Integer feedbackScore);
    
    /**
     * Update feedback
     */
    int updateFeedback(@Param("id") Long id, @Param("feedbackScore") Integer feedbackScore, @Param("feedbackComment") String feedbackComment);
}