package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.TeachingContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Teaching Content Mapper
 */
@Mapper
@Repository
public interface TeachingContentMapper extends BaseMapper<TeachingContent> {
    
    /**
     * Find content by course ID
     */
    List<TeachingContent> findByCourseId(@Param("courseId") Long courseId);
    
    /**
     * Find content by outline ID
     */
    List<TeachingContent> findByOutlineId(@Param("outlineId") Long outlineId);
    
    /**
     * Find content by type
     */
    List<TeachingContent> findByContentType(@Param("courseId") Long courseId, @Param("contentType") Integer contentType);
    
    /**
     * Find AI generated content
     */
    List<TeachingContent> findAiGeneratedContent(@Param("courseId") Long courseId);
}