package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * User Mapper
 */
@Mapper
@Repository
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * Find user by username
     */
    SysUser findByUsername(@Param("username") String username);
    
    /**
     * Update last login time
     */
    int updateLastLoginTime(@Param("id") Long id);
    
    /**
     * Update user role
     */
    int updateUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * Delete user role
     */
    int deleteUserRole(@Param("userId") Long userId);
    
    /**
     * Get user growth trends
     */
    List<Map<String, Object>> getUserGrowthTrends(@Param("days") Integer days);
    
    /**
     * Get user role distribution
     */
    Map<String, Object> getUserRoleDistribution();
    
    /**
     * Get users by role code
     */
    List<SysUser> getUsersByRole(@Param("roleCode") String roleCode);
}
