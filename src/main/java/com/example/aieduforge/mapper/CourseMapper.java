package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.Course;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Course Mapper
 */
@Mapper
@Repository
public interface CourseMapper extends BaseMapper<Course> {
    
    /**
     * Find courses by teacher ID
     * @param teacherId 教师ID
     * @param onlyEnabled 是否只返回启用的课程
     */
    List<Course> findByTeacherId(@Param("teacherId") Long teacherId, @Param("onlyEnabled") Boolean onlyEnabled);

    /**
     * Find courses by subject
     */
    List<Course> findBySubject(@Param("subject") String subject);
    
    /**
     * Find course by code
     */
    Course findByCourseCode(@Param("courseCode") String courseCode);
    
    /**
     * Find all courses with teacher names
     */
    List<Course> selectList();
    
    /**
     * Find courses with filters
     * @param courseName 课程名称（模糊查询）
     * @param subject 学科（精确查询）
     * @param teacherName 教师名称（模糊查询）
     */
    List<Course> findWithFilters(@Param("courseName") String courseName, 
                                  @Param("subject") String subject, 
                                  @Param("teacherName") String teacherName);
}