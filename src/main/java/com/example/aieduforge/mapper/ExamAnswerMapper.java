package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.ExamAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Exam Answer Mapper
 */
@Mapper
@Repository
public interface ExamAnswerMapper extends BaseMapper<ExamAnswer> {
    
    /**
     * Find answers by student ID
     */
    List<ExamAnswer> findByStudentId(@Param("studentId") Long studentId);
    
    /**
     * Find answers by question ID
     */
    List<ExamAnswer> findByQuestionId(@Param("questionId") Long questionId);
    
    /**
     * Find student answer for specific question
     */
    ExamAnswer findByStudentAndQuestion(@Param("studentId") Long studentId, @Param("questionId") Long questionId);
    
    /**
     * Find incorrect answers by student
     */
    List<ExamAnswer> findIncorrectAnswers(@Param("studentId") Long studentId);
    
    /**
     * Find answers by correctness
     */
    List<ExamAnswer> findByCorrectness(@Param("studentId") Long studentId, @Param("isCorrect") Integer isCorrect);
    
    /**
     * Get student answer statistics
     */
    List<Map<String, Object>> getStudentAnswerStatistics(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
    
    /**
     * Get pending grading answers with details
     */
    List<ExamAnswer> getPendingGradingAnswersWithDetails(@Param("courseId") Long courseId, 
                                                        @Param("questionType") Integer questionType,
                                                        @Param("offset") Integer offset,
                                                        @Param("size") Integer size);
    
    /**
     * Get answer detail with question and student info
     */
    Map<String, Object> getAnswerDetailWithInfo(@Param("answerId") Long answerId);
    
    /**
     * Get answers for grading with details (supports grading status filter)
     */
    List<ExamAnswer> getAnswersForGradingWithDetails(@Param("courseId") Long courseId, 
                                                    @Param("questionType") Integer questionType,
                                                    @Param("gradingStatus") Integer gradingStatus,
                                                    @Param("offset") Integer offset,
                                                    @Param("size") Integer size);
    
    /**
     * Update practice record score
     */
    int updatePracticeRecordScore(@Param("practiceId") Long practiceId, 
                                 @Param("totalScore") java.math.BigDecimal totalScore, 
                                 @Param("correctCount") Integer correctCount);
    
    /**
     * Get course overall accuracy
     */
    Double getCourseOverallAccuracy(@Param("courseId") Long courseId);
    
    /**
     * Get course average score
     */
    Double getCourseAverageScore(@Param("courseId") Long courseId);
    
    /**
     * Get course answer count
     */
    Integer getCourseAnswerCount(@Param("courseId") Long courseId);
    
    /**
     * Get student answer count for a course
     */
    Integer getStudentAnswerCount(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
    
    /**
     * Get student accuracy for a course
     */
    Double getStudentAccuracy(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
    
    /**
     * Get student average score for a course
     */
    Double getStudentAverageScore(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
    
    /**
     * Get course student count (distinct students who answered questions)
     */
    Integer getCourseStudentCount(@Param("courseId") Long courseId);
    
    /**
     * Get student rankings for a course
     */
    List<Map<String, Object>> getStudentRankings(@Param("courseId") Long courseId);
    
    /**
     * Get knowledge point statistics from questions
     */
    List<Map<String, Object>> getKnowledgePointStatsFromQuestions(@Param("courseId") Long courseId);
    
    /**
     * Get performance distribution for a course
     */
    Map<String, Object> getPerformanceDistribution(@Param("courseId") Long courseId);
    
    /**
     * Get student answers with details for analysis
     */
    List<Map<String, Object>> getStudentAnswersWithDetails(@Param("courseId") Long courseId);
    
    /**
     * Get student knowledge point statistics
     */
    List<Map<String, Object>> getStudentKnowledgePointStats(@Param("studentId") Long studentId, @Param("courseId") Long courseId);
}