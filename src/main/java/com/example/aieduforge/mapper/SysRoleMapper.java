package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Role Mapper
 */
@Mapper
@Repository
public interface SysRoleMapper extends BaseMapper<SysRole> {
    
    /**
     * Find roles by user ID
     */
    List<SysRole> findRolesByUserId(@Param("userId") Long userId);
    
    /**
     * Find role by code
     */
    SysRole findByRoleCode(@Param("roleCode") String roleCode);
}