package com.example.aieduforge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.aieduforge.entity.ExamQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExamQuestionMapper extends BaseMapper<ExamQuestion> {
    /**
     * 随机获取指定数量的题目
     */
    List<ExamQuestion> findRandomQuestions(@Param("courseId") Long courseId, @Param("count") Integer count);

    /**
     * 根据难度等级获取题目
     */
    List<ExamQuestion> findByDifficultyLevel(@Param("courseId") Long courseId, @Param("difficultyLevel") Integer difficultyLevel);

    /**
     * 根据题目类型随机获取指定数量的题目
     */
    List<ExamQuestion> findRandomQuestionsByType(@Param("courseId") Long courseId, @Param("questionType") Integer questionType, @Param("count") Integer count);
}
