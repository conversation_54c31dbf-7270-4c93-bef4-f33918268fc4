package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.GradingRequest;
import com.example.aieduforge.entity.ExamAnswer;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.TeacherGradingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Teacher Grading Controller
 */
@Slf4j
@RestController
@RequestMapping("/teacher/grading")
@PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
public class TeacherGradingController {

    @Autowired
    private TeacherGradingService teacherGradingService;

    /**
     * 获取答案列表（支持按评分状态筛选）
     */
    @GetMapping("/pending")
    public Result<List<ExamAnswer>> getPendingGradingAnswers(
            @RequestParam(required = false) Long courseId,
            @RequestParam(required = false) Integer questionType,
            @RequestParam(required = false) Integer gradingStatus,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {
        
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        List<ExamAnswer> answers = teacherGradingService.getAnswersForGrading(
            user.getUserId(), courseId, questionType, gradingStatus, current, size);
        
        return Result.success("获取答案列表成功", answers);
    }

    /**
     * 获取答案详情（包含题目信息）
     */
    @GetMapping("/answer/{answerId}")
    public Result<Map<String, Object>> getAnswerDetail(@PathVariable Long answerId,
                                                      Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        Map<String, Object> detail = teacherGradingService.getAnswerDetail(answerId, user.getUserId());
        
        if (detail != null) {
            return Result.success("获取答案详情成功", detail);
        } else {
            return Result.error("答案不存在或无权限访问");
        }
    }

    /**
     * 提交人工评分
     */
    @PostMapping("/submit")
    public Result<String> gradeAnswer(@Valid @RequestBody GradingRequest request,
                                    Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        boolean success = teacherGradingService.gradeAnswer(
            request.getAnswerId(),
            request.getScore(),
            request.getIsCorrect(),
            request.getFeedback(),
            request.getImprovementSuggestion(),
            user.getUserId()
        );
        
        if (success) {
            return Result.success("评分成功");
        } else {
            return Result.error("评分失败，请重试");
        }
    }

    /**
     * 批量评分
     */
    @PostMapping("/batch-grade")
    public Result<String> batchGradeAnswers(@Valid @RequestBody List<GradingRequest> requests,
                                          Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        int successCount = teacherGradingService.batchGradeAnswers(requests, user.getUserId());
        
        return Result.success(String.format("批量评分完成，成功评分 %d 道题", successCount));
    }

    /**
     * 获取评分统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getGradingStatistics(
            @RequestParam(required = false) Long courseId,
            Authentication authentication) {
        
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        Map<String, Object> statistics = teacherGradingService.getGradingStatistics(
            user.getUserId(), courseId);
        
        return Result.success("获取评分统计成功", statistics);
    }
}