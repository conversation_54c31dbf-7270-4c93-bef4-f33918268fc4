package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.mapper.SysUserMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * System User Controller
 */
@RestController
@RequestMapping("/system/users")
public class SysUserController extends BaseController<SysUser, Long> {

    /**
     * Get user by ID (Admin only)
     */
    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public Result<SysUser> getById(@PathVariable Long id) {
        return super.getById(id);
    }

    /**
     * Create user (Admin only)
     */
    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public Result<SysUser> create(@RequestBody SysUser entity) {
        return super.create(entity);
    }

    /**
     * Update user (Admin only)
     */
    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public Result<SysUser> update(@PathVariable Long id, @RequestBody SysUser entity) {
        return super.update(id, entity);
    }

    /**
     * Delete user (Admin only)
     */
    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> delete(@PathVariable Long id) {
        return super.delete(id);
    }

    /**
     * Change user status (Admin only)
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> changeStatus(@PathVariable Long id, @RequestParam Integer status) {
        SysUser user = ((SysUserMapper) baseMapper).selectById(id);
        if (user != null) {
            user.setStatus(status);
            baseMapper.updateById(user);
            return Result.<Void>success("Status updated successfully", null);
        }
        return Result.<Void>error("User not found");
    }
}