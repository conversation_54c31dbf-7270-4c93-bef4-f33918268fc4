package com.example.aieduforge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.AiModelDTO;
import com.example.aieduforge.dto.ModelConnectionTestDTO;
import com.example.aieduforge.entity.AiModel;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.AiModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * AI Model Management Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/ai-models")
@PreAuthorize("hasRole('ADMIN')")
public class AiModelController {

    @Autowired
    private AiModelService aiModelService;

    /**
     * Get paginated list of AI models
     */
    @GetMapping
    public Result<IPage<AiModelDTO>> getModelList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String provider,
            @RequestParam(required = false) Boolean isActive) {
        
        Page<AiModel> page = new Page<>(current, size);
        IPage<AiModelDTO> result = aiModelService.getModelList(page, provider, isActive);
        return Result.success(result);
    }

    /**
     * Get all active models
     */
    @GetMapping("/active")
    public Result<List<AiModelDTO>> getActiveModels() {
        List<AiModelDTO> models = aiModelService.getActiveModels();
        return Result.success(models);
    }

    /**
     * Get model by ID
     */
    @GetMapping("/{id}")
    public Result<AiModelDTO> getModelById(@PathVariable Long id) {
        AiModelDTO model = aiModelService.getModelById(id);
        if (model == null) {
            return Result.error("Model not found");
        }
        return Result.success(model);
    }

    /**
     * Get current active model
     */
    @GetMapping("/current/active")
    public Result<AiModelDTO> getActiveModel() {
        AiModelDTO model = aiModelService.getActiveModel();
        if (model == null) {
            return Result.error("No active model found");
        }
        return Result.success(model);
    }

    /**
     * Get default model
     */
    @GetMapping("/current/default")
    public Result<AiModelDTO> getDefaultModel() {
        AiModelDTO model = aiModelService.getDefaultModel();
        if (model == null) {
            return Result.error("No default model found");
        }
        return Result.success(model);
    }

    /**
     * Create new AI model
     */
    @PostMapping
    public Result<AiModelDTO> createModel(
            @Valid @RequestBody AiModelDTO modelDTO,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            AiModelDTO createdModel = aiModelService.createModel(modelDTO, userPrincipal.getUserId());
            log.info("Admin {} created AI model: {}", userPrincipal.getUsername(), createdModel.getModelName());
            return Result.success(createdModel);
        } catch (Exception e) {
            log.error("Failed to create AI model: {}", e.getMessage());
            return Result.error("Failed to create model: " + e.getMessage());
        }
    }

    /**
     * Update AI model
     */
    @PutMapping("/{id}")
    public Result<AiModelDTO> updateModel(
            @PathVariable Long id,
            @Valid @RequestBody AiModelDTO modelDTO,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            AiModelDTO updatedModel = aiModelService.updateModel(id, modelDTO, userPrincipal.getUserId());
            log.info("Admin {} updated AI model: {}", userPrincipal.getUsername(), updatedModel.getModelName());
            return Result.success(updatedModel);
        } catch (Exception e) {
            log.error("Failed to update AI model {}: {}", id, e.getMessage());
            return Result.error("Failed to update model: " + e.getMessage());
        }
    }

    /**
     * Delete AI model
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteModel(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            boolean deleted = aiModelService.deleteModel(id);
            if (deleted) {
                log.info("Admin {} deleted AI model with ID: {}", userPrincipal.getUsername(), id);
                return Result.success();
            } else {
                return Result.error("Model not found or cannot be deleted");
            }
        } catch (Exception e) {
            log.error("Failed to delete AI model {}: {}", id, e.getMessage());
            return Result.error("Failed to delete model: " + e.getMessage());
        }
    }

    /**
     * Set model as active
     */
    @PostMapping("/{id}/activate")
    public Result<Void> setActiveModel(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            boolean success = aiModelService.setActiveModel(id);
            if (success) {
                log.info("Admin {} set AI model {} as active", userPrincipal.getUsername(), id);
                return Result.success();
            } else {
                return Result.error("Failed to set model as active");
            }
        } catch (Exception e) {
            log.error("Failed to activate AI model {}: {}", id, e.getMessage());
            return Result.error("Failed to activate model: " + e.getMessage());
        }
    }

    /**
     * Set model as default
     */
    @PostMapping("/{id}/set-default")
    public Result<Void> setDefaultModel(
            @PathVariable Long id,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            boolean success = aiModelService.setDefaultModel(id);
            if (success) {
                log.info("Admin {} set AI model {} as default", userPrincipal.getUsername(), id);
                return Result.success();
            } else {
                return Result.error("Failed to set model as default");
            }
        } catch (Exception e) {
            log.error("Failed to set AI model {} as default: {}", id, e.getMessage());
            return Result.error("Failed to set model as default: " + e.getMessage());
        }
    }

    /**
     * Test model connection
     */
    @PostMapping("/{id}/test")
    public Result<ModelConnectionTestDTO> testModelConnection(@PathVariable Long id) {
        try {
            ModelConnectionTestDTO result = aiModelService.testModelConnection(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to test AI model connection {}: {}", id, e.getMessage());
            return Result.error("Failed to test connection: " + e.getMessage());
        }
    }

    /**
     * Test model connection with custom parameters (before saving)
     */
    @PostMapping("/test")
    public Result<ModelConnectionTestDTO> testModelConnection(@Valid @RequestBody AiModelDTO modelDTO) {
        try {
            ModelConnectionTestDTO result = aiModelService.testModelConnection(modelDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to test AI model connection: {}", e.getMessage());
            return Result.error("Failed to test connection: " + e.getMessage());
        }
    }

    /**
     * Batch test all models
     */
    @PostMapping("/test-all")
    public Result<List<ModelConnectionTestDTO>> testAllModels() {
        try {
            List<ModelConnectionTestDTO> results = aiModelService.testAllModels();
            return Result.success(results);
        } catch (Exception e) {
            log.error("Failed to test all AI models: {}", e.getMessage());
            return Result.error("Failed to test all models: " + e.getMessage());
        }
    }

    /**
     * Get available providers
     */
    @GetMapping("/providers")
    public Result<List<String>> getProviders() {
        List<String> providers = List.of(
            "openrouter",
            "openai", 
            "anthropic",
            "ollama",
            "google",
            "azure",
            "huggingface",
            "custom"
        );
        return Result.success(providers);
    }
}