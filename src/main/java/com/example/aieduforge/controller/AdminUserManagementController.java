package com.example.aieduforge.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.SysRole;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.mapper.SysRoleMapper;
import com.example.aieduforge.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Admin User Management Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/users")
@PreAuthorize("hasRole('ADMIN')")
public class AdminUserManagementController {

    @Autowired
    private SysUserService sysUserService;
    
    @Autowired
    private SysRoleMapper sysRoleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * Get users with pagination
     */
    @GetMapping
    public Result<Page<SysUser>> getUsers(@RequestParam(defaultValue = "1") Integer current,
                                        @RequestParam(defaultValue = "10") Integer size,
                                        @RequestParam(required = false) String username,
                                        @RequestParam(required = false) String realName,
                                        @RequestParam(required = false) String role,
                                        @RequestParam(required = false) Integer status) {
        Page<SysUser> page = new Page<>(current, size);
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        
        // Basic filters
        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like("username", username);
        }
        if (realName != null && !realName.trim().isEmpty()) {
            queryWrapper.like("real_name", realName);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        // Role filter - through exists subquery
        if (role != null && !role.trim().isEmpty()) {
            queryWrapper.exists("SELECT 1 FROM sys_user_role ur " +
                              "INNER JOIN sys_role r ON ur.role_id = r.id " +
                              "WHERE ur.user_id = sys_user.id AND r.role_code = '" + role + "'");
        }
        
        queryWrapper.orderByDesc("create_time");
        Page<SysUser> result = sysUserService.page(page, queryWrapper);
        
        // Remove passwords and add role information
        result.getRecords().forEach(user -> {
            user.setPassword(null);
            // Get user roles
            List<SysRole> roles = sysRoleMapper.findRolesByUserId(user.getId());
            String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
            user.setRole(roleCode);
        });
        
        return Result.success(result);
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    public Result<SysUser> getUserById(@PathVariable Long id) {
        SysUser user = sysUserService.getById(id);
        if (user != null) {
            user.setPassword(null); // Remove password from response
            // Get user roles
            List<SysRole> roles = sysRoleMapper.findRolesByUserId(user.getId());
            String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
            user.setRole(roleCode);
            return Result.success(user);
        }
        return Result.error("User not found");
    }

    /**
     * Create new user
     */
    @PostMapping
    public Result<SysUser> createUser(@Valid @RequestBody SysUser user) {
        // Check if username exists
        if (sysUserService.findByUsername(user.getUsername()) != null) {
            return Result.error("Username already exists");
        }
        
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setStatus(1);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        if (sysUserService.save(user)) {
            user.setPassword(null); // Remove password from response
            return Result.success("User created successfully", user);
        }
        return Result.error("Failed to create user");
    }

    /**
     * Update user
     */
    @PutMapping("/{id}")
    public Result<SysUser> updateUser(@PathVariable Long id, @Valid @RequestBody SysUser user) {
        try {
            // Use the new updateUserWithRole method that handles role updates
            SysUser updatedUser = sysUserService.updateUserWithRole(id, user);
            return Result.success("User updated successfully", updatedUser);
        } catch (Exception e) {
            log.error("Failed to update user: {}", e.getMessage());
            return Result.error("Failed to update user: " + e.getMessage());
        }
    }

    /**
     * Delete user
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(@PathVariable Long id) {
        SysUser user = sysUserService.getById(id);
        if (user == null) {
            return Result.<Void>error("User not found");
        }
        
        // Prevent deleting admin user
        if ("admin".equals(user.getUsername())) {
            return Result.<Void>error("Cannot delete admin user");
        }
        
        if (sysUserService.removeById(id)) {
            return Result.<Void>success("User deleted successfully", null);
        }
        return Result.<Void>error("Failed to delete user");
    }

    /**
     * Change user status
     */
    @PutMapping("/{id}/status")
    public Result<Void> changeUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        SysUser user = sysUserService.getById(id);
        if (user == null) {
            return Result.<Void>error("User not found");
        }
        
        // Prevent disabling admin user
        if ("admin".equals(user.getUsername()) && status == 0) {
            return Result.<Void>error("Cannot disable admin user");
        }
        
        user.setStatus(status);
        user.setUpdateTime(LocalDateTime.now());
        
        if (sysUserService.updateById(user)) {
            return Result.<Void>success("User status updated successfully", null);
        }
        return Result.<Void>error("Failed to update user status");
    }

    /**
     * Reset user password
     */
    @PutMapping("/{id}/reset-password")
    public Result<Void> resetPassword(@PathVariable Long id, @RequestParam String newPassword) {
        SysUser user = sysUserService.getById(id);
        if (user == null) {
            return Result.<Void>error("User not found");
        }
        
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        
        if (sysUserService.updateById(user)) {
            return Result.<Void>success("Password reset successfully", null);
        }
        return Result.<Void>error("Failed to reset password");
    }

    /**
     * Get all roles
     */
    @GetMapping("/roles")
    public Result<List<SysRole>> getAllRoles() {
        List<SysRole> roles = sysRoleMapper.selectList(new QueryWrapper<SysRole>().eq("status", 1));
        return Result.success(roles);
    }

    /**
     * Get user roles
     */
    @GetMapping("/{id}/roles")
    public Result<List<SysRole>> getUserRoles(@PathVariable Long id) {
        List<SysRole> roles = sysRoleMapper.findRolesByUserId(id);
        return Result.success(roles);
    }

    /**
     * Search users
     */
    @GetMapping("/search")
    public Result<List<SysUser>> searchUsers(@RequestParam String keyword) {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper
            .like("username", keyword)
            .or()
            .like("real_name", keyword)
            .or()
            .like("email", keyword)
        );
        queryWrapper.eq("status", 1);
        queryWrapper.orderByDesc("create_time");
        
        List<SysUser> users = sysUserService.list(queryWrapper);
        users.forEach(user -> user.setPassword(null)); // Remove passwords
        
        return Result.success(users);
    }

    /**
     * Get recent login users
     */
    @GetMapping("/recent")
    public Result<List<SysUser>> getRecentUsers(@RequestParam(defaultValue = "10") Integer limit) {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("last_login_time")
                   .orderByDesc("last_login_time")
                   .last("LIMIT " + limit);
        
        List<SysUser> users = sysUserService.list(queryWrapper);
        
        // Remove passwords and add role information
        users.forEach(user -> {
            user.setPassword(null);
            // Get user roles
            List<SysRole> roles = sysRoleMapper.findRolesByUserId(user.getId());
            String roleCode = roles.isEmpty() ? "STUDENT" : roles.get(0).getRoleCode();
            user.setRole(roleCode);
        });
        
        return Result.success(users);
    }
}