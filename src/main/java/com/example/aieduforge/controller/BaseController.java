package com.example.aieduforge.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.aieduforge.common.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * Base Controller for common CRUD operations
 */
public abstract class BaseController<T, ID extends Serializable> {

    @Autowired
    protected BaseMapper<T> baseMapper;

    /**
     * Get entity by ID
     */
    @GetMapping("/{id}")
    public Result<T> getById(@PathVariable ID id) {
        T entity = baseMapper.selectById(id);
        if (entity != null) {
            return Result.success(entity);
        }
        return Result.error("Entity not found");
    }

    /**
     * Get all entities with pagination
     */
    @GetMapping
    public Result<Page<T>> list(@RequestParam(defaultValue = "1") Integer current,
                               @RequestParam(defaultValue = "10") Integer size) {
        Page<T> page = new Page<>(current, size);
        Page<T> result = baseMapper.selectPage(page, new QueryWrapper<>());
        return Result.success(result);
    }

    /**
     * Create new entity
     */
    @PostMapping
    public Result<T> create(@RequestBody T entity) {
        int result = baseMapper.insert(entity);
        if (result > 0) {
            return Result.success("Created successfully", entity);
        }
        return Result.error("Failed to create entity");
    }

    /**
     * Update entity
     */
    @PutMapping("/{id}")
    public Result<T> update(@PathVariable ID id, @RequestBody T entity) {
        int result = baseMapper.updateById(entity);
        if (result > 0) {
            return Result.success("Updated successfully", entity);
        }
        return Result.error("Failed to update entity");
    }

    /**
     * Delete entity by ID
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable ID id) {
        int result = baseMapper.deleteById(id);
        if (result > 0) {
            return Result.<Void>success("Deleted successfully", null);
        }
        return Result.<Void>error("Failed to delete entity");
    }

    /**
     * Batch delete entities
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDelete(@RequestBody List<ID> ids) {
        int result = baseMapper.deleteBatchIds(ids);
        if (result > 0) {
            return Result.<Void>success("Batch deleted successfully", null);
        }
        return Result.<Void>error("Failed to batch delete entities");
    }
}