package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 验证码控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth/captcha")
@RequiredArgsConstructor
public class CaptchaController {
    
    private final CaptchaService captchaService;
    
    /**
     * 生成验证码
     */
    @GetMapping("/generate")
    public Result<Map<String, Object>> generateCaptcha(@RequestParam String sessionId) {
        try {
            Map<String, Object> captchaData = captchaService.generateCaptcha(sessionId);
            return Result.success(captchaData);
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            return Result.error("生成验证码失败");
        }
    }
    
    /**
     * 验证验证码
     */
    @PostMapping("/verify")
    public Result<Boolean> verifyCaptcha(@RequestBody Map<String, String> request) {
        try {
            String sessionId = request.get("sessionId");
            String captcha = request.get("captcha");
            
            boolean isValid = captchaService.verifyCaptcha(sessionId, captcha);
            return Result.success(isValid);
        } catch (Exception e) {
            log.error("验证验证码失败", e);
            return Result.error("验证验证码失败");
        }
    }
    
    /**
     * 检查验证码是否启用
     */
    @GetMapping("/enabled")
    public Result<Map<String, Object>> isCaptchaEnabled() {
        try {
            boolean enabled = captchaService.isCaptchaEnabled();
            Map<String, Object> result = new HashMap<>();
            result.put("enabled", enabled);
            return Result.success(result);
        } catch (Exception e) {
            log.error("检查验证码状态失败", e);
            return Result.error("检查验证码状态失败");
        }
    }
}