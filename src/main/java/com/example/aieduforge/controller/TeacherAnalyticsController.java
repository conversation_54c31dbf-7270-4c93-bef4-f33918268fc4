package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.AnalysisResults;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.AnalysisResultsService;
import com.example.aieduforge.service.StatisticsService;
import com.example.aieduforge.service.StudentStatsService;
import com.example.aieduforge.service.AIService;
import com.example.aieduforge.mapper.SysUserMapper;
import com.example.aieduforge.mapper.LearningAnalyticsMapper;
import com.example.aieduforge.mapper.ExamAnswerMapper;
import com.example.aieduforge.entity.SysUser;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * Teacher Analytics Controller
 * 教师学情分析控制器
 */
@Slf4j
@RestController
@RequestMapping("/teacher/analytics")
@PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
public class TeacherAnalyticsController {

    @Autowired
    private StatisticsService statisticsService;
    
    @Autowired
    private StudentStatsService studentStatsService;
    
    @Autowired
    private AIService aiService;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private LearningAnalyticsMapper learningAnalyticsMapper;
    
    @Autowired
    private ExamAnswerMapper examAnswerMapper;
    
    @Autowired
    private AnalysisResultsService analysisResultsService;

    /**
     * 获取课程学生列表
     */
    @GetMapping("/course-students/{courseId}")
    public Result<List<SysUser>> getCourseStudents(@PathVariable Long courseId,
                                                   Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            // 查询选修该课程的学生
            // 这里需要根据实际的课程-学生关联表来查询
            // 暂时查询所有学生用户作为示例
            QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1); // 只查询正常状态的用户
            
            List<SysUser> students = sysUserMapper.selectList(queryWrapper);
            
            // 过滤出学生角色的用户（这里简化处理，实际应该通过用户角色关联表查询）
            List<SysUser> courseStudents = new ArrayList<>();
            for (SysUser student : students) {
                if (student.getUsername().startsWith("student") || 
                    student.getRealName() != null && !student.getRealName().contains("老师") && !student.getRealName().contains("管理员")) {
                    courseStudents.add(student);
                }
            }
            
            return Result.success("获取课程学生列表成功", courseStudents);
            
        } catch (Exception e) {
            log.error("获取课程学生列表失败", e);
            return Result.error("获取课程学生列表失败：" + e.getMessage());
        }
    }

    /**
     * 课程整体分析
     */
    @GetMapping("/course-overall/{courseId}")
    public Result<Map<String, Object>> analyzeCourseOverall(@PathVariable Long courseId,
                                                           Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            // 获取课程统计数据
            Map<String, Object> courseStats = statisticsService.getCoursePerformanceStats(
                courseId, LocalDate.now().minusDays(30), LocalDate.now());
            
            // 构建分析结果
            Map<String, Object> analysis = new HashMap<>();
            
            // 基础统计数据
            analysis.put("totalStudents", getCourseStudentCount(courseId));
            analysis.put("overallAccuracy", getOverallAccuracy(courseId));
            analysis.put("avgScore", getAverageScore(courseId));
            analysis.put("totalAnswers", getTotalAnswers(courseId));
            
            // AI洞察
            String aiInsights = generateAIInsights(courseId);
            analysis.put("aiInsights", aiInsights);
            
            // 学生表现分布
            Map<String, Object> performanceDistribution = getPerformanceDistribution(courseId);
            analysis.put("performanceDistribution", performanceDistribution);
            
            // 知识点分析
            List<Map<String, Object>> knowledgeAnalysis = getKnowledgePointAnalysis(courseId);
            analysis.put("knowledgePointAnalyses", knowledgeAnalysis);
            
            // 学生排名
            List<Map<String, Object>> studentRankings = getStudentRankings(courseId);
            analysis.put("studentRankings", studentRankings);
            
            // 教学建议
            String teachingSuggestions = generateTeachingSuggestions(courseId);
            analysis.put("teachingSuggestions", teachingSuggestions);
            
            return Result.success("课程分析完成", analysis);
            
        } catch (Exception e) {
            log.error("课程整体分析失败", e);
            return Result.error("课程分析失败：" + e.getMessage());
        }
    }

    /**
     * 获取综合分析报告
     */
    @GetMapping("/comprehensive-report")
    public Result<Map<String, Object>> getComprehensiveReport(@RequestParam Long studentId,
                                                             @RequestParam Long courseId,
                                                             Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            Map<String, Object> report = new HashMap<>();
            
            // 学生基本信息
            SysUser student = sysUserMapper.selectById(studentId);
            Map<String, Object> basicInfo = new HashMap<>();
            basicInfo.put("studentName", student.getRealName());
            basicInfo.put("totalAnswers", getTotalAnswersByStudent(studentId, courseId));
            basicInfo.put("totalPractices", getTotalPracticesByStudent(studentId, courseId));
            report.put("basicInfo", basicInfo);
            
            // 学习表现
            Map<String, Object> performance = new HashMap<>();
            performance.put("overallAccuracy", getStudentAccuracy(studentId, courseId));
            performance.put("avgTeacherScore", getStudentAvgScore(studentId, courseId));
            performance.put("performanceLevel", getPerformanceLevel(studentId, courseId));
            report.put("performance", performance);
            
            // 知识点掌握情况
            List<Map<String, Object>> knowledgeMastery = getStudentKnowledgeMastery(studentId, courseId);
            report.put("knowledgeMastery", knowledgeMastery);
            
            // AI诊断
            Map<String, Object> aiDiagnosis = generateStudentAIDiagnosisMap(studentId, courseId);
            report.put("aiDiagnosis", aiDiagnosis);
            
            // 保存分析结果
            try {
                Long analysisResultId = analysisResultsService.saveAnalysisResult(
                    "student_individual", 
                    studentId, 
                    courseId,
                    report, 
                    user.getUserId()
                );
                report.put("analysisResultId", analysisResultId);
            } catch (Exception e) {
                log.warn("保存分析结果失败，但不影响返回结果", e);
            }
            
            return Result.success("获取综合分析报告成功", report);
            
        } catch (Exception e) {
            log.error("获取综合分析报告失败", e);
            return Result.error("获取综合分析报告失败：" + e.getMessage());
        }
    }

    /**
     * 智能答案分析
     */
    @PostMapping("/answer-analysis")
    public Result<Map<String, Object>> intelligentAnswerAnalysis(@RequestBody Map<String, Object> request,
                                                               Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            Long courseId = Long.valueOf(request.get("courseId").toString());
            String questionContent = (String) request.get("questionContent");
            String correctAnswer = (String) request.get("correctAnswer");
            String studentAnswer = (String) request.get("studentAnswer");
            Long studentId = request.get("studentId") != null ? 
                Long.valueOf(request.get("studentId").toString()) : null;
            
            // 使用AI服务分析答案
            String analysis = aiService.analyzeStudentAnswer(questionContent, correctAnswer, studentAnswer);
            
            Map<String, Object> result = new HashMap<>();
            result.put("analysis", analysis);
            result.put("questionContent", questionContent);
            result.put("correctAnswer", correctAnswer);
            result.put("studentAnswer", studentAnswer);
            
            // 如果提供了学生ID，添加个性化分析
            if (studentId != null) {
                String personalizedAnalysis = generatePersonalizedAnalysis(studentId, courseId, analysis);
                result.put("personalizedAnalysis", personalizedAnalysis);
            }
            
            return Result.success("答案分析完成", result);
            
        } catch (Exception e) {
            log.error("智能答案分析失败", e);
            return Result.error("答案分析失败：" + e.getMessage());
        }
    }

    /**
     * 批量分析学生
     */
    @PostMapping("/batch-analyze")
    public Result<List<Map<String, Object>>> batchAnalyzeStudents(@RequestBody Map<String, Object> request,
                                                                 Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            @SuppressWarnings("unchecked")
            List<Long> studentIds = (List<Long>) request.get("studentIds");
            Long courseId = Long.valueOf(request.get("courseId").toString());
            
            List<Map<String, Object>> results = new ArrayList<>();
            
            for (Long studentId : studentIds) {
                Map<String, Object> studentAnalysis = new HashMap<>();
                
                SysUser student = sysUserMapper.selectById(studentId);
                studentAnalysis.put("studentId", studentId);
                studentAnalysis.put("studentName", student.getRealName());
                
                // 学习表现
                Map<String, Object> performance = new HashMap<>();
                performance.put("overallAccuracy", getStudentAccuracy(studentId, courseId));
                performance.put("avgTeacherScore", getStudentAvgScore(studentId, courseId));
                performance.put("performanceLevel", getPerformanceLevel(studentId, courseId));
                studentAnalysis.put("performance", performance);
                
                results.add(studentAnalysis);
            }
            
            return Result.success("批量分析完成", results);
            
        } catch (Exception e) {
            log.error("批量分析学生失败", e);
            return Result.error("批量分析失败：" + e.getMessage());
        }
    }

    /**
     * 获取快速洞察
     */
    @GetMapping("/quick-insights/{courseId}")
    public Result<Map<String, Object>> getQuickInsights(@PathVariable Long courseId,
                                                       Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            Map<String, Object> insights = new HashMap<>();
            
            // 快速统计
            insights.put("studentCount", getCourseStudentCount(courseId));
            insights.put("avgAccuracy", getOverallAccuracy(courseId));
            insights.put("recentActivity", getRecentActivity(courseId));
            
            // 关键洞察
            String keyInsights = generateKeyInsights(courseId);
            insights.put("keyInsights", keyInsights);
            
            return Result.success("获取快速洞察成功", insights);
            
        } catch (Exception e) {
            log.error("获取快速洞察失败", e);
            return Result.error("获取快速洞察失败：" + e.getMessage());
        }
    }

    /**
     * 生成分析报告
     */
    @PostMapping("/generate-report")
    public Result<Map<String, Object>> generateAnalysisReport(@RequestBody Map<String, Object> request,
                                                             Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            String type = (String) request.get("type");
            Long studentId = request.containsKey("studentId") && request.get("studentId") != null ? 
                Long.valueOf(request.get("studentId").toString()) : null;
            Long courseId = request.containsKey("courseId") && request.get("courseId") != null ? 
                Long.valueOf(request.get("courseId").toString()) : null;
                
            if (courseId == null) {
                return Result.error("缺少课程ID参数");
            }
            
            Map<String, Object> result = new HashMap<>();
            
            if ("student_individual".equals(type) && studentId != null) {
                // 生成学生个体分析报告
                Map<String, Object> report = generateStudentReport(studentId, courseId, user.getUserId());
                Long analysisResultId = analysisResultsService.saveAnalysisResult(type, studentId, courseId, report, user.getUserId());
                
                result.put("reportType", "学生个体分析报告");
                result.put("analysisResultId", analysisResultId);
                result.put("studentName", report.get("basicInfo") != null ? 
                    ((Map<String, Object>) report.get("basicInfo")).get("studentName") : "未知学生");
                
            } else if ("course_overall".equals(type)) {
                // 生成课程整体分析报告
                Map<String, Object> report = generateCourseReport(courseId, user.getUserId());
                Long analysisResultId = analysisResultsService.saveAnalysisResult(type, courseId, null, report, user.getUserId());
                
                result.put("reportType", "课程整体分析报告");
                result.put("analysisResultId", analysisResultId);
                result.put("courseName", report.get("courseName"));
            }
            
            result.put("generateTime", new Date());
            result.put("message", "报告生成并保存成功");
            
            return Result.success("报告生成成功", result);
            
        } catch (Exception e) {
            log.error("生成分析报告失败", e);
            return Result.error("生成报告失败：" + e.getMessage());
        }
    }

    /**
     * 导出分析数据为Excel
     */
    @GetMapping("/export/excel/{analysisResultId}")
    public ResponseEntity<byte[]> exportAnalysisToExcel(@PathVariable Long analysisResultId,
                                                       Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            byte[] excelData = analysisResultsService.exportAnalysisToExcel(analysisResultId);
            
            // 获取分析结果信息用于文件名
            AnalysisResults analysisResults = analysisResultsService.getById(analysisResultId);
            String fileName = "学生分析报告_" + System.currentTimeMillis() + ".xlsx";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", 
                URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);
                    
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 获取分析历史
     */
    @GetMapping("/history")
    public Result<List<Map<String, Object>>> getAnalysisHistory(Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            List<AnalysisResults> historyList = analysisResultsService.getAnalysisHistoryByTeacher(user.getUserId());
            List<Map<String, Object>> result = new ArrayList<>();
            
            for (AnalysisResults analysis : historyList) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", analysis.getId());
                item.put("analysisType", analysis.getAnalysisType());
                item.put("targetId", analysis.getTargetId());
                item.put("createTime", analysis.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                
                // 根据分析类型获取目标名称
                if ("student_individual".equals(analysis.getAnalysisType())) {
                    SysUser student = sysUserMapper.selectById(analysis.getTargetId());
                    item.put("targetName", student != null ? student.getRealName() : "未知学生");
                    item.put("typeName", "学生个体分析");
                    
                    // 添加课程信息
                    if (analysis.getCourseId() != null) {
                        // 这里可以通过courseMapper获取课程名称，暂时用简单查询
                        item.put("courseId", analysis.getCourseId());
                        item.put("courseName", "课程ID: " + analysis.getCourseId());
                    }
                } else if ("course_overall".equals(analysis.getAnalysisType())) {
                    item.put("targetName", "课程ID: " + analysis.getTargetId());
                    item.put("typeName", "课程整体分析");
                    item.put("courseId", analysis.getTargetId());
                    item.put("courseName", "课程ID: " + analysis.getTargetId());
                }
                
                result.add(item);
            }
            
            return Result.success("获取分析历史成功", result);
            
        } catch (Exception e) {
            log.error("获取分析历史失败", e);
            return Result.error("获取分析历史失败：" + e.getMessage());
        }
    }

    /**
     * 获取学生答案数据用于深度分析
     */
    @GetMapping("/student-answers/{courseId}")
    public Result<List<Map<String, Object>>> getStudentAnswers(@PathVariable Long courseId,
                                                              Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            
            // 获取该课程的所有学生答案，包含题目和学生信息
            List<Map<String, Object>> studentAnswers = examAnswerMapper.getStudentAnswersWithDetails(courseId);
            
            return Result.success("获取学生答案成功", studentAnswers);
            
        } catch (Exception e) {
            log.error("获取学生答案失败", e);
            return Result.error("获取学生答案失败：" + e.getMessage());
        }
    }

    // 以下是辅助方法，用于获取各种统计数据
    
    private int getCourseStudentCount(Long courseId) {
        try {
            // 查询该课程实际参与答题的学生数量
            return examAnswerMapper.getCourseStudentCount(courseId);
        } catch (Exception e) {
            log.warn("获取课程学生数量失败，返回默认值", e);
            return 0;
        }
    }
    
    private double getOverallAccuracy(Long courseId) {
        try {
            return examAnswerMapper.getCourseOverallAccuracy(courseId);
        } catch (Exception e) {
            return 0; // 默认值
        }
    }
    
    private double getAverageScore(Long courseId) {
        try {
            return examAnswerMapper.getCourseAverageScore(courseId);
        } catch (Exception e) {
            return 0; // 默认值
        }
    }
    
    private int getTotalAnswers(Long courseId) {
        try {
            return examAnswerMapper.getCourseAnswerCount(courseId);
        } catch (Exception e) {
            return 0; // 默认值
        }
    }
    
    private String generateAIInsights(Long courseId) {
        return "该课程整体表现良好，学生对基础概念掌握较好，但在实际应用方面还需加强练习。建议增加更多实践性题目。";
    }
    
    private Map<String, Object> getPerformanceDistribution(Long courseId) {
        try {
            return examAnswerMapper.getPerformanceDistribution(courseId);
        } catch (Exception e) {
            log.warn("获取学生表现分布失败，返回默认值", e);
            // 返回默认分布
            Map<String, Object> defaultDistribution = new HashMap<>();
            defaultDistribution.put("excellent", 0);
            defaultDistribution.put("good", 0);
            defaultDistribution.put("average", 0);
            defaultDistribution.put("needsImprovement", 0);
            defaultDistribution.put("excellentPercent", 0.0);
            defaultDistribution.put("goodPercent", 0.0);
            defaultDistribution.put("averagePercent", 0.0);
            defaultDistribution.put("needsImprovementPercent", 0.0);
            return defaultDistribution;
        }
    }
    
    private List<Map<String, Object>> getKnowledgePointAnalysis(Long courseId) {
        try {
            // 先尝试从learning_analytics表获取数据
            List<Map<String, Object>> knowledgeData = learningAnalyticsMapper.getKnowledgePointMastery(courseId);
            
            // 如果learning_analytics表没有数据，从exam_question表生成基础分析
            if (knowledgeData == null || knowledgeData.isEmpty()) {
                knowledgeData = generateKnowledgeAnalysisFromQuestions(courseId);
            }
            
            return knowledgeData;
        } catch (Exception e) {
            log.warn("获取知识点分析失败，返回空列表", e);
            return new ArrayList<>();
        }
    }
    
    private List<Map<String, Object>> getStudentRankings(Long courseId) {
        try {
            return examAnswerMapper.getStudentRankings(courseId);
        } catch (Exception e) {
            log.warn("获取学生排名失败，返回空列表", e);
            return new ArrayList<>();
        }
    }
    
    private List<Map<String, Object>> generateKnowledgeAnalysisFromQuestions(Long courseId) {
        List<Map<String, Object>> knowledgeAnalysis = new ArrayList<>();
        
        try {
            // 基于题目的knowledge_points字段生成知识点分析
            List<Map<String, Object>> questionStats = examAnswerMapper.getKnowledgePointStatsFromQuestions(courseId);
            
            for (Map<String, Object> stat : questionStats) {
                Map<String, Object> analysis = new HashMap<>();
                analysis.put("knowledgePoint", stat.get("knowledgePoint"));
                analysis.put("totalQuestions", stat.get("totalQuestions"));
                analysis.put("avgAccuracy", stat.get("avgAccuracy"));
                analysis.put("difficultyLevel", getDifficultyDescription(stat.get("avgDifficulty")));
                analysis.put("teachingSuggestion", generateTeachingSuggestion(stat.get("avgAccuracy")));
                knowledgeAnalysis.add(analysis);
            }
        } catch (Exception e) {
            log.warn("从题目生成知识点分析失败", e);
        }
        
        return knowledgeAnalysis;
    }
    
    private String getDifficultyDescription(Object difficulty) {
        if (difficulty == null) return "未知";
        int difficultyLevel;
        if (difficulty instanceof BigDecimal) {
            difficultyLevel = ((BigDecimal) difficulty).intValue();
        } else if (difficulty instanceof Integer) {
            difficultyLevel = (Integer) difficulty;
        } else {
            return "未知";
        }
        
        switch (difficultyLevel) {
            case 1: return "简单";
            case 2: return "中等";
            case 3: return "困难";
            default: return "未知";
        }
    }
    
    private String generateTeachingSuggestion(Object accuracy) {
        if (accuracy == null) return "需要更多数据分析";
        
        double accuracyValue;
        if (accuracy instanceof BigDecimal) {
            accuracyValue = ((BigDecimal) accuracy).doubleValue();
        } else if (accuracy instanceof Double) {
            accuracyValue = (Double) accuracy;
        } else {
            return "需要更多数据分析";
        }
        
        if (accuracyValue >= 85) return "掌握良好，可以进入下一阶段学习";
        else if (accuracyValue >= 70) return "需要加强练习，建议提供更多示例";
        else return "重点难点，需要个别辅导";
    }
    
    private String generateTeachingSuggestions(Long courseId) {
        return "建议在下次课程中重点讲解重难点知识，并提供更多实践机会。可以考虑分组学习，让优秀学生帮助其他同学。";
    }
    
    private int getTotalAnswersByStudent(Long studentId, Long courseId) {
        return examAnswerMapper.getStudentAnswerCount(studentId, courseId);
    }
    
    private int getTotalPracticesByStudent(Long studentId, Long courseId) {
        // 查询学生练习次数
        return 15; // 临时值
    }
    
    private double getStudentAccuracy(Long studentId, Long courseId) {
        return examAnswerMapper.getStudentAccuracy(studentId, courseId);
    }
    
    private double getStudentAvgScore(Long studentId, Long courseId) {
        return examAnswerMapper.getStudentAverageScore(studentId, courseId);
    }
    
    private String getPerformanceLevel(Long studentId, Long courseId) {
        double accuracy = getStudentAccuracy(studentId, courseId);
        if (accuracy >= 90) return "优秀";
        else if (accuracy >= 80) return "良好";
        else if (accuracy >= 70) return "中等";
        else return "待提高";
    }
    
    private List<Map<String, Object>> getStudentKnowledgeMastery(Long studentId, Long courseId) {
        try {
            // 先尝试从learning_analytics表获取数据
            List<Map<String, Object>> analyticsData = learningAnalyticsMapper.getStudentLearningProgress(studentId, courseId);
            
            // 如果learning_analytics表没有数据，从exam_answer表生成
            if (analyticsData == null || analyticsData.isEmpty()) {
                analyticsData = generateKnowledgeMasteryFromAnswers(studentId, courseId);
            }
            
            return analyticsData;
        } catch (Exception e) {
            log.warn("获取学生知识点掌握情况失败，返回空列表", e);
            return new ArrayList<>();
        }
    }
    
    private List<Map<String, Object>> generateKnowledgeMasteryFromAnswers(Long studentId, Long courseId) {
        List<Map<String, Object>> knowledgeMastery = new ArrayList<>();
        
        try {
            // 基于学生答题情况生成知识点掌握分析
            List<Map<String, Object>> answerStats = examAnswerMapper.getStudentKnowledgePointStats(studentId, courseId);
            
            for (Map<String, Object> stat : answerStats) {
                Map<String, Object> mastery = new HashMap<>();
                mastery.put("knowledgePoint", stat.get("knowledgePoint"));
                mastery.put("totalQuestions", stat.get("totalQuestions"));
                mastery.put("correctQuestions", stat.get("correctCount")); // 修改字段名
                mastery.put("accuracyRate", stat.get("accuracy")); // 修改字段名
                
                // 根据正确率确定掌握等级
                Object accuracyObj = stat.get("accuracy");
                double accuracy = 0.0;
                if (accuracyObj instanceof BigDecimal) {
                    accuracy = ((BigDecimal) accuracyObj).doubleValue();
                } else if (accuracyObj instanceof Double) {
                    accuracy = (Double) accuracyObj;
                } else if (accuracyObj instanceof Number) {
                    accuracy = ((Number) accuracyObj).doubleValue();
                }
                
                int masteryLevel;
                String masteryDescription;
                if (accuracy >= 90) {
                    masteryLevel = 3;
                    masteryDescription = "精通";
                } else if (accuracy >= 75) {
                    masteryLevel = 2;
                    masteryDescription = "熟练掌握";
                } else if (accuracy >= 60) {
                    masteryLevel = 1;
                    masteryDescription = "初步掌握";
                } else {
                    masteryLevel = 0;
                    masteryDescription = "未掌握";
                }
                
                mastery.put("masteryLevel", masteryLevel);
                mastery.put("masteryDescription", masteryDescription); // 添加掌握程度描述
                knowledgeMastery.add(mastery);
            }
        } catch (Exception e) {
            log.warn("从答题记录生成知识点掌握情况失败", e);
        }
        
        return knowledgeMastery;
    }
    
    private String generateStudentAIDiagnosis(Long studentId, Long courseId) {
        return "该学生在基础概念掌握方面表现良好，但在复杂问题解决上还需要加强练习。";
    }
    
    private Map<String, Object> generateStudentAIDiagnosisMap(Long studentId, Long courseId) {
        Map<String, Object> diagnosis = new HashMap<>();
        
        // 获取学生表现数据
        double accuracy = getStudentAccuracy(studentId, courseId);
        double avgScore = getStudentAvgScore(studentId, courseId);
        
        // 整体评估
        String overallAssessment;
        if (accuracy >= 90) {
            overallAssessment = "该学生表现优秀，对课程内容掌握扎实，学习能力强，能够独立解决复杂问题。";
        } else if (accuracy >= 80) {
            overallAssessment = "该学生表现良好，基础知识掌握较好，但在部分难点上还需要加强练习。";
        } else if (accuracy >= 70) {
            overallAssessment = "该学生表现中等，基础概念理解基本正确，但需要更多练习来提高熟练度。";
        } else {
            overallAssessment = "该学生在学习上遇到一些困难，需要重点关注基础知识的巩固和理解。";
        }
        diagnosis.put("overallAssessment", overallAssessment);
        
        // 主要优势
        List<String> strengths = new ArrayList<>();
        if (accuracy >= 85) {
            strengths.add("答题正确率高，基础知识掌握扎实");
        }
        if (avgScore >= 80) {
            strengths.add("平均分数较高，学习效果良好");
        }
        if (accuracy >= 70) {
            strengths.add("学习态度认真，能够坚持完成练习");
        }
        if (strengths.isEmpty()) {
            strengths.add("积极参与课程学习，有学习的主动性");
        }
        diagnosis.put("strengths", strengths);
        
        // 需要改进
        List<String> weaknesses = new ArrayList<>();
        if (accuracy < 70) {
            weaknesses.add("基础知识掌握不够牢固，需要加强基础练习");
        }
        if (avgScore < 60) {
            weaknesses.add("答题质量有待提高，需要注意答题技巧");
        }
        if (accuracy < 85) {
            weaknesses.add("部分知识点理解不够深入，需要重点复习");
        }
        if (weaknesses.isEmpty()) {
            weaknesses.add("可以尝试挑战更高难度的题目");
        }
        diagnosis.put("weaknesses", weaknesses);
        
        // 个性化建议
        List<String> personalizedSuggestions = new ArrayList<>();
        if (accuracy < 70) {
            personalizedSuggestions.add("建议从基础题目开始，逐步提高难度");
            personalizedSuggestions.add("多做练习题，加强对基础概念的理解");
        } else if (accuracy < 85) {
            personalizedSuggestions.add("针对错题进行专项练习，查漏补缺");
            personalizedSuggestions.add("尝试解决更多综合性问题");
        } else {
            personalizedSuggestions.add("可以尝试更高难度的挑战题目");
            personalizedSuggestions.add("帮助其他同学，通过教学相长提高自己");
        }
        personalizedSuggestions.add("定期复习已学知识，保持学习连续性");
        diagnosis.put("personalizedSuggestions", personalizedSuggestions);
        
        // 下一步学习计划
        String nextStepPlan;
        if (accuracy >= 85) {
            nextStepPlan = "继续保持优秀的学习状态，可以开始学习更高级的内容，同时帮助其他同学共同进步。";
        } else if (accuracy >= 70) {
            nextStepPlan = "重点复习薄弱知识点，通过大量练习提高熟练度，争取在下次测试中取得更好成绩。";
        } else {
            nextStepPlan = "制定详细的学习计划，从基础开始系统复习，建议寻求老师或同学的帮助，逐步提高学习效果。";
        }
        diagnosis.put("nextStepPlan", nextStepPlan);
        
        return diagnosis;
    }
    
    private String generatePersonalizedAnalysis(Long studentId, Long courseId, String baseAnalysis) {
        return "基于该学生的历史表现，" + baseAnalysis + " 建议针对性地加强相关知识点的练习。";
    }
    
    private String getRecentActivity(Long courseId) {
        return "最近7天内有15名学生活跃，完成了85道题目。";
    }
    
    private String generateKeyInsights(Long courseId) {
        return "课程整体进度良好，但需要关注后进学生的学习情况。";
    }
    
    /**
     * 生成学生报告
     */
    private Map<String, Object> generateStudentReport(Long studentId, Long courseId, Long teacherId) {
        Map<String, Object> report = new HashMap<>();
        
        // 学生基本信息
        SysUser student = sysUserMapper.selectById(studentId);
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("studentName", student.getRealName());
        basicInfo.put("totalAnswers", getTotalAnswersByStudent(studentId, courseId));
        basicInfo.put("totalPractices", getTotalPracticesByStudent(studentId, courseId));
        report.put("basicInfo", basicInfo);
        
        // 学习表现
        Map<String, Object> performance = new HashMap<>();
        performance.put("overallAccuracy", getStudentAccuracy(studentId, courseId));
        performance.put("avgTeacherScore", getStudentAvgScore(studentId, courseId));
        performance.put("performanceLevel", getPerformanceLevel(studentId, courseId));
        report.put("performance", performance);
        
        // 知识点掌握情况
        List<Map<String, Object>> knowledgeMastery = getStudentKnowledgeMastery(studentId, courseId);
        report.put("knowledgeMastery", knowledgeMastery);
        
        // AI诊断
        Map<String, Object> aiDiagnosis = generateStudentAIDiagnosisMap(studentId, courseId);
        report.put("aiDiagnosis", aiDiagnosis);
        
        return report;
    }
    
    /**
     * 生成课程报告
     */
    private Map<String, Object> generateCourseReport(Long courseId, Long teacherId) {
        Map<String, Object> report = new HashMap<>();
        
        // 课程基本信息
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("courseId", courseId);
        basicInfo.put("courseName", "课程名称"); // 这里需要从数据库获取实际课程名称
        basicInfo.put("totalStudents", getCourseStudentCount(courseId));
        basicInfo.put("totalAnswers", getTotalAnswers(courseId));
        report.put("basicInfo", basicInfo);
        
        // 整体表现
        Map<String, Object> performance = new HashMap<>();
        performance.put("overallAccuracy", getOverallAccuracy(courseId));
        performance.put("averageScore", getAverageScore(courseId));
        report.put("performance", performance);
        
        // 表现分布
        Map<String, Object> performanceDistribution = getPerformanceDistribution(courseId);
        report.put("performanceDistribution", performanceDistribution);
        
        // 知识点分析
        List<Map<String, Object>> knowledgeAnalysis = getKnowledgePointAnalysis(courseId);
        report.put("knowledgePointAnalyses", knowledgeAnalysis);
        
        // 学生排名
        List<Map<String, Object>> studentRankings = getStudentRankings(courseId);
        report.put("studentRankings", studentRankings);
        
        // AI洞察
        String aiInsights = generateAIInsights(courseId);
        report.put("aiInsights", aiInsights);
        
        // 教学建议
        String teachingSuggestions = generateTeachingSuggestions(courseId);
        report.put("teachingSuggestions", teachingSuggestions);
        
        return report;
    }
}