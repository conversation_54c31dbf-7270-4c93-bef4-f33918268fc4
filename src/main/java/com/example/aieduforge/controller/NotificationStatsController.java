package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.service.NotificationStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 通知统计控制器
 */
@Slf4j
@RestController
@RequestMapping("/notification-stats")
@PreAuthorize("hasRole('ADMIN')")
public class NotificationStatsController {

    @Autowired
    private NotificationStatsService notificationStatsService;

    /**
     * 获取通知概览统计
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getOverviewStats() {
        Map<String, Object> stats = notificationStatsService.getOverviewStats();
        return Result.success(stats);
    }

    /**
     * 获取通知发送趋势
     */
    @GetMapping("/trend")
    public Result<Map<String, Object>> getTrendStats(@RequestParam(defaultValue = "7") Integer days) {
        Map<String, Object> stats = notificationStatsService.getTrendStats(days);
        return Result.success(stats);
    }

    /**
     * 获取通知类型分布
     */
    @GetMapping("/type-distribution")
    public Result<Map<String, Object>> getTypeDistribution() {
        Map<String, Object> stats = notificationStatsService.getTypeDistribution();
        return Result.success(stats);
    }

    /**
     * 获取用户阅读统计
     */
    @GetMapping("/read-stats")
    public Result<Map<String, Object>> getReadStats() {
        Map<String, Object> stats = notificationStatsService.getReadStats();
        return Result.success(stats);
    }

    /**
     * 获取WebSocket连接统计
     */
    @GetMapping("/websocket-stats")
    public Result<Map<String, Object>> getWebSocketStats() {
        Map<String, Object> stats = notificationStatsService.getWebSocketStats();
        return Result.success(stats);
    }
}