package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.EnhancedAIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 增强AI控制器 - 基于知识库的智能功能
 */
@Slf4j
@RestController
@RequestMapping("/ai/enhanced")
public class EnhancedAIController {

    @Autowired
    private EnhancedAIService enhancedAIService;

    /**
     * 基于知识库回答问题
     */
    @PostMapping("/answer")
    public Result<Map<String, Object>> answerQuestionWithKnowledge(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        String question = (String) request.get("question");
        String subject = (String) request.get("subject");
        Long courseId = request.get("courseId") != null ? 
            Long.valueOf(request.get("courseId").toString()) : null;
        
        if (question == null || question.trim().isEmpty()) {
            return Result.error("问题不能为空");
        }
        
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        Long studentId = user.getUserId();
        
        Map<String, Object> result = enhancedAIService.answerQuestionWithKnowledgeAndSave(
            question, subject, courseId, studentId);
        return Result.success("回答生成成功", result);
    }

    /**
     * 搜索相关知识库内容
     */
    @GetMapping("/search")
    public Result<List<Map<String, Object>>> searchKnowledge(
            @RequestParam String query,
            @RequestParam(required = false) String subject,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<Map<String, Object>> results = enhancedAIService.searchRelevantKnowledge(query, subject, limit);
        return Result.success(results);
    }

    /**
     * 基于知识库生成练习题
     */
    @PostMapping("/generate-questions")
    public Result<List<Map<String, Object>>> generateQuestionsFromKnowledge(
            @RequestBody Map<String, Object> request) {
        String subject = (String) request.get("subject");
        Long courseId = request.get("courseId") != null ? 
            Long.valueOf(request.get("courseId").toString()) : null;
        int count = request.get("count") != null ? 
            Integer.parseInt(request.get("count").toString()) : 5;
        
        List<Map<String, Object>> questions = enhancedAIService.generateQuestionsFromKnowledge(subject, courseId, count);
        return Result.success("题目生成成功", questions);
    }

    /**
     * 分析学生答案
     */
    @PostMapping("/analyze-answer")
    public Result<Map<String, Object>> analyzeStudentAnswer(@RequestBody Map<String, Object> request) {
        String question = (String) request.get("question");
        String studentAnswer = (String) request.get("studentAnswer");
        String correctAnswer = (String) request.get("correctAnswer");
        
        if (question == null || studentAnswer == null) {
            return Result.error("问题和学生答案不能为空");
        }
        
        Map<String, Object> analysis = enhancedAIService.analyzeStudentAnswer(question, studentAnswer, correctAnswer);
        return Result.success("答案分析完成", analysis);
    }

    /**
     * 推荐学习资源
     */
    @GetMapping("/recommend-resources")
    public Result<List<Map<String, Object>>> recommendLearningResources(
            @RequestParam String topic,
            @RequestParam(required = false) String subject,
            Authentication authentication) {
        
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        Long studentId = user.getUserId();
        
        List<Map<String, Object>> recommendations = enhancedAIService.recommendLearningResources(topic, subject, studentId);
        return Result.success(recommendations);
    }

    /**
     * 生成个性化学习计划
     */
    @PostMapping("/study-plan")
    public Result<Map<String, Object>> generateStudyPlan(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        Long studentId = user.getUserId();
        Long courseId = request.get("courseId") != null ? 
            Long.valueOf(request.get("courseId").toString()) : null;
        
        Map<String, Object> studyPlan = enhancedAIService.generatePersonalizedStudyPlan(studentId, courseId);
        return Result.success("学习计划生成成功", studyPlan);
    }

    /**
     * 获取知识文件内容预览
     */
    @GetMapping("/knowledge-content/{knowledgeId}")
    public Result<Map<String, Object>> getKnowledgeContent(@PathVariable Long knowledgeId) {
        try {
            Map<String, Object> content = enhancedAIService.getKnowledgeFileContent(knowledgeId);
            return Result.success("获取知识内容成功", content);
        } catch (Exception e) {
            log.error("获取知识内容失败: {}", e.getMessage());
            return Result.error("获取知识内容失败: " + e.getMessage());
        }
    }

    /**
     * 保存学生问答记录（不调用AI，仅保存）
     */
    @PostMapping("/save-question")
    public Result<Map<String, Object>> saveStudentQuestion(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        
        String question = (String) request.get("question");
        String answer = (String) request.get("answer");
        String subject = (String) request.get("subject");
        Long courseId = request.get("courseId") != null ? 
            Long.valueOf(request.get("courseId").toString()) : null;
        String questionSource = (String) request.getOrDefault("questionSource", "knowledge_qa");
        
        if (question == null || question.trim().isEmpty()) {
            return Result.error("问题不能为空");
        }
        
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        Long studentId = user.getUserId();
        
        try {
            Map<String, Object> result = enhancedAIService.saveQuestionAndAnswer(
                question, answer, subject, courseId, studentId, questionSource);
            return Result.success("问答记录保存成功", result);
        } catch (Exception e) {
            log.error("保存问答记录失败: {}", e.getMessage());
            return Result.error("保存问答记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识库统计信息
     */
    @GetMapping("/knowledge-stats")
    public Result<Map<String, Object>> getKnowledgeStatistics() {
        // 这里可以调用相关服务获取知识库统计信息
        Map<String, Object> stats = Map.of(
            "message", "知识库统计功能开发中",
            "timestamp", System.currentTimeMillis()
        );
        return Result.success(stats);
    }
}