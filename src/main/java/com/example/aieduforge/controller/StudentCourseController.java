package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.TeachingContent;
import com.example.aieduforge.entity.PracticeRecord;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.TeachingContentService;
import com.example.aieduforge.service.PracticeService;
import com.example.aieduforge.service.SysUserService;
import com.example.aieduforge.security.UserPrincipal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Student Course Controller - for browsing courses and content
 */
@Slf4j
@RestController
@RequestMapping("/student/courses")
@PreAuthorize("hasRole('STUDENT') or hasRole('ADMIN')")
public class StudentCourseController {

    @Autowired
    private CourseService courseService;
    
    @Autowired
    private TeachingContentService teachingContentService;
    
    @Autowired
    private PracticeService practiceService;
    
    @Autowired
    private SysUserService sysUserService;
    
    @Autowired
    private com.example.aieduforge.service.StudentLearningService studentLearningService;

    /**
     * Get all available courses
     */
    @GetMapping
    public Result<List<Course>> getAllCourses(Authentication authentication) {
        List<Course> courses = courseService.list();
        
        // Get current student ID for statistics
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        Long studentId = user.getUserId();
        
        // Enrich courses with additional information
        for (Course course : courses) {
            // Set teacher name
            if (course.getTeacherId() != null) {
                SysUser teacher = sysUserService.getById(course.getTeacherId());
                if (teacher != null) {
                    course.setTeacherName(teacher.getRealName());
                }
            }
            
            // Set total content count
            List<TeachingContent> contents = teachingContentService.findByCourseId(course.getId());
            course.setTotalContent(contents.size());
            
            // Set practice count for this student
            List<PracticeRecord> practiceRecords = practiceService.getPracticeHistory(studentId, course.getId());
            course.setPracticeCount(practiceRecords.size());
            
            // Calculate learned count and progress based on questions asked in learning assistant
            int progress = studentLearningService.calculateLearningProgress(studentId, course.getId());
            course.setProgress(progress);
            
            // Calculate learned count based on progress and total content
            int learnedCount = contents.size() > 0 ? (progress * contents.size()) / 100 : 0;
            course.setLearnedCount(learnedCount);
        }
        
        return Result.success(courses);
    }

    /**
     * Get course by ID
     */
    @GetMapping("/{id}")
    public Result<Course> getCourse(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course != null) {
            return Result.success(course);
        }
        return Result.error("Course not found");
    }

    /**
     * Get courses by subject
     */
    @GetMapping("/subject/{subject}")
    public Result<List<Course>> getCoursesBySubject(@PathVariable String subject) {
        List<Course> courses = courseService.findBySubject(subject);
        return Result.success(courses);
    }

    /**
     * Get teaching content for a course
     */
    @GetMapping("/{courseId}/content")
    public Result<List<Map<String, Object>>> getCourseContent(@PathVariable Long courseId) {
        List<TeachingContent> contents = teachingContentService.findByCourseId(courseId);
        
        // 转换为前端期望的格式，包含正确的字段映射
        List<Map<String, Object>> result = contents.stream().map(content -> {
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("id", content.getId());
            contentMap.put("courseId", content.getCourseId());
            contentMap.put("title", content.getTitle());
            contentMap.put("content", content.getContent());
            contentMap.put("contentType", content.getContentType());
            // 确保难度等级有合理的默认值（1-5星）
            Integer difficultyLevel = content.getDifficultyLevel();
            if (difficultyLevel == null || difficultyLevel < 1 || difficultyLevel > 5) {
                difficultyLevel = 3; // 默认中等难度
            }
            contentMap.put("difficulty", difficultyLevel); // 映射字段名
            contentMap.put("difficultyLevel", difficultyLevel); // 保持兼容性
            contentMap.put("timeAllocation", content.getTimeAllocation());
            contentMap.put("aiGenerated", content.getAiGenerated());
            contentMap.put("createTime", content.getCreateTime());
            contentMap.put("updateTime", content.getUpdateTime());
            contentMap.put("chapterName", content.getChapterName());
            
            // 添加描述字段（如果没有单独的描述字段，可以使用内容的前100个字符）
            String description = content.getContent();
            if (description != null && description.length() > 100) {
                description = description.substring(0, 100) + "...";
            }
            contentMap.put("description", description);
            
            return contentMap;
        }).toList();
        
        return Result.success(result);
    }

    /**
     * Get teaching content by type
     */
    @GetMapping("/{courseId}/content/type/{contentType}")
    public Result<List<TeachingContent>> getContentByType(@PathVariable Long courseId, 
                                                        @PathVariable Integer contentType) {
        List<TeachingContent> contents = teachingContentService.findByContentType(courseId, contentType);
        return Result.success(contents);
    }

    /**
     * Get specific teaching content
     */
    @GetMapping("/content/{contentId}")
    public Result<TeachingContent> getTeachingContent(@PathVariable Long contentId) {
        TeachingContent content = teachingContentService.getById(contentId);
        if (content != null) {
            return Result.success(content);
        }
        return Result.error("Content not found");
    }
    

}