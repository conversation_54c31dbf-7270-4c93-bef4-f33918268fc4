package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.ExamQuestionDTO;
import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.service.ExamQuestionService;
import com.example.aieduforge.utils.QuestionConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/teacher/exam-questions")
@PreAuthorize("hasRole('TEACHER')")
public class ExamQuestionController {

    @Autowired
    private ExamQuestionService examQuestionService;

    /**
     * 保存单个题目
     */
    @PostMapping
    public Result<Object> saveQuestion(@RequestBody ExamQuestionDTO questionDTO) {
        try {
            ExamQuestion question = QuestionConverter.toEntity(questionDTO);
            boolean saved = examQuestionService.save(question);
            return saved ? Result.success("题目保存成功") : Result.error("保存失败");
        } catch (Exception e) {
            log.error("Failed to save question", e);
            return Result.error("保存题目失败：" + e.getMessage());
        }
    }

    /**
     * 批量保存题目
     */
    @PostMapping("/batch")
    public Result<Object> batchSaveQuestions(@RequestBody List<ExamQuestionDTO> questionsDTO) {
        try {
            List<ExamQuestion> questions = questionsDTO.stream()
                .map(QuestionConverter::toEntity)
                .collect(Collectors.toList());

            boolean saved = examQuestionService.saveBatch(questions);
            return saved ? Result.success("题目批量保存成功") : Result.error("批量保存失败");
        } catch (Exception e) {
            log.error("Failed to batch save questions", e);
            return Result.error("批量保存题目失败：" + e.getMessage());
        }
    }

    /**
     * 获取课程题目列表
     */
    @GetMapping("/course/{courseId}")
    public Result<List<ExamQuestionDTO>> getQuestionsByCourse(@PathVariable Long courseId) {
        try {
            List<ExamQuestion> questions = examQuestionService.getQuestionsByCourse(courseId);
            List<ExamQuestionDTO> questionDTOs = questions.stream()
                .map(QuestionConverter::toDTO)
                .collect(Collectors.toList());

            return Result.success(questionDTOs);
        } catch (Exception e) {
            log.error("Failed to get questions for course {}", courseId, e);
            return Result.error("获取课程题目失败：" + e.getMessage());
        }
    }
}
