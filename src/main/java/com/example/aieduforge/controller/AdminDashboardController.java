package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.service.StatisticsService;
import com.example.aieduforge.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin Dashboard Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/dashboard")
@PreAuthorize("hasRole('ADMIN')")
public class AdminDashboardController {

    @Autowired
    private StatisticsService statisticsService;
    
    @Autowired
    private NotificationService notificationService;

    /**
     * Get dashboard overview data
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getDashboardOverview() {
        Map<String, Object> overview = statisticsService.getDashboardOverview();
        return Result.success(overview);
    }

    /**
     * Get usage statistics
     */
    @GetMapping("/usage")
    public Result<Map<String, Object>> getUsageStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Map<String, Object> stats = statisticsService.getUsageStatistics(startDate, endDate);
        return Result.success(stats);
    }

    /**
     * Get usage trends
     */
    @GetMapping("/trends")
    public Result<Map<String, Object>> getUsageTrends(@RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> trends = statisticsService.getUsageTrends(days);
        
        // Transform data for frontend chart
        Map<String, Object> chartData = new HashMap<>();
        List<String> dates = new ArrayList<>();
        List<Integer> newUsers = new ArrayList<>();
        List<Integer> activeUsers = new ArrayList<>();
        
        // If no data, create a basic structure with recent dates
        if (trends.isEmpty()) {
            LocalDate today = LocalDate.now();
            for (int i = days - 1; i >= 0; i--) {
                LocalDate date = today.minusDays(i);
                dates.add(date.toString());
                newUsers.add(0);
                activeUsers.add(0);
            }
        } else {
            for (Map<String, Object> trend : trends) {
                String dateStr = trend.get("date").toString();
                // Handle different date formats
                if (dateStr.contains("T")) {
                    dateStr = dateStr.substring(0, 10); // Extract YYYY-MM-DD part
                }
                dates.add(dateStr);
                newUsers.add(((Number) trend.getOrDefault("newUsers", 0)).intValue());
                activeUsers.add(((Number) trend.getOrDefault("activeUsers", 0)).intValue());
            }
        }
        
        chartData.put("dates", dates);
        chartData.put("newUsers", newUsers);
        chartData.put("activeUsers", activeUsers);
        
        return Result.success(chartData);
    }

    /**
     * Get active users statistics
     */
    @GetMapping("/active-users")
    public Result<Map<String, Object>> getActiveUsersStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        Map<String, Object> stats = statisticsService.getActiveUsersStats(date);
        return Result.success(stats);
    }

    /**
     * Get module usage ranking
     */
    @GetMapping("/module-ranking")
    public Result<List<Map<String, Object>>> getModuleUsageRanking(
            @RequestParam Integer userType,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<Map<String, Object>> ranking = statisticsService.getModuleUsageRanking(userType, startDate, endDate);
        return Result.success(ranking);
    }

    /**
     * Get teacher efficiency statistics
     */
    @GetMapping("/teacher-efficiency")
    public Result<Map<String, Object>> getTeacherEfficiencyStats(
            @RequestParam(required = false) Long teacherId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Map<String, Object> stats = statisticsService.getTeacherEfficiencyStats(teacherId, startDate, endDate);
        return Result.success(stats);
    }

    /**
     * Get course performance statistics
     */
    @GetMapping("/course-performance")
    public Result<Map<String, Object>> getCoursePerformanceStats(
            @RequestParam Long courseId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Map<String, Object> stats = statisticsService.getCoursePerformanceStats(courseId, startDate, endDate);
        return Result.success(stats);
    }

    /**
     * Get knowledge point difficulty analysis
     */
    @GetMapping("/knowledge-difficulty")
    public Result<List<Map<String, Object>>> getKnowledgePointDifficulty(@RequestParam Long courseId) {
        List<Map<String, Object>> difficulty = statisticsService.getKnowledgePointDifficulty(courseId);
        return Result.success(difficulty);
    }

    /**
     * Get teacher usage statistics for dashboard
     */
    @GetMapping("/teacher-usage")
    public Result<Map<String, Object>> getTeacherUsageStats() {
        Map<String, Object> stats = statisticsService.getTeacherUsageStats();
        return Result.success(stats);
    }

    /**
     * Get student usage statistics for dashboard
     */
    @GetMapping("/student-usage")
    public Result<Map<String, Object>> getStudentUsageStats() {
        Map<String, Object> stats = statisticsService.getStudentUsageStats();
        return Result.success(stats);
    }

    /**
     * Get comprehensive dashboard statistics
     */
    @GetMapping("/comprehensive-stats")
    public Result<Map<String, Object>> getComprehensiveStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 基础概览数据
        Map<String, Object> overview = statisticsService.getDashboardOverview();
        stats.put("overview", overview);
        
        // 教师使用统计
        Map<String, Object> teacherStats = statisticsService.getTeacherUsageStats();
        stats.put("teacherUsage", teacherStats);
        
        // 学生使用统计
        Map<String, Object> studentStats = statisticsService.getStudentUsageStats();
        stats.put("studentUsage", studentStats);
        
        return Result.success(stats);
    }
}