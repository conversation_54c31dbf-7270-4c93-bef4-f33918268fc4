package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.ContentGenerationRequest;
import com.example.aieduforge.entity.TeachingContent;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.TeachingContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Teaching Content Controller for Teachers
 */
@Slf4j
@RestController
@RequestMapping("/teacher/content")
@PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
public class TeachingContentController {

    @Autowired
    private TeachingContentService teachingContentService;

    /**
     * Generate teaching content using AI
     */
    @PostMapping("/generate")
    public Result<TeachingContent> generateContent(@Valid @RequestBody ContentGenerationRequest request,
                                                 Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        TeachingContent content = teachingContentService.generateContent(request, user.getUserId());
        return Result.success("Teaching content generated successfully", content);
    }

    /**
     * Get content by course ID
     */
    @GetMapping("/course/{courseId}")
    public Result<List<TeachingContent>> getContentByCourse(@PathVariable Long courseId) {
        List<TeachingContent> contents = teachingContentService.findByCourseId(courseId);
        return Result.success(contents);
    }

    /**
     * Get content by outline ID
     */
    @GetMapping("/outline/{outlineId}")
    public Result<List<TeachingContent>> getContentByOutline(@PathVariable Long outlineId) {
        List<TeachingContent> contents = teachingContentService.findByOutlineId(outlineId);
        return Result.success(contents);
    }

    /**
     * Get content by type
     */
    @GetMapping("/course/{courseId}/type/{contentType}")
    public Result<List<TeachingContent>> getContentByType(@PathVariable Long courseId, 
                                                        @PathVariable Integer contentType) {
        List<TeachingContent> contents = teachingContentService.findByContentType(courseId, contentType);
        return Result.success(contents);
    }

    /**
     * Get content by ID
     */
    @GetMapping("/{id}")
    public Result<TeachingContent> getContent(@PathVariable Long id, Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        TeachingContent content = teachingContentService.getById(id);
        if (content == null) {
            return Result.error("Content not found");
        }
        
        // Check permission (admin can access all)
        if (!"ADMIN".equals(user.getRole()) && 
            !teachingContentService.isTeacherOwnsContent(id, user.getUserId())) {
            return Result.error("Access denied");
        }
        
        return Result.success(content);
    }

    /**
     * Update teaching content
     */
    @PutMapping("/{id}")
    public Result<TeachingContent> updateContent(@PathVariable Long id, 
                                               @Valid @RequestBody TeachingContent content,
                                               Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        TeachingContent updatedContent = teachingContentService.updateContent(id, content, user.getUserId());
        return Result.success("Content updated successfully", updatedContent);
    }

    /**
     * Delete teaching content
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteContent(@PathVariable Long id, Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        boolean deleted = teachingContentService.deleteContent(id, user.getUserId());
        if (deleted) {
            return Result.<Void>success("Content deleted successfully", null);
        }
        return Result.<Void>error("Failed to delete content");
    }

    /**
     * Create manual teaching content (without AI)
     */
    @PostMapping
    public Result<TeachingContent> createContent(@Valid @RequestBody TeachingContent content,
                                               Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        content.setCreateUserId(user.getUserId());
        content.setAiGenerated(0); // Manual creation
        
        if (teachingContentService.save(content)) {
            return Result.success("Content created successfully", content);
        }
        return Result.error("Failed to create content");
    }
}