package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.ChangePasswordRequest;
import com.example.aieduforge.dto.LoginRequest;
import com.example.aieduforge.dto.LoginResponse;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.service.SysUserService;
import com.example.aieduforge.service.CaptchaService;
import com.example.aieduforge.service.SystemSettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

import java.util.Map;

/**
 * Authentication Controller
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private SystemSettingsService systemSettingsService;

    /**
     * User login
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        log.info("User login attempt: {}", loginRequest.getUsername());

        // 检查验证码是否启用
        if (captchaService.isCaptchaEnabled()) {
            String captcha = loginRequest.getCaptcha();
            String sessionId = loginRequest.getSessionId();

            if (sessionId == null) {
                sessionId = request.getSession().getId();
            }

            if (!StringUtils.hasText(captcha)) {
                return Result.error("请输入验证码");
            }

            // 验证验证码
            if (!captchaService.verifyCaptcha(sessionId, captcha)) {
                return Result.error("验证码错误或已过期");
            }
        }

        LoginResponse response = sysUserService.login(loginRequest);
        return Result.success("Login successful", response);
    }

    /**
     * User registration
     */
    @PostMapping("/register")
    public Result<SysUser> register(@Valid @RequestBody SysUser user) {
        log.info("User registration attempt: {}", user.getUsername());
        SysUser registeredUser = sysUserService.register(user);
        // Remove password from response
        registeredUser.setPassword(null);
        return Result.success("Registration successful", registeredUser);
    }

    /**
     * Logout (client-side token removal)
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        return Result.<Void>success("Logout successful", null);
    }

    /**
     * Get current user info
     */
    @GetMapping("/me")
    public Result<SysUser> getCurrentUser() {
        SysUser currentUser = sysUserService.getCurrentUser();
        // Remove password from response
        currentUser.setPassword(null);
        return Result.success("User info retrieved", currentUser);
    }

    /**
     * Update user profile
     */
    @PutMapping("/profile")
    public Result<SysUser> updateProfile(@RequestBody SysUser user) {
        log.info("Update user profile: {}", user.getUsername());
        SysUser updatedUser = sysUserService.updateProfile(user);
        // Remove password from response
        updatedUser.setPassword(null);
        return Result.success("Profile updated successfully", updatedUser);
    }

    /**
     * Change password
     */
    @PutMapping("/change-password")
    public Result<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        log.info("Change password for user");
        sysUserService.changePassword(request);
        return Result.<Void>success("Password changed successfully", null);
    }

    /**
     * Upload avatar
     */
    @PostMapping("/avatar")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        log.info("Upload avatar: {}", file.getOriginalFilename());
        String avatarUrl = sysUserService.uploadAvatar(file);
        return Result.success("Avatar uploaded successfully", avatarUrl);
    }


    /**
     * 登录页配置：是否显示演示账号
     */
    @GetMapping("/login/demo-config")
    public Result<Map<String, Object>> getLoginDemoConfig() {
        try {
            boolean showDemo = "1".equals(systemSettingsService.getSettingValue("login_show_demo_accounts", "1"));
            String demoJson = systemSettingsService.getSettingValue("login_demo_accounts", "[]");
            Map<String, Object> data = new java.util.HashMap<>();
            data.put("showDemoAccounts", showDemo);
            data.put("demoAccounts", demoJson);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取登录页演示账号配置失败", e);
            return Result.error("获取登录页演示账号配置失败");
        }
    }
}
