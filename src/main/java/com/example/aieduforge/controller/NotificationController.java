package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import com.example.aieduforge.security.UserPrincipal;

import java.util.List;
import java.util.Map;

/**
 * Notification Controller
 */
@Slf4j
@RestController
@RequestMapping("/notifications")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    /**
     * Get user notifications
     */
    @GetMapping("/my")
    public Result<Map<String, Object>> getMyNotifications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        UserPrincipal userPrincipal = getCurrentUser();
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        
        Map<String, Object> result = notificationService.getUserNotifications(
            userPrincipal.getUserId(), userPrincipal.getRole(), page, size);
        
        return Result.success(result);
    }

    /**
     * Get unread notification count
     */
    @GetMapping("/unread-count")
    public Result<Integer> getUnreadCount() {
        UserPrincipal userPrincipal = getCurrentUser();
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        
        int count = notificationService.getUnreadCount(userPrincipal.getUserId(), userPrincipal.getRole());
        return Result.success(count);
    }

    /**
     * Mark notification as read
     */
    @PostMapping("/{id}/read")
    public Result<String> markAsRead(@PathVariable Long id) {
        UserPrincipal userPrincipal = getCurrentUser();
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = notificationService.markAsRead(id, userPrincipal.getUserId());
        return success ? Result.success("标记成功") : Result.error("标记失败");
    }

    /**
     * Mark all notifications as read
     */
    @PostMapping("/read-all")
    public Result<String> markAllAsRead() {
        UserPrincipal userPrincipal = getCurrentUser();
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = notificationService.markAllAsRead(userPrincipal.getUserId(), userPrincipal.getRole());
        return success ? Result.success("全部标记成功") : Result.error("标记失败");
    }

    /**
     * Publish notification (Admin only)
     */
    @PostMapping("/publish")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> publishNotification(@RequestBody Map<String, Object> request) {
        UserPrincipal userPrincipal = getCurrentUser();
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        
        try {
            String title = (String) request.get("title");
            String content = (String) request.get("content");
            String targetRole = (String) request.get("targetRole");
            Integer type = (Integer) request.getOrDefault("type", 1);
            Integer priority = (Integer) request.getOrDefault("priority", 1);
            
            boolean success = notificationService.publishNotification(
                title, content, type, targetRole, null, priority, userPrincipal.getUserId());
            
            return success ? Result.success("发布成功") : Result.error("发布失败");
        } catch (Exception e) {
            log.error("发布通知失败", e);
            return Result.error("发布失败：" + e.getMessage());
        }
    }

    /**
     * Get latest system notifications (for dashboard)
     */
    @GetMapping("/latest")
    public Result<Map<String, Object>> getLatestSystemNotifications(
            @RequestParam(defaultValue = "5") Integer limit) {
        
        Map<String, Object> result = notificationService.getLatestSystemNotifications(limit);
        return Result.success(result);
    }

    /**
     * Get notification detail
     */
    @GetMapping("/{id}")
    public Result<Map<String, Object>> getNotificationDetail(@PathVariable Long id) {
        UserPrincipal userPrincipal = getCurrentUser();
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        
        Map<String, Object> result = notificationService.getNotificationDetail(id, userPrincipal.getUserId());
        return Result.success(result);
    }

    /**
     * Delete notification (Admin only)
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> deleteNotification(@PathVariable Long id) {
        boolean success = notificationService.deleteNotification(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * Get all notifications for admin management
     */
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Map<String, Object>> getAllNotifications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) String targetRole,
            @RequestParam(required = false) Integer priority) {
        
        Map<String, Object> result = notificationService.getAllNotifications(page, size, type, targetRole, priority);
        return Result.success(result);
    }

    /**
     * Get current user from security context
     */
    private UserPrincipal getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            return (UserPrincipal) authentication.getPrincipal();
        }
        return null;
    }
}