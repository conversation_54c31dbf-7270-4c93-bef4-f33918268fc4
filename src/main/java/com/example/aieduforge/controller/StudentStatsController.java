package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.StudentLearningStatsDTO;
import com.example.aieduforge.entity.LearningAnalytics;
import com.example.aieduforge.mapper.LearningAnalyticsMapper;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.StudentStatsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/student/learning")
@PreAuthorize("hasRole('STUDENT')")
public class StudentStatsController {

    @Autowired
    private StudentStatsService studentStatsService;

    @Autowired
    private LearningAnalyticsMapper learningAnalyticsMapper;

    @GetMapping("/stats")
    public Result<StudentLearningStatsDTO> getStats(Authentication authentication,
                                                   @RequestParam(defaultValue = "30") Integer days) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        StudentLearningStatsDTO stats = studentStatsService.getStudentStats(user.getUserId(), days);
        return Result.success(stats);
    }

    @GetMapping("/records")
    public Result<Page<LearningAnalytics>> getRecords(Authentication authentication,
                                                     @RequestParam(required = false) String type,
                                                     @RequestParam(required = false) String startDate,
                                                     @RequestParam(required = false) String endDate,
                                                     @RequestParam(defaultValue = "1") Integer page,
                                                     @RequestParam(defaultValue = "10") Integer size) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();

        QueryWrapper<LearningAnalytics> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("student_id", user.getUserId());

        if (StringUtils.hasText(type)) {
            queryWrapper.eq("record_type", type);
        }

        if (StringUtils.hasText(startDate)) {
            queryWrapper.ge("create_time", startDate);
        }

        if (StringUtils.hasText(endDate)) {
            queryWrapper.le("create_time", endDate);
        }

        queryWrapper.orderByDesc("create_time");

        Page<LearningAnalytics> pageResult = new Page<>(page, size);
        Page<LearningAnalytics> records = learningAnalyticsMapper.selectPage(pageResult, queryWrapper);

        return Result.success(records);
    }
}
