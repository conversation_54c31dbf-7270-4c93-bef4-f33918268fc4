package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.CourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Course Controller for Teachers
 */
@Slf4j
@RestController
@RequestMapping("/teacher/courses")
@PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
public class CourseController {

    @Autowired
    private CourseService courseService;

    /**
     * Get courses by current teacher
     */
    @GetMapping
    public Result<List<Course>> getMyCourses(Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        List<Course> courses = courseService.findByTeacherId(user.getUserId());
        return Result.success(courses);
    }

    /**
     * Get course by ID
     */
    @GetMapping("/{id}")
    public Result<Course> getCourse(@PathVariable Long id, Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        Course course = courseService.getById(id);
        if (course == null) {
            return Result.error("Course not found");
        }
        
        // Check if teacher owns this course (admin can access all)
        if (!"ADMIN".equals(user.getRole()) && !course.getTeacherId().equals(user.getUserId())) {
            return Result.error("Access denied");
        }
        
        return Result.success(course);
    }

    /**
     * Create new course
     */
    @PostMapping
    public Result<Course> createCourse(@Valid @RequestBody Course course, Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        Course createdCourse = courseService.createCourse(course, user.getUserId());
        return Result.success("Course created successfully", createdCourse);
    }

    /**
     * Update course
     */
    @PutMapping("/{id}")
    public Result<Course> updateCourse(@PathVariable Long id, @Valid @RequestBody Course course, 
                                     Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        Course updatedCourse = courseService.updateCourse(id, course, user.getUserId());
        return Result.success("Course updated successfully", updatedCourse);
    }

    /**
     * Delete course
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteCourse(@PathVariable Long id, Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        boolean deleted = courseService.deleteCourse(id, user.getUserId());
        if (deleted) {
            return Result.<Void>success("Course deleted successfully", null);
        }
        return Result.<Void>error("Failed to delete course");
    }

    /**
     * Get courses by subject
     */
    @GetMapping("/subject/{subject}")
    public Result<List<Course>> getCoursesBySubject(@PathVariable String subject) {
        List<Course> courses = courseService.findBySubject(subject);
        return Result.success(courses);
    }
}