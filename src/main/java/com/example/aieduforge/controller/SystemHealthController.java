package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.mapper.AIKnowledgeDataMapper;
import com.example.aieduforge.service.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统健康检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/system")
public class SystemHealthController {

    @Autowired
    private AIService aiService;
    
    @Autowired
    private AIKnowledgeDataMapper aiKnowledgeDataMapper;

    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查数据库连接
            Map<String, Object> dbStats = aiKnowledgeDataMapper.getKnowledgeStatistics();
            health.put("database", "healthy");
            health.put("knowledgeBase", dbStats);
            
            // 检查AI服务
            try {
                String testResponse = aiService.generateResponse("测试连接");
                health.put("aiService", testResponse != null ? "healthy" : "unhealthy");
            } catch (Exception e) {
                health.put("aiService", "unhealthy");
                health.put("aiError", e.getMessage());
            }
            
            // 系统信息
            health.put("timestamp", System.currentTimeMillis());
            health.put("status", "running");
            health.put("version", "1.0.0");
            
            return Result.success(health);
            
        } catch (Exception e) {
            log.error("Health check failed", e);
            health.put("status", "error");
            health.put("error", e.getMessage());
            return Result.error(500, "系统健康检查失败", health);
        }
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        info.put("jvm", Map.of(
            "totalMemory", runtime.totalMemory(),
            "freeMemory", runtime.freeMemory(),
            "maxMemory", runtime.maxMemory(),
            "processors", runtime.availableProcessors()
        ));
        
        // 系统属性
        info.put("system", Map.of(
            "javaVersion", System.getProperty("java.version"),
            "osName", System.getProperty("os.name"),
            "osVersion", System.getProperty("os.version"),
            "userDir", System.getProperty("user.dir")
        ));
        
        return Result.success(info);
    }
}