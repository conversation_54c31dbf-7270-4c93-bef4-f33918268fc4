package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.KnowledgeBase;
import com.example.aieduforge.mapper.AIKnowledgeDataMapper;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.ResourceManagementService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin Resource Management Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/resources")
@PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
public class AdminResourceController {

    @Autowired
    private ResourceManagementService resourceManagementService;
    
    @Autowired
    private CourseService courseService;
    
    @Autowired
    private AIKnowledgeDataMapper aiKnowledgeDataMapper;

    /**
     * Upload knowledge base file
     */
    @PostMapping("/knowledge/upload")
    public Result<KnowledgeBase> uploadKnowledgeFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false) Long courseId,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String tags,
            @RequestParam(required = false) String description,
            Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        log.info("Upload parameters - courseId: {}, subject: {}, tags: {}, description: {}", 
                courseId, subject, tags, description);
        
        KnowledgeBase knowledgeBase = resourceManagementService.uploadKnowledgeFile(
            file, courseId, subject, tags, description, user.getUserId());
        return Result.success("File uploaded successfully", knowledgeBase);
    }

    /**
     * Get all knowledge base files with advanced filtering
     */
    @GetMapping("/knowledge")
    public Result<Map<String, Object>> getAllKnowledgeFiles(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String fileType) {
        
        log.info("Getting knowledge files with filters - keyword: {}, subject: {}, fileType: {}", 
                keyword, subject, fileType);
        
        // 使用服务层的高级搜索方法
        Map<String, Object> searchParams = new HashMap<>();
        searchParams.put("keyword", keyword);
        searchParams.put("subject", subject);
        searchParams.put("fileType", fileType);
        searchParams.put("current", current);
        searchParams.put("size", size);
        
        Map<String, Object> serviceResult = resourceManagementService.searchKnowledgeWithFilters(searchParams);
        
        // 转换KnowledgeBase列表为前端期望的格式
        @SuppressWarnings("unchecked")
        List<KnowledgeBase> records = (List<KnowledgeBase>) serviceResult.get("records");
        List<Map<String, Object>> formattedRecords = records.stream()
                .map(this::convertToFrontendFormat)
                .toList();
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", formattedRecords);
        result.put("total", serviceResult.get("total"));
        result.put("current", serviceResult.get("current"));
        result.put("size", serviceResult.get("size"));
        
        return Result.success(result);
    }

    /**
     * Search knowledge base
     */
    @GetMapping("/knowledge/search")
    public Result<List<KnowledgeBase>> searchKnowledge(@RequestParam String keyword) {
        List<KnowledgeBase> results = resourceManagementService.searchKnowledge(keyword);
        return Result.success(results);
    }

    /**
     * Get knowledge base by subject
     */
    @GetMapping("/knowledge/subject/{subject}")
    public Result<List<KnowledgeBase>> getKnowledgeBySubject(@PathVariable String subject) {
        List<KnowledgeBase> files = resourceManagementService.getKnowledgeBySubject(subject);
        return Result.success(files);
    }

    /**
     * Get knowledge base by course
     */
    @GetMapping("/knowledge/course/{courseId}")
    public Result<List<KnowledgeBase>> getKnowledgeByCourse(@PathVariable Long courseId) {
        List<KnowledgeBase> files = resourceManagementService.getKnowledgeByCourse(courseId);
        return Result.success(files);
    }

    /**
     * Get knowledge base by tags
     */
    @GetMapping("/knowledge/tags")
    public Result<List<KnowledgeBase>> getKnowledgeByTags(@RequestParam String tags) {
        List<KnowledgeBase> files = resourceManagementService.getKnowledgeByTags(tags);
        return Result.success(files);
    }

    /**
     * Update knowledge base info
     */
    @PutMapping("/knowledge/{id}")
    public Result<KnowledgeBase> updateKnowledgeInfo(@PathVariable Long id,
                                                    @Valid @RequestBody KnowledgeBase knowledgeBase,
                                                    Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        KnowledgeBase updated = resourceManagementService.updateKnowledgeInfo(id, knowledgeBase, user.getUserId());
        return Result.success("Knowledge info updated successfully", updated);
    }

    /**
     * Delete knowledge base file
     */
    @DeleteMapping("/knowledge/{id}")
    public Result<Void> deleteKnowledgeFile(@PathVariable Long id, Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        boolean deleted = resourceManagementService.deleteKnowledgeFile(id, user.getUserId());
        if (deleted) {
            return Result.<Void>success("Knowledge file deleted successfully", null);
        }
        return Result.<Void>error("Failed to delete knowledge file");
    }

    /**
     * Export course resources as DOCX
     */
    @GetMapping("/export/course/{courseId}")
    @PreAuthorize("hasRole('ADMIN')")
    public void exportCourseResources(@PathVariable Long courseId, HttpServletResponse response) {
        try {
            resourceManagementService.exportCourseResourcesAsDocx(courseId, response);
        } catch (Exception e) {
            log.error("Failed to export course resources for courseId: {}", courseId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Export knowledge base as DOCX
     */
    @GetMapping("/export/knowledge")
    @PreAuthorize("hasRole('ADMIN')")
    public void exportKnowledgeBase(HttpServletResponse response) {
        try {
            resourceManagementService.exportKnowledgeBaseAsDocx(response);
        } catch (Exception e) {
            log.error("Failed to export knowledge base", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Export single resource as DOCX
     */
    @GetMapping("/export/resource/{resourceId}")
    @PreAuthorize("hasRole('ADMIN')")
    public void exportSingleResource(@PathVariable Long resourceId, 
                                   @RequestParam String type,
                                   HttpServletResponse response) {
        try {
            resourceManagementService.exportSingleResourceAsDocx(resourceId, type, response);
        } catch (Exception e) {
            log.error("Failed to export resource with id: {} and type: {}", resourceId, type, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get file type statistics
     */
    @GetMapping("/statistics/file-types")
    public Result<List<Map<String, Object>>> getFileTypeStatistics() {
        List<Map<String, Object>> stats = resourceManagementService.getFileTypeStatistics();
        return Result.success(stats);
    }

    /**
     * Get resource usage statistics
     */
    @GetMapping("/statistics/usage")
    public Result<Map<String, Object>> getResourceUsageStats() {
        Map<String, Object> stats = resourceManagementService.getResourceUsageStats();
        return Result.success(stats);
    }

    /**
     * Download knowledge base file
     */
    @GetMapping("/knowledge/{id}/download")
    public void downloadKnowledgeFile(@PathVariable Long id, 
                                    HttpServletResponse response,
                                    Authentication authentication) {
        try {
            UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
            resourceManagementService.downloadKnowledgeFile(id, user.getUserId(), response);
        } catch (Exception e) {
            log.error("Failed to download file with id: {}", id, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get all courses for dropdown selection
     */
    @GetMapping("/courses")
    public Result<List<Course>> getAllCourses() {
        try {
            List<Course> courses = courseService.list();
            return Result.success(courses);
        } catch (Exception e) {
            log.error("Failed to get courses", e);
            return Result.error("Failed to get courses: " + e.getMessage());
        }
    }

    /**
     * Get knowledge base file details
     */
    @GetMapping("/knowledge/{id}")
    public Result<Map<String, Object>> getKnowledgeFileDetails(@PathVariable Long id) {
        try {
            Map<String, Object> details = resourceManagementService.getKnowledgeFileDetails(id);
            if (details != null) {
                return Result.success(details);
            }
            return Result.error("Knowledge file not found");
        } catch (Exception e) {
            log.error("Failed to get knowledge file details for id: {}", id, e);
            return Result.error("Failed to get file details: " + e.getMessage());
        }
    }

    /**
     * Get course resources
     */
    @GetMapping("/course/{courseId}/resources")
    public Result<List<Map<String, Object>>> getCourseResources(@PathVariable Long courseId) {
        try {
            List<Map<String, Object>> resources = resourceManagementService.getCourseResources(courseId);
            return Result.success(resources);
        } catch (Exception e) {
            log.error("Failed to get course resources for courseId: {}", courseId, e);
            return Result.error("Failed to get course resources: " + e.getMessage());
        }
    }

    /**
     * Convert KnowledgeBase to frontend expected format
     */
    private Map<String, Object> convertToFrontendFormat(KnowledgeBase kb) {
        Map<String, Object> result = new HashMap<>();
        
        result.put("id", kb.getId());
        result.put("fileName", kb.getTitle()); // title作为fileName
        result.put("fileSize", kb.getFileSize() != null ? kb.getFileSize() : 0L);
        result.put("fileType", kb.getFileType());
        result.put("subject", kb.getSubject());
        result.put("courseId", kb.getCourseId());
        result.put("courseName", getCourseNameById(kb.getCourseId())); // 获取课程名称
        result.put("tags", kb.getTags());
        result.put("description", kb.getDescription());
        result.put("uploadTime", kb.getCreateTime() != null ? 
            kb.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
        result.put("downloadCount", kb.getDownloadCount() != null ? kb.getDownloadCount() : 0);
        result.put("aiProcessed", hasAIProcessedData(kb.getId())); // 检查是否有AI处理数据
        result.put("status", kb.getStatus());
        result.put("uploadUserId", kb.getUploadUserId());
        
        return result;
    }

    /**
     * Get course name by course ID
     */
    private String getCourseNameById(Long courseId) {
        if (courseId == null) {
            return null;
        }
        try {
            Course course = courseService.getById(courseId);
            return course != null ? course.getCourseName() : null;
        } catch (Exception e) {
            log.warn("Failed to get course name for courseId: {}", courseId, e);
            return null;
        }
    }

    /**
     * Check if knowledge base has AI processed data
     */
    private Boolean hasAIProcessedData(Long knowledgeBaseId) {
        try {
            // 使用AIKnowledgeDataMapper来检查AI处理数据
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<com.example.aieduforge.entity.AIKnowledgeData> wrapper = 
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            wrapper.eq("knowledge_base_id", knowledgeBaseId);
            Long count = aiKnowledgeDataMapper.selectCount(wrapper);
            return count != null && count > 0;
        } catch (Exception e) {
            log.warn("Failed to check AI processed data for knowledgeBaseId: {}", knowledgeBaseId, e);
            return false;
        }
    }
}