package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.QuestionRequest;
import com.example.aieduforge.entity.AiAnswer;
import com.example.aieduforge.entity.StudentQuestion;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.StudentLearningService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Student Learning Controller
 */
@Slf4j
@RestController
@RequestMapping("/student/learning")
@PreAuthorize("hasRole('STUDENT') or hasRole('ADMIN')")
public class StudentLearningController {

    @Autowired
    private StudentLearningService studentLearningService;

    /**
     * Ask a question to AI learning assistant
     */
    @PostMapping("/ask")
    public Result<AiAnswer> askQuestion(@Valid @RequestBody QuestionRequest request,
                                      Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        StudentQuestion question = new StudentQuestion();
        question.setStudentId(user.getUserId());
        question.setCourseId(request.getCourseId());
        question.setQuestionContent(request.getQuestionContent());
        question.setQuestionType(request.getQuestionType() != null ? request.getQuestionType() : 1);
        question.setContextInfo(request.getContextInfo());
        
        AiAnswer answer = studentLearningService.askQuestion(question);
        return Result.success("Question answered successfully", answer);
    }

    /**
     * Get question history
     */
    @GetMapping("/questions")
    public Result<List<StudentQuestion>> getQuestionHistory(@RequestParam(required = false) Long courseId,
                                                          Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        List<StudentQuestion> questions = studentLearningService.getQuestionHistory(user.getUserId(), courseId);
        return Result.success(questions);
    }

    /**
     * Get recent questions
     */
    @GetMapping("/questions/recent")
    public Result<List<StudentQuestion>> getRecentQuestions(@RequestParam(defaultValue = "10") Integer limit,
                                                          Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        List<StudentQuestion> questions = studentLearningService.getRecentQuestions(user.getUserId(), limit);
        return Result.success(questions);
    }

    /**
     * Get AI answer for a question
     */
    @GetMapping("/answer/{questionId}")
    public Result<AiAnswer> getAiAnswer(@PathVariable Long questionId) {
        AiAnswer answer = studentLearningService.getAiAnswer(questionId);
        if (answer != null) {
            return Result.success(answer);
        }
        return Result.error("Answer not found");
    }

    /**
     * Rate AI answer
     */
    @PostMapping("/answer/{answerId}/rate")
    public Result<Void> rateAnswer(@PathVariable Long answerId,
                                 @RequestParam Integer rating,
                                 @RequestParam(required = false) String comment) {
        boolean success = studentLearningService.rateAnswer(answerId, rating, comment);
        if (success) {
            return Result.<Void>success("Answer rated successfully", null);
        }
        return Result.<Void>error("Failed to rate answer");
    }

    /**
     * Update question status
     */
    @PutMapping("/question/{questionId}/status")
    public Result<Void> updateQuestionStatus(@PathVariable Long questionId,
                                           @RequestParam Integer status,
                                           Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        boolean success = studentLearningService.updateQuestionStatus(questionId, status, user.getUserId());
        if (success) {
            return Result.<Void>success("Question status updated successfully", null);
        }
        return Result.<Void>error("Failed to update question status");
    }
}