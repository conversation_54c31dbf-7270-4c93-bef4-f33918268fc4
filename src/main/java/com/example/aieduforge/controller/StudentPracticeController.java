package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.AnswerRequest;
import com.example.aieduforge.dto.PracticeRequest;
import com.example.aieduforge.entity.ExamAnswer;
import com.example.aieduforge.entity.ExamQuestion;
import com.example.aieduforge.entity.PracticeRecord;
import com.example.aieduforge.security.UserPrincipal;
import com.example.aieduforge.service.PracticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Student Practice Controller
 */
@Slf4j
@RestController
@RequestMapping("/student/practice")
@PreAuthorize("hasRole('STUDENT') or hasRole('ADMIN')")
public class StudentPracticeController {

    @Autowired
    private PracticeService practiceService;

    /**
     * Generate practice questions
     */
    @PostMapping("/generate")
    public Result<List<ExamQuestion>> generatePractice(@Valid @RequestBody PracticeRequest request,
                                                     Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        List<ExamQuestion> questions = practiceService.generatePracticeQuestions(
            user.getUserId(), request.getCourseId(), request.getPracticeType(), request.getQuestionCount(), request.getQuestionType());
        
        return Result.success("Practice questions generated successfully", questions);
    }

    /**
     * Start practice session
     */
    @PostMapping("/start")
    public Result<PracticeRecord> startPractice(@Valid @RequestBody PracticeRequest request,
                                              Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        // 检查是否提供了题目ID列表
        List<Long> questionIds;
        if (request.getQuestionIds() != null && !request.getQuestionIds().isEmpty()) {
            // 使用前端提供的题目ID
            questionIds = request.getQuestionIds();
            log.info("使用前端提供的题目ID: {}", questionIds);
        } else {
            // 如果没有提供题目ID，则生成题目
            List<ExamQuestion> questions = practiceService.generatePracticeQuestions(
                user.getUserId(), request.getCourseId(), request.getPracticeType(), request.getQuestionCount(), request.getQuestionType());
            questionIds = questions.stream().map(ExamQuestion::getId).collect(Collectors.toList());
            log.info("后端生成的题目ID: {}", questionIds);
        }
        
        PracticeRecord record = practiceService.startPractice(
            user.getUserId(), request.getCourseId(), request.getPracticeType(), questionIds);
        
        return Result.success("Practice session started successfully", record);
    }

    /**
     * Submit answer for a question
     */
    @PostMapping("/answer")
    public Result<ExamAnswer> submitAnswer(@Valid @RequestBody AnswerRequest request,
                                         Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        ExamAnswer answer = practiceService.submitAnswer(
            request.getPracticeId(), request.getQuestionId(), request.getStudentAnswer(), user.getUserId());
        
        return Result.success("Answer submitted successfully", answer);
    }

    /**
     * Complete practice session
     */
    @PostMapping("/{practiceId}/complete")
    public Result<PracticeRecord> completePractice(@PathVariable Long practiceId,
                                                 Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        PracticeRecord record = practiceService.completePractice(practiceId, user.getUserId());
        return Result.success("Practice session completed successfully", record);
    }

    /**
     * Get practice history
     */
    @GetMapping("/history")
    public Result<List<PracticeRecord>> getPracticeHistory(@RequestParam(required = false) Long courseId,
                                                          Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        List<PracticeRecord> records = practiceService.getPracticeHistory(user.getUserId(), courseId);
        return Result.success(records);
    }

    /**
     * Get practice statistics
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getPracticeStatistics(@RequestParam(required = false) Long courseId,
                                                           Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        Map<String, Object> stats = practiceService.getPracticeStatistics(user.getUserId(), courseId);
        if (stats != null && !stats.isEmpty()) {
            return Result.success(stats);
        }
        return Result.error("No practice statistics found");
    }

    /**
     * Get wrong questions for review
     */
    @GetMapping("/wrong-questions")
    public Result<List<ExamQuestion>> getWrongQuestions(@RequestParam(required = false) Long courseId,
                                                       Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        List<ExamQuestion> wrongQuestions = practiceService.getWrongQuestions(user.getUserId(), courseId);
        return Result.success(wrongQuestions);
    }

    /**
     * Get student answer details
     */
    @GetMapping("/answer/{questionId}")
    public Result<ExamAnswer> getStudentAnswer(@PathVariable Long questionId,
                                             Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        ExamAnswer answer = practiceService.getStudentAnswer(user.getUserId(), questionId);
        if (answer != null) {
            return Result.success(answer);
        }
        return Result.error("Answer not found");
    }

    /**
     * Get practice record details
     */
    @GetMapping("/{practiceId}")
    public Result<com.example.aieduforge.dto.PracticeReviewDTO> getPracticeRecord(@PathVariable Long practiceId,
                                                   Authentication authentication) {
        UserPrincipal user = (UserPrincipal) authentication.getPrincipal();
        
        try {
            com.example.aieduforge.dto.PracticeReviewDTO reviewData = practiceService.getPracticeReviewData(practiceId, user.getUserId());
            if (reviewData != null) {
                return Result.success("Practice record retrieved successfully", reviewData);
            } else {
                return Result.error("Practice record not found");
            }
        } catch (Exception e) {
            log.error("Error retrieving practice record: {}", e.getMessage());
            return Result.error("Failed to retrieve practice record");
        }
    }
}