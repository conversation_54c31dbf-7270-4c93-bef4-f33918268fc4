package com.example.aieduforge.controller.admin;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.service.impl.StatisticsDataGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * Admin Statistics Management Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/statistics")
@PreAuthorize("hasRole('ADMIN')")
public class AdminStatisticsController {

    @Autowired
    private StatisticsDataGeneratorService statisticsDataGeneratorService;

    /**
     * Manually generate statistics for a specific date
     */
    @PostMapping("/generate")
    public Result<String> generateStatistics(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            statisticsDataGeneratorService.generateStatisticsForDate(date);
            return Result.success("Statistics generated successfully for date: " + date);
        } catch (Exception e) {
            log.error("Failed to generate statistics for date: {}", date, e);
            return Result.error("Failed to generate statistics: " + e.getMessage());
        }
    }

    /**
     * Generate statistics for the last N days
     */
    @PostMapping("/generate-range")
    public Result<String> generateStatisticsRange(@RequestParam Integer days) {
        try {
            LocalDate endDate = LocalDate.now().minusDays(1);
            LocalDate startDate = endDate.minusDays(days - 1);
            
            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                statisticsDataGeneratorService.generateStatisticsForDate(date);
            }
            
            return Result.success("Statistics generated successfully for " + days + " days");
        } catch (Exception e) {
            log.error("Failed to generate statistics range for {} days", days, e);
            return Result.error("Failed to generate statistics: " + e.getMessage());
        }
    }
}