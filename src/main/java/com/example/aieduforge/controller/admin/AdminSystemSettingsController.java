package com.example.aieduforge.controller.admin;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.entity.SysRole;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.entity.SystemSettings;
import com.example.aieduforge.mapper.SysRoleMapper;
import com.example.aieduforge.mapper.SysUserMapper;
import com.example.aieduforge.service.SysUserService;
import com.example.aieduforge.service.SystemSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员系统设置控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system-settings")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class AdminSystemSettingsController {

    private final SystemSettingsService systemSettingsService;
    private final SysUserService sysUserService;
    private final SysRoleMapper sysRoleMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 获取所有系统设置
     */
    @GetMapping("/all")
    public Result<List<SystemSettings>> getAllSettings() {
        try {
            List<SystemSettings> settings = systemSettingsService.getAllSettings();
            return Result.success(settings);
        } catch (Exception e) {
            log.error("获取系统设置失败", e);
            return Result.error("获取系统设置失败");
        }
    }

    /**
     * 获取验证码设置
     */
    @GetMapping("/captcha")
    public Result<Map<String, Object>> getCaptchaSettings() {
        try {
            Map<String, Object> captchaSettings = new HashMap<>();
            captchaSettings.put("enabled", "1".equals(systemSettingsService.getSettingValue("captcha_enabled", "1")));
            captchaSettings.put("length", Integer.parseInt(systemSettingsService.getSettingValue("captcha_length", "4")));
            captchaSettings.put("expireMinutes", Integer.parseInt(systemSettingsService.getSettingValue("captcha_expire_minutes", "5")));

            return Result.success(captchaSettings);
        } catch (Exception e) {
            log.error("获取验证码设置失败", e);
            return Result.error("获取验证码设置失败");
        }
    }

    /**
     * 切换验证码开关
     */
    @PostMapping("/captcha/toggle")
    public Result<String> toggleCaptchaEnabled(@RequestBody Map<String, Boolean> request) {
        try {
            Boolean enabled = request.get("enabled");
            if (enabled == null) {
                return Result.error("参数错误");
            }

            systemSettingsService.toggleCaptchaEnabled(enabled);
            return Result.success("验证码开关设置成功");
        } catch (Exception e) {
            log.error("切换验证码开关失败", e);
            return Result.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 更新验证码长度
     */
    @PostMapping("/captcha/length")
    public Result<String> updateCaptchaLength(@RequestBody Map<String, Integer> request) {
        try {
            Integer length = request.get("length");
            if (length == null) {
                return Result.error("参数错误");
            }

            systemSettingsService.updateCaptchaLength(length);
            return Result.success("验证码长度设置成功");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新验证码长度失败", e);
            return Result.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 更新验证码过期时间
     */
    @PostMapping("/captcha/expire-minutes")
    public Result<String> updateCaptchaExpireMinutes(@RequestBody Map<String, Integer> request) {
        try {
            Integer expireMinutes = request.get("expireMinutes");
            if (expireMinutes == null) {
                return Result.error("参数错误");
            }

            systemSettingsService.updateCaptchaExpireMinutes(expireMinutes);
            return Result.success("验证码过期时间设置成功");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新验证码过期时间失败", e);
            return Result.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 更新任意系统设置
     */
    @PostMapping("/update")
    public Result<String> updateSetting(@RequestBody Map<String, String> request) {
        try {
            String settingKey = request.get("settingKey");
            String settingValue = request.get("settingValue");

            if (settingKey == null || settingValue == null) {
                return Result.error("参数错误");
            }

            systemSettingsService.updateSetting(settingKey, settingValue);
            return Result.success("系统设置更新成功");
        } catch (Exception e) {
            log.error("更新系统设置失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取登录页相关设置
     */
    @GetMapping("/login")
    public Result<Map<String, Object>> getLoginPageSettings() {
        try {
            Map<String, Object> settings = new HashMap<>();
            boolean showDemo = "1".equals(systemSettingsService.getSettingValue("login_show_demo_accounts", "1"));
            settings.put("showDemoAccounts", showDemo);
            return Result.success(settings);
        } catch (Exception e) {
            log.error("获取登录页设置失败", e);
            return Result.error("获取登录页设置失败");
        }
    }

    /**
     * 切换登录页演示账号展示开关
     */
    @PostMapping("/login/demo-accounts/toggle")
    public Result<String> toggleLoginDemoAccounts(@RequestBody Map<String, Boolean> request) {
        try {
            Boolean enabled = request.get("enabled");
            if (enabled == null) {
                return Result.error("参数错误");
            }
            systemSettingsService.updateSetting("login_show_demo_accounts", enabled ? "1" : "0");
            return Result.success("登录页演示账号展示开关设置成功");
        } catch (Exception e) {
            log.error("切换登录页演示账号展示开关失败", e);
            return Result.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 获取/保存登录页演示账号列表
     * 使用 system_settings: key=login_demo_accounts, value=JSON 数组
     */
    @GetMapping("/login/demo-accounts")
    public Result<Object> getLoginDemoAccounts() {
        try {
            String json = systemSettingsService.getSettingValue("login_demo_accounts", "[]");
            return Result.success(com.fasterxml.jackson.databind.json.JsonMapper.builder().build().readTree(json));
        } catch (Exception e) {
            log.error("获取登录页演示账号列表失败", e);
            return Result.error("获取登录页演示账号列表失败");
        }
    }

    @PostMapping("/login/demo-accounts")
    public Result<String> saveLoginDemoAccounts(@RequestBody String jsonArray) {
        try {
            // 简单校验为JSON数组
            com.fasterxml.jackson.databind.JsonNode node = com.fasterxml.jackson.databind.json.JsonMapper.builder().build().readTree(jsonArray);
            if (!node.isArray()) {
                return Result.error("参数必须为JSON数组");
            }
            systemSettingsService.updateSetting("login_demo_accounts", jsonArray);
            return Result.success("登录页演示账号列表已保存");
        } catch (Exception e) {
            log.error("保存登录页演示账号列表失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 初始化演示账号到数据库
     */
    @PostMapping("/login/demo-accounts/init")
    public Result<String> initDemoAccounts() {
        try {
            // 检查演示账号是否已存在
            if (sysUserService.findByUsername("admin") == null) {
                createDemoUser("admin", "admin123", "系统管理员", "<EMAIL>", "ADMIN");
            }
            if (sysUserService.findByUsername("teacher001") == null) {
                createDemoUser("teacher001", "admin123", "演示教师", "<EMAIL>", "TEACHER");
            }
            if (sysUserService.findByUsername("student001") == null) {
                createDemoUser("student001", "admin123", "演示学生", "<EMAIL>", "STUDENT");
            }

            // 更新演示账号配置
            String demoAccountsJson = "[" +
                "{\"label\":\"管理员\",\"username\":\"admin\",\"password\":\"admin123\",\"tagType\":\"danger\"}," +
                "{\"label\":\"教师\",\"username\":\"teacher001\",\"password\":\"admin123\",\"tagType\":\"warning\"}," +
                "{\"label\":\"学生\",\"username\":\"student001\",\"password\":\"admin123\",\"tagType\":\"success\"}" +
                "]";
            systemSettingsService.updateSetting("login_demo_accounts", demoAccountsJson);

            return Result.success("演示账号初始化成功");
        } catch (Exception e) {
            log.error("初始化演示账号失败", e);
            return Result.error("初始化失败：" + e.getMessage());
        }
    }

    private void createDemoUser(String username, String password, String realName, String email, String roleCode) {
        SysUser user = new SysUser();
        user.setUsername(username);
        user.setPassword(password); // 会在service中加密
        user.setRealName(realName);
        user.setEmail(email);
        user.setStatus(1);

        // 注册用户
        SysUser savedUser = sysUserService.register(user);

        // 分配角色
        SysRole role = sysRoleMapper.findByRoleCode(roleCode);
        if (role != null) {
            sysUserMapper.updateUserRole(savedUser.getId(), role.getId());
        }
    }


}