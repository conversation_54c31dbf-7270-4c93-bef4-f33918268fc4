package com.example.aieduforge.controller.admin;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.CourseCreateRequest;
import com.example.aieduforge.dto.CourseResponse;
import com.example.aieduforge.dto.TeacherAssignmentRequest;
import com.example.aieduforge.entity.Course;
import com.example.aieduforge.entity.SysUser;
import com.example.aieduforge.service.CourseService;
import com.example.aieduforge.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Course Controller for Admin
 */
@Slf4j
@RestController
@RequestMapping("/admin/courses")
@PreAuthorize("hasRole('ADMIN')")
public class AdminCourseController {

    @Autowired
    private CourseService courseService;
    
    @Autowired
    private SysUserService sysUserService;

    /**
     * Get courses with optional filters
     */
    @GetMapping
    public Result<List<Course>> getCourses(
            @RequestParam(required = false) String courseName,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String teacherName) {
        List<Course> courses = courseService.findWithFilters(courseName, subject, teacherName);
        return Result.success(courses);
    }

    /**
     * Get course by ID
     */
    @GetMapping("/{id}")
    public Result<Course> getCourse(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return Result.error("Course not found");
        }
        return Result.success(course);
    }

    /**
     * Create new course
     */
    @PostMapping
    public Result<Course> createCourse(@Valid @RequestBody Course course) {
        Course createdCourse = courseService.createCourse(course);
        return Result.success("Course created successfully", createdCourse);
    }

    /**
     * Update course
     */
    @PutMapping("/{id}")
    public Result<Course> updateCourse(@PathVariable Long id, @Valid @RequestBody Course course) {
        Course existingCourse = courseService.getById(id);
        if (existingCourse == null) {
            return Result.error("Course not found");
        }

        Course updatedCourse = courseService.updateCourse(id, course);
        return Result.success("Course updated successfully", updatedCourse);
    }

    /**
     * Delete course
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteCourse(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return Result.error("Course not found");
        }

        boolean deleted = courseService.deleteCourse(id);
        if (deleted) {
            return Result.success();
        }
        return Result.error("Failed to delete course");
    }

    /**
     * Change course status
     */
    @PutMapping("/{id}/status")
    public Result<Void> changeCourseStatus(@PathVariable Long id, @RequestParam Integer status) {
        Course course = courseService.getById(id);
        if (course == null) {
            return Result.error("Course not found");
        }

        boolean updated = courseService.updateStatus(id, status);
        if (updated) {
            return Result.success();
        }
        return Result.error("Failed to update course status");
    }

    /**
     * Create course with teacher assignment
     */
    @PostMapping("/with-teacher")
    public Result<CourseResponse> createCourseWithTeacher(@Valid @RequestBody CourseCreateRequest request) {
        try {
            // Validate teacher exists and has TEACHER role
            SysUser teacher = sysUserService.getById(request.getTeacherId());
            if (teacher == null) {
                return Result.error("Teacher not found");
            }
            
            CourseResponse courseResponse = courseService.createCourseFromRequest(request);
            return Result.success("Course created and assigned to teacher successfully", courseResponse);
        } catch (Exception e) {
            log.error("Failed to create course with teacher: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * Assign teacher to existing course
     */
    @PutMapping("/assign-teacher")
    public Result<Void> assignTeacher(@Valid @RequestBody TeacherAssignmentRequest request) {
        try {
            // Check if course exists
            Course course = courseService.getById(request.getCourseId());
            if (course == null) {
                return Result.error("Course not found");
            }
            
            // Check if teacher exists
            SysUser teacher = sysUserService.getById(request.getTeacherId());
            if (teacher == null) {
                return Result.error("Teacher not found");
            }
            
            boolean assigned = courseService.assignTeacher(request.getCourseId(), request.getTeacherId());
            if (assigned) {
                return Result.success("Teacher assigned to course successfully");
            }
            return Result.error("Failed to assign teacher to course");
        } catch (Exception e) {
            log.error("Failed to assign teacher: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * Get all teachers for course assignment
     */
    @GetMapping("/teachers")
    public Result<List<SysUser>> getTeachers() {
        try {
            List<SysUser> teachers = sysUserService.getTeachers();
            return Result.success(teachers);
        } catch (Exception e) {
            log.error("Failed to get teachers: {}", e.getMessage());
            return Result.error("Failed to get teachers list");
        }
    }

    /**
     * Get courses with detailed information (including teacher details)
     */
    @GetMapping("/detailed")
    public Result<List<CourseResponse>> getCoursesWithDetails() {
        try {
            List<CourseResponse> courses = courseService.getAllCoursesWithDetails();
            return Result.success(courses);
        } catch (Exception e) {
            log.error("Failed to get courses with details: {}", e.getMessage());
            return Result.error("Failed to get courses with details");
        }
    }

    /**
     * Get course with detailed information by ID
     */
    @GetMapping("/{id}/detailed")
    public Result<CourseResponse> getCourseWithDetails(@PathVariable Long id) {
        try {
            CourseResponse courseResponse = courseService.getCourseWithDetails(id);
            return Result.success(courseResponse);
        } catch (Exception e) {
            log.error("Failed to get course details: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * Check if course code exists
     */
    @GetMapping("/check-code")
    public Result<Boolean> checkCourseCodeExists(@RequestParam String courseCode) {
        try {
            boolean exists = courseService.existsByCourseCode(courseCode);
            return Result.success(exists);
        } catch (Exception e) {
            log.error("Failed to check course code: {}", e.getMessage());
            return Result.error("Failed to check course code");
        }
    }
}
