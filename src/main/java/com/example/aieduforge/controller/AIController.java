package com.example.aieduforge.controller;

import com.example.aieduforge.common.Result;
import com.example.aieduforge.dto.QuestionDTO;
import com.example.aieduforge.dto.QuestionGenerationRequest;
import com.example.aieduforge.service.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AI Service Controller for Testing and Direct AI Calls
 */
@Slf4j
@RestController
@RequestMapping("/ai")
@PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
public class AIController {

    @Autowired
    private AIService aiService;

    /**
     * Test Ollama connection
     */
    @GetMapping("/test")
    public Result<String> testConnection() {
        boolean connected = aiService.testConnection();
        if (connected) {
            return Result.success("AI service is connected and ready");
        } else {
            return Result.error("AI service is not available");
        }
    }

    /**
     * Direct AI content generation (for testing)
     */
    @PostMapping("/generate/content")
    public Result<String> generateContent(@RequestParam String courseOutline,
                                        @RequestParam String chapterName,
                                        @RequestParam String contentType) {
        String content = aiService.generateTeachingContent(courseOutline, chapterName, contentType);
        return Result.success("Content generated", content);
    }

    /**
     * Direct AI question generation (for testing)
     */
    @PostMapping("/generate/questions")
    public Result<List<QuestionDTO>> generateQuestions(@RequestParam String teachingContent,
                                                     @RequestParam String questionType,
                                                     @RequestParam Integer difficulty,
                                                     @RequestParam Integer count) {
        List<QuestionDTO> questions = aiService.generateExamQuestions(teachingContent, questionType, difficulty, count);
        return Result.success("Questions generated", questions);
    }

    /**
     * Generate questions from request body (for frontend)
     */
    @PostMapping("/generate-questions")
    public Result<List<QuestionDTO>> generateQuestionsFromBody(@RequestBody QuestionGenerationRequest request) {
        try {
            log.info("Generating questions with request: {}", request);
            List<QuestionDTO> questions = aiService.generateExamQuestions(
                request.getTeachingContent(), 
                request.getQuestionType(), 
                request.getDifficultyLevel(), 
                request.getQuestionCount()
            );
            return Result.success("Questions generated successfully", questions);
        } catch (Exception e) {
            log.error("Error generating questions: ", e);
            return Result.error("Failed to generate questions: " + e.getMessage());
        }
    }

    /**
     * Generate content from request body (for frontend)
     */
    @PostMapping("/generate-content")
    public Result<String> generateContentFromBody(@RequestBody Map<String, Object> request) {
        try {
            log.info("Generating content with request: {}", request);
            String courseOutline = (String) request.get("courseOutline");
            String chapterName = (String) request.get("chapterName");
            
            // 处理contentType，可能是Integer或String
            String contentType;
            Object contentTypeObj = request.get("contentType");
            if (contentTypeObj instanceof Integer) {
                Integer contentTypeInt = (Integer) contentTypeObj;
                // 将数字转换为对应的文字描述
                switch (contentTypeInt) {
                    case 1:
                        contentType = "知识讲解";
                        break;
                    case 2:
                        contentType = "实训练习";
                        break;
                    case 3:
                        contentType = "指导说明";
                        break;
                    default:
                        contentType = "知识讲解";
                        break;
                }
            } else {
                contentType = (String) contentTypeObj;
            }
            
            String content = aiService.generateTeachingContent(courseOutline, chapterName, contentType);
            return Result.success("Content generated successfully", content);
        } catch (Exception e) {
            log.error("Error generating content: ", e);
            return Result.error("Failed to generate content: " + e.getMessage());
        }
    }

    /**
     * Answer question from request body (for frontend)
     */
    @PostMapping("/answer-question")
    public Result<String> answerQuestionFromBody(@RequestBody Map<String, Object> request) {
        try {
            log.info("Answering question with request: {}", request);
            // 这里需要根据实际需求实现问答逻辑
            // 暂时返回一个占位响应
            return Result.success("Question answered successfully", "AI问答功能正在开发中");
        } catch (Exception e) {
            log.error("Error answering question: ", e);
            return Result.error("Failed to answer question: " + e.getMessage());
        }
    }

    /**
     * Analyze answer from request body (for frontend)
     */
    @PostMapping("/analyze-answer")
    public Result<String> analyzeAnswerFromBody(@RequestBody Map<String, Object> request) {
        try {
            log.info("Analyzing answer with request: {}", request);
            String question = (String) request.get("question");
            String correctAnswer = (String) request.get("correctAnswer");
            String studentAnswer = (String) request.get("studentAnswer");
            
            String analysis = aiService.analyzeStudentAnswer(question, correctAnswer, studentAnswer);
            return Result.success("Answer analyzed successfully", analysis);
        } catch (Exception e) {
            log.error("Error analyzing answer: ", e);
            return Result.error("Failed to analyze answer: " + e.getMessage());
        }
    }

    /**
     * Direct AI answer analysis (for testing)
     */
    @PostMapping("/analyze/answer")
    public Result<String> analyzeAnswer(@RequestParam String question,
                                      @RequestParam String correctAnswer,
                                      @RequestParam String studentAnswer) {
        String analysis = aiService.analyzeStudentAnswer(question, correctAnswer, studentAnswer);
        return Result.success("Answer analyzed", analysis);
    }

    /**
     * Direct AI error analysis (for testing)
     */
    @PostMapping("/analyze/error")
    public Result<String> analyzeError(@RequestParam String studentErrors,
                                     @RequestParam String knowledgePoints) {
        String analysis = aiService.generateErrorAnalysis(studentErrors, knowledgePoints);
        return Result.success("Error analyzed", analysis);
    }
}