package com.example.aieduforge.utils;

import com.example.aieduforge.dto.ExamQuestionDTO;
import com.example.aieduforge.entity.ExamQuestion;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class QuestionConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static ExamQuestion toEntity(ExamQuestionDTO dto) {
        if (dto == null) {
            return null;
        }

        ExamQuestion entity = new ExamQuestion();
        entity.setId(dto.getId());
        entity.setCourseId(dto.getCourseId());
        entity.setQuestionType(parseQuestionType(dto.getQuestionType()));
        entity.setTitle(dto.getTitle());
        entity.setContent(dto.getContent());

        // 将选项Map转换为JSON字符串
        if (dto.getOptions() != null) {
            try {
                entity.setOptions(objectMapper.writeValueAsString(dto.getOptions()));
            } catch (JsonProcessingException e) {
                log.error("Failed to convert options to JSON", e);
            }
        }

        entity.setCorrectAnswer(dto.getCorrectAnswer());
        entity.setAnswerAnalysis(dto.getAnswerAnalysis());
        entity.setDifficultyLevel(dto.getDifficulty());
        entity.setScore(dto.getScore());
        entity.setKnowledgePoints(dto.getKnowledgePoints());
        entity.setAiGenerated(dto.getAiGenerated());
        entity.setGenerationPrompt(dto.getGenerationPrompt());
        entity.setStatus(1); // 默认状态为启用

        return entity;
    }

    public static ExamQuestionDTO toDTO(ExamQuestion entity) {
        if (entity == null) {
            return null;
        }

        ExamQuestionDTO dto = new ExamQuestionDTO();
        dto.setId(entity.getId());
        dto.setCourseId(entity.getCourseId());
        dto.setQuestionType(getQuestionTypeString(entity.getQuestionType()));
        dto.setTitle(entity.getTitle());
        dto.setContent(entity.getContent());

        // 将JSON字符串转换为选项Map
        if (entity.getOptions() != null) {
            try {
                Map<String, String> options = objectMapper.readValue(
                    entity.getOptions(),
                    new TypeReference<Map<String, String>>() {}
                );
                dto.setOptions(options);
            } catch (JsonProcessingException e) {
                log.error("Failed to parse options JSON", e);
            }
        }

        dto.setCorrectAnswer(entity.getCorrectAnswer());
        dto.setAnswerAnalysis(entity.getAnswerAnalysis());
        dto.setDifficulty(entity.getDifficultyLevel());
        dto.setScore(entity.getScore());
        dto.setKnowledgePoints(entity.getKnowledgePoints());
        dto.setAiGenerated(entity.getAiGenerated());
        dto.setGenerationPrompt(entity.getGenerationPrompt());

        return dto;
    }

    private static Integer parseQuestionType(String typeString) {
        return switch (typeString) {
            case "SINGLE_CHOICE" -> 1;
            case "MULTIPLE_CHOICE" -> 2;
            case "FILL_BLANK" -> 3;
            case "SHORT_ANSWER" -> 4;
            case "PROGRAMMING" -> 5;
            case "CASE_ANALYSIS" -> 6;
            default -> 1;
        };
    }

    private static String getQuestionTypeString(Integer type) {
        return switch (type) {
            case 1 -> "SINGLE_CHOICE";
            case 2 -> "MULTIPLE_CHOICE";
            case 3 -> "FILL_BLANK";
            case 4 -> "SHORT_ANSWER";
            case 5 -> "PROGRAMMING";
            case 6 -> "CASE_ANALYSIS";
            default -> "SINGLE_CHOICE";
        };
    }
}
