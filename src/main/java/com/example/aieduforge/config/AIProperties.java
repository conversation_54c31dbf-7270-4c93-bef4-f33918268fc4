package com.example.aieduforge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI Configuration Properties
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai")
public class AIProperties {
    
    private OpenRouter openRouter = new OpenRouter();
    private Prompts prompts = new Prompts();
    
    @Data
    public static class OpenRouter {
        private String baseUrl;
        private String apiKey;
        private String modelName;
        private Integer timeout;
        private Integer maxTokens;
        private Double temperature;
    }
    
    @Data
    public static class Prompts {
        private String questionGeneration;
        private String answerAnalysis;
        private String contentGeneration;
        private String errorAnalysis;
    }
}