package com.example.aieduforge.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 设置连接超时为30秒
        factory.setConnectTimeout(30000);
        // 设置读取超时为60秒
        factory.setReadTimeout(60000);

        return new RestTemplate(factory);
    }
}
