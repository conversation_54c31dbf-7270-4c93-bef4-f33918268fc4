package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Teaching Content Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("teaching_content")
public class TeachingContent {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("outline_id")
    private Long outlineId;
    
    @TableField("content_type")
    private Integer contentType;
    
    @TableField("title")
    private String title;
    
    @TableField("content")
    private String content;
    
    @TableField("ai_generated")
    private Integer aiGenerated;
    
    @TableField("generation_prompt")
    private String generationPrompt;
    
    @TableField("time_allocation")
    private Integer timeAllocation;
    
    @TableField("difficulty_level")
    private Integer difficultyLevel;
    
    @TableField("create_user_id")
    private Long createUserId;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("chapter_name")
    private String chapterName;
}