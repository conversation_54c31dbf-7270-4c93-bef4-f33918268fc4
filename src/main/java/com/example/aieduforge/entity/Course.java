package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Course Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("course")
public class Course {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("course_name")
    private String courseName;
    
    @TableField("course_code")
    private String courseCode;
    
    @TableField("description")
    private String description;
    
    @TableField("subject")
    private String subject;
    
    @TableField("teacher_id")
    private Long teacherId;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    // Non-database field for teacher name
    @TableField(exist = false)
    private String teacherName;
    
    // Non-database fields for statistics
    @TableField(exist = false)
    private Integer learnedCount;
    
    @TableField(exist = false)
    private Integer totalContent;
    
    @TableField(exist = false)
    private Integer practiceCount;
    
    @TableField(exist = false)
    private Integer progress;
}