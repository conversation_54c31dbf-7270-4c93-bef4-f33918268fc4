package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知模板实体类
 */
@Data
@TableName("notification_template")
public class NotificationTemplate {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String name; // 模板名称
    
    private String title; // 通知标题模板
    
    private String content; // 通知内容模板
    
    private Integer type; // 通知类型
    
    private String targetRole; // 目标角色
    
    private Integer priority; // 优先级
    
    private String description; // 模板描述
    
    private Integer status; // 状态：1-启用，0-禁用
    
    private Long createUserId; // 创建用户ID
    
    private LocalDateTime createTime; // 创建时间
    
    private LocalDateTime updateTime; // 更新时间
}