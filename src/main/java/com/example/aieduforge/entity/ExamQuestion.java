package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("exam_question")
public class ExamQuestion {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long courseId;

    private Long outlineId;

    private Integer questionType;

    private String title;

    private String content;

    private String options;

    private String correctAnswer;

    private String answerAnalysis;

    private Integer difficultyLevel;

    private Double score;

    private String knowledgePoints;

    private Integer aiGenerated;

    private String generationPrompt;

    private Long createUserId;

    private Integer status;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
