package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Course Outline Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("course_outline")
public class CourseOutline {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("chapter_name")
    private String chapterName;
    
    @TableField("chapter_order")
    private Integer chapterOrder;
    
    @TableField("content")
    private String content;
    
    @TableField("learning_objectives")
    private String learningObjectives;
    
    @TableField("key_points")
    private String keyPoints;
    
    @TableField("difficulty_points")
    private String difficultyPoints;
    
    @TableField("time_allocation")
    private Integer timeAllocation;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}