package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Usage Statistics Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("usage_statistics")
public class UsageStatistics {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("user_type")
    private Integer userType;
    
    @TableField("module_name")
    private String moduleName;
    
    @TableField("action_type")
    private String actionType;
    
    @TableField("access_count")
    private Integer accessCount;
    
    @TableField("duration_seconds")
    private Integer durationSeconds;
    
    @TableField("stat_date")
    private LocalDate statDate;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}