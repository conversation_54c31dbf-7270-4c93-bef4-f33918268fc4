package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI Answer Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_answer")
public class AiAnswer {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("question_id")
    private Long questionId;
    
    @TableField("answer_content")
    private String answerContent;
    
    @TableField("confidence_score")
    private BigDecimal confidenceScore;
    
    @TableField("reference_sources")
    private String referenceSources;
    
    @TableField("ai_model")
    private String aiModel;
    
    @TableField("generation_time")
    private Integer generationTime;
    
    @TableField("feedback_score")
    private Integer feedbackScore;
    
    @TableField("feedback_comment")
    private String feedbackComment;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}