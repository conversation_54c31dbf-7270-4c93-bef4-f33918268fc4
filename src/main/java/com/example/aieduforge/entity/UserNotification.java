package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * User Notification Entity
 */
@Data
@TableName("user_notification")
public class UserNotification {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private Long notificationId;
    
    private Integer isRead;
    
    private LocalDateTime readTime;
    
    private LocalDateTime createTime;
}