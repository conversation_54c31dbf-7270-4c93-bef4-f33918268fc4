package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分析结果实体类
 */
@Data
@TableName("analysis_results")
public class AnalysisResults {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 分析类型：student_individual(个体学生分析), course_overall(课程整体分析), batch_student(批量学生分析)
     */
    private String analysisType;
    
    /**
     * 目标ID：学生ID或课程ID
     */
    private Long targetId;
    
    /**
     * 课程ID：用于区分不同课程的分析结果
     */
    private Long courseId;
    
    /**
     * 分析结果JSON数据
     */
    private String analysisResult;
    
    /**
     * 执行分析的教师ID
     */
    private Long teacherId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}