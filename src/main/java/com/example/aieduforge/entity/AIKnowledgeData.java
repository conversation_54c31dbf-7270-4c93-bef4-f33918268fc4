package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI知识库数据实体类
 */
@Data
@TableName("ai_knowledge_data")
public class AIKnowledgeData {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long knowledgeBaseId; // 关联知识库文件ID
    
    private String extractedText; // 提取的文本内容
    
    private String summary; // AI生成的摘要
    
    private String keyPoints; // 关键知识点
    
    private String smartTags; // AI生成的智能标签
    
    private String structuredData; // 结构化数据（JSON格式）
    
    private String vectorData; // 向量化数据（用于相似度搜索）
    
    private Integer wordCount; // 词数统计
    
    private Integer characterCount; // 字符数统计
    
    private String difficulty; // 难度等级
    
    private String aiModel; // 使用的AI模型
    
    private Integer processingTime; // 处理耗时（毫秒）
    
    private Integer status; // 状态：1-正常，0-失效
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}