package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI Model Configuration Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_model")
public class AiModel {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Model display name
     */
    @TableField("model_name")
    private String modelName;

    /**
     * AI provider (openrouter, ollama, openai, etc.)
     */
    @TableField("provider")
    private String provider;

    /**
     * API endpoint URL
     */
    @TableField("api_endpoint")
    private String apiEndpoint;

    /**
     * API key (encrypted)
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * Model identifier
     */
    @TableField("model_id")
    private String modelId;

    /**
     * Model description
     */
    @TableField("description")
    private String description;

    /**
     * Maximum tokens
     */
    @TableField("max_tokens")
    private Integer maxTokens;

    /**
     * Temperature setting
     */
    @TableField("temperature")
    private BigDecimal temperature;

    /**
     * Request timeout in seconds
     */
    @TableField("timeout_seconds")
    private Integer timeoutSeconds;

    /**
     * Whether model is currently active
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * Whether this is the default model
     */
    @TableField("is_default")
    private Boolean isDefault;

    /**
     * Last connection test result (0=unknown, 1=success, 2=failed)
     */
    @TableField("connection_status")
    private Integer connectionStatus;

    /**
     * Last connection test time
     */
    @TableField("last_test_time")
    private LocalDateTime lastTestTime;

    /**
     * Last error message if connection failed
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * Creator user ID
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * Create time
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * Update time
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // Connection status constants
    public static final int CONNECTION_UNKNOWN = 0;
    public static final int CONNECTION_SUCCESS = 1;
    public static final int CONNECTION_FAILED = 2;
}