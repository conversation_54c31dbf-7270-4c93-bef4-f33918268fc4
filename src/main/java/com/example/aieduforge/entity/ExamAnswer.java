package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Exam Answer Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("exam_answer")
public class ExamAnswer {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("question_id")
    private Long questionId;
    
    @TableField("student_id")
    private Long studentId;
    
    @TableField("student_answer")
    private String studentAnswer;
    
    @TableField("is_correct")
    private Integer isCorrect;
    
    @TableField("score")
    private BigDecimal score;
    
    @TableField("ai_feedback")
    private String aiFeedback;
    
    @TableField("error_analysis")
    private String errorAnalysis;
    
    @TableField("improvement_suggestion")
    private String improvementSuggestion;
    
    @TableField("answer_time")
    private LocalDateTime answerTime;
    
    @TableField("evaluation_time")
    private LocalDateTime evaluationTime;
    
    @TableField("needs_manual_review")
    private Integer needsManualReview;
    
    @TableField("practice_id")
    private Long practiceId;
    
    @TableField("ai_score")
    private BigDecimal aiScore;
    
    @TableField("teacher_score")
    private BigDecimal teacherScore;
    
    @TableField("teacher_feedback")
    private String teacherFeedback;
    
    @TableField("grading_status")
    private Integer gradingStatus;
    
    @TableField("teacher_id")
    private Long teacherId;
    
    @TableField("teacher_grading_time")
    private LocalDateTime teacherGradingTime;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    // 非数据库字段，用于查询时的额外信息
    @TableField(exist = false)
    private String questionTitle;
    
    @TableField(exist = false)
    private String questionContent;
    
    @TableField(exist = false)
    private String correctAnswer;
    
    @TableField(exist = false)
    private Integer questionType;
    
    @TableField(exist = false)
    private Integer difficultyLevel;
    
    @TableField(exist = false)
    private BigDecimal questionScore;
    
    @TableField(exist = false)
    private String courseName;
    
    @TableField(exist = false)
    private String studentName;
    
    @TableField(exist = false)
    private String studentUsername;
    
    @TableField(exist = false)
    private String answerAnalysis;
}