package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Teaching Efficiency Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("teaching_efficiency")
public class TeachingEfficiency {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("teacher_id")
    private Long teacherId;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("preparation_time")
    private Integer preparationTime;
    
    @TableField("correction_time")
    private Integer correctionTime;
    
    @TableField("design_time")
    private Integer designTime;
    
    @TableField("student_count")
    private Integer studentCount;
    
    @TableField("avg_student_score")
    private BigDecimal avgStudentScore;
    
    @TableField("pass_rate")
    private BigDecimal passRate;
    
    @TableField("efficiency_index")
    private BigDecimal efficiencyIndex;
    
    @TableField("optimization_suggestion")
    private String optimizationSuggestion;
    
    @TableField("stat_date")
    private LocalDate statDate;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}