package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Student Question Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("student_question")
public class StudentQuestion {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("student_id")
    private Long studentId;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("question_content")
    private String questionContent;
    
    @TableField("question_type")
    private Integer questionType;
    
    @TableField("context_info")
    private String contextInfo;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableField("question_source")
    private String questionSource;
}