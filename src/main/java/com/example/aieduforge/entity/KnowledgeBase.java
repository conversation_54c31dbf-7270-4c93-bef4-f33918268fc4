package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Knowledge Base Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("knowledge_base")
public class KnowledgeBase {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("title")
    private String title;
    
    @TableField("content")
    private String content;
    
    @TableField("file_path")
    private String filePath;
    
    @TableField("file_type")
    private String fileType;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("subject")
    private String subject;
    
    @TableField("tags")
    private String tags;
    
    @TableField("upload_user_id")
    private Long uploadUserId;
    
    @TableField("status")
    private Integer status;
    
    @TableField("file_size")
    private Long fileSize;
    
    @TableField("download_count")
    private Integer downloadCount;
    
    @TableField("description")
    private String description;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}