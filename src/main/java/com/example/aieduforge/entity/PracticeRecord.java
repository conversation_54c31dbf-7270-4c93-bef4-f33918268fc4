package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Practice Record Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("practice_record")
public class PracticeRecord {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("student_id")
    private Long studentId;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("practice_type")
    private Integer practiceType;
    
    @TableField("question_ids")
    private String questionIds;
    
    @TableField("total_questions")
    private Integer totalQuestions;
    
    @TableField("correct_count")
    private Integer correctCount;
    
    @TableField("score")
    private BigDecimal score;
    
    @TableField("time_spent")
    private Integer timeSpent;
    
    @TableField("completion_status")
    private Integer completionStatus;
    
    @TableField("start_time")
    private LocalDateTime startTime;
    
    @TableField("end_time")
    private LocalDateTime endTime;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    // 非数据库字段，用于前端显示
    @TableField(exist = false)
    private BigDecimal totalPossibleScore;
    
    @TableField(exist = false)
    private String courseName;
    
    @TableField(exist = false)
    private java.util.List<java.util.Map<String, Object>> detailedScores;
}