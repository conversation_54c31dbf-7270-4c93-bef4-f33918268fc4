package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统通知实体类
 */
@Data
@TableName("sys_notification")
public class SysNotification {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String title;
    
    private String content;
    
    private Integer type; // 1-系统通知，2-课程通知，3-作业通知
    
    private String targetRole; // ALL, TEACHER, STUDENT, ADMIN
    
    private Long targetUserId;
    
    private Integer priority; // 1-普通，2-重要，3-紧急
    
    private Integer status; // 1-正常，0-禁用
    
    private Integer isRead; // 1-已读，0-未读
    
    private LocalDateTime readTime;
    
    private LocalDateTime expireTime;
    
    private Long createUserId;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}