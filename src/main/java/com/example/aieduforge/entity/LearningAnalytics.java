package com.example.aieduforge.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Learning Analytics Entity
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("learning_analytics")
public class LearningAnalytics {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("student_id")
    private Long studentId;
    
    @TableField("course_id")
    private Long courseId;
    
    @TableField("knowledge_point")
    private String knowledgePoint;
    
    @TableField("total_questions")
    private Integer totalQuestions;
    
    @TableField("correct_questions")
    private Integer correctQuestions;
    
    @TableField("accuracy_rate")
    private BigDecimal accuracyRate;
    
    @TableField("avg_score")
    private BigDecimal avgScore;
    
    @TableField("total_time_spent")
    private Integer totalTimeSpent;
    
    @TableField("mastery_level")
    private Integer masteryLevel;
    
    @TableField("last_practice_time")
    private LocalDateTime lastPracticeTime;
    
    @TableField("stat_date")
    private LocalDate statDate;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}