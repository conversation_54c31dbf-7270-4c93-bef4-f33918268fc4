package com.example.aieduforge.exception;

/**
 * AI模型连接异常
 */
public class ModelConnectionException extends RuntimeException {
    
    private String modelName;
    private String endpoint;
    private Integer httpStatus;
    private String rawErrorMessage;
    
    public ModelConnectionException(String message) {
        super(message);
        this.rawErrorMessage = message;
    }
    
    public ModelConnectionException(String message, Throwable cause) {
        super(message, cause);
        this.rawErrorMessage = message;
    }
    
    public ModelConnectionException(String message, String modelName, String endpoint) {
        super(message);
        this.modelName = modelName;
        this.endpoint = endpoint;
        this.rawErrorMessage = message;
    }
    
    public ModelConnectionException(String message, String modelName, String endpoint, Integer httpStatus) {
        super(message);
        this.modelName = modelName;
        this.endpoint = endpoint;
        this.httpStatus = httpStatus;
        this.rawErrorMessage = message;
    }
    
    public String getModelName() {
        return modelName;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public Integer getHttpStatus() {
        return httpStatus;
    }
    
    public String getRawErrorMessage() {
        return rawErrorMessage;
    }
}
