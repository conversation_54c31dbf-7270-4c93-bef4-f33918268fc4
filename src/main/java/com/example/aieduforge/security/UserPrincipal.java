package com.example.aieduforge.security;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.security.Principal;

/**
 * User Principal for Security Context
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements Principal {
    
    private Long userId;
    private String username;
    private String role;
    
    @Override
    public String getName() {
        return username;
    }
}