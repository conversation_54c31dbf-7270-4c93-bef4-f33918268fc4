package com.example.aieduforge.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知WebSocket服务端点
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/notification/{userId}")
public class NotificationWebSocket {
    
    /**
     * 存储所有连接的会话，key为用户ID
     */
    private static final Map<Long, Session> sessions = new ConcurrentHashMap<>();
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") Long userId) {
        sessions.put(userId, session);
        log.info("用户{}建立WebSocket连接，当前在线人数：{}", userId, sessions.size());
        
        // 发送连接成功消息
        sendMessage(session, createMessage("connect", "连接成功", null));
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(@PathParam("userId") Long userId) {
        sessions.remove(userId);
        log.info("用户{}断开WebSocket连接，当前在线人数：{}", userId, sessions.size());
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("userId") Long userId) {
        log.info("收到用户{}的消息：{}", userId, message);
        
        try {
            // 解析客户端消息
            Map<String, Object> clientMessage = objectMapper.readValue(message, Map.class);
            String type = (String) clientMessage.get("type");
            
            if ("ping".equals(type)) {
                // 心跳检测
                sendMessage(session, createMessage("pong", "心跳响应", null));
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error, @PathParam("userId") Long userId) {
        log.error("用户{}的WebSocket连接发生错误", userId, error);
    }
    
    /**
     * 向指定用户发送通知
     */
    public static void sendNotificationToUser(Long userId, Map<String, Object> notification) {
        Session session = sessions.get(userId);
        if (session != null && session.isOpen()) {
            sendMessage(session, createMessage("notification", "新通知", notification));
        }
    }
    
    /**
     * 向所有在线用户发送通知
     */
    public static void sendNotificationToAll(Map<String, Object> notification) {
        sessions.forEach((userId, session) -> {
            if (session.isOpen()) {
                sendMessage(session, createMessage("notification", "新通知", notification));
            }
        });
    }
    
    /**
     * 向指定角色的用户发送通知
     */
    public static void sendNotificationToRole(String role, Map<String, Object> notification) {
        // 这里需要根据实际业务逻辑获取指定角色的用户列表
        // 简化处理，向所有在线用户发送
        sendNotificationToAll(notification);
    }
    
    /**
     * 发送消息到指定会话
     */
    private static void sendMessage(Session session, Map<String, Object> message) {
        try {
            String jsonMessage = objectMapper.writeValueAsString(message);
            session.getBasicRemote().sendText(jsonMessage);
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }
    
    /**
     * 创建消息对象
     */
    private static Map<String, Object> createMessage(String type, String message, Object data) {
        return Map.of(
            "type", type,
            "message", message,
            "data", data != null ? data : Map.of(),
            "timestamp", System.currentTimeMillis()
        );
    }
    
    /**
     * 获取当前在线用户数
     */
    public static int getOnlineCount() {
        return sessions.size();
    }
    
    /**
     * 检查用户是否在线
     */
    public static boolean isUserOnline(Long userId) {
        Session session = sessions.get(userId);
        return session != null && session.isOpen();
    }
}