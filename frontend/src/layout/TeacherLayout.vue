<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px" class="layout-sidebar">
        <div class="logo">
          <h3>教师工作台</h3>
        </div>
        <el-menu
          :default-active="$route.path"
          router
          background-color="#001529"
          text-color="#fff"
          active-text-color="#409eff"
        >
          <el-menu-item index="/teacher/courses">
            <el-icon><Notebook /></el-icon>
            <span>课程管理</span>
          </el-menu-item>
          <el-menu-item index="/teacher/content">
            <el-icon><EditPen /></el-icon>
            <span>内容管理</span>
          </el-menu-item>
          <el-menu-item index="/teacher/grading">
            <el-icon><DocumentChecked /></el-icon>
            <span>作业评分</span>
          </el-menu-item>
          <el-menu-item index="/teacher/ai-assistant">
            <el-icon><ChatDotRound /></el-icon>
            <span>AI助手</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主体内容 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="layout-header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/teacher' }">教师工作台</el-breadcrumb-item>
              <el-breadcrumb-item>{{ getPageTitle() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <NotificationBell />
            <el-dropdown @command="handleCommand">
              <span class="user-dropdown">
                <el-avatar 
                  :size="32" 
                  :src="userStore.userInfo?.avatar ? userStore.avatarUrl : undefined"
                  :style="{ backgroundColor: userStore.userInfo?.avatar ? 'transparent' : '#409eff' }"
                >
                  <span v-if="!userStore.userInfo?.avatar">{{ userStore.userInfo?.realName?.charAt(0) }}</span>
                </el-avatar>
                <span class="username">{{ userStore.userInfo?.realName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="dashboard">
                    <el-icon><House /></el-icon>
                    返回首页
                  </el-dropdown-item>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人信息
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="layout-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 个人信息弹窗 -->
    <UserProfile v-model="showProfile" @success="handleProfileSuccess" />
  </div>
</template>

<script setup>
import { ref } from 'vue'

import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import NotificationBell from '@/components/NotificationBell.vue'
import UserProfile from '@/components/UserProfile.vue'


const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const showProfile = ref(false)

const getPageTitle = () => {
  const titles = {
    '/teacher/courses': '课程管理',
    '/teacher/content': '内容管理',
    '/teacher/grading': '作业评分',
    '/teacher/ai-assistant': 'AI助手'
  }
  return titles[route.path] || '教师工作台'
}

const handleCommand = (command) => {
  switch (command) {
    case 'dashboard':
      router.push('/dashboard')
      break
    case 'profile':
      showProfile.value = true
      break
    case 'logout':
      ElMessageBox.confirm('确认退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userStore.logout()
        router.push('/login')
      })
      break
  }
}

const handleProfileSuccess = () => {
  showProfile.value = false
}
</script>

<style scoped>
.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #1f2937;
}

.logo h3 {
  color: #fff;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
}
</style>