import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

class NotificationWebSocket {
  constructor() {
    this.ws = null
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.heartbeatInterval = 30000
    this.listeners = new Map()
  }

  // 连接WebSocket
  connect() {
    const userStore = useUserStore()
    if (!userStore.userInfo?.userId) {
      console.warn('用户未登录，无法建立WebSocket连接')
      return
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    const wsUrl = `${protocol}//${host}/websocket/notification/${userStore.userInfo.userId}`

    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventHandlers()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.scheduleReconnect()
    }
  }

  // 设置事件处理器
  setupEventHandlers() {
    this.ws.onopen = (event) => {
      console.log('WebSocket连接已建立')
      this.reconnectAttempts = 0
      this.startHeartbeat()
      this.emit('connect', event)
    }

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason)
      this.stopHeartbeat()
      this.emit('disconnect', event)
      
      // 如果不是主动关闭，则尝试重连
      if (event.code !== 1000) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
      this.emit('error', error)
    }
  }

  // 处理接收到的消息
  handleMessage(message) {
    const { type, data } = message

    switch (type) {
      case 'connect':
        console.log('WebSocket连接成功确认')
        break
      case 'pong':
        // 心跳响应
        break
      case 'notification':
        this.handleNotification(data)
        break
      default:
        console.log('收到未知类型的消息:', message)
    }

    this.emit('message', message)
  }

  // 处理通知消息
  handleNotification(notification) {
    console.log('收到新通知:', notification)
    
    // 显示通知提示
    const priority = notification.priority || 1
    const messageType = priority === 3 ? 'error' : priority === 2 ? 'warning' : 'info'
    
    ElMessage({
      message: `新通知：${notification.title}`,
      type: messageType,
      duration: 5000,
      showClose: true
    })

    // 触发通知事件
    this.emit('notification', notification)
  }

  // 发送消息
  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  // 发送心跳
  sendHeartbeat() {
    this.send({ type: 'ping', timestamp: Date.now() })
  }

  // 开始心跳
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat()
    }, this.heartbeatInterval)
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 安排重连
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`WebSocket将在${this.reconnectInterval}ms后进行第${this.reconnectAttempts}次重连`)

    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }

  // 关闭连接
  close() {
    this.stopHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.ws) {
      this.ws.close(1000, '主动关闭')
      this.ws = null
    }
  }

  // 添加事件监听器
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  // 移除事件监听器
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 触发事件
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调执行失败:', error)
        }
      })
    }
  }

  // 获取连接状态
  getReadyState() {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED
  }

  // 检查是否已连接
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }
}

// 创建全局实例
const notificationWS = new NotificationWebSocket()

export default notificationWS