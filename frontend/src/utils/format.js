import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'

// 配置dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)
dayjs.extend(duration)

/**
 * 格式化日期时间
 * @param {string|Date} date 日期
 * @param {string} format 格式化字符串
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '-'
  return dayjs(date).format(format)
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 相对时间格式化
 * @param {string|Date} date 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '-'
  return dayjs(date).fromNow()
}

/**
 * 格式化持续时间（秒）
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的持续时间
 */
export function formatDuration(seconds) {
  if (!seconds || seconds < 0) return '0秒'
  
  const duration = dayjs.duration(seconds, 'seconds')
  const hours = Math.floor(duration.asHours())
  const minutes = duration.minutes()
  const secs = duration.seconds()
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, decimals = 0) {
  if (num === null || num === undefined) return '-'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 格式化百分比
 * @param {number} num 数字（0-1之间）
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的百分比
 */
export function formatPercentage(num, decimals = 1) {
  if (num === null || num === undefined) return '-'
  return (Number(num) * 100).toFixed(decimals) + '%'
}

/**
 * 格式化分数
 * @param {number} score 分数
 * @param {number} total 总分
 * @returns {string} 格式化后的分数
 */
export function formatScore(score, total = 100) {
  if (score === null || score === undefined) return '-'
  return `${Number(score).toFixed(1)}/${total}`
}

/**
 * 获取状态文本
 * @param {number} status 状态值
 * @param {Object} statusMap 状态映射
 * @returns {string} 状态文本
 */
export function getStatusText(status, statusMap = {}) {
  return statusMap[status] || '未知'
}

/**
 * 获取状态类型（用于el-tag的type）
 * @param {number} status 状态值
 * @param {Object} typeMap 类型映射
 * @returns {string} 状态类型
 */
export function getStatusType(status, typeMap = {}) {
  return typeMap[status] || 'info'
}

// 常用状态映射
export const USER_STATUS_MAP = {
  0: '禁用',
  1: '正常'
}

export const USER_STATUS_TYPE_MAP = {
  0: 'danger',
  1: 'success'
}

export const QUESTION_TYPE_MAP = {
  1: '选择题',
  2: '填空题',
  3: '简答题',
  4: '编程题',
  5: '综合题'
}

export const DIFFICULTY_LEVEL_MAP = {
  1: '简单',
  2: '中等',
  3: '困难'
}

export const DIFFICULTY_LEVEL_TYPE_MAP = {
  1: 'success',
  2: 'warning',
  3: 'danger'
}

export const CONTENT_TYPE_MAP = {
  1: '知识讲解',
  2: '实训练习',
  3: '指导说明'
}

export const PRACTICE_TYPE_MAP = {
  1: '随机练习',
  2: '专项练习',
  3: '错题重做'
}

export const COMPLETION_STATUS_MAP = {
  1: '进行中',
  2: '已完成',
  3: '已放弃'
}

export const COMPLETION_STATUS_TYPE_MAP = {
  1: 'warning',
  2: 'success',
  3: 'danger'
}

export const ANSWER_CORRECTNESS_MAP = {
  0: '错误',
  1: '正确',
  2: '部分正确'
}

export const ANSWER_CORRECTNESS_TYPE_MAP = {
  0: 'danger',
  1: 'success',
  2: 'warning'
}