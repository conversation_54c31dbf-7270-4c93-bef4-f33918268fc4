<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    class="error-dialog"
  >
    <ErrorDisplay
      :error-data="errorData"
      @retry="handleRetry"
      @select-model="handleSelectModel"
    />
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">{{ t('error.actions.close') }}</el-button>
        <el-button type="primary" @click="handleRetry" :loading="retrying">
          {{ t('error.actions.retryTest') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import ErrorDisplay from './ErrorDisplay.vue'

// I18n
const { t } = useI18n()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  errorData: {
    type: Object,
    default: () => ({})
  },
  modelName: {
    type: String,
    default: ''
  },
  retrying: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'retry', 'selectModel'])

// Reactive data
const visible = ref(props.modelValue)

// Computed
const dialogTitle = computed(() => {
  const modelName = props.errorData?.errorInfo?.modelName || props.modelName
  return modelName ? t('error.dialog.titleWithModel', { modelName }) : t('error.dialog.title')
})

// Watch
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// Methods
const handleRetry = () => {
  emit('retry')
}

const handleSelectModel = (modelName) => {
  emit('selectModel', modelName)
  visible.value = false
}
</script>

<style scoped>
.error-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>