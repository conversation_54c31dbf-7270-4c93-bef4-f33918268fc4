<template>
  <div class="notification-bell">
    <el-popover placement="bottom-end" :width="400" trigger="click" popper-class="notification-popover">
      <template #reference>
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
          <el-button circle size="large" class="bell-button">
            <el-icon>
              <Bell />
            </el-icon>
          </el-button>
        </el-badge>
      </template>

      <div class="notification-panel">
        <div class="notification-header">
          <span class="title">通知消息</span>
          <div class="header-actions">
            <el-button v-if="unreadCount > 0" type="text" size="small" @click="markAllAsRead">
              全部已读
            </el-button>
            <el-button type="text" size="small" @click="closeNotificationPanel" style="margin-left: 8px;">
              <el-icon>
                <Close />
              </el-icon>
            </el-button>
          </div>
        </div>

        <div class="notification-list" v-loading="loading">
          <div v-for="notification in notifications" :key="notification.id" class="notification-item"
            :class="{ 'unread': !notification.is_read }" @click="handleNotificationClick(notification)"
            :data-read-status="notification.is_read">
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-text">{{ notification.content }}</div>
              <div class="notification-time">{{ formatTime(notification.create_time) }}</div>
            </div>
            <div class="notification-status">
              <el-tag v-if="notification.priority === 3" type="danger" size="small">
                紧急
              </el-tag>
              <el-tag v-else-if="notification.priority === 2" type="warning" size="small">
                重要
              </el-tag>
            </div>
          </div>

          <div v-if="notifications.length === 0" class="empty-state">
            <el-empty description="暂无通知" :image-size="80" />
          </div>
        </div>

        <div class="notification-footer">
          <el-button type="text" @click="viewAllNotifications">查看全部</el-button>
        </div>
      </div>
    </el-popover>

    <!-- 查看全部通知的大弹窗 -->
    <el-dialog v-model="allNotificationsVisible" title="全部通知" width="80%" :close-on-click-modal="false"
      class="notification-dialog">
      <div class="all-notifications-content">
       
        <!-- 通知列表 -->
        <div class="all-notifications-list" v-loading="allNotificationsLoading">
          <div v-for="notification in allNotifications" :key="notification.id" class="notification-item-large"
            :class="{ 'unread': !notification.is_read }" @click="handleNotificationClick(notification)"
            :data-read-status="notification.is_read">
            <div class="notification-indicator">
              <div v-if="!notification.is_read" class="unread-dot"></div>
            </div>

            <div class="notification-content-large">
              <div class="notification-header-large">
                <h4 class="notification-title-large">{{ notification.title }}</h4>
                <div class="notification-meta-large">
                  <el-tag v-if="notification.priority === 3" type="danger" size="small">
                    紧急
                  </el-tag>
                  <el-tag v-else-if="notification.priority === 2" type="warning" size="small">
                    重要
                  </el-tag>
                  <el-tag v-if="notification.type === 1" size="small">
                    系统通知
                  </el-tag>
                  <el-tag v-else-if="notification.type === 2" type="success" size="small">
                    课程通知
                  </el-tag>
                  <el-tag v-else-if="notification.type === 3" type="warning" size="small">
                    作业通知
                  </el-tag>
                </div>
              </div>

              <div class="notification-body-large">
                <p class="notification-text-large">{{ notification.content }}</p>
              </div>

              <div class="notification-footer-large">
                <span class="notification-time">{{ formatTime(notification.create_time) }}</span>
                <span v-if="notification.is_read && notification.read_time" class="read-time">
                  已读于 {{ formatTime(notification.read_time) }}
                </span>
              </div>
            </div>

            <div class="notification-actions-large">
              <el-button v-if="!notification.is_read" type="text" size="small"
                @click.stop="markAsRead(notification.id, notification)">
                标记已读
              </el-button>
            </div>
          </div>

          <div v-if="allNotifications.length === 0" class="empty-state">
            <el-empty description="暂无通知" :image-size="120" />
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination" v-if="allNotificationsPagination.total > 0">
          <el-pagination v-model:current-page="allNotificationsPagination.page"
            v-model:page-size="allNotificationsPagination.size" :total="allNotificationsPagination.total"
            :page-sizes="[10, 20, 50]" layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchAllNotifications" @current-change="fetchAllNotifications" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Bell, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {
  getUserNotifications,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead
} from '@/api/notification'
import { formatTime } from '@/utils/format'
import notificationWS from '@/utils/websocket'

const router = useRouter()
const notifications = ref([])
const unreadCount = ref(0)
const loading = ref(false)

// 查看全部通知的弹窗相关
const allNotificationsVisible = ref(false)
const allNotifications = ref([])
const allNotificationsLoading = ref(false)
const allNotificationsPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const filterForm = reactive({
  type: null,
  isRead: null
})

// 获取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true
    const response = await getUserNotifications({ page: 1, size: 10 })
    if (response.code === 200) {
      // 确保 is_read 字段被正确解析为布尔值
      const processedNotifications = (response.data.notifications || []).map(notification => ({
        ...notification,
        is_read: Boolean(notification.is_read)
      }))
      notifications.value = processedNotifications
    }
  } catch (error) {
    console.error('获取通知失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取未读数量
const fetchUnreadCount = async () => {
  try {
    const response = await getUnreadNotificationCount()
    if (response.code === 200) {
      unreadCount.value = response.data || 0
    }
  } catch (error) {
    console.error('获取未读数量失败:', error)
  }
}

// 点击通知
const handleNotificationClick = async (notification) => {
  if (!notification.is_read) {
    try {
      await markNotificationAsRead(notification.id)
      notification.is_read = true  // 使用布尔值 true
      notification.read_time = new Date().toISOString()
      unreadCount.value = Math.max(0, unreadCount.value - 1)
      
      // 同时更新另一个列表中对应的通知
      if (allNotificationsVisible.value) {
        const allNotification = allNotifications.value.find(n => n.id === notification.id)
        if (allNotification) {
          allNotification.is_read = true
          allNotification.read_time = notification.read_time
        }
      }
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    const response = await markAllNotificationsAsRead()
    if (response.code === 200) {
      const currentTime = new Date().toISOString()
      // 更新小铃铛列表
      notifications.value.forEach(n => {
        n.is_read = true
        n.read_time = currentTime
      })
      // 更新大弹窗列表
      allNotifications.value.forEach(n => {
        n.is_read = true
        n.read_time = currentTime
      })
      unreadCount.value = 0
      ElMessage.success('已全部标记为已读')
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 查看全部通知
const viewAllNotifications = () => {
  allNotificationsVisible.value = true
  fetchAllNotifications()
}

// 获取全部通知
const fetchAllNotifications = async () => {
  try {
    allNotificationsLoading.value = true
    const params = {
      page: allNotificationsPagination.page,
      size: allNotificationsPagination.size,
      ...filterForm
    }
    const response = await getUserNotifications(params)
    if (response.code === 200) {
      // 确保 is_read 字段被正确解析为布尔值
      const processedNotifications = (response.data.notifications || []).map(notification => ({
        ...notification,
        is_read: Boolean(notification.is_read)
      }))
      allNotifications.value = processedNotifications
      allNotificationsPagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取全部通知失败:', error)
    ElMessage.error('获取通知列表失败')
  } finally {
    allNotificationsLoading.value = false
  }
}

// 标记单个通知为已读（改进版）
const markAsRead = async (notificationId, notification) => {
  try {
    const response = await markNotificationAsRead(notificationId)
    if (response.code === 200) {
      // 更新通知状态
      notification.is_read = true  // 使用布尔值 true 而不是数字 1
      notification.read_time = new Date().toISOString()

      // 更新未读数量
      unreadCount.value = Math.max(0, unreadCount.value - 1)

      // 同时更新小铃铛列表中对应的通知
      const bellNotification = notifications.value.find(n => n.id === notificationId)
      if (bellNotification) {
        bellNotification.is_read = true
        bellNotification.read_time = notification.read_time
      }

      ElMessage.success('已标记为已读')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 关闭通知面板
const closeNotificationPanel = () => {
  // 这个方法用于关闭popover，但Element Plus的popover需要通过其他方式关闭
  // 可以通过点击外部区域或ESC键关闭
}

// 定时刷新
let refreshTimer = null

const startRefresh = () => {
  refreshTimer = setInterval(() => {
    fetchUnreadCount()
    fetchNotifications()
  }, 30000) // 30秒刷新一次
}

const stopRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  fetchNotifications()
  fetchUnreadCount()
  startRefresh()

  // 连接WebSocket
  notificationWS.connect()

  // 监听新通知
  notificationWS.on('notification', (notification) => {
    // 刷新通知列表和未读数量
    fetchNotifications()
    fetchUnreadCount()
  })
})

onUnmounted(() => {
  stopRefresh()

  // 断开WebSocket连接
  notificationWS.close()
})
</script>

<style scoped>
.notification-bell {
  position: relative;
}

.notification-badge {
  cursor: pointer;
}

.bell-button {
  border: none;
  background: transparent;
  color: #606266;
  font-size: 18px;
}

.bell-button:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.notification-panel {
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.notification-header .title {
  font-weight: 600;
  color: #303133;
}

.notification-list {
  flex: 1;
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background-color: #409eff;
  border-radius: 50%;
}

.notification-content {
  flex: 1;
  position: relative;
  padding-left: 12px;
}

.notification-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.notification-text {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  color: #909399;
  font-size: 11px;
}

.notification-status {
  display: flex;
  align-items: flex-start;
  padding-top: 2px;
}

.notification-footer {
  padding: 12px 0;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 大弹窗样式 */
.all-notifications-content {
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.filter-bar {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.all-notifications-list {
  flex: 1;
  max-height: 50vh;
  overflow-y: auto;
  margin-bottom: 20px;
}

.notification-item-large {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.notification-item-large:hover {
  background-color: #f8f9fa;
}

.notification-item-large.unread {
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
}

.notification-indicator {
  width: 20px;
  display: flex;
  align-items: flex-start;
  padding-top: 5px;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: #409eff;
  border-radius: 50%;
}

.notification-content-large {
  flex: 1;
  margin-left: 15px;
}

.notification-header-large {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.notification-title-large {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.notification-meta-large {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 15px;
}

.notification-body-large {
  margin-bottom: 15px;
}

.notification-text-large {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.notification-footer-large {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.read-time {
  color: #67c23a;
}

.notification-actions-large {
  display: flex;
  align-items: flex-start;
  padding-top: 5px;
  margin-left: 15px;
}

.pagination {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}
</style>

<style>
.notification-popover {
  padding: 16px !important;
}

.notification-dialog .el-dialog__body {
  padding: 20px !important;
}
</style>