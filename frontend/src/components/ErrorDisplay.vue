<template>
  <div class="error-display">
    <!-- Erro<PERSON> -->
    <div class="error-header">
      <div class="error-icon-container">
        <el-icon :class="['error-icon', getErrorIconClass(errorData.errorInfo?.errorType)]" :size="32">
          <component :is="getErrorIcon(errorData.errorInfo?.errorType)" />
        </el-icon>
      </div>
      
      <div class="error-title-section">
        <h3 class="error-title">{{ errorData.errorInfo?.title || 'Connection Failed' }}</h3>
        <div class="error-meta">
          <el-tag :type="getErrorTypeTag(errorData.errorInfo?.errorType)" size="small">
            {{ errorData.errorInfo?.errorType || 'UNKNOWN_ERROR' }}
          </el-tag>
          <span class="error-code">{{ errorData.errorInfo?.errorCode }}</span>
          <span class="error-time">{{ formatTime(errorData.errorInfo?.timestamp) }}</span>
        </div>
      </div>
      
      <div class="error-actions">
        <el-button size="small" @click="copyError" icon="DocumentCopy">
          {{ t('error.actions.copyError') }}
        </el-button>
        <el-button size="small" type="primary" @click="$emit('retry')" icon="Refresh">
          {{ t('error.actions.retry') }}
        </el-button>
      </div>
    </div>

    <!-- Error Message -->
    <div class="error-message">
      <p>{{ errorData.errorInfo?.message || 'An unexpected error occurred' }}</p>
      <div v-if="errorData.errorInfo?.modelName" class="model-info">
        <strong>{{ t('error.technical.model') }}:</strong> {{ errorData.errorInfo.modelName }}
        <span v-if="errorData.errorInfo?.provider" class="provider">
          ({{ errorData.errorInfo.provider }})
        </span>
      </div>
    </div>

    <!-- Rate Limit Info (for 429 errors) -->
    <div v-if="errorData.troubleshooting?.rateLimitInfo" class="rate-limit-info">
      <el-alert
        :title="t('error.rateLimit.title')"
        type="warning"
        :closable="false"
        show-icon
      >
        <div class="rate-limit-details">
          <div class="rate-limit-item">
            <span class="label">限制:</span>
            <span class="value">{{ errorData.troubleshooting.rateLimitInfo.limit || 'N/A' }}</span>
          </div>
          <div class="rate-limit-item">
            <span class="label">剩余:</span>
            <span class="value">{{ errorData.troubleshooting.rateLimitInfo.remaining || '0' }}</span>
          </div>
          <div class="rate-limit-item">
            <span class="label">重置时间:</span>
            <span class="value">{{ formatResetTime(errorData.troubleshooting.rateLimitInfo.resetTime) }}</span>
          </div>
          <div v-if="errorData.troubleshooting.rateLimitInfo.upgradeMessage" class="upgrade-message">
            <el-icon><InfoFilled /></el-icon>
            {{ errorData.troubleshooting.rateLimitInfo.upgradeMessage }}
          </div>
        </div>
      </el-alert>
    </div>

    <!-- Expandable Sections -->
    <div class="expandable-sections">
      <!-- Troubleshooting Section -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item :title="t('error.troubleshooting.title')" name="troubleshooting">
          <div v-if="errorData.troubleshooting?.suggestions?.length" class="suggestions">
            <ul>
              <li v-for="(suggestion, index) in errorData.troubleshooting.suggestions" :key="index">
                {{ suggestion }}
              </li>
            </ul>
          </div>
          
          <div v-if="errorData.troubleshooting?.alternativeModels?.length" class="alternative-models">
            <h4>{{ t('error.troubleshooting.alternativeModels') }}:</h4>
            <div class="model-tags">
              <el-tag
                v-for="model in errorData.troubleshooting.alternativeModels"
                :key="model"
                type="info"
                size="small"
                @click="$emit('selectModel', model)"
                class="clickable-tag"
              >
                {{ model }}
              </el-tag>
            </div>
          </div>
          
          <div v-if="errorData.troubleshooting?.documentationLinks?.length" class="documentation-links">
            <h4>{{ t('error.troubleshooting.documentation') }}:</h4>
            <ul>
              <li v-for="(link, index) in errorData.troubleshooting.documentationLinks" :key="index">
                <el-link :href="link" target="_blank" type="primary">
                  {{ getLinkTitle(link) }}
                </el-link>
              </li>
            </ul>
          </div>
        </el-collapse-item>

        <!-- Technical Details Section -->
        <el-collapse-item :title="t('error.technical.title')" name="technical">
          <div class="technical-details">
            <div v-if="errorData.errorDetails?.endpoint" class="detail-item">
              <strong>{{ t('error.technical.endpoint') }}:</strong>
              <code>{{ errorData.errorDetails.endpoint }}</code>
            </div>
            
            <div v-if="errorData.errorDetails?.userId" class="detail-item">
              <strong>{{ t('error.technical.userId') }}:</strong>
              <code>{{ errorData.errorDetails.userId }}</code>
            </div>
            
            <div v-if="errorData.errorInfo?.httpStatus" class="detail-item">
              <strong>{{ t('error.technical.httpStatus') }}:</strong>
              <el-tag :type="getHttpStatusType(errorData.errorInfo.httpStatus)" size="small">
                {{ errorData.errorInfo.httpStatus }}
              </el-tag>
            </div>
            
            <div v-if="errorData.errorDetails?.metadata" class="detail-item">
              <strong>{{ t('error.technical.metadata') }}:</strong>
              <pre class="metadata-json">{{ JSON.stringify(errorData.errorDetails.metadata, null, 2) }}</pre>
            </div>
          </div>
        </el-collapse-item>

        <!-- Raw Error Section -->
        <el-collapse-item :title="t('error.technical.originalMessage')" name="raw">
          <div class="raw-error">
            <div class="json-container">
              <div class="json-header">
                <span>{{ t('error.technical.formattedJson') }}</span>
                <el-button size="small" @click="copyJson" icon="DocumentCopy">
                  {{ t('error.actions.copyJson') }}
                </el-button>
              </div>
              <pre class="json-content">{{ errorData.formattedJson || '{}' }}</pre>
            </div>
            
            <div v-if="errorData.errorDetails?.originalMessage" class="original-message">
              <h4>{{ t('error.technical.originalMessage') }}:</h4>
              <pre class="original-content">{{ errorData.errorDetails.originalMessage }}</pre>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  CircleClose,
  Warning,
  QuestionFilled,
  InfoFilled,
  DocumentCopy,
  Refresh
} from '@element-plus/icons-vue'

// I18n
const { t } = useI18n()

// Props
const props = defineProps({
  errorData: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['retry', 'selectModel'])

// Reactive data
const activeCollapse = ref(['troubleshooting'])

// Methods
const getErrorIcon = (errorType) => {
  switch (errorType) {
    case 'RATE_LIMIT_EXCEEDED':
      return Warning
    case 'AUTHENTICATION_ERROR':
    case 'AUTHORIZATION_ERROR':
      return CircleClose
    default:
      return QuestionFilled
  }
}

const getErrorIconClass = (errorType) => {
  switch (errorType) {
    case 'RATE_LIMIT_EXCEEDED':
      return 'error-warning'
    case 'AUTHENTICATION_ERROR':
    case 'AUTHORIZATION_ERROR':
      return 'error-danger'
    default:
      return 'error-unknown'
  }
}

const getErrorTypeTag = (errorType) => {
  switch (errorType) {
    case 'RATE_LIMIT_EXCEEDED':
      return 'warning'
    case 'AUTHENTICATION_ERROR':
    case 'AUTHORIZATION_ERROR':
      return 'danger'
    case 'BAD_REQUEST':
      return 'warning'
    default:
      return 'info'
  }
}

const getHttpStatusType = (status) => {
  if (status >= 200 && status < 300) return 'success'
  if (status >= 400 && status < 500) return 'warning'
  if (status >= 500) return 'danger'
  return 'info'
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

const formatResetTime = (resetTime) => {
  if (!resetTime) return 'Unknown'
  try {
    return new Date(resetTime).toLocaleString()
  } catch {
    return resetTime
  }
}

const getLinkTitle = (link) => {
  if (link.includes('limits')) return 'API限制说明'
  if (link.includes('errors')) return '错误代码参考'
  if (link.includes('authentication')) return '认证文档'
  return '文档链接'
}

const copyError = async () => {
  try {
    const errorText = `
${t('error.types.' + (props.errorData.errorInfo?.errorType || 'UNKNOWN_ERROR'))}: ${props.errorData.errorInfo?.errorType || 'UNKNOWN'}
${t('error.messages.connectionFailed')}: ${props.errorData.errorInfo?.message || 'N/A'}
${t('error.technical.model')}: ${props.errorData.errorInfo?.modelName || 'N/A'}
${t('error.technical.httpStatus')}: ${props.errorData.errorInfo?.httpStatus || 'N/A'}
${t('error.technical.timestamp')}: ${formatTime(props.errorData.errorInfo?.timestamp)}
    `.trim()
    
    await navigator.clipboard.writeText(errorText)
    ElMessage.success(t('error.success.errorCopied'))
  } catch (error) {
    ElMessage.error(t('error.errors.copyFailed'))
  }
}

const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(props.errorData.formattedJson || '{}')
    ElMessage.success(t('error.success.jsonCopied'))
  } catch (error) {
    ElMessage.error(t('error.errors.copyFailed'))
  }
}
</script>

<style scoped>
.error-display {
  border: 1px solid #f56c6c;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
}

.error-header {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background: #fef0f0;
  border-bottom: 1px solid #fbc4c4;
  gap: 12px;
}

.error-icon-container {
  flex-shrink: 0;
}

.error-icon {
  padding: 8px;
  border-radius: 50%;
  background: white;
}

.error-icon.error-danger {
  color: #f56c6c;
  background: #fef0f0;
}

.error-icon.error-warning {
  color: #e6a23c;
  background: #fdf6ec;
}

.error-icon.error-unknown {
  color: #909399;
  background: #f4f4f5;
}

.error-title-section {
  flex: 1;
}

.error-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.error-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606266;
}

.error-code {
  padding: 2px 6px;
  background: #f4f4f5;
  border-radius: 4px;
  font-family: monospace;
}

.error-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.error-message {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.error-message p {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
}

.model-info {
  font-size: 13px;
  color: #606266;
}

.provider {
  color: #909399;
}

.rate-limit-info {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.rate-limit-details {
  margin-top: 8px;
}

.rate-limit-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 13px;
}

.rate-limit-item .label {
  font-weight: 500;
  color: #606266;
}

.rate-limit-item .value {
  font-family: monospace;
  color: #303133;
}

.upgrade-message {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 8px;
  background: #fdf6ec;
  border-radius: 4px;
  font-size: 12px;
  color: #e6a23c;
}

.expandable-sections {
  border-top: 1px solid #ebeef5;
}

.suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.suggestions li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.alternative-models {
  margin-top: 16px;
}

.alternative-models h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.model-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.clickable-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.documentation-links {
  margin-top: 16px;
}

.documentation-links h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.documentation-links ul {
  margin: 0;
  padding-left: 20px;
}

.technical-details .detail-item {
  margin-bottom: 12px;
}

.technical-details .detail-item strong {
  display: inline-block;
  width: 80px;
  color: #606266;
  font-size: 13px;
}

.technical-details code {
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
}

.metadata-json {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin: 8px 0 0 0;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

.json-container {
  margin-bottom: 16px;
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.json-header span {
  font-weight: 500;
  color: #303133;
}

.json-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.original-message h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.original-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.el-collapse-item__header) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-collapse-item__content) {
  padding: 16px;
}
</style>