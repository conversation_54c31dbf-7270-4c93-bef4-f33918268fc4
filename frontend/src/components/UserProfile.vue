<template>
  <el-dialog
    v-model="visible"
    title="个人信息"
    width="600px"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" class="profile-tabs">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form
          ref="basicFormRef"
          :model="basicForm"
          :rules="basicRules"
          label-width="80px"
          class="profile-form"
        >
          <div class="avatar-section">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <el-avatar
                :size="80"
                :src="basicForm.avatar ? getAvatarUrl(basicForm.avatar) : undefined"
                class="avatar"
                :style="{ backgroundColor: basicForm.avatar ? 'transparent' : '#409eff' }"
              >
                <span v-if="!basicForm.avatar">{{ basicForm.realName?.charAt(0) }}</span>
              </el-avatar>
              <div class="avatar-overlay">
                <el-icon><Camera /></el-icon>
                <span>更换头像</span>
              </div>
            </el-upload>
          </div>

          <el-form-item label="用户名" prop="username">
            <el-input v-model="basicForm.username" disabled />
          </el-form-item>

          <el-form-item label="真实姓名" prop="realName">
            <el-input v-model="basicForm.realName" placeholder="请输入真实姓名" />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="basicForm.email" placeholder="请输入邮箱" />
          </el-form-item>

          <el-form-item label="手机号" prop="phone">
            <el-input v-model="basicForm.phone" placeholder="请输入手机号" />
          </el-form-item>

          <el-form-item label="角色">
            <el-tag :type="getRoleType(basicForm.role)">
              {{ getRoleText(basicForm.role) }}
            </el-tag>
          </el-form-item>

          <el-form-item label="注册时间">
            <span>{{ basicForm.createTime ? formatDateTime(basicForm.createTime) : '未知' }}</span>
          </el-form-item>

          <el-form-item label="最后登录">
            <span>{{ basicForm.lastLoginTime ? formatDateTime(basicForm.lastLoginTime) : '未登录' }}</span>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 修改密码 -->
      <el-tab-pane label="修改密码" name="password">
        <el-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="100px"
          class="profile-form"
        >
          <el-form-item label="当前密码" prop="oldPassword">
            <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              placeholder="请输入当前密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSave"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { updateUserProfile, changePassword, uploadAvatar } from '@/api/auth'
import { formatDate, formatDateTime } from '@/utils/format'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const userStore = useUserStore()
const visible = ref(false)
const activeTab = ref('basic')
const loading = ref(false)

// 表单引用
const basicFormRef = ref()
const passwordFormRef = ref()

// 基本信息表单
const basicForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  avatar: '',
  role: '',
  createTime: '',
  lastLoginTime: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 上传配置
const uploadUrl = '/api/auth/avatar'
const uploadHeaders = {
  'Authorization': `Bearer ${userStore.token}`
}

// 表单验证规则
const basicRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 监听显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    await initForm()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 初始化表单
const initForm = async () => {
  try {
    // 重新获取最新的用户信息
    await userStore.getUserInfo()
    const userInfo = userStore.userInfo
    
    if (userInfo) {
      Object.assign(basicForm, {
        username: userInfo.username || '',
        realName: userInfo.realName || '',
        email: userInfo.email || '',
        phone: userInfo.phone || '',
        avatar: userInfo.avatar || '',
        role: userInfo.role || '',
        createTime: userInfo.createTime || '',
        lastLoginTime: userInfo.lastLoginTime || ''
      })
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
  
  // 重置密码表单
  Object.assign(passwordForm, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  
  activeTab.value = 'basic'
}

// 获取角色类型
const getRoleType = (role) => {
  const types = {
    'ADMIN': 'danger',
    'TEACHER': 'warning',
    'STUDENT': 'success'
  }
  return types[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const texts = {
    'ADMIN': '管理员',
    'TEACHER': '教师',
    'STUDENT': '学生'
  }
  return texts[role] || '未知'
}



// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传成功
const handleAvatarSuccess = async (response) => {
  if (response.code === 200) {
    basicForm.avatar = response.data
    // 更新用户存储中的头像信息
    if (userStore.userInfo) {
      userStore.userInfo.avatar = response.data
    }
    // 更新头像时间戳以刷新显示
    userStore.updateAvatarTimestamp()
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
}

// 保存操作
const handleSave = async () => {
  if (activeTab.value === 'basic') {
    await saveBasicInfo()
  } else if (activeTab.value === 'password') {
    await savePassword()
  }
}

// 保存基本信息
const saveBasicInfo = async () => {
  try {
    await basicFormRef.value.validate()
    loading.value = true
    
    const updateData = {
      realName: basicForm.realName,
      email: basicForm.email,
      phone: basicForm.phone,
      avatar: basicForm.avatar
    }
    
    await updateUserProfile(updateData)
    
    // 更新用户存储
    await userStore.getUserInfo()
    
    ElMessage.success('个人信息更新成功')
    emit('success')
  } catch (error) {
    ElMessage.error(error.message || '更新失败')
  } finally {
    loading.value = false
  }
}

// 保存密码
const savePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    loading.value = true
    
    await changePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功')
    
    // 重置密码表单
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    nextTick(() => {
      passwordFormRef.value?.clearValidate()
    })
    
    emit('success')
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.message || '修改密码失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 获取头像URL
const getAvatarUrl = (avatar) => {
  if (!avatar) return ''
  return '/api' + avatar + '?t=' + Date.now()
}
</script>

<style scoped>
.profile-tabs {
  margin-top: 20px;
}

.profile-form {
  padding: 20px 0;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.avatar-uploader {
  position: relative;
  cursor: pointer;
}

.avatar {
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  transition: border-color 0.3s;
}

.avatar-uploader:hover .avatar {
  border-color: #409eff;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.avatar-uploader:hover .avatar-overlay {
  opacity: 1;
}

.dialog-footer {
  text-align: right;
}
</style>