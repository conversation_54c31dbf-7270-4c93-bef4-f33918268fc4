<template>
  <div class="error-demo">
    <h2>AI Model Error Message Formatting Demo</h2>
    
    <div class="demo-controls">
      <el-button @click="showRateLimitError" type="warning">
        Show Rate Limit Error (429)
      </el-button>
      <el-button @click="showAuthError" type="danger">
        Show Authentication Error (401)
      </el-button>
      <el-button @click="showNetworkError" type="info">
        Show Network Error
      </el-button>
      <el-button @click="showServiceError" type="warning">
        Show Service Error (503)
      </el-button>
    </div>

    <div v-if="currentError" class="demo-display">
      <h3>Formatted Error Display:</h3>
      <ErrorDisplay
        :error-data="currentError"
        @retry="handleRetry"
        @select-model="handleSelectModel"
      />
    </div>

    <div class="comparison" v-if="rawError">
      <h3>Original Raw Error (Before Optimization):</h3>
      <pre class="raw-error-display">{{ rawError }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ErrorDisplay from './ErrorDisplay.vue'

const currentError = ref(null)
const rawError = ref('')

const showRateLimitError = () => {
  rawError.value = `模型 "google/gemma-3n-e2b-it:free" 连接测试失败: Connection failed: 429 Too Many Requests on POST request for "https://openrouter.ai/api/v1/chat/completions": "{"error":{"message":"Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day","code":429,"metadata":{"headers":{"X-RateLimit-Limit":"50","X-RateLimit-Remaining":"0","X-RateLimit-Reset":"1753401600000"},"provider_name":null}},"user_id":"user_2yAyT42o9MqZowHa6UErygzYozq"}"`

  currentError.value = {
    errorInfo: {
      title: 'Rate Limit Exceeded',
      message: 'Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day',
      errorCode: '429',
      httpStatus: 429,
      errorType: 'RATE_LIMIT_EXCEEDED',
      timestamp: new Date().toISOString(),
      modelName: 'google/gemma-3n-e2b-it:free',
      provider: 'OpenRouter'
    },
    errorDetails: {
      originalMessage: rawError.value,
      endpoint: 'https://openrouter.ai/api/v1/chat/completions',
      userId: 'user_2yAyT42o9MqZowHa6UErygzYozq',
      metadata: {
        headers: {
          'X-RateLimit-Limit': '50',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': '1753401600000'
        },
        provider_name: null
      }
    },
    troubleshooting: {
      suggestions: [
        'Wait for the rate limit to reset',
        'Consider upgrading your plan for higher limits',
        'Implement request throttling in your application',
        'Try using a different model with higher limits'
      ],
      documentationLinks: [
        'https://openrouter.ai/docs/limits',
        'https://openrouter.ai/docs/errors',
        'https://openrouter.ai/docs/authentication'
      ],
      alternativeModels: [
        'gpt-3.5-turbo',
        'claude-3-haiku',
        'gemma-2-9b-it'
      ],
      rateLimitInfo: {
        limit: '50',
        remaining: '0',
        resetTime: new Date(1753401600000).toISOString(),
        resetTimestamp: 1753401600000,
        upgradeMessage: 'Add 10 credits to unlock 1000 free model requests per day'
      }
    },
    formattedJson: JSON.stringify({
      httpStatus: 429,
      errorMessage: 'Rate limit exceeded: free-models-per-day. Add 10 credits to unlock 1000 free model requests per day',
      endpoint: 'https://openrouter.ai/api/v1/chat/completions',
      userId: 'user_2yAyT42o9MqZowHa6UErygzYozq',
      metadata: {
        headers: {
          'X-RateLimit-Limit': '50',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': '1753401600000'
        }
      }
    }, null, 2)
  }
}

const showAuthError = () => {
  rawError.value = `Connection failed: 401 Unauthorized: {"error":{"message":"Invalid API key provided","code":401,"type":"authentication_error"}}`

  currentError.value = {
    errorInfo: {
      title: 'Authentication Failed',
      message: 'Invalid API key provided',
      errorCode: '401',
      httpStatus: 401,
      errorType: 'AUTHENTICATION_ERROR',
      timestamp: new Date().toISOString(),
      modelName: 'gpt-3.5-turbo',
      provider: 'OpenAI'
    },
    errorDetails: {
      originalMessage: rawError.value,
      endpoint: 'https://api.openai.com/v1/chat/completions'
    },
    troubleshooting: {
      suggestions: [
        'Check your API key is correct',
        'Verify your account is active',
        'Ensure proper authentication headers are set'
      ],
      documentationLinks: [
        'https://platform.openai.com/docs/api-reference/authentication'
      ]
    },
    formattedJson: JSON.stringify({
      httpStatus: 401,
      errorMessage: 'Invalid API key provided',
      endpoint: 'https://api.openai.com/v1/chat/completions'
    }, null, 2)
  }
}

const showNetworkError = () => {
  rawError.value = `Connection failed: java.net.ConnectException: Connection timeout: Unable to connect to https://api.openai.com/v1/chat/completions`

  currentError.value = {
    errorInfo: {
      title: 'Connection Failed',
      message: 'Network connection timeout',
      errorCode: 'NETWORK_ERROR',
      httpStatus: null,
      errorType: 'NETWORK_ERROR',
      timestamp: new Date().toISOString(),
      modelName: 'gpt-4',
      provider: 'OpenAI'
    },
    errorDetails: {
      originalMessage: rawError.value,
      endpoint: 'https://api.openai.com/v1/chat/completions'
    },
    troubleshooting: {
      suggestions: [
        'Check your internet connection',
        'Verify the model endpoint is correct',
        'Try again in a few moments',
        'Check if there are any firewall restrictions'
      ]
    },
    formattedJson: JSON.stringify({
      errorMessage: 'Network connection timeout',
      endpoint: 'https://api.openai.com/v1/chat/completions'
    }, null, 2)
  }
}

const showServiceError = () => {
  rawError.value = `Connection failed: 503 Service Unavailable: {"error":{"message":"Service temporarily unavailable","code":503,"type":"service_unavailable"}}`

  currentError.value = {
    errorInfo: {
      title: 'Service Unavailable',
      message: 'Service temporarily unavailable',
      errorCode: '503',
      httpStatus: 503,
      errorType: 'SERVICE_UNAVAILABLE',
      timestamp: new Date().toISOString(),
      modelName: 'claude-3-opus',
      provider: 'Anthropic'
    },
    errorDetails: {
      originalMessage: rawError.value,
      endpoint: 'https://api.anthropic.com/v1/messages'
    },
    troubleshooting: {
      suggestions: [
        'The service is temporarily unavailable',
        'Try again in a few minutes',
        'Check the provider status page',
        'Consider using an alternative model'
      ],
      alternativeModels: [
        'gpt-3.5-turbo',
        'claude-3-haiku'
      ]
    },
    formattedJson: JSON.stringify({
      httpStatus: 503,
      errorMessage: 'Service temporarily unavailable',
      endpoint: 'https://api.anthropic.com/v1/messages'
    }, null, 2)
  }
}

const handleRetry = () => {
  ElMessage.info('Retry functionality would trigger model connection test')
}

const handleSelectModel = (modelName) => {
  ElMessage.success(`Selected alternative model: ${modelName}`)
}
</script>

<style scoped>
.error-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-controls {
  margin: 20px 0;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.demo-display {
  margin: 30px 0;
}

.comparison {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.raw-error-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  color: #666;
}

h2 {
  color: #303133;
  margin-bottom: 20px;
}

h3 {
  color: #606266;
  margin: 20px 0 10px 0;
}
</style>