<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <el-icon class="error-icon"><Warning /></el-icon>
      </div>
      <div class="error-info">
        <h1>404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在或已被移动。</p>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const goHome = () => {
  const defaultRoute = userStore.getDefaultRoute()
  router.push(defaultRoute)
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.error-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
}

.error-image {
  margin-bottom: 30px;
}

.error-icon {
  font-size: 120px;
  color: #f56c6c;
}

.error-info h1 {
  font-size: 72px;
  font-weight: 700;
  color: #303133;
  margin: 0 0 10px 0;
  line-height: 1;
}

.error-info h2 {
  font-size: 24px;
  color: #606266;
  margin: 0 0 15px 0;
  font-weight: 500;
}

.error-info p {
  font-size: 16px;
  color: #909399;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}
</style>