<template>
  <div class="student-records">
    <div class="page-header">
      <h2>学习记录</h2>
      <p>查看您的练习历程和成绩统计</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon practice">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPractices }}</div>
              <div class="stat-label">练习次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon accuracy">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.averageAccuracy }}%</div>
              <div class="stat-label">平均正确率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon score">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.averageScore }}</div>
              <div class="stat-label">平均得分</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon time">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ formatDuration(stats.totalTime) }}</div>
              <div class="stat-label">累计用时</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 练习记录列表 -->
    <el-card class="records-card">
      <template #header>
        <div class="card-header">
          <span>练习记录</span>
          <div class="header-actions">
            <el-select v-model="filterCourse" placeholder="选择课程" size="small" clearable @change="loadRecords">
              <el-option
                v-for="course in courses"
                :key="course.id"
                :label="course.course_name"
                :value="course.id"
              />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="loadRecords"
            />
          </div>
        </div>
      </template>

      <el-table :data="records" v-loading="loading" stripe>
        <el-table-column prop="courseName" label="课程"  />
        
        <el-table-column prop="practiceType" label="练习类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getPracticeTypeColor(row.practice_type)">
              {{ getPracticeTypeName(row.practice_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="totalQuestions" label="题目数量" width="100">
          <template #default="{ row }">
            {{ row.total_questions }}题
          </template>
        </el-table-column>
        
        <el-table-column prop="correctCount" label="正确数量" width="100">
          <template #default="{ row }">
            {{ row.correct_count }}/{{ row.total_questions }}
          </template>
        </el-table-column>
        
        <el-table-column prop="accuracy" label="正确率" width="100">
          <template #default="{ row }">
            <span :class="getAccuracyClass(getAccuracy(row))">
              {{ getAccuracy(row) }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="score" label="得分" width="100">
          <template #default="{ row }">
            <span :class="getScoreClass(row.score)">
              {{ row.score }}分
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="timeSpent" label="用时" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.time_spent) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="completionStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.completion_status)">
              {{ getStatusText(row.completion_status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="练习时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRecords"
          @current-change="loadRecords"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="练习详情" width="900px">
      <div v-if="selectedRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程">{{ selectedRecord.courseName }}</el-descriptions-item>
          <el-descriptions-item label="练习类型">
            <el-tag :type="getPracticeTypeColor(selectedRecord.practice_type)">
              {{ getPracticeTypeName(selectedRecord.practice_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="题目数量">{{ selectedRecord.total_questions }}题</el-descriptions-item>
          <el-descriptions-item label="正确数量">{{ selectedRecord.correct_count }}题</el-descriptions-item>
          <el-descriptions-item label="正确率">{{ getAccuracy(selectedRecord) }}%</el-descriptions-item>
          <el-descriptions-item label="得分">{{ selectedRecord.score }}分</el-descriptions-item>
          <el-descriptions-item label="用时">{{ formatDuration(selectedRecord.time_spent) }}</el-descriptions-item>
          <el-descriptions-item label="练习时间">{{ formatDateTime(selectedRecord.create_time) }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="practiceDetails.length > 0" class="practice-details">
          <h4>答题详情</h4>
          <el-table :data="practiceDetails" size="small">
            <el-table-column prop="questionTitle" label="题目" min-width="200" />
            <el-table-column prop="studentAnswer" label="学生答案" min-width="150" />
            <el-table-column prop="isCorrect" label="是否正确" width="100">
              <template #default="{ row }">
                <el-tag :type="row.is_correct ? 'success' : 'danger'">
                  {{ row.is_correct ? '正确' : '错误' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="得分" width="80">
              <template #default="{ row }">
                {{ row.score }}分
              </template>
            </el-table-column>
            <el-table-column prop="aiFeedback" label="AI反馈" min-width="200" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getPracticeHistory, getPracticeStatistics, getAllCourses, getPracticeRecord } from '@/api/student'
import { formatDate } from '@/utils/format'

const loading = ref(false)
const showDetailDialog = ref(false)
const selectedRecord = ref(null)
const filterCourse = ref('')
const dateRange = ref([])

const stats = reactive({
  totalPractices: 0,
  averageAccuracy: 0,
  averageScore: 0,
  totalTime: 0
})

const records = ref([])
const courses = ref([])
const practiceDetails = ref([])

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const loadStats = async () => {
  try {
    const response = await getPracticeStatistics()
    
    if (response.data) {
      const statsData = response.data
      
      // 修正字段名映射
      stats.totalPractices = statsData.total_practices || 0
      stats.averageScore = Math.round(statsData.avg_score || 0)
      stats.totalTime = statsData.total_time || 0
      
      // 计算平均正确率
      if (statsData.total_questions && statsData.total_correct) {
        stats.averageAccuracy = Math.round((statsData.total_correct / statsData.total_questions) * 100)
      } else {
        stats.averageAccuracy = 0
      }
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
    // 如果接口失败，从记录数据中计算统计
    if (records.value.length > 0) {
      calculateStatsFromRecords()
    } else {
      stats.totalPractices = 0
      stats.averageAccuracy = 0
      stats.averageScore = 0
      stats.totalTime = 0
    }
  }
}

// 从记录数据中计算统计信息
const calculateStatsFromRecords = () => {
  if (records.value.length === 0) return
  
  let totalScore = 0
  let totalTime = 0
  let totalQuestions = 0
  let totalCorrect = 0
  
  records.value.forEach(record => {
    totalScore += Number(record.score) || 0
    totalTime += Number(record.time_spent) || 0
    totalQuestions += Number(record.total_questions) || 0
    totalCorrect += Number(record.correct_count) || 0
  })
  
  stats.totalPractices = records.value.length
  stats.averageScore = Math.round(totalScore / records.value.length)
  stats.averageAccuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0
  stats.totalTime = totalTime

}

const loadCourses = async () => {
  try {
    const response = await getAllCourses()
    courses.value = response.data || []
  } catch (error) {
    console.error('Failed to load courses:', error)
    courses.value = []
  }
}

const loadRecords = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size
    }
    
    if (filterCourse.value) {
      params.courseId = filterCourse.value
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = formatDate(dateRange.value[0], 'yyyy-MM-dd')
      params.endDate = formatDate(dateRange.value[1], 'yyyy-MM-dd')
    }
    
    const response = await getPracticeHistory(params)
    if (response.data) {
      if (Array.isArray(response.data)) {
        records.value = response.data
        pagination.total = response.data.length
      } else if (response.data.records) {
        records.value = response.data.records
        pagination.total = response.data.total || 0
      } else {
        records.value = []
        pagination.total = 0
      }
      
      // 处理记录数据
      for (const record of records.value) {
        // 如果后端已经返回了课程名称，优先使用
        if (!record.courseName && record.course_id) {
          const course = courses.value.find(c => c.id === record.course_id)
          record.courseName = course ? course.course_name : `未知课程(ID:${record.course_id})`
        }
        
        // 确保数据类型正确 - 检查所有可能的字段名
        record.total_questions = Number(record.totalQuestions || record.total_questions) || 0
        record.correct_count = Number(record.correctCount || record.correct_count) || 0
        record.score = Number(record.score) || 0
        record.time_spent = Number(record.timeSpent || record.time_spent) || 0
        record.practice_type = Number(record.practiceType || record.practice_type) || 1
        record.completion_status = Number(record.completionStatus || record.completion_status) || 1
        
        // 确保时间字段正确映射
        if (!record.createTime && record.create_time) {
          record.createTime = record.create_time
        }
        

      }
    }
  } catch (error) {
    console.error('Failed to load records:', error)
    ElMessage.error('加载记录失败')
    records.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const viewDetail = async (record) => {
  selectedRecord.value = record
  
  // 加载练习详情
  try {
    const response = await getPracticeRecord(record.id)
    if (response.data && response.data.detailedScores) {
      practiceDetails.value = response.data.detailedScores.map(detail => ({
        questionTitle: detail.questionTitle || '题目',
        studentAnswer: detail.studentAnswer || '-',
        is_correct: detail.isCorrect === 1,
        score: detail.finalScore || 0,
        aiFeedback: detail.aiFeedback || '-',
      }))
    } else {
      practiceDetails.value = []
    }
  } catch (error) {
    console.error('Failed to load practice details:', error)
    practiceDetails.value = []
  }
  
  showDetailDialog.value = true
}

const getPracticeTypeName = (type) => {
  const names = {
    1: '随机练习',
    2: '专项练习',
    3: '错题重做'
  }
  return names[type] || '练习'
}

const getPracticeTypeColor = (type) => {
  const colors = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return colors[type] || 'info'
}

const getAccuracy = (record) => {
  const totalQuestions = Number(record.total_questions) || 0
  const correctCount = Number(record.correct_count) || 0
  
  if (totalQuestions === 0) return 0
  return Math.round((correctCount / totalQuestions) * 100)
}

const getStatusText = (status) => {
  const statusMap = {
    1: '进行中',
    2: '已完成',
    3: '已取消'
  }
  return statusMap[status] || '未知'
}

const getStatusColor = (status) => {
  const colorMap = {
    1: 'warning',
    2: 'success',
    3: 'danger'
  }
  return colorMap[status] || 'info'
}

const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

const getAccuracyClass = (accuracy) => {
  if (accuracy >= 90) return 'accuracy-excellent'
  if (accuracy >= 80) return 'accuracy-good'
  if (accuracy >= 60) return 'accuracy-pass'
  return 'accuracy-fail'
}

// 获取正确率进度条颜色
const getAccuracyColor = (accuracy) => {
  if (accuracy >= 90) return '#67c23a'
  if (accuracy >= 80) return '#409eff'
  if (accuracy >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 获取课程进度颜色
const getCourseProgressColor = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#409eff', percentage: 60 },
  { color: '#67c23a', percentage: 80 },
  { color: '#11998e', percentage: 100 }
]

const formatDuration = (seconds) => {
  if (seconds === null || seconds === undefined || seconds === '') return '-'
  
  const totalSeconds = Number(seconds)
  if (isNaN(totalSeconds) || totalSeconds <= 0) return '-'
  
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const remainingSeconds = totalSeconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟${remainingSeconds}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${remainingSeconds}秒`
  } else {
    return `${remainingSeconds}秒`
  }
}

const formatDateTime = (timestamp) => {
  if (!timestamp) return '-'
  
  try {
    // 处理不同的时间格式
    let date
    if (typeof timestamp === 'string') {
      // 如果是ISO字符串格式，直接解析
      date = new Date(timestamp)
    } else if (typeof timestamp === 'number') {
      // 如果是数字时间戳
      date = new Date(timestamp)
    } else {
      return '-'
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-'
    }
    
    // 手动格式化日期，避免依赖formatDate函数
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return '-'
  }
}

onMounted(async () => {
  // 先加载课程数据，再加载记录数据，确保课程名称能正确映射
  await loadCourses()
  await loadRecords()
  // 记录加载完成后，尝试加载统计数据，如果失败则从记录计算
  await loadStats()
  // 如果统计数据为空，从记录数据计算
  if (stats.totalPractices === 0 && records.value.length > 0) {
    calculateStatsFromRecords()
  }
})
</script>

<style scoped>
.student-records {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 32px;
  padding: 16px 0;
}

/* 重写 Element Plus 卡片样式 */
.stat-card :deep(.el-card__body) {
  padding: 0 !important;
  height: 100%;
}

.stat-card {
  height: 120px;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 20px;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 251, 0.9)),
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.6);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%),
    conic-gradient(from 0deg, transparent, rgba(120, 119, 198, 0.05), transparent, rgba(255, 119, 198, 0.05), transparent);
  animation: rotate 20s linear infinite;
  opacity: 0;
  transition: opacity 0.6s ease;
  pointer-events: none;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, 0.8);
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 251, 0.95)),
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.15) 0%, transparent 50%);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover::after {
  left: 100%;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes wave {
  0%, 100% { transform: translateX(-50%) translateY(-50%) scale(1); }
  50% { transform: translateX(-50%) translateY(-50%) scale(1.1); }
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  height: 75px;
  padding: 20px;
  box-sizing: border-box;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

.stat-icon.practice { 
  background: linear-gradient(135deg, #11998e, #38ef7d);
}
.stat-icon.accuracy { 
  background: linear-gradient(135deg, #f2994a, #f2c94c);
}
.stat-icon.score { 
  background: linear-gradient(135deg, #ee0979, #ff6a00);
}
.stat-icon.time { 
  background: linear-gradient(135deg, #36d1dc, #5b86e5);
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
  min-width: 0;
  height: 100%;
}

.stat-value, .stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin: 0;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-label {
  font-size: 11px;
  color: #909399;
  font-weight: 500;
  line-height: 1;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-trend {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-trend .up {
  color: #67c23a;
  display: flex;
  align-items: center;
}

.stat-trend .down {
  color: #f56c6c;
  display: flex;
  align-items: center;
}

.stat-extra {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 4px;
}

.streak {
  font-size: 12px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-progress {
  margin-top: 8px;
}

.stat-progress-info {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stat-progress-info .separator {
  color: #dcdfe6;
  margin: 0 2px;
}

.stat-progress-info .total {
  color: #909399;
  font-size: 20px;
}

:deep(.el-progress-bar__outer) {
  background-color: rgba(0,0,0,0.04);
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
  transition: all 0.3s;
}

.chart-card {
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.chart-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fb;
}

.chart-container {
  height: 350px;
  padding: 20px;
}

.records-card {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.pagination {
  margin-top: 20px;
  padding: 15px 20px;
  text-align: right;
  background: #f8f9fb;
  border-top: 1px solid #ebeef5;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: #f8f9fb !important;
}

:deep(.el-table--striped .el-table__row--striped td) {
  background: #fafafa;
}

.score-excellent { color: #67c23a; font-weight: bold; }
.score-good { color: #409eff; font-weight: bold; }
.score-pass { color: #e6a23c; font-weight: bold; }
.score-fail { color: #f56c6c; font-weight: bold; }

.accuracy-excellent { color: #67c23a; }
.accuracy-good { color: #409eff; }
.accuracy-pass { color: #e6a23c; }
.accuracy-fail { color: #f56c6c; }

.practice-details {
  margin-top: 20px;
}

.practice-details h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.record-details, .ai-feedback {
  margin-top: 20px;
}

.details-content, .feedback-content {
  padding: 16px;
  background: #f8f9fb;
  border-radius: 8px;
  white-space: pre-wrap;
  line-height: 1.6;
}
</style>
