<template>
  <div class="student-practice">
    <div class="page-header">
      <h2>在线练习</h2>
      <p>智能练习系统，根据学习情况生成个性化题目</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：练习配置 -->
      <el-col :span="8">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>练习配置</span>
            </div>
          </template>
          
          <el-form :model="practiceForm" label-width="100px">
            <el-form-item label="选择课程">
              <el-select 
                v-model="practiceForm.courseId" 
                placeholder="请选择课程"
                @change="onCourseChange"
              >
                <el-option
                  v-for="course in courses"
                  :key="course.id"
                  :label="course.courseName"
                  :value="course.id"
                />
              </el-select>
            </el-form-item>
            

            
            <el-form-item label="题目类型">
              <el-select v-model="practiceForm.questionType" placeholder="选择题目类型">
                <el-option label="单选题" :value="1" />
                <el-option label="多选题" :value="2" />
                <el-option label="填空题" :value="3" />
                <el-option label="简答题" :value="4" />
                <el-option label="编程题" :value="5" />
          
              </el-select>
            </el-form-item>
            
            <el-form-item label="难度等级">
              <el-radio-group v-model="practiceForm.difficulty">
                <el-radio :label="1">简单</el-radio>
                <el-radio :label="2">中等</el-radio>
                <el-radio :label="3">困难</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="题目数量">
              <el-input-number 
                v-model="practiceForm.questionCount" 
                :min="1" 
                :max="20" 
                controls-position="right"
              />
            </el-form-item>
            
            <el-form-item label="练习类型">
              <el-radio-group v-model="practiceForm.practiceType">
                <el-radio :label="1">随机练习</el-radio>
                <el-radio :label="2">专项练习</el-radio>
                <el-radio :label="3">错题重做</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="练习模式">
              <el-radio-group v-model="practiceForm.mode">
                <el-radio label="practice">练习模式</el-radio>
                <el-radio label="exam">考试模式</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item>
              <el-button 
                type="primary" 
                @click="startPracticeSession"
                :loading="generating"
                :disabled="!practiceForm.courseId"
                style="width: 100%"
              >
                <el-icon><VideoPlay /></el-icon>
                {{ generating ? 'AI生成中...' : '开始练习' }}
              </el-button>
            </el-form-item>
          </el-form>
          
          <!-- 历史练习记录 -->
          <div class="practice-history">
            <h4>最近练习</h4>
            <div v-if="recentPractices.length === 0" class="empty-history">
              <el-empty description="暂无练习记录" :image-size="80" />
            </div>
            <div v-else>
              <div 
                v-for="record in recentPractices" 
                :key="record.id"
                class="history-item"
                @click="reviewPractice(record)"
              >
                <div class="history-info">
                  <h5>{{ record.courseName }}</h5>
                  <p>{{ getPracticeTypeName(record.practiceType) }} · {{ record.totalQuestions }}题</p>
                  <div class="history-meta">
                    <span class="score">得分: {{ record.score || 0 }} / {{ record.totalPossibleScore || (record.totalQuestions * 5) }} 分</span>
                    <span class="time">{{ formatDate(record.createTime) }}</span>
                  </div>
                </div>
                <div class="history-status">
                  <el-tag :type="getScoreColor(record.score, record.totalPossibleScore)">
                    {{ getScoreLevel(record.score, record.totalPossibleScore) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：练习区域 -->
      <el-col :span="16">
        <!-- 练习进行中 -->
        <el-card v-if="currentPractice" class="practice-card">
          <template #header>
            <div class="practice-header">
              <div class="practice-info">
                <span>{{ currentPractice.courseName }} - {{ currentPractice.questionType }}</span>
                <el-tag v-if="isReviewMode" type="warning">回顾模式</el-tag>
                <el-tag v-else type="info">{{ practiceForm.mode === 'exam' ? '考试模式' : '练习模式' }}</el-tag>
              </div>
              <div class="practice-progress">
                <span>{{ currentQuestionIndex + 1 }} / {{ currentPractice.questions.length }}</span>
                <div v-if="practiceForm.mode === 'exam'" class="timer">
                  <el-icon><Timer /></el-icon>
                  <span>{{ formatTime(remainingTime) }}</span>
                </div>
              </div>
            </div>
          </template>
          
          <div class="question-container">
            <div class="question-progress">
              <el-progress 
                :percentage="((currentQuestionIndex + 1) / currentPractice.questions.length) * 100"
                :stroke-width="6"
              />
            </div>
            
            
            <div v-if="currentQuestion" class="question-content">
              <div class="question-header">
                <h3>第 {{ currentQuestionIndex + 1 }} 题</h3>
                <el-tag :type="getDifficultyColor(currentQuestion.difficulty)">
                  {{ getDifficultyText(currentQuestion.difficulty) }}
                </el-tag>
              </div>
              
              <div class="question-text">{{ currentQuestion.questionText || currentQuestion.content || currentQuestion.title }}</div>
              
              
              <!-- 选择题 -->
              <div v-if="getQuestionTypeString(currentQuestion.questionType) === 'CHOICE'" class="question-options">
                <el-radio-group v-model="currentAnswer" size="large" :disabled="isReviewMode">
                  <el-radio 
                    v-for="(option, index) in getQuestionOptions(currentQuestion)" 
                    :key="index"
                    :label="option.key"
                    class="option-item"
                    :class="{
                      'correct-option': isReviewMode && option.key === currentQuestion.correctAnswer,
                      'wrong-option': isReviewMode && option.key === currentAnswer && option.key !== currentQuestion.correctAnswer
                    }"
                  >
                    {{ option.key }}. {{ option.value }}
                    <el-icon v-if="isReviewMode && option.key === currentQuestion.correctAnswer" class="correct-icon">
                      <Check />
                    </el-icon>
                    <el-icon v-if="isReviewMode && option.key === currentAnswer && option.key !== currentQuestion.correctAnswer" class="wrong-icon">
                      <Close />
                    </el-icon>
                  </el-radio>
                </el-radio-group>
              </div>
              
              <!-- 填空题 -->
              <div v-else-if="getQuestionTypeString(currentQuestion.questionType) === 'FILL'" class="question-input">
                <el-input
                  v-model="currentAnswer"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入答案..."
                  :disabled="isReviewMode"
                />
                <div v-if="isReviewMode && currentQuestion.correctAnswer" class="answer-review">
                  <div class="correct-answer">
                    <span class="label">正确答案：</span>
                    <span class="answer">{{ currentQuestion.correctAnswer }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 简答题/编程题 -->
              <div v-else class="question-input">
                <el-input
                  v-model="currentAnswer"
                  type="textarea"
                  :rows="8"
                  placeholder="请输入详细答案..."
                  :disabled="isReviewMode"
                />
                <div v-if="isReviewMode && currentQuestion.correctAnswer" class="answer-review">
                  <div class="correct-answer">
                    <span class="label">正确答案：</span>
                    <span class="answer">{{ currentQuestion.correctAnswer }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="question-actions">
              <el-button 
                @click="previousQuestion"
                :disabled="currentQuestionIndex === 0"
              >
                <el-icon><ArrowLeft /></el-icon>
                上一题
              </el-button>
              
              <el-button 
                v-if="currentQuestionIndex < currentPractice.questions.length - 1"
                type="primary"
                @click="nextQuestion"
              >
                下一题
                <el-icon><ArrowRight /></el-icon>
              </el-button>
              
              <!-- 回顾模式下的按钮 -->
              <el-button 
                v-if="isReviewMode"
                type="info"
                @click="exitReviewMode"
              >
                <el-icon><Back /></el-icon>
                退出回顾
              </el-button>
              
              <!-- 练习模式下的提交按钮 -->
              <el-button 
                v-else-if="currentQuestionIndex === currentPractice.questions.length - 1"
                type="success"
                @click="submitPractice"
                :loading="submitting"
              >
                <el-icon><Check /></el-icon>
                {{ submitting ? '提交中...' : '提交答案' }}
              </el-button>
            </div>
          </div>
        </el-card>
        
        <!-- 练习结果 -->
        <el-card v-else-if="practiceResult" class="result-card scrollable-result">
          <template #header>
            <div class="result-header">
              <span>练习结果</span>
              <div class="result-actions">
                <el-button 
                  v-if="practiceResult.reviewData" 
                  type="primary" 
                  @click="startQuestionReview"
                >
                  <el-icon><DocumentChecked /></el-icon>
                  回顾题目
                </el-button>
                <el-button @click="resetPractice">返回练习</el-button>
              </div>
            </div>
          </template>
          
          <div class="result-content scrollable-content">
            <div class="result-summary">
              <div class="score-display">
                <div class="score-circle">
                  <el-progress 
                    type="circle" 
                    :percentage="practiceResult.score"
                    :width="120"
                    :stroke-width="8"
                    :color="getScoreProgressColor(practiceResult.score)"
                  >
                    <template #default="{ percentage }">
                      <span class="score-text">{{ percentage }}%</span>
                    </template>
                  </el-progress>
                </div>
                <div class="score-info">
                  <h3>{{ getScoreLevel(practiceResult.score) }}</h3>
                  <p>得分: {{ practiceResult.actualScore }} / {{ practiceResult.totalPossibleScore }} 分</p>
                  <p>正确率: {{ practiceResult.correctCount }} / {{ practiceResult.totalCount }}</p>
                  <p>用时: {{ formatDuration(practiceResult.duration) }}</p>
                </div>
              </div>
              
              <div class="result-stats">
                <div class="stat-item">
                  <span class="stat-label">总题数</span>
                  <span class="stat-value">{{ practiceResult.totalCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">正确数</span>
                  <span class="stat-value correct">{{ practiceResult.correctCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">错误数</span>
                  <span class="stat-value error">{{ practiceResult.errorCount }}</span>
                </div>
              </div>
            </div>
            
            <!-- 评分状态提示 -->
            <div v-if="practiceResult.pendingGrading" class="grading-status">
              <el-alert
                title="评分状态"
                :description="practiceResult.statusMessage"
                type="warning"
                :closable="false"
                show-icon
              />
            </div>

            <!-- 详细评分信息 -->
            <div v-if="practiceResult.detailedScores" class="detailed-scores">
              <h4>
                <el-icon><DataAnalysis /></el-icon>
                详细评分
              </h4>
              <div class="score-breakdown">
                <div 
                  v-for="(item, index) in practiceResult.detailedScores" 
                  :key="index"
                  class="score-item"
                  :class="{ 'pending-review': item.needsManualReview === 1 && item.gradingStatus !== 2 }"
                >
                  <div class="question-info">
                    <span class="question-title">{{ item.questionTitle || `第${index + 1}题` }}</span>
                    <el-tag :type="getQuestionTypeColor(item.questionType)" size="small">
                      {{ getQuestionTypeName(item.questionType) }}
                    </el-tag>
                    <el-tag 
                      v-if="item.needsManualReview === 1 && item.gradingStatus !== 2" 
                      type="warning" 
                      size="small"
                    >
                      等待教师评分
                    </el-tag>
                    <el-tag 
                      v-else-if="item.gradingStatus === 2" 
                      type="success" 
                      size="small"
                    >
                      已评分
                    </el-tag>
                  </div>
                  
                  <div class="score-details">
                    <!-- AI评分信息 -->
                    <div class="score-section ai-section">
                      <div class="section-header">
                        <el-icon><Monitor /></el-icon>
                        <span>AI智能评分</span>
                      </div>
                      <div class="score-row">
                        <span class="label">AI评分：</span>
                        <span class="value ai-score">{{ item.aiScore || 0 }}分</span>
                        <span class="total">/ {{ item.totalScore || 0 }}分</span>
                      </div>
                      <div v-if="item.aiFeedback" class="feedback-row">
                        <span class="label">AI分析：</span>
                        <div class="feedback ai-feedback">{{ item.aiFeedback }}</div>
                      </div>
                      <div v-if="item.improvementSuggestion" class="feedback-row">
                        <span class="label">学习建议：</span>
                        <div class="feedback ai-suggestion">{{ item.improvementSuggestion }}</div>
                      </div>
                    </div>

                    <!-- 教师评分信息 -->
                    <div v-if="item.gradingStatus === 2" class="score-section teacher-section">
                      <div class="section-header">
                        <el-icon><User /></el-icon>
                        <span>教师评分</span>
                      </div>
                      <div class="score-row">
                        <span class="label">教师评分：</span>
                        <span class="value teacher-score">{{ item.teacherScore }}分</span>
                        <span class="total">/ {{ item.totalScore || 0 }}分</span>
                      </div>
                      <div v-if="item.teacherFeedback" class="feedback-row">
                        <span class="label">教师评语：</span>
                        <div class="feedback teacher-feedback">{{ item.teacherFeedback }}</div>
                      </div>
                    </div>
                    
                    <!-- 等待评分提示 -->
                    <div v-else-if="item.needsManualReview === 1" class="score-section pending-section">
                      <div class="section-header">
                        <el-icon><Clock /></el-icon>
                        <span>等待教师评分</span>
                      </div>
                      <div class="pending-message">
                        <el-alert
                          title="主观题正在评分中"
                          description="教师将对您的答案进行详细评分，请耐心等待"
                          type="warning"
                          :closable="false"
                          show-icon
                        />
                      </div>
                    </div>

                    <!-- 最终得分 -->
                    <div class="score-row final-score">
                      <span class="label">最终得分：</span>
                      <span class="value final">{{ item.finalScore || item.aiScore || 0 }}分</span>
                      <span class="total">/ {{ item.totalScore || 0 }}分</span>
                      <span v-if="item.gradingStatus !== 2 && item.needsManualReview === 1" class="note">
                        (当前为AI参考分，最终分数以教师评分为准)
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI分析建议 -->
            <div v-if="practiceResult.aiAnalysis" class="ai-analysis">
              <h4>
                <el-icon><ChatDotRound /></el-icon>
                AI学习建议
              </h4>
              <div class="analysis-content">{{ practiceResult.aiAnalysis }}</div>
            </div>
            
            <!-- 错题回顾 -->
            <div v-if="practiceResult.wrongQuestions?.length > 0" class="wrong-questions">
              <h4>错题回顾</h4>
              <div 
                v-for="(question, index) in practiceResult.wrongQuestions" 
                :key="index"
                class="wrong-question-item"
              >
                <div class="question-info">
                  <h5>{{ question.questionText }}</h5>
                  <div class="answer-comparison">
                    <div class="your-answer">
                      <span class="label">您的答案:</span>
                      <span class="answer error">{{ question.studentAnswer }}</span>
                    </div>
                    <div class="correct-answer">
                      <span class="label">正确答案:</span>
                      <span class="answer correct">{{ question.correctAnswer }}</span>
                    </div>
                  </div>
                  <div v-if="question.explanation" class="explanation">
                    <span class="label">解析:</span>
                    <span class="content">{{ question.explanation }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 默认状态 -->
        <el-card v-else class="welcome-card">
          <div class="welcome-content">
            <div class="welcome-icon">
              <el-icon><DocumentChecked /></el-icon>
            </div>
            <h3>开始您的智能练习</h3>
            <p>请在左侧配置练习参数，AI将为您生成个性化的练习题目</p>
            <div class="features">
              <div class="feature-item">
                <el-icon><Star /></el-icon>
                <span>AI智能出题</span>
              </div>
              <div class="feature-item">
                <el-icon><DataAnalysis /></el-icon>
                <span>智能分析</span>
              </div>
              <div class="feature-item">
                <el-icon><TrendCharts /></el-icon>
                <span>学习跟踪</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Check, Star, DataAnalysis, VideoPlay, Timer, ArrowLeft, ArrowRight, 
  Back, Close, DocumentChecked, TrendCharts, ChatDotRound, User, Clock, 
  Monitor, Avatar
} from '@element-plus/icons-vue'
import { getStudentCourses, generatePracticeQuestions, startPractice, submitAnswer, completePractice, getPracticeHistory, getPracticeRecord } from '@/api/student'
import { formatDate } from '@/utils/format'

const generating = ref(false)
const submitting = ref(false)
const currentPractice = ref(null)
const currentQuestionIndex = ref(0)
const currentAnswer = ref('')
const practiceResult = ref(null)
const remainingTime = ref(0)
const timer = ref(null)
const isReviewMode = ref(false)

const courses = ref([])
const recentPractices = ref([])
const answers = ref({})

const practiceForm = reactive({
  courseId: '',
  questionType: 1, // 默认为单选题
  difficulty: 1,
  questionCount: 5,
  practiceType: 1, // 1-随机练习，2-专项练习，3-错题重做
  mode: 'practice'
})

const currentQuestion = computed(() => {
  if (!currentPractice.value || !currentPractice.value.questions) {
    return null
  }
  
  return currentPractice.value.questions[currentQuestionIndex.value]
})

const loadCourses = async () => {
  try {
    const response = await getStudentCourses()
    // 处理响应数据，可能是数组或包含records的对象
    if (Array.isArray(response.data)) {
      courses.value = response.data
    } else if (response.data.records) {
      courses.value = response.data.records
    } else {
      courses.value = response.data || []
    }
  } catch (error) {
    console.error('Failed to load courses:', error)
    ElMessage.error('加载课程失败')
  }
}



// 处理课程选择变化
const onCourseChange = (courseId) => {
  // 重置相关表单数据
  practiceForm.questionType = 1 // 重置为默认的单选题
  practiceForm.difficulty = 1 // 重置为简单难度
  practiceForm.questionCount = 5 // 重置为默认数量
  practiceForm.practiceType = 1 // 重置为随机练习
  practiceForm.mode = 'practice' // 重置为练习模式
  
  // 如果选择了课程，显示提示
  if (courseId) {
    ElMessage.success('已切换课程，表单已重置')
  }
}

const loadPracticeHistory = async () => {
  try {
    const response = await getPracticeHistory({ current: 1, size: 5 })
    if (Array.isArray(response.data)) {
      recentPractices.value = response.data
    } else if (response.data.records) {
      recentPractices.value = response.data.records
    } else {
      recentPractices.value = response.data || []
    }
  } catch (error) {
    console.error('Failed to load practice history:', error)
  }
}

// 清理当前状态的函数
const cleanupCurrentState = () => {
  // 清理练习状态
  currentPractice.value = null
  practiceResult.value = null
  currentQuestionIndex.value = 0
  currentAnswer.value = ''
  answers.value = {}
  
  // 清理回顾模式
  isReviewMode.value = false
  
  // 清理计时器
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  
  remainingTime.value = 0
}

const startPracticeSession = async () => {
  // 检查是否有正在进行的练习（已经创建了数据库记录）
  if (currentPractice.value) {
    ElMessageBox.alert(
      '您当前有正在进行的练习，请先完成当前练习或提交答案后再开始新练习。\n\n如需放弃当前练习，请刷新页面。',
      '无法开始新练习',
      {
        confirmButtonText: '我知道了',
        type: 'warning'
      }
    )
    return
  }
  
  // 检查是否在回顾模式或查看结果
  if (practiceResult.value || isReviewMode.value) {
    let message = '您当前正在查看练习结果，确认要开始新练习吗？'
    
    if (isReviewMode.value) {
      message = '您当前正在回顾练习，开始新练习将退出回顾模式。确认要开始新练习吗？'
    }
    
    const result = await ElMessageBox.confirm(
      message,
      '确认开始新练习',
      {
        confirmButtonText: '确认开始',
        cancelButtonText: '取消',
        type: 'info'
      }
    ).catch(() => false)
    
    if (!result) {
      return
    }
    
    // 只清理查看状态，不清理练习状态
    practiceResult.value = null
    isReviewMode.value = false
  }
  
  // 表单验证
  if (!practiceForm.courseId) {
    ElMessage.warning('请选择课程')
    return
  }
  
  if (!practiceForm.questionType) {
    ElMessage.warning('请选择题目类型')
    return
  }
  
  if (!practiceForm.difficulty) {
    ElMessage.warning('请选择难度等级')
    return
  }
  
  if (!practiceForm.questionCount || practiceForm.questionCount < 1) {
    ElMessage.warning('请设置题目数量（至少1题）')
    return
  }
  
  if (!practiceForm.practiceType) {
    ElMessage.warning('请选择练习类型')
    return
  }
  
  // 防止重复点击
  if (generating.value) {
    return
  }
  
  generating.value = true
  try {
    // 构建符合后端PracticeRequest的参数
    const requestData = {
      courseId: practiceForm.courseId,
      practiceType: practiceForm.practiceType,
      questionCount: practiceForm.questionCount,
      difficultyLevel: practiceForm.difficulty,
      questionType: practiceForm.questionType // 直接使用数字类型
    }
    
    console.log('发送练习请求:', requestData)
    
    // 第一步：生成题目
    const questionsResponse = await generatePracticeQuestions(requestData)
    const questions = questionsResponse.data || []
    
    // 第二步：创建练习记录，传递题目ID确保一致性
    const startPracticeData = {
      ...requestData,
      questionIds: questions.map(q => q.id) // 传递生成的题目ID
    }
    const practiceResponse = await startPractice(startPracticeData)
    const practiceRecord = practiceResponse.data
    
    // 构建练习对象，使用真实的practiceId
    const practiceData = {
      id: practiceRecord.id, // 使用后端返回的真实ID
      courseName: courses.value.find(c => c.id === practiceForm.courseId)?.courseName || '未知课程',
      questionType: practiceForm.questionType,
      questions: questions,
      timeLimit: practiceForm.mode === 'exam' ? 30 : null // 考试模式30分钟
    }
    
    console.log('创建的练习记录:', practiceRecord)
    console.log('练习数据:', practiceData)
    
    // 强制触发响应式更新
    currentPractice.value = practiceData
    await nextTick()
    
    currentQuestionIndex.value = 0
    currentAnswer.value = ''
    answers.value = {}
    practiceResult.value = null
    
    // 如果是考试模式，启动计时器
    if (practiceForm.mode === 'exam') {
      remainingTime.value = currentPractice.value.timeLimit * 60 // 转换为秒
      startTimer()
    }
    
    ElMessage.success('练习题目生成成功，开始答题吧！')
  } catch (error) {
    console.error('Failed to generate practice questions:', error)
    ElMessage.error('生成练习失败，请重试')
  } finally {
    generating.value = false
  }
}

const startTimer = () => {
  timer.value = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      clearInterval(timer.value)
      ElMessage.warning('时间到！自动提交答案')
      submitPractice()
    }
  }, 1000)
}

const nextQuestion = () => {
  saveCurrentAnswer()
  if (currentQuestionIndex.value < currentPractice.value.questions.length - 1) {
    currentQuestionIndex.value++
    loadCurrentAnswer()
  }
}

const previousQuestion = () => {
  saveCurrentAnswer()
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    loadCurrentAnswer()
  }
}

const saveCurrentAnswer = () => {
  if (currentQuestion.value) {
    answers.value[currentQuestion.value.id] = currentAnswer.value
  }
}

const loadCurrentAnswer = () => {
  if (currentQuestion.value) {
    currentAnswer.value = answers.value[currentQuestion.value.id] || ''
  }
}

// 检查未作答的题目
const checkUnansweredQuestions = () => {
  const unanswered = []
  if (currentPractice.value && currentPractice.value.questions) {
    currentPractice.value.questions.forEach((question, index) => {
      const answer = answers.value[question.id]
      if (!answer || answer.trim() === '') {
        unanswered.push(index + 1) // 题目编号从1开始
      }
    })
  }
  return unanswered
}

const submitPractice = async () => {
  saveCurrentAnswer()
  
  // 检查是否有未作答的题目
  const unansweredQuestions = checkUnansweredQuestions()
  if (unansweredQuestions.length > 0) {
    const questionNumbers = unansweredQuestions.map(q => `第${q}题`).join('、')
    ElMessageBox.confirm(
      `您还有 ${unansweredQuestions.length} 道题未作答：${questionNumbers}。确认要提交吗？`,
      '提交确认',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '继续答题',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    ).then(() => {
      doSubmit()
    }).catch(() => {
      // 用户选择继续答题，跳转到第一个未作答的题目
      currentQuestionIndex.value = unansweredQuestions[0] - 1
      loadCurrentAnswer()
    })
    return
  }
  
  if (practiceForm.mode === 'exam') {
    // 考试模式直接提交
    await doSubmit()
  } else {
    // 练习模式确认提交
    ElMessageBox.confirm('确认提交答案吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      doSubmit()
    })
  }
}

const doSubmit = async () => {
  submitting.value = true
  try {
    // 逐个提交每个答案
    const questions = currentPractice.value.questions
    const submitPromises = []
    
    for (const question of questions) {
      const studentAnswer = answers.value[question.id]
      if (studentAnswer && studentAnswer.trim()) {
        const answerData = {
          practiceId: currentPractice.value.id,
          questionId: question.id,
          studentAnswer: studentAnswer.trim(),
          questionType: question.questionType // 关键：传递题目类型用于后端判断评分方式
        }
        submitPromises.push(submitAnswer(answerData))
      }
    }
    
    // 等待所有答案提交完成
    await Promise.all(submitPromises)
    
    // 完成练习
    const response = await completePractice(currentPractice.value.id)
    
    // 等待后端处理完成后获取实际结果
    const practiceRecord = response.data
    
    // 构建练习结果
    const totalCount = questions.length
    const answeredCount = Object.keys(answers.value).filter(key => answers.value[key]?.trim()).length
    
    // 分析题目类型
    const objectiveQuestions = questions.filter(q => [1, 2, 3].includes(q.questionType)) // 选择题、填空题
    const subjectiveQuestions = questions.filter(q => [4, 5, 6].includes(q.questionType)) // 简答题、编程题、案例分析题
    
    let displayScore = practiceRecord.score || 0
    let correctCount = practiceRecord.correctCount || 0
    let scoreStatus = 'final'
    let statusMessage = '最终成绩'
    
    // 检查是否有主观题待评分
    const detailedScores = practiceRecord.detailedScores || []
    const hasPendingGrading = detailedScores.some(item => 
      item.needsManualReview === 1 && item.gradingStatus !== 2
    )
    
    if (hasPendingGrading) {
      scoreStatus = 'pending'
      statusMessage = '主观题正在评分中，最终成绩将在教师评分完成后更新'
    }
    
    // 计算用时
    let duration = 0
    if (practiceForm.mode === 'exam' && currentPractice.value.timeLimit) {
      duration = currentPractice.value.timeLimit * 60 - remainingTime.value
    } else if (practiceRecord.timeSpent) {
      duration = practiceRecord.timeSpent
    }
    
    // 计算总可能得分
    const totalPossibleScore = currentPractice.value.questions.reduce((sum, q) => sum + (q.score || 0), 0)
    
    // 计算百分比分数
    const scorePercentage = totalPossibleScore > 0 ? Math.round((displayScore / totalPossibleScore) * 100) : 0
    
    practiceResult.value = {
      score: scorePercentage, // 使用百分比而不是实际分数
      actualScore: Math.round(displayScore), // 保留实际分数用于显示
      totalPossibleScore: totalPossibleScore, // 总可能得分
      totalCount: totalCount,
      correctCount: correctCount,
      errorCount: totalCount - correctCount,
      answeredCount: answeredCount,
      duration: duration,
      hasSubjectiveQuestions: subjectiveQuestions.length > 0,
      pendingGrading: scoreStatus === 'pending',
      scoreStatus: scoreStatus,
      statusMessage: statusMessage,
      detailedScores: detailedScores, // 添加详细评分信息
      aiAnalysis: generateAIAnalysis(correctCount, totalCount, answeredCount, subjectiveQuestions.length > 0, detailedScores)
    }
    
    currentPractice.value = null
    
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
    
    if (subjectiveQuestions.length > 0 && scoreStatus === 'pending') {
      ElMessage.success('答案提交成功！主观题正在等待教师评分，最终成绩将在评分完成后更新。')
    } else {
      ElMessage.success('答案提交成功！')
    }
    
    loadPracticeHistory()
  } catch (error) {
    console.error('Failed to submit practice:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生成AI分析建议
const generateAIAnalysis = (correctCount, totalCount, answeredCount, hasSubjectiveQuestions = false, detailedScores = []) => {
  const accuracy = totalCount > 0 ? (correctCount / totalCount * 100).toFixed(1) : 0
  const completionRate = totalCount > 0 ? (answeredCount / totalCount * 100).toFixed(1) : 0
  
  let analysis = `📊 本次练习完成率：${completionRate}%`
  
  if (hasSubjectiveQuestions) {
    const objectiveCount = detailedScores.filter(item => [1, 2, 3].includes(item.questionType)).length
    const subjectiveCount = detailedScores.filter(item => [4, 5, 6].includes(item.questionType)).length
    
    if (objectiveCount > 0) {
      const objectiveCorrect = detailedScores.filter(item => 
        [1, 2, 3].includes(item.questionType) && item.isCorrect === 1
      ).length
      const objectiveAccuracy = objectiveCount > 0 ? (objectiveCorrect / objectiveCount * 100).toFixed(1) : 0
      analysis += `，客观题正确率：${objectiveAccuracy}%`
    }
    
    analysis += `\n\n📝 题目构成：客观题${objectiveCount}道，主观题${subjectiveCount}道。主观题最终成绩将在教师评分后更新。`
  } else {
    analysis += `，正确率：${accuracy}%。`
  }
  
  // 根据正确率给出具体建议
  if (accuracy >= 95) {
    analysis += '\n\n🎉 表现卓越！您对知识点的掌握非常扎实，可以尝试挑战更高难度的题目。'
  } else if (accuracy >= 85) {
    analysis += '\n\n✨ 表现优秀！基础知识掌握良好，建议继续保持学习节奏，适当增加练习难度。'
  } else if (accuracy >= 70) {
    analysis += '\n\n👍 表现良好！大部分知识点已掌握，建议重点复习错题涉及的知识点，查漏补缺。'
  } else if (accuracy >= 60) {
    analysis += '\n\n📚 基础掌握一般，建议系统复习相关章节，多做基础练习题巩固知识点。'
  } else if (accuracy >= 40) {
    analysis += '\n\n💪 需要加强学习，建议重新学习相关章节的基础概念，可以寻求老师或同学的帮助。'
  } else {
    analysis += '\n\n🔄 建议从基础开始系统学习，多花时间理解核心概念，不要急于做题。'
  }
  
  // 分析题目类型表现
  if (detailedScores.length > 0) {
    const typePerformance = analyzeQuestionTypePerformance(detailedScores)
    if (typePerformance) {
      analysis += '\n\n' + typePerformance
    }
  }
  
  // 完成度建议
  if (completionRate < 100) {
    analysis += `\n\n⚠️ 有${totalCount - answeredCount}道题未完成，建议合理安排时间，确保完成所有题目。`
  } else if (completionRate === 100 && accuracy >= 80) {
    analysis += '\n\n🎯 完成度和正确率都很好，继续保持这种认真的学习态度！'
  }
  
  return analysis;
}

// 分析不同题目类型的表现
const analyzeQuestionTypePerformance = (detailedScores) => {
  const typeStats = {}
  
  detailedScores.forEach(item => {
    const typeName = getQuestionTypeName(item.questionType)
    if (!typeStats[typeName]) {
      typeStats[typeName] = { total: 0, correct: 0 }
    }
    typeStats[typeName].total++
    if (item.isCorrect === 1) {
      typeStats[typeName].correct++
    }
  })
  
  let analysis = '📈 各题型表现分析：'
  const suggestions = []
  
  Object.entries(typeStats).forEach(([typeName, stats]) => {
    const accuracy = stats.total > 0 ? (stats.correct / stats.total * 100).toFixed(1) : 0
    analysis += `\n• ${typeName}：${stats.correct}/${stats.total} (${accuracy}%)`
    
    // 根据题型表现给出建议
    if (accuracy < 60) {
      switch (typeName) {
        case '单选题':
          suggestions.push('单选题需要加强基础概念理解，注意排除干扰选项')
          break
        case '多选题':
          suggestions.push('多选题要仔细分析每个选项，避免遗漏或多选')
          break
        case '填空题':
          suggestions.push('填空题需要准确记忆关键术语和概念')
          break
        case '简答题':
          suggestions.push('简答题要注意答案的完整性和逻辑性')
          break
        case '编程题':
          suggestions.push('编程题需要多练习，注意代码规范和逻辑正确性')
          break
      }
    }
  })
  
  if (suggestions.length > 0) {
    analysis += '\n\n💡 改进建议：\n• ' + suggestions.join('\n• ')
  }
  
  return analysis
}

const resetPractice = () => {
  // 使用统一的清理函数
  cleanupCurrentState()
  // 重新加载练习历史
  loadPracticeHistory()
}

const reviewPractice = async (record) => {
  try {
    // 获取练习详情
    const response = await getPracticeRecord(record.id)
    const practiceDetail = response?.data
    
    // 检查返回的数据是否有效
    if (!practiceDetail) {
      console.warn('Practice detail is null, using record data as fallback')
      // 直接显示练习结果，使用记录数据
      showPracticeResult(record)
      return
    }
    
    // 构建完整的练习结果数据
    const totalCount = practiceDetail.record?.totalQuestions || record.questionCount || 0
    const correctCount = practiceDetail.record?.correctCount || record.correctCount || 0
    const actualScore = practiceDetail.record?.score || record.score || 0
    const totalPossibleScore = record.totalPossibleScore || (totalCount * 5) // 默认每题5分
    const scorePercentage = totalPossibleScore > 0 ? Math.round((actualScore / totalPossibleScore) * 100) : 0
    
    // 检查是否有详细评分信息 - 修复：从 record 中获取 detailedScores
    const detailedScores = practiceDetail.record?.detailedScores || []
    const hasPendingGrading = detailedScores.some(item => 
      item.needsManualReview === 1 && item.gradingStatus !== 2
    )
    
    console.log('Practice detail:', practiceDetail)
    console.log('Detailed scores:', detailedScores)
    
    // 构建练习结果
    practiceResult.value = {
      score: scorePercentage,
      actualScore: Math.round(actualScore),
      totalPossibleScore: totalPossibleScore,
      totalCount: totalCount,
      correctCount: correctCount,
      errorCount: totalCount - correctCount,
      answeredCount: totalCount, // 历史记录默认都已完成
      duration: practiceDetail.record?.timeSpent || record.timeSpent || 0,
      hasSubjectiveQuestions: detailedScores.some(item => [4, 5, 6].includes(item.questionType)),
      pendingGrading: hasPendingGrading,
      scoreStatus: hasPendingGrading ? 'pending' : 'final',
      statusMessage: hasPendingGrading ? '主观题正在评分中，最终成绩将在教师评分完成后更新' : '最终成绩',
      detailedScores: detailedScores,
      aiAnalysis: generateAIAnalysis(correctCount, totalCount, totalCount, detailedScores.some(item => [4, 5, 6].includes(item.questionType))),
      wrongQuestions: practiceDetail.wrongQuestions || [],
      isReview: true // 标记为回顾模式
    }
    
    // 如果有题目数据，也设置题目回顾功能
    if (practiceDetail.questions && practiceDetail.questions.length > 0) {
      const reviewData = {
        id: record.id,
        courseName: record.courseName || '未知课程',
        questionType: record.questionType,
        questions: practiceDetail.questions,
        answers: practiceDetail.answers || {},
        timeLimit: null
      }
      
      // 保存回顾数据供后续使用
      practiceResult.value.reviewData = reviewData
    }
    
    // 清空当前练习状态，直接显示结果
    currentPractice.value = null
    isReviewMode.value = false
    currentQuestionIndex.value = 0
    currentAnswer.value = ''
    answers.value = {}
    
    ElMessage.success('练习结果加载成功')
  } catch (error) {
    console.error('Failed to load practice review:', error)
    // 出错时使用基础数据显示结果
    showPracticeResult(record)
  }
}

// 显示基础练习结果的辅助函数
const showPracticeResult = (record) => {
  const totalCount = record.questionCount || 0
  const correctCount = record.correctCount || 0
  const actualScore = record.score || 0
  const totalPossibleScore = record.totalPossibleScore || (totalCount * 5)
  const scorePercentage = totalPossibleScore > 0 ? Math.round((actualScore / totalPossibleScore) * 100) : 0
  
  practiceResult.value = {
    score: scorePercentage,
    actualScore: Math.round(actualScore),
    totalPossibleScore: totalPossibleScore,
    totalCount: totalCount,
    correctCount: correctCount,
    errorCount: totalCount - correctCount,
    answeredCount: totalCount,
    duration: record.timeSpent || 0,
    hasSubjectiveQuestions: false,
    pendingGrading: false,
    scoreStatus: 'final',
    statusMessage: '最终成绩',
    detailedScores: [],
    aiAnalysis: generateAIAnalysis(correctCount, totalCount, totalCount, false),
    wrongQuestions: [],
    isReview: true
  }
  
  currentPractice.value = null
  isReviewMode.value = false
  
  ElMessage.success('练习结果加载成功')
}

// 开始题目回顾
const startQuestionReview = () => {
  if (!practiceResult.value?.reviewData) {
    ElMessage.warning('没有可回顾的题目数据')
    return
  }
  
  // 设置回顾模式
  isReviewMode.value = true
  currentPractice.value = practiceResult.value.reviewData
  currentQuestionIndex.value = 0
  currentAnswer.value = ''
  answers.value = practiceResult.value.reviewData.answers || {}
  
  // 加载第一题的答案
  loadCurrentAnswer()
  
  // 暂时隐藏结果界面
  const tempResult = practiceResult.value
  practiceResult.value = null
  
  // 保存结果数据以便返回
  currentPractice.value.tempResult = tempResult
  
  ElMessage.success('进入题目回顾模式')
}

// 退出回顾模式
const exitReviewMode = () => {
  // 恢复练习结果界面
  if (currentPractice.value?.tempResult) {
    practiceResult.value = currentPractice.value.tempResult
    
    // 清理练习状态，但保留结果
    currentPractice.value = null
    isReviewMode.value = false
    currentQuestionIndex.value = 0
    currentAnswer.value = ''
    answers.value = {}
    
    ElMessage.success('已退出回顾模式')
  } else {
    // 如果没有临时结果，完全清理状态
    cleanupCurrentState()
    ElMessage.success('已退出回顾模式')
  }
}

const getScoreColor = (score, totalScore) => {
  // 如果没有分数，返回默认颜色
  if (!score || score === 0) return 'danger'
  
  // 计算百分比
  let percentage
  if (totalScore && totalScore > 0) {
    // 使用实际分数和总分数计算百分比
    percentage = (score / totalScore) * 100
  } else {
    // 处理小数形式的分数（0-1）和百分比形式的分数（0-100）
    percentage = score <= 1 ? score * 100 : score
  }
  
  if (percentage >= 90) return 'success'
  if (percentage >= 70) return 'warning'
  return 'danger'
}

const getScoreLevel = (score, totalScore) => {
  // 如果没有分数，返回需要加强
  if (!score || score === 0) return '需要加强'
  
  // 计算百分比
  let percentage
  if (totalScore && totalScore > 0) {
    // 使用实际分数和总分数计算百分比
    percentage = (score / totalScore) * 100
  } else {
    // 处理小数形式的分数（0-1）和百分比形式的分数（0-100）
    percentage = score <= 1 ? score * 100 : score
  }
  
  if (percentage >= 90) return '优秀'
  if (percentage >= 80) return '良好'
  if (percentage >= 70) return '及格'
  return '需要加强'
}

const getScoreProgressColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

const getDifficultyColor = (difficulty) => {
  const colors = { 1: 'success', 2: 'warning', 3: 'danger' }
  return colors[difficulty] || 'info'
}

const getDifficultyText = (difficulty) => {
  const texts = { 1: '简单', 2: '中等', 3: '困难' }
  return texts[difficulty] || '未知'
}

const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) {
    return '0分0秒'
  }
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

// 题目类型转换函数 - 数字到字符串
const getQuestionTypeString = (questionType) => {
  // 如果已经是字符串，直接返回
  if (typeof questionType === 'string') {
    return questionType
  }
  
  // 数字到字符串的映射（根据数据库实际存储）
  const typeMap = {
    1: 'CHOICE',        // 单选题
    2: 'CHOICE',        // 多选题（也显示为选择题界面）
    3: 'FILL',          // 填空题
    4: 'SHORT_ANSWER',  // 简答题
    5: 'PROGRAMMING',   // 编程题
    6: 'SHORT_ANSWER'   // 案例分析题（显示为简答题界面）
  }
  
  return typeMap[questionType] || 'CHOICE'
}

// 题目类型转换函数 - 字符串到数字
const getQuestionTypeNumber = (questionType) => {
  // 如果已经是数字，直接返回
  if (typeof questionType === 'number') {
    return questionType
  }
  
  // 字符串到数字的映射（与数据库一致）
  const typeMap = {
    'SINGLE_CHOICE': 1,    // 单选题
    'MULTIPLE_CHOICE': 2,  // 多选题
    'FILL_BLANK': 3,       // 填空题
    'SHORT_ANSWER': 4,     // 简答题
    'PROGRAMMING': 5,      // 编程题
    'CASE_ANALYSIS': 6     // 案例分析题
  }
  
  return typeMap[questionType] || questionType
}

// 获取题目选项，处理不同的数据格式
const getQuestionOptions = (question) => {
  if (!question) {
    return []
  }
  
  // 如果已经有options数组，直接返回
  if (question.options && Array.isArray(question.options)) {
    return question.options
  }
  
  // 如果options是对象（不是字符串），直接处理
  if (question.options && typeof question.options === 'object' && !Array.isArray(question.options)) {
    const options = Object.keys(question.options).map(key => ({
      key: key,
      value: question.options[key]
    }))
    return options
  }
  
  // 如果options是字符串，尝试解析
  if (question.options && typeof question.options === 'string') {
    try {
      // 尝试JSON解析
      const parsedOptions = JSON.parse(question.options)
      
      if (Array.isArray(parsedOptions)) {
        return parsedOptions
      } else if (typeof parsedOptions === 'object') {
        // 处理 {"A":"...", "B":"...", "C":"...", "D":"..."} 格式
        const options = Object.keys(parsedOptions).map(key => ({
          key: key,
          value: parsedOptions[key]
        }))
        return options
      }
    } catch (e) {
      // 尝试按行分割或其他格式
      const lines = question.options.split('\n').filter(line => line.trim())
      if (lines.length > 0) {
        const options = lines.map((line, index) => {
          const key = String.fromCharCode(65 + index) // A, B, C, D...
          return { key, value: line.trim() }
        })
        return options
      }
    }
  }
  
  // 如果有选项字符串，解析成数组
  if (question.optionA || question.optionB || question.optionC || question.optionD) {
    const options = []
    if (question.optionA) options.push({ key: 'A', value: question.optionA })
    if (question.optionB) options.push({ key: 'B', value: question.optionB })
    if (question.optionC) options.push({ key: 'C', value: question.optionC })
    if (question.optionD) options.push({ key: 'D', value: question.optionD })
    return options
  }
  
  // 如果是选择题但没有选项，生成默认选项
  if (getQuestionTypeString(question.questionType) === 'CHOICE') {
    return [
      { key: 'A', value: '选项A' },
      { key: 'B', value: '选项B' },
      { key: 'C', value: '选项C' },
      { key: 'D', value: '选项D' }
    ]
  }
  
  return []
}

// 工具函数
const getQuestionTypeName = (type) => {
  const names = {
    1: '单选题',
    2: '多选题', 
    3: '填空题',
    4: '简答题',
    5: '编程题',
    6: '案例分析题'
  }
  return names[type] || '未知'
}

const getQuestionTypeColor = (type) => {
  const colors = {
    1: 'success',
    2: 'warning',
    3: 'info',
    4: 'primary',
    5: 'danger',
    6: 'warning'
  }
  return colors[type] || 'info'
}

const getPracticeTypeName = (type) => {
  const names = {
    1: '随机练习',
    2: '专项练习',
    3: '错题重做'
  }
  return names[type] || '练习'
}

onMounted(() => {
  loadCourses()
  loadPracticeHistory()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<style scoped>
.student-practice {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.config-card,
.practice-card,
.result-card,
.welcome-card {
  height: calc(100vh - 200px);
}

.config-card {
  overflow-y: auto;
}

.practice-history {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.practice-history h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.history-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.history-info h5 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.history-info p {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.history-meta {
  display: flex;
  gap: 10px;
  font-size: 12px;
}

.score {
  color: #409eff;
  font-weight: 600;
}

.practice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.practice-progress {
  display: flex;
  align-items: center;
  gap: 15px;
  font-weight: 600;
}

.timer {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #f56c6c;
}

.question-container {
  padding: 20px;
}

.question-progress {
  margin-bottom: 30px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-header h3 {
  margin: 0;
  color: #303133;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 25px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.question-options {
  margin-bottom: 30px;
}

.option-item {
  display: block;
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s;
}

.option-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.question-input {
  margin-bottom: 30px;
}

.question-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-content {
  padding: 20px;
}

.result-summary {
  display: flex;
  gap: 40px;
  margin-bottom: 30px;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 30px;
}

.score-text {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.score-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.score-info p {
  margin: 5px 0;
  color: #606266;
}

.result-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.stat-value.correct {
  color: #67c23a;
  font-weight: 600;
}

.stat-value.error {
  color: #f56c6c;
  font-weight: 600;
}

.ai-analysis {
  margin-bottom: 30px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.ai-analysis h4 {
  margin: 0 0 15px 0;
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-content {
  line-height: 1.6;
  color: #606266;
}

.wrong-questions h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.wrong-question-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.wrong-question-item h5 {
  margin: 0 0 15px 0;
  color: #303133;
}

.answer-comparison {
  margin-bottom: 15px;
}

.your-answer,
.correct-answer,
.explanation {
  margin-bottom: 10px;
  display: flex;
  gap: 10px;
}

.label {
  font-weight: 600;
  min-width: 80px;
}

.answer.error {
  color: #f56c6c;
}

.answer.correct {
  color: #67c23a;
}

.welcome-content {
  text-align: center;
  padding: 60px 20px;
}

.welcome-icon {
  font-size: 64px;
  color: #409eff;
  margin-bottom: 20px;
}

.welcome-content h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.welcome-content p {
  margin: 0 0 30px 0;
  color: #606266;
}

.features {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #909399;
}

.feature-item .el-icon {
  font-size: 24px;
}

/* 回顾模式样式 */
.correct-option {
  background-color: #f0f9ff !important;
  border-color: #67c23a !important;
}

.wrong-option {
  background-color: #fef0f0 !important;
  border-color: #f56c6c !important;
}

.correct-icon {
  color: #67c23a;
  margin-left: 8px;
}

.wrong-icon {
  color: #f56c6c;
  margin-left: 8px;
}

.answer-review {
  margin-top: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.answer-review .label {
  font-weight: 600;
  color: #303133;
}

.answer-review .answer {
  color: #67c23a;
  font-weight: 500;
  margin-left: 8px;
}

/* 详细评分信息样式 */
.detailed-scores {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.detailed-scores h4 {
  margin: 0 0 15px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-breakdown {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.score-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
}

.score-item.pending-review {
  border-color: #e6a23c;
  background: #fdf6ec;
}

.score-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.question-title {
  font-weight: bold;
  color: #303133;
  flex: 1;
}

.score-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.score-section {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
  border-left: 4px solid #dcdfe6;
}

.score-section.ai-section {
  border-left-color: #409eff;
  background: #ecf5ff;
}

.score-section.teacher-section {
  border-left-color: #67c23a;
  background: #f0f9ff;
}

.score-section.pending-section {
  border-left-color: #e6a23c;
  background: #fdf6ec;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-weight: bold;
  color: #303133;
  font-size: 14px;
}

.score-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 14px;
}

.score-row.final-score {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e4e7ed;
  font-weight: bold;
}

.score-row .label {
  color: #606266;
  min-width: 80px;
  font-weight: 500;
}

.score-row .value {
  font-weight: bold;
}

.score-row .value.ai-score {
  color: #409eff;
}

.score-row .value.teacher-score {
  color: #67c23a;
}

.score-row .value.final {
  color: #303133;
}

.score-row .total {
  color: #909399;
}

.score-row .note {
  color: #e6a23c;
  font-size: 12px;
  margin-left: 10px;
}

.feedback-row {
  margin-top: 8px;
}

.feedback-row .label {
  color: #606266;
  font-size: 12px;
  margin-bottom: 4px;
  display: block;
}

.feedback {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-line;
}

.feedback.ai-feedback {
  background: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.feedback.teacher-feedback {
  background: #f0f9ff;
  border-color: #95de64;
  color: #52c41a;
}

.feedback.ai-suggestion {
  background: #fff7e6;
  border-color: #ffd666;
  color: #fa8c16;
}

.pending-message {
  margin-top: 8px;
}

/* AI分析样式 */
.ai-analysis {
  margin-top: 20px;
}

.ai-analysis h4 {
  margin: 0 0 15px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-content {
  background: #e8f4fd;
  border: 1px solid #409eff;
  border-radius: 6px;
  padding: 15px;
  line-height: 1.6;
  color: #409eff;
  white-space: pre-line;
}

/* 练习结果头部样式 */
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-actions {
  display: flex;
  gap: 10px;
}

/* 滚动容器样式 */
.scrollable-result {
  height: calc(100vh - 180px);
  max-height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
}

.scrollable-result .el-card__body {
  flex: 1;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  padding-bottom: 60px; /* 增加底部间距，确保内容不被遮挡 */
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 8px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 4px;
  transition: background 0.3s;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* Firefox 滚动条样式 */
.scrollable-content {
  scrollbar-width: thin;
  scrollbar-color: #c0c4cc #f5f5f5;
}

/* 确保内容区域有足够的间距 */
.scrollable-content > *:last-child {
  margin-bottom: 30px;
}

/* 详细评分区域优化 */
.detailed-scores {
  margin-bottom: 30px;
}

.ai-analysis {
  margin-bottom: 30px;
}

.wrong-questions {
  margin-bottom: 30px;
}

/* 响应式调整 */
@media (max-height: 800px) {
  .scrollable-result {
    height: calc(100vh - 160px);
    max-height: calc(100vh - 160px);
  }
}

@media (max-height: 600px) {
  .scrollable-result {
    height: calc(100vh - 140px);
    max-height: calc(100vh - 140px);
  }
}

/* 滚动内容样式 */
.scrollable-content {
  max-height: 75vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  padding-bottom: 20px;
}

/* 自定义滚动条样式 - WebKit浏览器 */
.scrollable-content::-webkit-scrollbar {
  width: 8px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
  margin: 4px 0;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.scrollable-content::-webkit-scrollbar-thumb:active {
  background: #909090;
}

/* Firefox滚动条样式 */
.scrollable-content {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 确保内容区域有足够的间距 */
.result-content {
  position: relative;
}

/* 各个内容区块之间的间距 */
.result-summary,
.grading-status,
.detailed-scores,
.ai-analysis,
.wrong-questions {
  margin-bottom: 24px;
}

.result-summary:last-child,
.grading-status:last-child,
.detailed-scores:last-child,
.ai-analysis:last-child,
.wrong-questions:last-child {
  margin-bottom: 0;
}

/* 响应式调整 */
@media (max-height: 800px) {
  .scrollable-content {
    max-height: 70vh;
  }
}

@media (max-height: 600px) {
  .scrollable-content {
    max-height: 65vh;
  }
}

/* 滚动提示 */
.scrollable-content::before {
  content: '';
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background: linear-gradient(to bottom, rgba(255,255,255,0.9) 0%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

.scrollable-content::after {
  content: '';
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 0;
  background: linear-gradient(to top, rgba(255,255,255,0.9) 0%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}
</style>