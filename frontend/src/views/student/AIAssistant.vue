<template>
  <div class="ai-knowledge-base">
    <div class="page-header">
      <h2>AI知识库智能问答</h2>
      <p>基于全知识库的智能问答系统，支持跨学科知识查询</p>
    </div>

    <el-row :gutter="20">
      <!-- 主要问答区域 -->
      <el-col :span="18">
        <el-card class="main-chat-card">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon><ChatDotRound /></el-icon>
                <span>智能知识问答</span>
              </div>
              <div class="header-actions">
                <el-button size="small" @click="clearChat" type="info" plain>
                  <el-icon><Delete /></el-icon>
                  清空对话
                </el-button>
              </div>
            </div>
          </template>

          <!-- 搜索和筛选区域 -->
          <div class="search-filters">
            <el-row :gutter="15">
              <el-col :span="8">
                <el-input
                  v-model="searchQuery"
                  placeholder="输入搜索关键词..."
                  size="small"
                  @keyup.enter="searchKnowledgeBase"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="searchFilters.subject"
                  placeholder="学科领域"
                  size="small"
                  style="width: 100%"
                >
                  <el-option label="全部学科" value="" />
                  <el-option label="计算机科学" value="计算机科学" />
                  <el-option label="数学" value="数学" />
                  <el-option label="物理" value="物理" />
                  <el-option label="化学" value="化学" />
                  <el-option label="英语" value="英语" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="searchFilters.difficulty"
                  placeholder="难度等级"
                  size="small"
                  style="width: 100%"
                >
                  <el-option label="全部难度" :value="null" />
                  <el-option label="基础" :value="1" />
                  <el-option label="中等" :value="2" />
                  <el-option label="困难" :value="3" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="searchFilters.type"
                  placeholder="问题类型"
                  size="small"
                  style="width: 100%"
                >
                  <el-option label="全部类型" value="" />
                  <el-option label="概念解释" value="concept" />
                  <el-option label="原理分析" value="principle" />
                  <el-option label="实例应用" value="application" />
                  <el-option label="对比分析" value="comparison" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-button
                  size="small"
                  type="primary"
                  @click="searchKnowledgeBase"
                  :loading="searching"
                  :disabled="!searchQuery.trim()"
                >
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 对话区域 -->
          <div class="chat-container">
            <el-scrollbar ref="chatScrollbar" :height="chatContainerHeight">
              <div class="chat-messages">
                <!-- 欢迎消息 -->
                <div v-if="chatMessages.length === 0" class="welcome-section">
                  <div class="welcome-icon">
                    <el-icon size="48"><ChatDotRound /></el-icon>
                  </div>
                  <h3>欢迎使用AI知识问答系统</h3>
                  <p>我可以帮助您：</p>
                  <div class="feature-grid">
                    <div class="feature-item">
                      <el-icon><Search /></el-icon>
                      <span>跨学科知识查询</span>
                    </div>
                    <div class="feature-item">
                      <el-icon><Connection /></el-icon>
                      <span>知识点关联分析</span>
                    </div>
                    <div class="feature-item">
                      <el-icon><TrendCharts /></el-icon>
                      <span>深度概念解释</span>
                    </div>
                    <div class="feature-item">
                      <el-icon><Guide /></el-icon>
                      <span>学习路径推荐</span>
                    </div>
                  </div>
                </div>

                <!-- 对话消息 -->
                <div
                  v-for="(message, index) in chatMessages"
                  :key="index"
                  class="message-wrapper"
                  :class="message.type"
                >
                  <div class="message-avatar">
                    <el-avatar v-if="message.type === 'user'" :size="36">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <div v-else class="ai-avatar">
                      <el-icon><Robot /></el-icon>
                    </div>
                  </div>
                  <div class="message-content">
                    <div class="message-bubble">
                      <div v-if="message.loading" class="typing-indicator">
                        <span></span><span></span><span></span>
                      </div>
                      <div
                        v-else-if="message.type === 'ai'"
                        class="message-text markdown-content"
                        v-html="md.render(message.content)"
                      ></div>
                      <div v-else class="message-text">
                        {{ message.content }}
                      </div>
                    </div>
                    <div class="message-meta">
                      <span class="message-time">{{
                        formatTime(message.timestamp)
                      }}</span>
                      <div
                        v-if="message.type === 'ai' && message.sources"
                        class="message-sources"
                      >
                        <el-tag
                          v-for="source in message.sources"
                          :key="source"
                          size="small"
                          type="info"
                        >
                          {{ source }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <div class="input-container">
              <el-input
                v-model="currentQuestion"
                type="textarea"
                :rows="2"
                placeholder="请输入您的问题，支持跨学科知识查询..."
                :disabled="isThinking"
                @keydown.ctrl.enter="sendMessage"
                class="question-input"
              />
              <div class="input-actions">
                <div class="input-tips">
                  <span>Ctrl + Enter 发送</span>
                  <span v-if="searchFilters.subject"
                    >当前领域：{{ searchFilters.subject }}</span
                  >
                </div>
                <el-button
                  type="primary"
                  @click="sendMessage"
                  :loading="isThinking"
                  :disabled="!currentQuestion.trim()"
                  size="default"
                  class="send-button"
                >
                  <el-icon><Promotion /></el-icon>
                  {{ isThinking ? "思考中..." : "发送问题" }}
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 侧边栏 -->
      <el-col :span="6">
        <!-- 快速提示 -->
        <el-card class="sidebar-card">
          <template #header>
            <div class="sidebar-header">
              <el-icon><Lightning /></el-icon>
              <span>快速提示</span>
            </div>
          </template>
          <div class="quick-prompts">
            <div class="prompt-category">
              <h4>概念解释</h4>
              <div class="prompt-buttons">
                <el-button
                  v-for="prompt in conceptPrompts"
                  :key="prompt"
                  size="small"
                  type="info"
                  plain
                  @click="usePrompt(prompt)"
                  class="prompt-btn"
                >
                  {{ prompt }}
                </el-button>
              </div>
            </div>
            <div class="prompt-category">
              <h4>学习指导</h4>
              <div class="prompt-buttons">
                <el-button
                  v-for="prompt in learningPrompts"
                  :key="prompt"
                  size="small"
                  type="success"
                  plain
                  @click="usePrompt(prompt)"
                  class="prompt-btn"
                >
                  {{ prompt }}
                </el-button>
              </div>
            </div>
            <div class="prompt-category">
              <h4>问题分析</h4>
              <div class="prompt-buttons">
                <el-button
                  v-for="prompt in analysisPrompts"
                  :key="prompt"
                  size="small"
                  type="warning"
                  plain
                  @click="usePrompt(prompt)"
                  class="prompt-btn"
                >
                  {{ prompt }}
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 知识图谱 -->
        <el-card class="sidebar-card">
          <template #header>
            <div class="sidebar-header">
              <el-icon><Connection /></el-icon>
              <span>相关知识</span>
            </div>
          </template>
          <div v-if="relatedKnowledge.length > 0" class="knowledge-graph">
            <div
              v-for="knowledge in relatedKnowledge"
              :key="knowledge.id"
              class="knowledge-item"
              @click="exploreKnowledge(knowledge)"
            >
              <div class="knowledge-title">{{ knowledge.title }}</div>
              <div class="knowledge-relation">{{ knowledge.relation }}</div>
              <el-progress
                :percentage="knowledge.relevance"
                :show-text="false"
                :stroke-width="4"
                color="#409eff"
              />
            </div>
          </div>
          <div v-else class="empty-state">
            <el-icon><Connection /></el-icon>
            <p>提问后将显示相关知识点</p>
          </div>
        </el-card>

        <!-- 学习统计 -->
        <el-card class="sidebar-card">
          <template #header>
            <div class="sidebar-header">
              <el-icon><TrendCharts /></el-icon>
              <span>学习统计</span>
            </div>
          </template>
          <div class="learning-stats">
            <div class="stat-item">
              <div class="stat-number">{{ userStats.totalQuestions }}</div>
              <div class="stat-label">总提问数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ userStats.knowledgePoints }}</div>
              <div class="stat-label">涉及知识点</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ userStats.subjects }}</div>
              <div class="stat-label">学科领域</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 知识预览对话框 -->
    <el-dialog
      v-model="previewDialog.visible"
      :title="previewDialog.title"
      width="70%"
      top="5vh"
      class="knowledge-preview-dialog"
    >
      <div class="preview-content">
        <div v-if="previewDialog.loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>
        <div v-else-if="previewDialog.error" class="error-container">
          <el-result
            icon="error"
            title="加载失败"
            :sub-title="previewDialog.error"
          >
            <template #extra>
              <el-button type="primary" @click="retryLoadPreview">重试</el-button>
            </template>
          </el-result>
        </div>
        <div v-else class="content-container">
          <!-- 文件信息 -->
          <div class="file-info">
            <div class="info-item">
              <el-icon><Document /></el-icon>
              <span>{{ previewDialog.fileType || '文档' }}</span>
            </div>
            <div class="info-item">
              <el-icon><Calendar /></el-icon>
              <span>{{ previewDialog.lastModified || '未知时间' }}</span>
            </div>
            <div class="info-item">
              <el-icon><View /></el-icon>
              <span>{{ previewDialog.size || '未知大小' }}</span>
            </div>
          </div>
          
          <!-- 文件内容 -->
          <div class="file-content">
            <el-scrollbar height="400px">
              <div 
                v-if="previewDialog.contentType === 'markdown'"
                class="markdown-content"
                v-html="md.render(previewDialog.content)"
              ></div>
              <div 
                v-else-if="previewDialog.contentType === 'text'"
                class="text-content"
              >
                <pre>{{ previewDialog.content }}</pre>
              </div>
              <div 
                v-else-if="previewDialog.contentType === 'html'"
                class="html-content"
                v-html="previewDialog.content"
              ></div>
              <div v-else class="unsupported-content">
                <el-result
                  icon="warning"
                  title="不支持的文件类型"
                  sub-title="无法预览此类型的文件内容"
                />
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialog.visible = false">关闭</el-button>
                
         </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import {
  answerQuestionWithKnowledge,
  searchKnowledge,
  recommendLearningResources,
  getKnowledgeFileContent,
  saveQuestionRecord,
} from "@/api/ai";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";

// 初始化markdown解析器
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value;
      } catch (__) {}
    }
    return "";
  },
});

// 响应式数据
const isThinking = ref(false);
const searching = ref(false);
const currentQuestion = ref("");
const searchQuery = ref("");
const chatScrollbar = ref();

// 动态计算聊天容器高度
const chatContainerHeight = ref("400px");

// 搜索筛选条件
const searchFilters = reactive({
  subject: "",
  difficulty: null,
  type: "",
});

// 聊天消息
const chatMessages = ref([]);

// 快速提示数据
const conceptPrompts = ref(["解释概念", "定义是什么", "基本原理", "核心思想"]);

const learningPrompts = ref(["学习方法", "入门指南", "实践建议", "常见误区"]);

const analysisPrompts = ref(["优缺点分析", "对比差异", "应用场景", "发展趋势"]);

const relatedKnowledge = ref([]);

const userStats = ref({
  totalQuestions: 0,
  knowledgePoints: 0,
  subjects: 0,
});

// 预览对话框数据
const previewDialog = reactive({
  visible: false,
  loading: false,
  error: null,
  knowledge: null,
  content: ''
});

// 发送消息
const sendMessage = async () => {
  if (!currentQuestion.value.trim() || isThinking.value) return;

  const question = currentQuestion.value.trim();

  // 添加用户消息
  chatMessages.value.push({
    type: "user",
    content: question,
    timestamp: Date.now(),
  });

  // 添加AI加载消息
  chatMessages.value.push({
    type: "ai",
    content: "",
    loading: true,
    timestamp: Date.now(),
  });

  // 清空输入框
  currentQuestion.value = "";

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  // 开始思考
  isThinking.value = true;

  try {
    // 调用AI回答API
    const response = await answerQuestionWithKnowledge({
      question: question,
      subject: searchFilters.subject,
      difficulty: searchFilters.difficulty,
      type: searchFilters.type,
    });

    // 更新最后一条AI消息
    const lastIndex = chatMessages.value.length - 1;
    const aiAnswer = response.data.answer || response.data;
    chatMessages.value[lastIndex] = {
      type: "ai",
      content: aiAnswer,
      loading: false,
      timestamp: Date.now(),
      sources: response.sources || [],
    };

    // 后台保存问答记录到数据库（不影响用户体验）
    saveQuestionToDatabase(question, aiAnswer);

    // 更新相关知识
    await updateRelatedKnowledge(question);

    // 更新用户统计
    updateUserStats();
  } catch (error) {
    console.error("Failed to get AI response:", error);

    // 更新错误消息
    const lastIndex = chatMessages.value.length - 1;
    chatMessages.value[lastIndex] = {
      type: "ai",
      content: "抱歉，我暂时无法回答这个问题。请稍后重试或换个问题。",
      loading: false,
      timestamp: Date.now(),
    };

    ElMessage.error("获取AI回答失败");
  } finally {
    isThinking.value = false;
    await nextTick();
    scrollToBottom();
  }
};

// 搜索知识库
const searchKnowledgeBase = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }

  searching.value = true;
  try {
    const response = await searchKnowledge({
      query: searchQuery.value.trim(),
      subject: searchFilters.subject,
      limit: 10,
    });

    if (response.data && response.data.length > 0) {
      // 显示搜索结果
      const searchResult =
        `搜索"${searchQuery.value}"找到 ${response.data.length} 个相关知识点：\n\n` +
        response.data
          .map(
            (item, index) =>
              `${index + 1}. ${item.title || item.name || "未知标题"}\n   ${
                item.summary || item.description || "暂无描述"
              }`
          )
          .join("\n\n");

      chatMessages.value.push({
        type: "ai",
        content: searchResult,
        timestamp: Date.now(),
        sources: ["知识库搜索"],
      });

      // 清空搜索框
      searchQuery.value = "";

      await nextTick();
      scrollToBottom();
    } else {
      ElMessage.info("未找到相关知识点");
    }
  } catch (error) {
    console.error("Failed to search knowledge base:", error);
    ElMessage.error("搜索失败");
  } finally {
    searching.value = false;
  }
};

// 使用快速提示
const usePrompt = (prompt) => {
  // 将提示词添加到输入框中，让用户可以继续编辑
  if (currentQuestion.value.trim()) {
    currentQuestion.value += ` ${prompt}`;
  } else {
    currentQuestion.value = prompt;
  }
  // 聚焦到输入框
  nextTick(() => {
    const textarea = document.querySelector(".question-input textarea");
    if (textarea) {
      textarea.focus();
      textarea.setSelectionRange(textarea.value.length, textarea.value.length);
    }
  });
};

// 探索知识点 - 显示预览窗口
const exploreKnowledge = async (knowledge) => {
  previewDialog.visible = true;
  previewDialog.loading = true;
  previewDialog.error = null;
  previewDialog.knowledge = knowledge;
  previewDialog.title = knowledge.title;
  
  try {
    // 模拟获取知识内容 - 这里应该调用实际的API
    await loadKnowledgeContent(knowledge);
  } catch (error) {
    console.error('Failed to load knowledge content:', error);
    previewDialog.error = '加载知识内容失败，请稍后重试';
  } finally {
    previewDialog.loading = false;
  }
};

// 加载知识内容
const loadKnowledgeContent = async (knowledge) => {
  try {
    // 调用实际的API获取知识文件内容
    const response = await getKnowledgeFileContent(knowledge.id);
    
    if (response.code === 200 && response.data) {
      const data = response.data;
      
      // 设置预览对话框的内容
      previewDialog.contentType = data.contentType || 'text';
      previewDialog.fileType = data.fileType || '文档';
      previewDialog.lastModified = data.createTime ? 
        new Date(data.createTime).toLocaleDateString('zh-CN') : '未知时间';
      previewDialog.size = data.fileSize ? 
        formatFileSize(data.fileSize) : '未知大小';
      previewDialog.content = data.content || '暂无内容';
      
      // 如果有摘要和关键点，添加到内容前面
      if (data.summary || data.keyPoints) {
        let enhancedContent = '';
        if (data.summary) {
          enhancedContent += `## 内容摘要\n${data.summary}\n\n`;
        }
        if (data.keyPoints) {
          enhancedContent += `## 关键知识点\n${data.keyPoints}\n\n`;
        }
        enhancedContent += `## 详细内容\n${data.content || ''}`;
        previewDialog.content = enhancedContent;
        previewDialog.contentType = 'markdown';
      }
    } else {
      throw new Error(response.message || '获取知识内容失败');
    }
  } catch (error) {
    console.error('Failed to load knowledge content:', error);
    // 如果API调用失败，使用模拟内容作为后备
    previewDialog.contentType = 'markdown';
    previewDialog.fileType = 'Markdown文档';
    previewDialog.lastModified = new Date().toLocaleDateString('zh-CN');
    previewDialog.size = '约 2.5KB';
    previewDialog.content = generateMarkdownContent(knowledge);
  }
};

// 生成Markdown内容
const generateMarkdownContent = (knowledge) => {
  return `# ${knowledge.title}

## 概述
${knowledge.title}是一个重要的知识点，在相关领域中具有重要意义。

## 核心概念
- **定义**: ${knowledge.title}的基本定义和核心思想
- **特点**: 主要特征和属性
- **应用**: 实际应用场景和案例

## 详细说明
\`\`\`javascript
// 示例代码
function example() {
  console.log('这是一个示例');
}
\`\`\`

## 相关知识点
- 相关概念A
- 相关概念B
- 相关概念C

## 学习建议
1. 理解基本概念
2. 掌握核心原理
3. 实践应用场景

> **提示**: 这是一个重要的知识点，建议深入学习。`;
};

// 生成文本内容
const generateTextContent = (knowledge) => {
  return `${knowledge.title}

这是关于${knowledge.title}的详细说明文档。

基本概念：
${knowledge.title}是一个重要的知识点，需要深入理解其核心原理和应用场景。

主要特点：
1. 具有重要的理论意义
2. 在实际应用中广泛使用
3. 与其他知识点密切相关

学习要点：
- 掌握基本定义
- 理解核心原理
- 熟悉应用场景
- 练习相关题目

参考资料：
相关教材和文献资料可以帮助更好地理解这个知识点。`;
};

// 生成HTML内容
const generateHtmlContent = (knowledge) => {
  return `<div class="knowledge-content">
    <h2>${knowledge.title}</h2>
    <div class="content-section">
      <h3>概述</h3>
      <p>这是关于<strong>${knowledge.title}</strong>的详细介绍。</p>
    </div>
    <div class="content-section">
      <h3>核心要点</h3>
      <ul>
        <li>重要概念和定义</li>
        <li>核心原理和机制</li>
        <li>实际应用和案例</li>
      </ul>
    </div>
    <div class="content-section">
      <h3>学习建议</h3>
      <ol>
        <li>理解基本概念</li>
        <li>掌握核心原理</li>
        <li>实践应用场景</li>
      </ol>
    </div>
  </div>`;
};

// 获取文件类型显示名称
const getFileTypeDisplay = (contentType) => {
  const typeMap = {
    'markdown': 'Markdown文档',
    'text': '文本文档',
    'html': 'HTML文档'
  };
  return typeMap[contentType] || '未知类型';
};

// 重试加载预览
const retryLoadPreview = () => {
  if (previewDialog.knowledge) {
    exploreKnowledge(previewDialog.knowledge);
  }
};



// 更新相关知识
const updateRelatedKnowledge = async (question) => {
  try {
    const response = await searchKnowledge({
      query: question,
      limit: 5,
    });

    if (response.data) {
      relatedKnowledge.value = response.data.map((item, index) => ({
        id: item.id || (item.knowledgeBaseId || (1000 + index)), // 使用实际的知识库ID
        title: item.title || item.name,
        relation: getRelationType(item.type),
        relevance: Math.floor(Math.random() * 30) + 70, // 模拟相关度
      }));
    }
  } catch (error) {
    console.error("Failed to update related knowledge:", error);
  }
};

// 获取关系类型
const getRelationType = (type) => {
  const relations = {
    concept: "概念关联",
    principle: "原理相关",
    application: "应用实例",
    comparison: "对比分析",
  };
  return relations[type] || "相关知识";
};

// 更新用户统计
const updateUserStats = () => {
  userStats.value.totalQuestions++;

  // 统计涉及的学科
  const subjects = new Set();
  chatMessages.value.forEach((msg) => {
    if (msg.type === "user" && searchFilters.subject) {
      subjects.add(searchFilters.subject);
    }
  });
  userStats.value.subjects = subjects.size;

  // 模拟知识点统计
  userStats.value.knowledgePoints = Math.floor(
    userStats.value.totalQuestions * 1.5
  );
};

// 清空对话
const clearChat = () => {
  chatMessages.value = [];
  relatedKnowledge.value = [];
  ElMessage.success("对话已清空");
};

// 格式化消息
const formatMessage = (content) => {
  return content.replace(/\n/g, "<br>");
};

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatScrollbar.value) {
      try {
        const scrollbar = chatScrollbar.value;
        let scrollElement = null;

        if (scrollbar.$refs && scrollbar.$refs.wrap$) {
          scrollElement = scrollbar.$refs.wrap$;
        } else if (scrollbar.$refs && scrollbar.$refs.wrap) {
          scrollElement = scrollbar.$refs.wrap;
        } else if (scrollbar.wrapRef) {
          scrollElement = scrollbar.wrapRef;
        }

        if (scrollElement) {
          scrollElement.scrollTop = scrollElement.scrollHeight;
        }
      } catch (error) {
        console.warn("Failed to scroll to bottom:", error);
      }
    }
  });
};

// 初始化用户统计
const initUserStats = () => {
  // 从localStorage加载用户统计
  try {
    const savedStats = localStorage.getItem("ai_knowledge_stats");
    if (savedStats) {
      const stats = JSON.parse(savedStats);
      userStats.value = { ...userStats.value, ...stats };
    }
  } catch (error) {
    console.warn("Failed to load user stats:", error);
  }
};

// 保存用户统计
const saveUserStats = () => {
  try {
    localStorage.setItem("ai_knowledge_stats", JSON.stringify(userStats.value));
  } catch (error) {
    console.warn("Failed to save user stats:", error);
  }
};

onMounted(() => {
  initUserStats();
  calculateChatHeight();
  window.addEventListener('resize', calculateChatHeight);
});

// 计算聊天容器高度
const calculateChatHeight = () => {
  // 计算可用高度：总高度 - 页面头部 - 搜索区域 - 输入区域 - 边距
  const windowHeight = window.innerHeight;
  const headerHeight = 120; // 页面头部高度
  const searchHeight = 80;  // 搜索筛选区域高度
  const inputHeight = 120;  // 输入区域高度
  const margins = 60;       // 各种边距
  
  const availableHeight = windowHeight - headerHeight - searchHeight - inputHeight - margins;
  chatContainerHeight.value = Math.max(300, availableHeight) + 'px';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 保存问答记录到数据库
const saveQuestionToDatabase = async (question, answer) => {
  try {
    await saveQuestionRecord({
      question: question,
      answer: answer,
      subject: searchFilters.subject,
      courseId: null, // 可以根据需要设置课程ID
      questionSource: 'knowledge_qa'
    });
    
    console.log('Question and answer saved to database');
  } catch (error) {
    console.error('Failed to save question to database:', error);
    // 不显示错误消息给用户，因为这是后台操作
  }
};

// 监听统计变化并保存
import { watch } from "vue";
watch(userStats, saveUserStats, { deep: true });
</script>

<style scoped>
.ai-knowledge-base {
  padding: 20px;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

/* 主要聊天卡片 */
.main-chat-card {
  height: calc(110vh - 130px);
  min-height: 600px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.main-chat-card .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 搜索筛选区域 */
.search-filters {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* 聊天容器 */
.chat-container {
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.chat-messages {
  padding: 20px;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 40px 20px;
}

.welcome-icon {
  margin-bottom: 20px;
  color: #409eff;
}

.welcome-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 20px;
}

.welcome-section p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 16px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.feature-item:hover {
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.feature-item .el-icon {
  font-size: 24px;
  color: #409eff;
}

.feature-item span {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

/* 消息样式 */
.message-wrapper {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.message-wrapper.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 12px;
}

.ai-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.message-content {
  flex: 1;
  max-width: 75%;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 16px;
  word-wrap: break-word;
  position: relative;
}

.message-wrapper.user .message-bubble {
  background: #409eff;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-wrapper.ai .message-bubble {
  background: white;
  color: #303133;
  border: 1px solid #e4e7ed;
  border-bottom-left-radius: 4px;
}

.message-text {
  line-height: 1.6;
  white-space: pre-wrap;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.message-time {
  font-size: 12px;
  color: #909399;
}

.message-sources {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 加载动画 */
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 输入区域 */
.input-section {
  border-top: 1px solid #e4e7ed;
  padding: 16px 20px 20px 20px;
  background: white;
  flex-shrink: 0; /* 防止输入区域被压缩 */
  position: relative;
  z-index: 10;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-input {
  border-radius: 8px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 12px;
  min-height: 44px; /* 确保按钮区域有足够高度 */
}

.input-tips {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
  flex: 1; /* 让提示文字占据剩余空间 */
  min-width: 0; /* 允许收缩 */
}

.send-button {
  min-width: 120px; /* 确保按钮有足够宽度 */
  height: 40px; /* 确保按钮有足够高度 */
  flex-shrink: 0; /* 防止按钮被压缩 */
  white-space: nowrap; /* 防止按钮文字换行 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .input-tips {
    justify-content: center;
  }
  
  .send-button {
    width: 100%;
    min-width: auto;
  }
}

/* 侧边栏 */
.sidebar-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

/* 快速提示 */
.quick-prompts {
  max-height: 400px;
  overflow-y: auto;
}

.prompt-category {
  margin-bottom: 20px;
}

.prompt-category:last-child {
  margin-bottom: 0;
}

.prompt-category h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.prompt-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.prompt-btn {
  flex: 0 0 auto;
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 14px;
  transition: all 0.3s;
}

.prompt-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 知识图谱 */
.knowledge-graph {
  max-height: 300px;
  overflow-y: auto;
}

.knowledge-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s;
}

.knowledge-item:hover {
  background: #e8f5e8;
  transform: translateX(4px);
}

.knowledge-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  font-weight: 500;
}

.knowledge-relation {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state .el-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

/* 学习统计 */
.learning-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

/* Markdown内容样式 */
.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial,
    sans-serif;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.5em;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 8px;
}
.markdown-content h2 {
  font-size: 1.3em;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 6px;
}
.markdown-content h3 {
  font-size: 1.2em;
}
.markdown-content h4 {
  font-size: 1.1em;
}
.markdown-content h5 {
  font-size: 1em;
}
.markdown-content h6 {
  font-size: 0.9em;
  color: #6a737d;
}

.markdown-content p {
  margin: 8px 0;
  line-height: 1.6;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-content li {
  margin: 4px 0;
  line-height: 1.5;
}

.markdown-content blockquote {
  margin: 8px 0;
  padding: 0 16px;
  border-left: 4px solid #dfe2e5;
  background-color: #f6f8fa;
  color: #6a737d;
}

.markdown-content code {
  padding: 2px 4px;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  margin: 12px 0;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
  line-height: 1.45;
}

.markdown-content pre code {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  font-size: 0.85em;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 12px 0;
  width: 100%;
}

.markdown-content th,
.markdown-content td {
  padding: 6px 12px;
  border: 1px solid #dfe2e5;
  text-align: left;
}

.markdown-content th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content hr {
  margin: 16px 0;
  border: none;
  border-top: 1px solid #e1e4e8;
}

/* 知识预览对话框样式 */
.knowledge-preview-dialog {
  border-radius: 12px;
}

.knowledge-preview-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

.knowledge-preview-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.knowledge-preview-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

.knowledge-preview-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #f0f0f0;
}

.preview-content {
  padding: 0;
}

.loading-container,
.error-container {
  padding: 40px 20px;
  text-align: center;
}

.content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 文件信息栏 */
.file-info {
  display: flex;
  gap: 24px;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #606266;
}

.info-item .el-icon {
  color: #909399;
}

/* 文件内容区域 */
.file-content {
  flex: 1;
  padding: 24px;
  background: white;
}

.text-content pre {
  margin: 0;
  padding: 16px;
  background: #f6f8fa;
  border-radius: 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.html-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
}

.html-content .knowledge-content {
  max-width: 100%;
}

.html-content .content-section {
  margin-bottom: 24px;
}

.html-content h2 {
  color: #303133;
  font-size: 24px;
  margin-bottom: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.html-content h3 {
  color: #409eff;
  font-size: 18px;
  margin-bottom: 12px;
}

.html-content ul, .html-content ol {
  padding-left: 20px;
  line-height: 1.6;
}

.html-content li {
  margin-bottom: 8px;
}

.unsupported-content {
  padding: 40px 20px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-chat-card {
    height: 600px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .ai-knowledge-base {
    padding: 12px;
  }

  .page-header h2 {
    font-size: 24px;
  }

  .page-header p {
    font-size: 14px;
  }

  .main-chat-card {
    height: 500px;
  }

  .search-filters .el-row {
    flex-direction: column;
  }

  .search-filters .el-col {
    width: 100% !important;
    margin-bottom: 8px;
  }

  .input-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .input-tips {
    justify-content: center;
    order: 2;
  }

  .learning-stats {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
