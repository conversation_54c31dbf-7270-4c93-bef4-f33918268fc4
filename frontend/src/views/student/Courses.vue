<template>
  <div class="student-courses">
    <div class="page-header">
      <h2>我的课程</h2>
      <p>查看和学习已选课程</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="学科">
          <el-select v-model="searchForm.subject" placeholder="选择学科" style="width: 150px">
            <el-option label="全部学科" value="" />
            <el-option label="计算机科学" value="计算机科学" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="英语" value="英语" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="进行中" value="1" />
            <el-option label="已结束" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadCourses">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 课程卡片列表 -->
    <div v-loading="loading" class="courses-grid">
      <el-card 
        v-for="course in courses" 
        :key="course.id" 
        class="course-card"
        shadow="hover"
      >
        <template #header>
          <div class="course-header">
            <h3>{{ course.courseName }}</h3>
            <el-tag :type="course.status === 1 ? 'success' : 'info'">
              {{ course.status === 1 ? '进行中' : '已结束' }}
            </el-tag>
          </div>
        </template>
        
        <div class="course-content">
          <div class="course-info">
            <p class="description">{{ course.description }}</p>
            <div class="meta-info">
              <div class="meta-item">
                <el-icon><User /></el-icon>
                <span>{{ course.teacherName }}</span>
              </div>
              <div class="meta-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ formatDate(course.createTime) }}</span>
              </div>
              <div class="meta-item">
                <el-icon><Document /></el-icon>
                <span>{{ course.subject }}</span>
              </div>
            </div>
          </div>
          
          <!-- 学习进度 -->
          <div class="progress-section">
            <div class="progress-header">
              <span>学习进度</span>
              <span class="progress-text">{{ course.progress || 0 }}%</span>
            </div>
            <el-progress 
              :percentage="course.progress || 0" 
              :stroke-width="8"
              :show-text="false"
            />
          </div>
          
          <!-- 统计信息 -->
          <div class="stats-section">
            <div class="stat-item">
              <span class="stat-label">已学内容</span>
              <span class="stat-value">{{ course.learnedCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总内容数</span>
              <span class="stat-value">{{ course.totalContent || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">练习次数</span>
              <span class="stat-value">{{ course.practiceCount || 0 }}</span>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="course-actions">
            <el-button 
              type="primary" 
              @click="enterCourse(course)"
              :disabled="course.status === 0"
            >
              <el-icon><VideoPlay /></el-icon>
              {{ course.status === 0 ? '课程已结束' : '开始学习' }}
            </el-button>
            <el-button @click="viewCourseDetail(course)">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
          </div>
        </template>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[12, 24, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadCourses"
        @current-change="loadCourses"
      />
    </div>

    <!-- 课程详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="课程详情" width="800px">
      <div v-if="selectedCourse">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程名称">{{ selectedCourse.courseName }}</el-descriptions-item>
          <el-descriptions-item label="授课教师">{{ selectedCourse.teacherName }}</el-descriptions-item>
          <el-descriptions-item label="学科">{{ selectedCourse.subject }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedCourse.status === 1 ? 'success' : 'info'">
              {{ selectedCourse.status === 1 ? '进行中' : '已结束' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedCourse.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="学习进度">{{ selectedCourse.progress || 0 }}%</el-descriptions-item>
        </el-descriptions>
        
        <div class="course-description">
          <h4>课程描述</h4>
          <p>{{ selectedCourse.description }}</p>
        </div>
        
        <div class="course-outline" v-if="selectedCourse.outline">
          <h4>课程大纲</h4>
          <div class="outline-content">{{ selectedCourse.outline }}</div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="enterCourse(selectedCourse)"
          :disabled="selectedCourse?.status === 0"
        >
          {{ selectedCourse?.status === 0 ? '课程已结束' : '开始学习' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getStudentCourses, getCourseContents } from '@/api/student'
import { formatDate } from '@/utils/format'

const router = useRouter()
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedCourse = ref(null)

const courses = ref([])

const searchForm = reactive({
  subject: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 12,
  total: 0
})

const loadCourses = async () => {
  loading.value = true
  try {
    const response = await getStudentCourses()
    // 处理响应数据，可能是数组或包含records的对象
    if (Array.isArray(response.data)) {
      courses.value = response.data
      pagination.total = response.data.length
    } else if (response.data.records) {
      courses.value = response.data.records
      pagination.total = response.data.total
    } else {
      courses.value = response.data || []
      pagination.total = courses.value.length
    }
    
    // 应用前端筛选
    if (searchForm.subject || searchForm.status) {
      courses.value = courses.value.filter(course => {
        const subjectMatch = !searchForm.subject || course.subject === searchForm.subject
        const statusMatch = !searchForm.status || course.status?.toString() === searchForm.status
        return subjectMatch && statusMatch
      })
    }
  } catch (error) {
    console.error('Failed to load courses:', error)
    ElMessage.error('加载课程失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.subject = ''
  searchForm.status = ''
  pagination.current = 1
  loadCourses()
}

const enterCourse = async (course) => {
  // 检查课程状态
  if (course.status === 0) {
    ElMessage.warning('该课程已结束，无法开始学习')
    return
  }
  
  // 检查课程是否有教学内容
  try {
    const response = await getCourseContents(course.id)
    const contents = response.data || []
    
    if (contents.length === 0) {
      ElMessage.warning('该课程暂无教学内容，无法开始学习。请联系老师添加教学内容。')
      return
    }
    
    // 跳转到学习页面，传递课程ID
    router.push({
      path: '/student/learning',
      query: { courseId: course.id }
    })
  } catch (error) {
    console.error('Failed to check course contents:', error)
    ElMessage.error('检查课程内容失败')
  }
}

const viewCourseDetail = (course) => {
  selectedCourse.value = course
  showDetailDialog.value = true
}

onMounted(() => {
  loadCourses()
})
</script>

<style scoped>
.student-courses {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.course-card {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.course-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #909399;
}

.progress-section {
  margin-top: auto;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-text {
  font-weight: 600;
  color: #409eff;
}

.stats-section {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.course-actions {
  display: flex;
  gap: 12px;
}

.course-actions .el-button {
  flex: 1;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.course-description,
.course-outline {
  margin-top: 20px;
}

.course-description h4,
.course-outline h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.outline-content {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  white-space: pre-wrap;
  line-height: 1.6;
}
</style>