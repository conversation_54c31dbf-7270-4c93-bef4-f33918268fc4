<template>
  <div class="student-learning">
    <div class="page-header">
      <h2>学习助手</h2>
      <p>AI智能问答，随时为您解答学习疑问</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：课程内容 -->
      <el-col :span="8">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <div>课程内容</div>
              <el-select 
                v-model="selectedCourseId" 
                placeholder="选择课程"
                @change="loadCourseContent"
                size="small"
                :disabled="activeCourses.length === 0"
              >
                <el-option
                  v-for="course in activeCourses"
                  :key="course.id"
                  :label="course.courseName"
                  :value="course.id"
                />
                <el-option
                  v-if="activeCourses.length === 0"
                  label="暂无进行中的课程"
                  value=""
                  disabled
                />
              </el-select>
            </div>
          </template>
          
          <el-scrollbar height="600px">
            <div v-if="loadingContent" class="loading-container">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else-if="courseContents.length === 0" class="empty-content">
              <el-empty description="暂无课程内容" />
            </div>
            <div v-else>
              <div 
                v-for="content in courseContents" 
                :key="content.id"
                class="content-item"
                :class="{ active: selectedContent?.id === content.id }"
                @click="selectContent(content)"
              >
                <div class="content-header">
                  <h4>{{ content.title }}</h4>
                  <el-tag :type="getContentTypeColor(content.contentType)" size="small">
                    {{ getContentTypeText(content.contentType) }}
                  </el-tag>
                </div>
                <p class="content-description">{{ content.description }}</p>
                <div class="content-meta">
                  <span class="difficulty">
                    <el-rate :model-value="content.difficulty" disabled size="small" />
                  </span>
                  <span class="create-time">{{ formatDate(content.createTime) }}</span>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>

      <!-- 右侧：AI问答区域 -->
      <el-col :span="16">
        <el-card class="chat-card">
          <template #header>
            <div class="card-header">
              <span>AI学习助手</span>
              <div class="header-actions">
                <el-button size="small" @click="clearChat">
                  <el-icon><Delete /></el-icon>
                  清空对话
                </el-button>
              </div>
            </div>
          </template>
          
          <!-- 聊天消息区域 -->
          <div class="chat-container">
            <el-scrollbar ref="chatScrollbar" height="500px">
              <div class="chat-messages">
                <!-- 欢迎消息 -->
                <div v-if="messages.length === 0" class="welcome-message">
                  <div class="ai-avatar">
                    <el-icon><ChatDotRound /></el-icon>
                  </div>
                  <div class="welcome-content">
                    <h3>欢迎使用AI学习助手！</h3>
                    <div v-if="activeCourses.length === 0">
                      <p style="color: #f56c6c;">暂无进行中的课程，无法使用学习助手功能。</p>
                      <p>请联系老师开启课程或等待新课程开始。</p>
                    </div>
                    <div v-else-if="selectedCourseId && courseContents.length === 0">
                      <p style="color: #f56c6c;">该课程暂无教学内容，无法使用学习助手功能。</p>
                      <p>请联系老师添加教学内容或选择其他课程。</p>
                    </div>
                    <div v-else-if="selectedCourseId && courseContents.length > 0 && !selectedContent">
                      <p style="color: #e6a23c;">请先选择左侧的教学内容，然后再开始提问。</p>
                      <p>选择教学内容后，我可以为您提供更精准的答案。</p>
                    </div>
                    <div v-else>
                      <p>我可以帮助您：</p>
                      <ul>
                        <li>解答课程相关问题</li>
                        <li>解释知识点概念</li>
                        <li>提供学习建议</li>
                        <li>分析练习题目</li>
                      </ul>
                      <p>请选择左侧的课程内容，然后向我提问吧！</p>
                    </div>
                  </div>
                </div>
                
                <!-- 对话消息 -->
                <div 
                  v-for="(message, index) in messages" 
                  :key="index"
                  class="message-item"
                  :class="message.type"
                >
                  <div class="message-avatar">
                    <el-avatar v-if="message.type === 'user'" :size="32">
                      {{ userStore.userInfo?.realName?.charAt(0) }}
                    </el-avatar>
                    <div v-else class="ai-avatar">
                      <el-icon><ChatDotRound /></el-icon>
                    </div>
                  </div>
                  <div class="message-content">
                    <div class="message-bubble">
                      <div v-if="message.type === 'ai' && message.loading" class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                      <div v-else-if="message.type === 'ai'" class="message-text markdown-content" v-html="md.render(message.content)"></div>
                      <div v-else class="message-text">{{ message.content }}</div>
                    </div>
                    <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
          
          <!-- 输入区域 -->
          <div class="chat-input">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              :placeholder="getInputPlaceholder()"
              @keydown.ctrl.enter="sendMessage"
              :disabled="sending || !canAskQuestion"
            />
            <div class="input-actions">
              <div class="input-tips">
                <span>Ctrl + Enter 发送</span>
                <span v-if="selectedContent">当前内容：{{ selectedContent.title }}</span>
              </div>
              <el-button 
                type="primary" 
                @click="sendMessage"
                :loading="sending"
                :disabled="!inputMessage.trim() || !canAskQuestion"
              >
                <el-icon><Promotion /></el-icon>
                {{ getSendButtonText() }}
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, getCurrentInstance, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getStudentCourses, getCourseContents } from '@/api/student'
import { askQuestion } from '@/api/student'
import { formatDate } from '@/utils/format'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

const route = useRoute()
const userStore = useUserStore()

// 初始化markdown解析器
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return '' // 使用外部默认转义
  }
})

const loadingContent = ref(false)
const sending = ref(false)
const selectedCourseId = ref('')
const selectedContent = ref(null)
const inputMessage = ref('')
const chatScrollbar = ref()

const courses = ref([])
const courseContents = ref([])
const messages = ref([])

// 计算属性：只显示进行中的课程
const activeCourses = computed(() => {
  return courses.value.filter(course => course.status === 1)
})

// 计算属性：是否可以提问
const canAskQuestion = computed(() => {
  // 必须有进行中的课程
  if (activeCourses.value.length === 0) return false
  
  // 必须选择了课程
  if (!selectedCourseId.value) return false
  
  // 必须有教学内容
  if (courseContents.value.length === 0) return false
  
  // 必须选择了教学内容
  if (!selectedContent.value) return false
  
  return true
})

// 聊天历史存储key
const getChatStorageKey = () => {
  const userId = userStore.userInfo?.userId || 'guest'
  const courseId = selectedCourseId.value || 'general'
  return `ai_chat_history_${userId}_${courseId}`
}

// 保存聊天历史到localStorage
const saveChatHistory = () => {
  try {
    const storageKey = getChatStorageKey()
    const chatData = {
      messages: messages.value,
      courseId: selectedCourseId.value,
      contentId: selectedContent.value?.id,
      timestamp: new Date().toISOString()
    }
    localStorage.setItem(storageKey, JSON.stringify(chatData))
  } catch (error) {
    console.warn('Failed to save chat history:', error)
  }
}

// 加载聊天历史从localStorage
const loadChatHistory = () => {
  try {
    const storageKey = getChatStorageKey()
    const savedData = localStorage.getItem(storageKey)
    if (savedData) {
      const chatData = JSON.parse(savedData)
      // 只加载最近24小时内的聊天记录
      const savedTime = new Date(chatData.timestamp)
      const now = new Date()
      const hoursDiff = (now - savedTime) / (1000 * 60 * 60)
      
      if (hoursDiff <= 24 && chatData.messages) {
        messages.value = chatData.messages
        // 如果有保存的内容选择，尝试恢复
        if (chatData.contentId && courseContents.value.length > 0) {
          const savedContent = courseContents.value.find(c => c.id === chatData.contentId)
          if (savedContent) {
            selectedContent.value = savedContent
          }
        }
        return true
      }
    }
  } catch (error) {
    console.warn('Failed to load chat history:', error)
  }
  return false
}

// 清除聊天历史
const clearChatHistory = () => {
  try {
    const storageKey = getChatStorageKey()
    localStorage.removeItem(storageKey)
  } catch (error) {
    console.warn('Failed to clear chat history:', error)
  }
}

const loadCourses = async () => {
  try {
    const response = await getStudentCourses()
    // 处理响应数据，可能是数组或包含records的对象
    if (Array.isArray(response.data)) {
      courses.value = response.data
    } else if (response.data.records) {
      courses.value = response.data.records
    } else {
      courses.value = response.data || []
    }
    
    // 如果URL中有courseId参数，自动选择该课程
    const courseId = route.query.courseId
    if (courseId) {
      selectedCourseId.value = parseInt(courseId)
      await loadCourseContent()
    }
  } catch (error) {
    console.error('Failed to load courses:', error)
    ElMessage.error('加载课程失败')
  }
}

const loadCourseContent = async () => {
  if (!selectedCourseId.value) return
  
  // 检查选择的课程是否已结束
  const selectedCourse = courses.value.find(course => course.id === selectedCourseId.value)
  if (selectedCourse && selectedCourse.status === 0) {
    ElMessage.warning('该课程已结束，无法进行学习')
    selectedCourseId.value = ''
    return
  }
  
  loadingContent.value = true
  try {
    const response = await getCourseContents(selectedCourseId.value)
    courseContents.value = response.data
    
    // 课程内容加载完成后，尝试加载聊天历史
    setTimeout(() => {
      loadChatHistory()
    }, 100)
  } catch (error) {
    console.error('Failed to load course content:', error)
    ElMessage.error('加载课程内容失败')
  } finally {
    loadingContent.value = false
  }
}

const selectContent = (content) => {
  // 如果选择了不同的教学内容，清空聊天记录
  if (selectedContent.value && selectedContent.value.id !== content.id) {
    messages.value = []
    clearChatHistory()
  }
  
  selectedContent.value = content
  ElMessage.success(`已选择内容：${content.title}`)
}

const sendMessage = async () => {
  if (!inputMessage.value.trim()) return
  
  // 检查是否选择了课程且课程状态正常
  if (selectedCourseId.value) {
    const selectedCourse = courses.value.find(course => course.id === selectedCourseId.value)
    if (selectedCourse && selectedCourse.status === 0) {
      ElMessage.warning('该课程已结束，无法提问')
      return
    }
  }
  
  // 检查是否选择了教学内容
  if (!selectedContent.value) {
    ElMessage.warning('请先选择左侧的教学内容，然后再提问')
    return
  }
  
  const userMessage = {
    type: 'user',
    content: inputMessage.value.trim(),
    timestamp: new Date()
  }
  
  messages.value.push(userMessage)
  
  // 添加AI回复占位符
  const aiMessage = {
    type: 'ai',
    content: '',
    loading: true,
    timestamp: new Date()
  }
  messages.value.push(aiMessage)
  
  // 强制触发响应式更新
  await nextTick()
  
  const question = inputMessage.value.trim()
  inputMessage.value = ''
  
  // 滚动到底部
  scrollToBottom()
  
  sending.value = true
  try {
    console.log('发送AI问题:', {
      questionContent: question,
      courseId: selectedCourseId.value,
      questionType: 1,
      contextInfo: selectedContent.value?.content || selectedContent.value?.description
    })
    
    const response = await askQuestion({
      questionContent: question,
      courseId: selectedCourseId.value,
      questionType: 1, // 1-知识询问
      contextInfo: selectedContent.value?.content || selectedContent.value?.description
    })
    
    console.log('AI响应:', response)
    
    // 更新AI回复
    const messageIndex = messages.value.length - 1
    const currentMessage = messages.value[messageIndex]
    
    // 处理AI回复内容
    let aiContent = ''
    if (response.data && response.data.answerContent) {
      aiContent = response.data.answerContent
    } else if (typeof response.data === 'string') {
      aiContent = response.data
    } else {
      aiContent = response.data || '抱歉，AI助手没有返回有效回复。'
    }
    
    // 使用Vue的响应式更新 - 替换整个数组来强制更新
    const updatedMessages = [...messages.value]
    updatedMessages[messageIndex] = {
      ...currentMessage,
      loading: false,
      content: aiContent
    }
    messages.value = updatedMessages
    
    // 强制触发响应式更新和调试
    console.log('更新后的消息列表:', messages.value)
    console.log('更新的消息内容:', aiContent)
    
    await nextTick()
    scrollToBottom()
    
    // 保存聊天历史
    saveChatHistory()
  } catch (error) {
    console.error('Failed to ask question:', error)
    
    // 更新错误消息 - 替换整个数组来强制更新
    const messageIndex = messages.value.length - 1
    const currentMessage = messages.value[messageIndex]
    
    const updatedMessages = [...messages.value]
    updatedMessages[messageIndex] = {
      ...currentMessage,
      loading: false,
      content: '抱歉，AI助手暂时无法回答您的问题，请稍后重试。'
    }
    messages.value = updatedMessages
    
    // 强制触发响应式更新
    await nextTick()
    scrollToBottom()
    
    ElMessage.error('提问失败')
    
    // 即使出错也保存聊天历史
    saveChatHistory()
  } finally {
    sending.value = false
  }
}

const clearChat = () => {
  messages.value = []
  clearChatHistory()
  ElMessage.success('对话已清空')
}

const scrollToBottom = () => {
  nextTick(() => {
    if (chatScrollbar.value) {
      try {
        // 尝试多种方式获取滚动元素
        const scrollbar = chatScrollbar.value
        let scrollElement = null
        
        if (scrollbar.$refs && scrollbar.$refs.wrap$) {
          scrollElement = scrollbar.$refs.wrap$
        } else if (scrollbar.$refs && scrollbar.$refs.wrap) {
          scrollElement = scrollbar.$refs.wrap
        } else if (scrollbar.wrapRef) {
          scrollElement = scrollbar.wrapRef
        }
        
        if (scrollElement) {
          scrollElement.scrollTop = scrollElement.scrollHeight
        } else {
          // 备用方案：直接查找滚动容器
          const chatContainer = document.querySelector('.chat-messages')
          if (chatContainer && chatContainer.parentElement) {
            chatContainer.parentElement.scrollTop = chatContainer.parentElement.scrollHeight
          }
        }
      } catch (error) {
        console.warn('Failed to scroll to bottom:', error)
      }
    }
  })
}

const getContentTypeText = (type) => {
  const types = {
    'COURSEWARE': '课件',
    'VIDEO': '视频',
    'DOCUMENT': '文档',
    'EXERCISE': '练习'
  }
  return types[type] || type
}

const getContentTypeColor = (type) => {
  const colors = {
    'COURSEWARE': 'primary',
    'VIDEO': 'success',
    'DOCUMENT': 'info',
    'EXERCISE': 'warning'
  }
  return colors[type] || ''
}

const formatTime = (date) => {
  return new Date(date).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getInputPlaceholder = () => {
  if (activeCourses.value.length === 0) {
    return '暂无进行中的课程，无法提问'
  }
  if (!selectedCourseId.value) {
    return '请先选择课程'
  }
  if (courseContents.value.length === 0) {
    return '该课程暂无教学内容，无法提问'
  }
  if (!selectedContent.value) {
    return '请先选择左侧的教学内容，然后再提问'
  }
  return '请输入您的问题...'
}

const getSendButtonText = () => {
  if (sending.value) return '发送中...'
  if (activeCourses.value.length === 0) return '暂无可用课程'
  if (!selectedCourseId.value) return '请选择课程'
  if (courseContents.value.length === 0) return '暂无教学内容'
  if (!selectedContent.value) return '请选择内容'
  return '发送'
}

onMounted(() => {
  loadCourses()
  // 如果没有选择课程，尝试加载通用聊天历史
  if (!selectedCourseId.value) {
    setTimeout(() => {
      loadChatHistory()
    }, 500)
  }
})
</script>

<style scoped>
.student-learning {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.content-card,
.chat-card {
  height: calc(100vh - 200px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-item {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.content-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.content-item.active {
  border-color: #409eff;
  background-color: #409eff;
  color: white;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.content-header h4 {
  margin: 0;
  font-size: 14px;
  flex: 1;
}

.content-description {
  margin: 8px 0;
  font-size: 12px;
  opacity: 0.8;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  opacity: 0.7;
}

.chat-card {
  height: 800px;
  display: flex;
  flex-direction: column;
}

.chat-card .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.chat-messages {
  padding: 20px;
}

.welcome-message {
  display: flex;
  gap: 15px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 12px;
  margin-bottom: 20px;
}

.ai-avatar {
  width: 40px;
  height: 40px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.welcome-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.welcome-content p {
  margin: 8px 0;
  color: #606266;
}

.welcome-content ul {
  margin: 10px 0;
  padding-left: 20px;
  color: #606266;
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-item.user .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
}

.message-item.user .message-bubble {
  background: #409eff;
  color: white;
}

.message-item.ai .message-bubble {
  background: #f5f7fa;
  color: #303133;
}

.message-text {
  white-space: pre-wrap;
  line-height: 1.6;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.chat-input {
  border-top: 1px solid #e4e7ed;
  padding: 20px;
  background: #fff;
  flex-shrink: 0;
  min-height: 140px;
  display: flex;
  flex-direction: column;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  flex-wrap: wrap;
  gap: 10px;
  min-height: 40px;
  flex-shrink: 0;
}

.input-tips {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
  flex-wrap: wrap;
  flex: 1;
}

.input-tips span {
  white-space: nowrap;
}

.loading-container,
.empty-content {
  padding: 20px;
  text-align: center;
}

/* 响应式布局优化 */
@media (max-width: 1200px) {
  .chat-card {
    height: 600px;
  }
  
  .chat-container .el-scrollbar {
    height: 380px !important;
  }
}

@media (max-width: 768px) {
  .input-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .input-tips {
    justify-content: center;
    order: 2;
  }
  
  .input-actions .el-button {
    order: 1;
    align-self: center;
  }
}

/* Markdown内容样式 */
.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 { font-size: 1.5em; border-bottom: 1px solid #e1e4e8; padding-bottom: 8px; }
.markdown-content h2 { font-size: 1.3em; border-bottom: 1px solid #e1e4e8; padding-bottom: 6px; }
.markdown-content h3 { font-size: 1.2em; }
.markdown-content h4 { font-size: 1.1em; }
.markdown-content h5 { font-size: 1em; }
.markdown-content h6 { font-size: 0.9em; color: #6a737d; }

.markdown-content p {
  margin: 8px 0;
  line-height: 1.6;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-content li {
  margin: 4px 0;
  line-height: 1.5;
}

.markdown-content blockquote {
  margin: 8px 0;
  padding: 0 16px;
  border-left: 4px solid #dfe2e5;
  background-color: #f6f8fa;
  color: #6a737d;
}

.markdown-content code {
  padding: 2px 4px;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  margin: 12px 0;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
  line-height: 1.45;
}

.markdown-content pre code {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  font-size: 0.85em;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 12px 0;
  width: 100%;
}

.markdown-content th,
.markdown-content td {
  padding: 6px 12px;
  border: 1px solid #dfe2e5;
  text-align: left;
}

.markdown-content th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content hr {
  margin: 16px 0;
  border: none;
  border-top: 1px solid #e1e4e8;
}
</style>