<template>
  <div class="login-container">
<div class="login-left">
      <div class="stars"></div>
      <div class="twinkling"></div>
      <div class="shooting-star"></div>
      <div class="logo">
        <img src="@/assets/logo.png" alt="Logo" class="logo-img" />
      </div>
      <div class="slogan">
          <h1>{{ t('auth.common.slogan') }}</h1>
        <h3>{{ t('auth.common.subSlogan') }}</h3>
      </div>
      <div class="space-scene">
        <img src="@/assets/astronaut.png" alt="Astronaut" class="astronaut" />
        <img src="@/assets/earth.png" alt="Earth" class="earth" />
      </div>
    </div>
    <div class="login-right">
      <div class="language-selector">
        <el-dropdown @command="changeLanguage">
          <span class="language-text">
            {{ t('auth.common.language') }} <i class="el-icon-arrow-down"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh">简体中文</el-dropdown-item>
              <el-dropdown-item command="en">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <div class="login-form-container">
        <h2 class="form-title">{{ t('auth.login.title') }}</h2>

        <!-- 社交登录已移除 -->

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              :placeholder="t('auth.login.username')"
              size="large"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              :placeholder="t('auth.login.password')"
              size="large"
              show-password
              class="custom-input"
            />
          </el-form-item>

          <!-- 验证码输入框 -->
          <el-form-item prop="captcha" v-if="captchaEnabled">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                :placeholder="t('auth.login.captcha')"
                size="large"
                class="captcha-input"
                :maxlength="captchaLen"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                <div v-else class="captcha-loading">加载中...</div>
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              class="login-button"
              @click="handleLogin"
            >
              {{ loading ? t('auth.login.loginLoading') : t('auth.login.loginButton') }}
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <span>{{ t('auth.login.noAccount') }}</span>
          <el-link type="primary" @click="$router.push('/register')">
            {{ t('auth.login.register') }}
          </el-link>
        </div>

        <div class="demo-accounts" v-if="showDemoAccounts">
          <h4>{{ t('auth.login.demoAccounts') }}</h4>
          <div class="demo-item" v-for="(item, idx) in demoAccounts" :key="idx" @click="applyDemo(item)">
            <el-tag :type="item.tagType || 'info'">{{ item.label || 'Demo' }}</el-tag>
            <span>{{ item.username }} / {{ item.password }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { getCaptcha, isCaptchaEnabled } from '@/api/captcha'
import { getLoginDemoConfig } from '@/api/auth'

const applyDemo = (item) => {
  loginForm.username = item.username
  loginForm.password = item.password
}


const router = useRouter()
const userStore = useUserStore()
const { t, locale } = useI18n()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  captcha: ''
})

const captchaEnabled = ref(true)
const captchaImage = ref('')
const captchaLen = ref(4)

const sessionId = ref('')
const showDemoAccounts = ref(true)
const demoAccounts = ref([])


const loginRules = {
  username: [
    { required: true, message: t('auth.login.usernameRequired'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('auth.login.passwordRequired'), trigger: 'blur' },
    { min: 3, message: t('auth.login.passwordLength'), trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: t('auth.login.captchaRequired'), trigger: 'blur' },
    { validator: (_, value, cb) => {
        if (!value || value.length !== captchaLen.value) {
          cb(new Error(t('auth.login.captchaLength', { n: captchaLen.value })))
        } else {
          cb()
        }
      }, trigger: 'blur' }
  ]
}

const changeLanguage = (lang) => {
  locale.value = lang
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const loginData = {
          username: loginForm.username,
          password: loginForm.password
        }

        // 如果启用了验证码，添加验证码信息
        if (captchaEnabled.value) {
          loginData.captcha = loginForm.captcha
          loginData.sessionId = sessionId.value
        }

        await userStore.login(loginData)
        ElMessage.success(t('auth.login.loginSuccess'))

        // 根据用户角色跳转到对应页面
        const defaultRoute = userStore.getDefaultRoute()
        router.push(defaultRoute)
      } catch (error) {
        console.error('Login failed:', error)
        // 如果登录失败且启用了验证码，刷新验证码
        if (captchaEnabled.value) {
          refreshCaptcha()
        }
      } finally {
        loading.value = false
      }
    }
  })
}

const fillDemoAccount = (type) => {
  switch (type) {
    case 'admin':
      loginForm.username = 'admin'
      loginForm.password = 'admin123'
      break
    case 'teacher':
      loginForm.username = 'teacher001'
      loginForm.password = 'admin123'
      break
    case 'student':
      loginForm.username = 'student001'
      loginForm.password = 'admin123'
      break
  }
}

// 检查验证码是否启用
const checkCaptchaStatus = async () => {
  try {
    const response = await isCaptchaEnabled()
    captchaEnabled.value = response.data.enabled
    if (captchaEnabled.value) {
      await loadCaptcha()
    }
  } catch (error) {
    console.error('检查验证码状态失败:', error)
  }
}

// 加载验证码
const loadCaptcha = async () => {
  try {
    // 生成新的sessionId
    const newSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    const response = await getCaptcha(newSessionId)
    captchaImage.value = response.data.image
    captchaLen.value = response.data.length || captchaLen.value
    sessionId.value = newSessionId
  } catch (error) {
    console.error('加载验证码失败:', error)
    ElMessage.error('验证码加载失败')
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  loginForm.captcha = ''
  loadCaptcha()
}

// 页面加载时检查验证码状态
  // 获取登录页演示账号配置
  const loadLoginDemoConfig = async () => {
    try {
      const res = await getLoginDemoConfig()
      showDemoAccounts.value = !!res.data?.showDemoAccounts
      const demo = res.data?.demoAccounts
      if (demo) {
        // 后端返回字符串，尝试解析
        try { demoAccounts.value = JSON.parse(demo) } catch { demoAccounts.value = [] }
      }
    } catch (e) {
      console.error('获取登录页演示账号配置失败:', e)
      showDemoAccounts.value = true // 失败时回退为显示
    }
  }

  // 页面加载时检查验证码状态与演示账号配置
  onMounted(() => {
    checkCaptchaStatus()
    loadLoginDemoConfig()
  })
</script>

<style scoped>
.login-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.login-left {
  flex: 1;
  background: #000;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 40px;
  color: white;
  overflow: hidden;
  box-shadow: inset 0 0 100px rgba(255, 255, 255, 0.2);
}

.stars {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: #000 url('@/assets/space-bg.png') no-repeat center center;
  background-size: cover;
  z-index: 0;
}

.twinkling {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.twinkling:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  box-shadow: 0 0 10px white, 0 0 15px white, 0 0 20px white;
}

.shooting-star {
  position: absolute;
  top: 50%;
  left: -100px;
  width: 100px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
  animation: shooting 5s linear infinite;
  z-index: 2;
  transform: rotate(-45deg);
}

.shooting-star:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 0 10px white, 0 0 20px white, 0 0 30px white;
}

@keyframes twinkle {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes shooting {
  0% {
    transform: translateX(0) translateY(0) rotate(-45deg);
    opacity: 1;
  }
  100% {
    transform: translateX(1000px) translateY(1000px) rotate(-45deg);
    opacity: 0;
  }
}

.logo {
  margin-bottom: 20px;
}

.logo-img {
  height: 40px;
}

.slogan {
  margin-top: 60px;
  z-index: 2;
}

.slogan h1 {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 10px;
}

.slogan h2 {
  font-size: 42px;
  font-weight: 700;
}

.space-scene {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.astronaut {
  position: absolute;
  width: 240px;
  top: 35%;
  right: 25%;
  animation: float 6s ease-in-out infinite;
  z-index: 2;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5));
}

.earth {
  position: absolute;
  width: 1200px;
  bottom: -30%;
  left: -20%;
  animation: rotate 60s linear infinite;
  z-index: 1;
  filter: drop-shadow(0 0 50px rgba(100, 200, 255, 0.5));
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-right {
  width: 600px;
  background: white;
  display: flex;
  flex-direction: column;
  position: relative;
}

.language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
}

.language-text {
  cursor: pointer;
  color: #606266;
}

.login-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px 40px;
  max-width: 450px;
  margin: 0 auto;
  width: 100%;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
}

.social-login {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.social-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 44px;
  border-radius: 8px;
  font-size: 14px;
}

.google-btn {
  border: 1px solid #dcdfe6;
  background-color: white;
  color: #606266;
}

.facebook-btn {
  border: 1px solid #dcdfe6;
  background-color: white;
  color: #606266;
}

.social-icon {
  width: 20px;
  height: 20px;
}

.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: #909399;
}

.divider:before,
.divider:after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ebeef5;
}

.divider-text {
  padding: 0 10px;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.custom-input {
  width: 100%;
}

.custom-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  height: 50px;
  width: 100%;
}

.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
  width: 100%;
}

.captcha-input {
  flex: 1;
  min-width: 0;
}

.captcha-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  height: 50px;
}

.captcha-image {
  width: 120px;
  height: 50px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  transition: all 0.3s;
  flex-shrink: 0; /* 防止验证码图片被压缩 */
}

.captcha-image:hover {
  border-color: #42b983;
  background-color: #f0f9ff;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.captcha-loading {
  font-size: 12px;
  color: #909399;
}

.login-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #42b983;
  border-color: #42b983;
  margin-top: 10px;
}

.login-button:hover {
  background-color: #36a070;
  border-color: #36a070;
}

.login-footer {
  text-align: center;
  color: #909399;
  font-size: 14px;
  margin-bottom: 30px;
}

.demo-accounts {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.demo-accounts h4 {
  text-align: center;
  color: #606266;
  margin-bottom: 15px;
  font-size: 14px;
}

.demo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.demo-item:hover {
  background-color: #f5f7fa;
}

.demo-item span {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-left {
    display: none;
  }

.login-right {
  width: 500px;
  background: white;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
}
}
</style>
