<template>
  <div class="register-container">
<div class="register-left">
      <div class="stars"></div>
      <div class="twinkling"></div>
      <div class="shooting-star"></div>
      <div class="logo">
        <img src="@/assets/logo.png" alt="Logo" class="logo-img" />
      </div>
      <div class="slogan">
        <h1>{{ t('auth.common.slogan') }}</h1>
        <h3>{{ t('auth.common.subSlogan') }}</h3>
      </div>
      <div class="space-scene">
        <img src="@/assets/astronaut.png" alt="Astronaut" class="astronaut" />
        <img src="@/assets/earth.png" alt="Earth" class="earth" />
      </div>
    </div>
    <div class="register-right">
      <div class="language-selector">
        <el-dropdown @command="changeLanguage">
          <span class="language-text">
            {{ t('auth.common.language') }} <i class="el-icon-arrow-down"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh">简体中文</el-dropdown-item>
              <el-dropdown-item command="en">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      
      <div class="register-form-container">
        <h2 class="form-title">{{ t('auth.register.title') }}</h2>
        
        <!-- 社交登录已移除 -->
        
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              :placeholder="t('auth.register.username')"
              size="large"
              class="custom-input"
            />
          </el-form-item>
          
          <el-form-item prop="realName">
            <el-input
              v-model="registerForm.realName"
              :placeholder="t('auth.register.realName')"
              size="large"
              class="custom-input"
            />
          </el-form-item>
          
          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              :placeholder="t('auth.register.email')"
              size="large"
              class="custom-input"
            />
          </el-form-item>
          
          <el-form-item prop="phone">
            <el-input
              v-model="registerForm.phone"
              :placeholder="t('auth.register.phone')"
              size="large"
              class="custom-input"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              :placeholder="t('auth.register.password')"
              size="large"
              show-password
              class="custom-input"
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              :placeholder="t('auth.register.confirmPassword')"
              size="large"
              show-password
              class="custom-input"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              class="register-button"
              @click="handleRegister"
            >
              {{ loading ? t('auth.register.registerLoading') : t('auth.register.registerButton') }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="register-footer">
          <span>{{ t('auth.register.hasAccount') }}</span>
          <el-link type="primary" @click="$router.push('/login')">
            {{ t('auth.register.login') }}
          </el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const userStore = useUserStore()
const { t, locale } = useI18n()

const registerFormRef = ref()
const loading = ref(false)

const registerForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error(t('auth.register.passwordMismatch')))
  } else {
    callback()
  }
}

const registerRules = {
  username: [
    { required: true, message: t('auth.register.usernameRequired'), trigger: 'blur' },
    { min: 3, max: 20, message: t('auth.register.usernameLength'), trigger: 'blur' }
  ],
  realName: [
    { required: true, message: t('auth.register.realNameRequired'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('auth.register.emailRequired'), trigger: 'blur' },
    { type: 'email', message: t('auth.register.emailInvalid'), trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: t('auth.register.phoneInvalid'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('auth.register.passwordRequired'), trigger: 'blur' },
    { min: 6, message: t('auth.register.passwordLength'), trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: t('auth.register.confirmPasswordRequired'), trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const changeLanguage = (lang) => {
  locale.value = lang
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await userStore.register(registerForm)
        ElMessage.success(t('auth.register.registerSuccess'))
        router.push('/login')
      } catch (error) {
        console.error('Register failed:', error)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.register-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.register-left {
  flex: 1;
  background: #000;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 40px;
  color: white;
  overflow: hidden;
  box-shadow: inset 0 0 100px rgba(255, 255, 255, 0.2);
}

.stars {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: #000 url('@/assets/space-bg.png') no-repeat center center;
  background-size: cover;
  z-index: 0;
}

.twinkling {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.twinkling:before {
  content: '';
  position: absolute;
  width: 3px;
  height: 3px;
  background: white;
  box-shadow: 
    20px 30px 2px 1px white,
    40px 80px 2px 1px white,
    80px 120px 1px 0px white,
    100px 50px 1px 0px white,
    150px 100px 2px 0px white,
    180px 180px 1px 0px white,
    220px 220px 1px 0px white,
    240px 50px 2px 1px white,
    280px 300px 2px 0px white,
    320px 120px 1px 0px white,
    340px 210px 2px 0px white,
    380px 320px 1px 1px white,
    400px 400px 2px 0px white,
    420px 30px 1px 0px white,
    440px 280px 1px 0px white,
    480px 180px 2px 1px white,
    520px 100px 1px 0px white,
    560px 330px 2px 0px white,
    600px 200px 1px 0px white,
    640px 120px 2px 0px white,
    680px 300px 1px 1px white,
    720px 220px 2px 0px white,
    760px 100px 1px 0px white,
    800px 380px 2px 1px white,
    840px 280px 1px 0px white,
    880px 180px 2px 0px white,
    920px 300px 1px 0px white,
    960px 100px 2px 1px white,
    1000px 220px 1px 0px white,
    1040px 380px 2px 0px white,
    1080px 120px 1px 1px white,
    1120px 290px 2px 0px white,
    1160px 200px 1px 0px white,
    1200px 80px 2px 1px white;
  animation: twinkle 10s ease-in-out infinite;
}

.twinkling:after {
  content: '';
  position: absolute;
  width: 2px;
  height: 2px;
  background: white;
  box-shadow: 
    50px 60px 1px 0px white,
    90px 150px 1px 0px white,
    130px 25px 1px 0px white,
    170px 280px 1px 0px white,
    210px 320px 1px 0px white,
    250px 70px 1px 0px white,
    290px 190px 1px 0px white,
    330px 230px 1px 0px white,
    370px 100px 1px 0px white,
    410px 350px 1px 0px white,
    450px 280px 1px 0px white,
    490px 120px 1px 0px white,
    530px 330px 1px 0px white,
    570px 50px 1px 0px white,
    610px 280px 1px 0px white,
    650px 210px 1px 0px white,
    690px 100px 1px 0px white,
    730px 250px 1px 0px white,
    770px 310px 1px 0px white,
    810px 180px 1px 0px white,
    850px 240px 1px 0px white,
    890px 90px 1px 0px white,
    930px 320px 1px 0px white,
    970px 270px 1px 0px white,
    1010px 130px 1px 0px white,
    1050px 300px 1px 0px white,
    1090px 220px 1px 0px white,
    1130px 80px 1px 0px white,
    1170px 350px 1px 0px white,
    1210px 260px 1px 0px white;
  animation: twinkle 8s ease-in-out infinite;
  animation-delay: 1s;
}

.shooting-star {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 150px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
  animation: shooting 5s linear infinite;
  z-index: 2;
  transform: rotate(135deg);
}

.shooting-star:after {
  content: '';
  position: absolute;
  top: -2px;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 0 10px white, 0 0 20px white, 0 0 30px white;
}

.shooting-star:before {
  content: '';
  position: absolute;
  top: 100px;
  right: -50px;
  width: 100px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
  transform: rotate(15deg);
  animation: shooting 7s linear infinite;
  animation-delay: 3s;
}

@keyframes twinkle {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

@keyframes shooting {
  0% { 
    transform: translateX(0) translateY(0) rotate(135deg);
    opacity: 1;
  }
  100% { 
    transform: translateX(-500px) translateY(500px) rotate(135deg);
    opacity: 0;
  }
}

.logo {
  margin-bottom: 20px;
}

.logo-img {
  height: 40px;
}

.slogan {
  margin-top: 60px;
  z-index: 2;
}

.slogan h1 {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 10px;
}

.slogan h2 {
  font-size: 42px;
  font-weight: 700;
}

.space-scene {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.astronaut {
  position: absolute;
  width: 240px;
  top: 35%;
  right: 25%;
  animation: float 6s ease-in-out infinite;
  z-index: 2;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.5));
}

.earth {
  position: absolute;
  width: 1200px;
  bottom: -30%;
  left: -20%;
  animation: rotate 60s linear infinite;
  z-index: 1;
  filter: drop-shadow(0 0 50px rgba(100, 200, 255, 0.5));
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.register-right {
  width: 500px;
  background: white;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
}

.language-selector {
  position: absolute;
  top: 20px;
  right: 20px;
}

.language-text {
  cursor: pointer;
  color: #606266;
}

.register-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px 40px;
  max-width: 450px;
  margin: 0 auto;
  width: 100%;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
}

.social-login {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.social-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 44px;
  border-radius: 8px;
  font-size: 14px;
}

.google-btn {
  border: 1px solid #dcdfe6;
  background-color: white;
  color: #606266;
}

.facebook-btn {
  border: 1px solid #dcdfe6;
  background-color: white;
  color: #606266;
}

.social-icon {
  width: 20px;
  height: 20px;
}

.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: #909399;
}

.divider:before,
.divider:after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ebeef5;
}

.divider-text {
  padding: 0 10px;
  font-size: 14px;
}

.register-form {
  margin-bottom: 20px;
}

.custom-input {
  width: 100%;
}

.custom-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  height: 50px;
  width: 100%;
}

.register-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #42b983;
  border-color: #42b983;
  margin-top: 10px;
}

.register-button:hover {
  background-color: #36a070;
  border-color: #36a070;
}

.register-footer {
  text-align: center;
  color: #909399;
  font-size: 14px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .register-container {
    flex-direction: column;
  }
  
  .register-left {
    display: none;
  }
  
  .register-right {
    width: 100%;
  }
  
  .register-form-container {
    padding: 30px 20px;
  }
}
</style>
