<template>
  <div class="dashboard-container">
    <!-- 3D Background Lines -->
    <div class="background-3d">
      <div class="grid-lines">
        <div class="grid-line horizontal" v-for="i in 8" :key="`h-${i}`" :style="{ top: `${i * 12.5}%` }"></div>
        <div class="grid-line vertical" v-for="i in 12" :key="`v-${i}`" :style="{ left: `${i * 8.33}%` }"></div>
      </div>
      <div class="floating-dots">
        <div class="dot" v-for="i in 15" :key="`dot-${i}`" :style="{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          animationDelay: `${Math.random() * 4}s`
        }"></div>
      </div>
      <div class="connection-lines">
        <svg class="connections-svg" viewBox="0 0 1920 1080">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:0.8" />
              <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:0.6" />
              <stop offset="100%" style="stop-color:#db2777;stop-opacity:0.4" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>

    <!-- Logo Header -->
    <div class="logo-header">
      <img src="@/asset/log_left_right.jpg" alt="AiEduForge Logo" class="main-logo" />
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <!-- Brand Section -->
        <div class="brand-section">
          <div class="brand-logo-3d">
            <div class="cube">
              <div class="cube-face front">
                <div class="face-content">
                  <div class="logo-symbol">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2.5" />
                      <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2.5" />
                      <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2.5" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="cube-face back">
                <div class="face-content">
                  <div class="brand-letter">Ai</div>

                </div>
              </div>
              <div class="cube-face left">
                <div class="face-content">
                  <div class="logo-symbol">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="currentColor" stroke-width="2.5" />
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="currentColor" stroke-width="2.5" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="cube-face right">
                <div class="face-content">
                  <div class="brand-text">Edu</div>
                </div>
              </div>
              <div class="cube-face top">
                <div class="face-content">
                  <div class="logo-symbol">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2.5" />
                      <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2.5" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="cube-face bottom">
                <div class="face-content">
                  <div class="brand-text-small">FORGE</div>
                </div>
              </div>
            </div>
          </div>
          <div class="brand-title-container">
            <h1 class="brand-title">
              <span class="brand-word ai-word">
                <span class="letter" data-letter="A">A</span>
                <span class="letter" data-letter="i">i</span>
              </span>
              <span class="brand-word edu-word">
                <span class="letter" data-letter="E">E</span>
                <span class="letter" data-letter="d">d</span>
                <span class="letter" data-letter="u">u</span>
              </span>
              <span class="brand-word forge-word">
                <span class="letter" data-letter="F">F</span>
                <span class="letter" data-letter="o">o</span>
                <span class="letter" data-letter="r">r</span>
                <span class="letter" data-letter="g">g</span>
                <span class="letter" data-letter="e">e</span>
              </span>
            </h1>
            <div class="brand-effects">
              <div class="glow-line"></div>
              <div class="particles-trail">
                <div class="particle" v-for="i in 20" :key="i"></div>
              </div>
            </div>
          </div>
          <p class="brand-subtitle">
            <span class="subtitle-word">基于</span>
            <span class="subtitle-highlight">AI</span>
            <span class="subtitle-word">的教学实训智能体软件</span>
          </p>
        </div>

        <!-- User Profile -->
        <div class="user-profile">
          <div class="profile-header">
            <el-button 
              type="text" 
              class="logout-button"
              @click="handleLogout"
              :icon="ArrowLeft"
            >
              返回登录
            </el-button>
          </div>
        
          <div class="avatar-container">
            <el-avatar :size="80" :src="userStore.userInfo?.avatar ? userStore.avatarUrl : undefined"
              class="user-avatar">
              <span v-if="!userStore.userInfo?.avatar" class="avatar-text">
                {{ userStore.userInfo?.realName?.charAt(0) }}
              </span>
            </el-avatar>
            <div class="status-dot"></div>
          </div>

          <div class="user-info">
            <h2 class="user-name">{{ userStore.userInfo?.realName || userStore.userInfo?.username }}</h2>
            <div class="user-role" :class="`role-${userStore.userInfo?.role?.toLowerCase()}`">
              <span class="role-text">{{ getRoleText(userStore.userInfo?.role) }}</span>
            </div>
          </div>
        </div>

        <!-- CTA Button -->
        <div class="cta-section">
          <button class="cta-button" @click="goToWorkspace">
            <span>进入工作台</span>
            <svg class="arrow-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="section-header">
        <h2 class="section-title">功能特色</h2>
        <p class="section-subtitle">探索AI驱动的教育解决方案</p>
      </div>

      <div class="features-grid">
        <div class="feature-card" v-if="userStore.isTeacher || userStore.isAdmin">
          <div class="feature-icon edit-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor"
                stroke-width="2" />
              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" />
            </svg>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">智能备课</h3>
            <p class="feature-description">AI辅助生成教学内容，提升备课效率</p>
          </div>
        </div>

        <div class="feature-card" v-if="userStore.isStudent || userStore.isAdmin">
          <div class="feature-icon reading-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" stroke="currentColor" stroke-width="2" />
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" stroke="currentColor" stroke-width="2" />
            </svg>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">在线学习</h3>
            <p class="feature-description">智能学习助手，个性化学习体验</p>
          </div>
        </div>

        <div class="feature-card" v-if="userStore.isStudent || userStore.isAdmin">
          <div class="feature-icon practice-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" />
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" stroke="currentColor" stroke-width="2" />
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" stroke="currentColor" stroke-width="2" />
            </svg>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">智能练习</h3>
            <p class="feature-description">自适应练习系统，精准评测反馈</p>
          </div>
        </div>

        <div class="feature-card" v-if="userStore.isAdmin">
          <div class="feature-icon analytics-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 3v18h18" stroke="currentColor" stroke-width="2" />
              <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke="currentColor" stroke-width="2" />
            </svg>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">数据分析</h3>
            <p class="feature-description">教学效果分析，数据驱动决策</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { onMounted, onUnmounted } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const getRoleText = (role) => {
  const roleTexts = {
    'ADMIN': '管理员',
    'TEACHER': '教师',
    'STUDENT': '学生'
  }
  return roleTexts[role] || '用户'
}

const goToWorkspace = () => {
  const workspaceRoute = userStore.getWorkspaceRoute()
  router.push(workspaceRoute)
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要返回登录页面吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 清除用户信息
    userStore.logout()
    
    // 跳转到登录页
    router.push('/login')
    
    ElMessage.success('已退出登录')
  } catch (error) {
    // 用户取消操作
  }
}

// 动态连接线
let animationId = null
const createDynamicConnections = () => {
  const svg = document.querySelector('.connections-svg')
  if (!svg) return

  const connections = []
  const points = []

  // 创建随机点
  for (let i = 0; i < 8; i++) {
    points.push({
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      vx: (Math.random() - 0.5) * 0.3,
      vy: (Math.random() - 0.5) * 0.3
    })
  }

  // 创建连接线
  for (let i = 0; i < points.length; i++) {
    for (let j = i + 1; j < points.length; j++) {
      if (Math.random() < 0.4) { // 40%的概率创建连接
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
        line.setAttribute('stroke', 'url(#lineGradient)')
        line.setAttribute('stroke-width', '2')
        line.setAttribute('opacity', '0.5')
        line.style.filter = 'blur(0.3px)'
        svg.appendChild(line)
        connections.push({ line, p1: i, p2: j })
      }
    }
  }

  const animate = () => {
    // 更新点的位置
    points.forEach(point => {
      point.x += point.vx
      point.y += point.vy

      // 边界反弹
      if (point.x <= 0 || point.x >= window.innerWidth) point.vx *= -1
      if (point.y <= 0 || point.y >= window.innerHeight) point.vy *= -1
    })

    // 更新连接线
    connections.forEach(({ line, p1, p2 }) => {
      const point1 = points[p1]
      const point2 = points[p2]

      line.setAttribute('x1', point1.x)
      line.setAttribute('y1', point1.y)
      line.setAttribute('x2', point2.x)
      line.setAttribute('y2', point2.y)

      // 根据距离调整透明度
      const distance = Math.sqrt(
        Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2)
      )
      const maxDistance = 300
      const opacity = Math.max(0.2, Math.min(0.7, 1 - distance / maxDistance))
      line.setAttribute('opacity', opacity)
    })

    animationId = requestAnimationFrame(animate)
  }

  animate()
}

onMounted(() => {
  setTimeout(() => {
    createDynamicConnections()
    initBrandAnimations()
  }, 100)
})

// 品牌标题动画增强
const initBrandAnimations = () => {
  const letters = document.querySelectorAll('.letter')

  // 为每个字母添加随机延迟的动画
  letters.forEach((letter, index) => {
    const delay = Math.random() * 2
    letter.style.animationDelay = `${delay}s`

    // 添加鼠标悬停效果
    letter.addEventListener('mouseenter', () => {
      letter.style.transform = 'translateY(-10px) scale(1.2) rotateZ(5deg)'
      letter.style.filter = 'drop-shadow(0 15px 30px rgba(99, 102, 241, 0.6))'
    })

    letter.addEventListener('mouseleave', () => {
      letter.style.transform = ''
      letter.style.filter = ''
    })
  })

  // 粒子轨迹随机化
  const particles = document.querySelectorAll('.particles-trail .particle')
  particles.forEach((particle, index) => {
    const randomDelay = Math.random() * 5
    const randomTop = 30 + Math.random() * 40 // 30% to 70%
    particle.style.animationDelay = `-${randomDelay}s`
    particle.style.top = `${randomTop}%`
  })
}

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})


</script>

<style scoped>
/* 全局容器 */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

/* Logo Header */
.logo-header {
  position: relative;
  z-index: 10;
  padding: 2rem 0 1rem;
  text-align: left;
  
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.main-logo {
  height: 60px;
  width: auto;
  max-width: 300px;
  object-fit: contain;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.main-logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.15));
}

/* 3D Background */
.background-3d {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.grid-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.2;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.4), transparent);
  animation: gridPulse 8s ease-in-out infinite;
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
}

.grid-line.vertical {
  width: 1px;
  height: 100%;
}

.grid-line:nth-child(odd) {
  animation-delay: 0s;
}

.grid-line:nth-child(even) {
  animation-delay: 2s;
}

@keyframes gridPulse {

  0%,
  100% {
    opacity: 0.15;
  }

  50% {
    opacity: 0.35;
  }
}

.floating-dots {
  position: absolute;
  width: 100%;
  height: 100%;
}

.dot {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.8) 0%, rgba(124, 58, 237, 0.4) 100%);
  border-radius: 50%;
  animation: dotFloat 8s ease-in-out infinite;
  box-shadow: 0 0 12px rgba(79, 70, 229, 0.5);
}

@keyframes dotFloat {

  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }

  33% {
    transform: translateY(-15px) scale(1.1);
    opacity: 0.9;
  }

  66% {
    transform: translateY(10px) scale(0.9);
    opacity: 0.7;
  }
}

.connection-lines {
  position: absolute;
  width: 100%;
  height: 100%;
}

.connections-svg {
  width: 100%;
  height: 100%;
  opacity: 0.6;
  filter: blur(0.3px);
}

/* Hero Section */
.hero-section {
  min-height: calc(100vh - 120px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  width: 100%;
}

/* Brand Section */
.brand-section {
  margin-bottom: 4rem;
}

.brand-logo-3d {
  width: 100px;
  height: 100px;
  margin: 0 auto 2rem;
  perspective: 1000px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cube {
  width: 80px;
  height: 80px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotateCube 8s infinite linear;
}

.cube-face {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.face-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: white;
  position: relative;
}

.logo-symbol svg {
  width: 28px;
  height: 28px;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
}

.brand-letter {
  font-size: 20px;
  font-weight: 900;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
  position: relative;
}

.brand-letter-small {
  font-size: 12px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
  position: absolute;
  top: 2px;
  right: -8px;
}

.brand-text {
  font-size: 11px;
  font-weight: 800;
  letter-spacing: 1px;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
  text-align: center;
}

.brand-text-small {
  font-size: 7px;
  font-weight: 800;
  letter-spacing: 0.8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
  text-align: center;
}

.cube-face.front {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  transform: rotateY(0deg) translateZ(40px);
}

.cube-face.back {
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  transform: rotateY(180deg) translateZ(40px);
}

.cube-face.left {
  background: linear-gradient(135deg, #ec4899, #f97316);
  transform: rotateY(-90deg) translateZ(40px);
}

.cube-face.right {
  background: linear-gradient(135deg, #f97316, #eab308);
  transform: rotateY(90deg) translateZ(40px);
}

.cube-face.top {
  background: linear-gradient(135deg, #eab308, #10b981);
  transform: rotateX(90deg) translateZ(40px);
}

.cube-face.bottom {
  background: linear-gradient(135deg, #10b981, #6366f1);
  transform: rotateX(-90deg) translateZ(40px);
}

@keyframes rotateCube {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }

  100% {
    transform: rotateX(360deg) rotateY(360deg);
  }
}

.brand-title-container {
  position: relative;
  margin-bottom: 2rem;
}

.brand-title {
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
}

.brand-word {
  display: inline-flex;
  position: relative;
}

.letter {
  display: inline-block;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: default;
}

.ai-word {
  font-family: 'Orbitron', 'Courier New', monospace;
  font-weight: 800;
  letter-spacing: 0.1em;
  transform: skew(-5deg);
}

.ai-word .letter {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: aiPulse 3s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
  position: relative;
}

.ai-word .letter::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  animation: aiScan 2s linear infinite;
  pointer-events: none;
}

.edu-word {
  font-family: 'Orbitron', 'Courier New', monospace;
  font-weight: 800;
  letter-spacing: 0.1em;
}

.edu-word .letter {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: eduGlow 3s ease-in-out infinite;
  position: relative;
}

.edu-word .letter:nth-child(1) {
  font-size: 1.1em;
  transform: translateY(-5px);
}

.edu-word .letter:nth-child(2) {
  font-size: 0.9em;
  transform: translateY(3px);
}

.edu-word .letter:nth-child(3) {
  font-size: 1.05em;
  transform: translateY(-2px);
}

.forge-word {
  font-family: 'Orbitron', 'Courier New', monospace;
  font-weight: 800;
  letter-spacing: 0.1em;
}

.forge-word .letter {
  background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: forgeShine 3s ease-in-out infinite;
  position: relative;
  display: inline-block;
}

.forge-word .letter::before {
  content: attr(data-letter);
  position: absolute;
  top: 2px;
  left: 2px;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.3) 0%, rgba(249, 115, 22, 0.3) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  z-index: -1;
  filter: blur(1px);
}

.forge-word .letter:nth-child(1) {
  animation-delay: 0s;
}

.forge-word .letter:nth-child(2) {
  animation-delay: 0.1s;
}

.forge-word .letter:nth-child(3) {
  animation-delay: 0.2s;
}

.forge-word .letter:nth-child(4) {
  animation-delay: 0.3s;
}

.forge-word .letter:nth-child(5) {
  animation-delay: 0.4s;
}

.letter::before {
  content: attr(data-letter);
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: blur(1px);
}

.letter:hover::before {
  opacity: 1;
  transform: translateY(0);
}

.letter:hover {
  transform: translateY(-5px) scale(1.1);
  filter: drop-shadow(0 10px 20px rgba(99, 102, 241, 0.4));
}

@keyframes aiPulse {

  0%,
  100% {
    filter: brightness(1) drop-shadow(0 0 10px rgba(99, 102, 241, 0.3));
    transform: scale(1);
  }

  50% {
    filter: brightness(1.2) drop-shadow(0 0 20px rgba(99, 102, 241, 0.6));
    transform: scale(1.05);
  }
}

@keyframes eduGlow {

  0%,
  100% {
    filter: brightness(1) drop-shadow(0 0 10px rgba(139, 92, 246, 0.3));
  }

  50% {
    filter: brightness(1.3) drop-shadow(0 0 25px rgba(139, 92, 246, 0.7));
  }
}

@keyframes forgeShine {

  0%,
  100% {
    filter: brightness(1) drop-shadow(0 0 10px rgba(236, 72, 153, 0.3));
  }

  50% {
    filter: brightness(1.4) drop-shadow(0 0 30px rgba(236, 72, 153, 0.8));
  }
}

.brand-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.glow-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(99, 102, 241, 0.6) 20%,
      rgba(139, 92, 246, 0.8) 50%,
      rgba(236, 72, 153, 0.6) 80%,
      transparent 100%);
  transform: translateY(-50%) scaleX(0);
  animation: lineExpand 4s ease-in-out infinite;
}

@keyframes lineExpand {

  0%,
  100% {
    transform: translateY(-50%) scaleX(0);
    opacity: 0;
  }

  50% {
    transform: translateY(-50%) scaleX(1);
    opacity: 1;
  }
}

.particles-trail {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particles-trail .particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: particleTrail 5s linear infinite;
}

.particles-trail .particle:nth-child(odd) {
  background: radial-gradient(circle, rgba(139, 92, 246, 0.8) 0%, transparent 70%);
  animation-delay: -1s;
}

.particles-trail .particle:nth-child(3n) {
  background: radial-gradient(circle, rgba(236, 72, 153, 0.8) 0%, transparent 70%);
  animation-delay: -2s;
}

@keyframes particleTrail {
  0% {
    left: -10px;
    top: 50%;
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }

  10% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }

  90% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }

  100% {
    left: calc(100% + 10px);
    top: 50%;
    opacity: 0;
    transform: translateY(-50%) scale(0);
  }
}

.brand-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  animation: subtitleFade 2s ease-out 0.5s both;
}

.subtitle-word {
  color: #64748b;
  transition: color 0.3s ease;
}

.subtitle-highlight {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  position: relative;
  animation: highlightPulse 2s ease-in-out infinite;
}

.subtitle-highlight::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  transform: scaleX(0);
  animation: underlineExpand 2s ease-in-out infinite;
}

@keyframes subtitleFade {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes highlightPulse {

  0%,
  100% {
    filter: brightness(1);
  }

  50% {
    filter: brightness(1.3) drop-shadow(0 0 10px rgba(99, 102, 241, 0.5));
  }
}

@keyframes underlineExpand {

  0%,
  100% {
    transform: scaleX(0);
  }

  50% {
    transform: scaleX(1);
  }
}

/* User Profile */
.user-profile {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin: 2.5rem 0;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(99, 102, 241, 0.1);
  position: relative;
}

.profile-header {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

.logout-button {
  color: #64748b;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(100, 116, 139, 0.05);
  border: 1px solid rgba(100, 116, 139, 0.1);
}

.logout-button:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
  transform: translateX(-2px);
}

.logout-button .el-icon {
  margin-right: 0.25rem;
}

.profile-logo {
  position: absolute;
  top: 1rem;
  right: 1rem;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.profile-logo:hover {
  opacity: 1;
}

.profile-brand-logo {
  height: 40px;
  width: auto;
  object-fit: contain;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.user-avatar {
  border: 4px solid rgba(99, 102, 241, 0.2);
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.2);
}

.avatar-text {
  font-size: 2rem;
  font-weight: 600;
  color: white;
}

.status-dot {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  background: #10b981;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
}

.user-info {
  text-align: center;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.user-role {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.role-admin {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.role-teacher {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.role-student {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

/* CTA Section */
.cta-section {
  margin-top: 2rem;
}

.cta-button {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 14px;
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.4);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 50px rgba(99, 102, 241, 0.5);
}

.arrow-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.cta-button:hover .arrow-icon {
  transform: translateX(4px);
}

/* Features Section */
.features-section {
  padding: 4rem 2rem;
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.section-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 240px));
  gap: 1.25rem;
  margin-top: 3rem;
  justify-content: center;
}

.feature-card {
  background: white;
  border-radius: 16px;
  padding: 1.25rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(99, 102, 241, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
}

.feature-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

.edit-icon {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.reading-icon {
  background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
}

.practice-icon {
  background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
}

.analytics-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.feature-description {
  color: #64748b;
  line-height: 1.4;
  font-size: 0.8rem;
}



/* 响应式设计 */
@media (max-width: 1024px) {
  .brand-title {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 220px));
    gap: 1rem;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .logo-header {
    padding: 1.5rem 0 0.5rem;
  }
  
  .main-logo {
    height: 50px;
    max-width: 250px;
  }
  
  .hero-section {
    min-height: auto;
    padding: 2rem 1rem;
  }
  
  .profile-brand-logo {
    height: 32px;
  }

  .brand-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 0.2rem;
  }

  .brand-word {
    justify-content: center;
  }

  .brand-subtitle {
    font-size: 1rem;
    flex-direction: column;
    gap: 0.2rem;
    text-align: center;
  }

  .particles-trail .particle {
    display: none;
    /* 在移动设备上隐藏粒子效果以提高性能 */
  }
}

.user-profile {
  padding: 2rem 1.5rem;
  margin: 2rem 0;
}

.features-section {
  padding: 4rem 1rem;
}

.section-title {
  font-size: 2rem;
}

.features-grid {
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
}

.feature-card {
  padding: 1rem;
  flex-direction: column;
  text-align: center;
  gap: 0.5rem;
}


@media (max-width: 480px) {
  .logo-header {
    padding: 1rem 0 0.5rem;
  }
  
  .main-logo {
    height: 40px;
    max-width: 200px;
  }
  
  .brand-title {
    font-size: 2rem;
  }

  .letter:hover {
    transform: translateY(-3px) scale(1.05);
    /* 减少移动设备上的动画幅度 */
  }
  
  .profile-brand-logo {
    height: 28px;
  }

  .user-profile {
    padding: 1.5rem;
  }

  .cta-button {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
</style>