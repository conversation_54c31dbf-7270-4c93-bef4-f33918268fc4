<template>
  <div class="notification-center">
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="goBack"
          style="margin-right: 16px;"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h2>通知中心</h2>
      </div>
      <div class="header-actions">
        <el-button 
          v-if="unreadCount > 0"
          type="primary" 
          @click="markAllAsRead"
          :loading="markingAll"
        >
          全部标记为已读
        </el-button>
      </div>
    </div>

    <el-card>
      <!-- 统计信息 -->
      <div class="stats-bar">
        <div class="stat-item">
          <span class="stat-label">全部通知</span>
          <span class="stat-value">{{ pagination.total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">未读通知</span>
          <span class="stat-value unread">{{ unreadCount }}</span>
        </div>
      </div>

      <!-- 筛选器 -->
      <div class="filter-bar">
        <el-form :model="queryForm" inline>
          <el-form-item label="通知类型">
            <el-select v-model="queryForm.type" placeholder="全部类型" clearable>
              <el-option label="系统通知" :value="1" />
              <el-option label="课程通知" :value="2" />
              <el-option label="作业通知" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="阅读状态">
            <el-select v-model="queryForm.isRead" placeholder="全部状态" clearable>
              <el-option label="未读" :value="0" />
              <el-option label="已读" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级">
            <el-select v-model="queryForm.priority" placeholder="全部优先级" clearable>
              <el-option label="普通" :value="1" />
              <el-option label="重要" :value="2" />
              <el-option label="紧急" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchNotifications">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 通知列表 -->
      <div class="notification-list" v-loading="loading">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.is_read }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-indicator">
            <div v-if="!notification.is_read" class="unread-dot"></div>
          </div>
          
          <div class="notification-content">
            <div class="notification-header">
              <h4 class="notification-title">{{ notification.title }}</h4>
              <div class="notification-meta">
                <el-tag 
                  v-if="notification.priority === 3" 
                  type="danger" 
                  size="small"
                >
                  紧急
                </el-tag>
                <el-tag 
                  v-else-if="notification.priority === 2" 
                  type="warning" 
                  size="small"
                >
                  重要
                </el-tag>
                <el-tag 
                  v-if="notification.type === 1"
                  size="small"
                >
                  系统通知
                </el-tag>
                <el-tag 
                  v-else-if="notification.type === 2"
                  type="success"
                  size="small"
                >
                  课程通知
                </el-tag>
                <el-tag 
                  v-else-if="notification.type === 3"
                  type="warning"
                  size="small"
                >
                  作业通知
                </el-tag>
              </div>
            </div>
            
            <div class="notification-body">
              <p class="notification-text">{{ notification.content }}</p>
            </div>
            
            <div class="notification-footer">
              <span class="notification-time">{{ formatTime(notification.create_time) }}</span>
              <span v-if="notification.is_read && notification.read_time" class="read-time">
                已读于 {{ formatTime(notification.read_time) }}
              </span>
            </div>
          </div>
          
          <div class="notification-actions">
            <el-button 
              v-if="!notification.is_read"
              type="text" 
              size="small"
              @click.stop="markAsRead(notification)"
            >
              标记已读
            </el-button>
          </div>
        </div>
        
        <div v-if="notifications.length === 0" class="empty-state">
          <el-empty description="暂无通知" :image-size="120" />
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchNotifications"
          @current-change="fetchNotifications"
        />
      </div>
    </el-card>

    <!-- 通知详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="通知详情"
      width="600px"
    >
      <div v-if="currentNotification" class="notification-detail">
        <div class="detail-header">
          <h3>{{ currentNotification.title }}</h3>
          <div class="detail-meta">
            <el-tag 
              v-if="currentNotification.priority === 3" 
              type="danger"
            >
              紧急
            </el-tag>
            <el-tag 
              v-else-if="currentNotification.priority === 2" 
              type="warning"
            >
              重要
            </el-tag>
            <el-tag 
              v-if="currentNotification.type === 1"
            >
              系统通知
            </el-tag>
            <el-tag 
              v-else-if="currentNotification.type === 2"
              type="success"
            >
              课程通知
            </el-tag>
            <el-tag 
              v-else-if="currentNotification.type === 3"
              type="warning"
            >
              作业通知
            </el-tag>
          </div>
        </div>
        
        <div class="detail-content">
          <p>{{ currentNotification.content }}</p>
        </div>
        
        <div class="detail-footer">
          <div class="detail-time">
            <span>发布时间：{{ formatTime(currentNotification.create_time) }}</span>
            <span v-if="currentNotification.is_read && currentNotification.read_time">
              阅读时间：{{ formatTime(currentNotification.read_time) }}
            </span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  getUserNotifications, 
  getUnreadNotificationCount,
  markNotificationAsRead, 
  markAllNotificationsAsRead 
} from '@/api/notification'
import { formatTime } from '@/utils/format'

const router = useRouter()
const loading = ref(false)
const markingAll = ref(false)
const notifications = ref([])
const detailDialogVisible = ref(false)
const currentNotification = ref(null)

const queryForm = reactive({
  type: null,
  isRead: null,
  priority: null
})

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.is_read).length
})

// 获取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...queryForm
    }
    const response = await getUserNotifications(params)
    if (response.code === 200) {
      notifications.value = response.data.notifications || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取通知列表失败:', error)
    ElMessage.error('获取通知列表失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = null
  })
  pagination.page = 1
  fetchNotifications()
}

// 点击通知
const handleNotificationClick = async (notification) => {
  currentNotification.value = notification
  detailDialogVisible.value = true
  
  // 如果未读，自动标记为已读
  if (!notification.is_read) {
    await markAsRead(notification, false)
  }
}

// 标记单个通知为已读
const markAsRead = async (notification, showMessage = true) => {
  try {
    const response = await markNotificationAsRead(notification.id)
    if (response.code === 200) {
      notification.is_read = 1
      notification.read_time = new Date().toISOString()
      if (showMessage) {
        ElMessage.success('已标记为已读')
      }
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    if (showMessage) {
      ElMessage.error('操作失败')
    }
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    markingAll.value = true
    const response = await markAllNotificationsAsRead()
    if (response.code === 200) {
      notifications.value.forEach(n => {
        n.is_read = 1
        n.read_time = new Date().toISOString()
      })
      ElMessage.success('已全部标记为已读')
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
    ElMessage.error('操作失败')
  } finally {
    markingAll.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchNotifications()
})
</script>

<style scoped>
.notification-center {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.stats-bar {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-value.unread {
  color: #f56c6c;
}

.filter-bar {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.notification-list {
  min-height: 400px;
}

.notification-item {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
}

.notification-indicator {
  width: 20px;
  display: flex;
  align-items: flex-start;
  padding-top: 5px;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: #409eff;
  border-radius: 50%;
}

.notification-content {
  flex: 1;
  margin-left: 15px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.notification-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 15px;
}

.notification-body {
  margin-bottom: 15px;
}

.notification-text {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.notification-time {
  color: #909399;
}

.read-time {
  color: #67c23a;
}

.notification-actions {
  display: flex;
  align-items: flex-start;
  padding-top: 5px;
  margin-left: 15px;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.pagination {
  margin-top: 30px;
  text-align: right;
}

.notification-detail {
  padding: 10px 0;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.detail-meta {
  display: flex;
  gap: 8px;
}

.detail-content {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.detail-content p {
  margin: 0;
  line-height: 1.8;
  color: #303133;
  font-size: 14px;
}

.detail-footer {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.detail-time {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 12px;
  color: #909399;
}
</style>