<template>
  <div class="courses-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>课程管理</h2>
        <p>管理您的教学课程，创建和编辑课程信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建课程
        </el-button>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="courses-grid">
      <div
        v-for="course in courses"
        :key="course.id"
        class="course-card"
        @click="viewCourse(course)"
      >
        <div class="course-header">
          <h3>{{ course.courseName }}</h3>
          <el-dropdown @command="(command) => handleCourseAction(command, course)">
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="content">管理内容</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        
        <div class="course-info">
          <p class="course-code">课程编码：{{ course.courseCode }}</p>
          <p class="course-subject">学科：{{ course.subject }}</p>
          <p class="course-desc">{{ course.description }}</p>
        </div>
        
        <div class="course-footer">
          <el-tag :type="course.status === 1 ? 'success' : 'danger'" size="small">
            {{ course.status === 1 ? '正常' : '禁用' }}
          </el-tag>
          <span class="create-time">
            {{ formatDate(course.createTime) }}
          </span>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="courses.length === 0" class="empty-state">
        <el-empty description="暂无课程">
          <el-button type="primary" @click="showCreateDialog = true">
            创建第一个课程
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 课程详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="课程详情"
      width="600px"
    >
      <div v-if="selectedCourse" class="course-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>课程名称：</label>
              <span>{{ selectedCourse.courseName }}</span>
            </div>
            <div class="detail-item">
              <label>课程编码：</label>
              <span>{{ selectedCourse.courseCode }}</span>
            </div>
            <div class="detail-item">
              <label>学科分类：</label>
              <span>{{ selectedCourse.subject }}</span>
            </div>
            <div class="detail-item">
              <label>授课教师：</label>
              <span>{{ selectedCourse.teacherName || '未指定' }}</span>
            </div>
            <div class="detail-item">
              <label>课程状态：</label>
              <el-tag :type="selectedCourse.status === 1 ? 'success' : 'danger'" size="small">
                {{ selectedCourse.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDate(selectedCourse.createTime) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDate(selectedCourse.updateTime) }}</span>
            </div>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>课程描述</h3>
          <p class="course-description">{{ selectedCourse.description || '暂无描述' }}</p>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="editSelectedCourse">编辑课程</el-button>
          <el-button type="success" @click="manageCourseContent">管理内容</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 创建/编辑课程对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingCourse ? '编辑课程' : '新建课程'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="courseFormRef"
        :model="courseForm"
        :rules="courseRules"
        label-width="100px"
      >
        <el-form-item label="课程名称" prop="courseName">
          <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" />
        </el-form-item>
        
        <el-form-item label="课程编码" prop="courseCode">
          <el-input v-model="courseForm.courseCode" placeholder="请输入课程编码" />
        </el-form-item>
        
        <el-form-item label="学科分类" prop="subject">
          <el-select v-model="courseForm.subject" placeholder="请选择学科">
            <el-option label="计算机科学" value="计算机科学" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="英语" value="英语" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程描述" prop="description">
          <el-input
            v-model="courseForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入课程描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="submitCourse">
          {{ editingCourse ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCourses, createCourse, updateCourse, deleteCourse } from '@/api/teacher'

const router = useRouter()

const courses = ref([])
const loading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedCourse = ref(null)
const editingCourse = ref(null)
const submitting = ref(false)
const courseFormRef = ref()

const courseForm = reactive({
  courseName: '',
  courseCode: '',
  subject: '',
  description: ''
})

const courseRules = {
  courseName: [
    { required: true, message: '请输入课程名称', trigger: 'blur' }
  ],
  courseCode: [
    { required: true, message: '请输入课程编码', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请选择学科分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入课程描述', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadCourses()
})

const loadCourses = async () => {
  loading.value = true
  try {
    const response = await getCourses()
    courses.value = response.data
  } catch (error) {
    console.error('Failed to load courses:', error)
  } finally {
    loading.value = false
  }
}

const viewCourse = (course) => {
  selectedCourse.value = course
  showDetailDialog.value = true
}

const handleCourseAction = (command, course) => {
  switch (command) {
    case 'edit':
      editCourse(course)
      break
    case 'content':
      router.push({
        name: 'TeacherCourseContent',
        params: { id: course.id }
      })
      break
    case 'delete':
      deleteCourseConfirm(course)
      break
  }
}

const editCourse = (course) => {
  editingCourse.value = course
  Object.assign(courseForm, {
    courseName: course.courseName,
    courseCode: course.courseCode,
    subject: course.subject,
    description: course.description
  })
  showCreateDialog.value = true
}

const deleteCourseConfirm = (course) => {
  ElMessageBox.confirm(
    `确认删除课程"${course.courseName}"吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteCourse(course.id)
      ElMessage.success('课程删除成功')
      loadCourses()
    } catch (error) {
      console.error('Failed to delete course:', error)
    }
  })
}

const submitCourse = async () => {
  if (!courseFormRef.value) return
  
  await courseFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (editingCourse.value) {
          await updateCourse(editingCourse.value.id, courseForm)
          ElMessage.success('课程更新成功')
        } else {
          await createCourse(courseForm)
          ElMessage.success('课程创建成功')
        }
        showCreateDialog.value = false
        loadCourses()
      } catch (error) {
        console.error('Failed to submit course:', error)
      } finally {
        submitting.value = false
      }
    }
  })
}

const resetForm = () => {
  editingCourse.value = null
  Object.assign(courseForm, {
    courseName: '',
    courseCode: '',
    subject: '',
    description: ''
  })
  if (courseFormRef.value) {
    courseFormRef.value.clearValidate()
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const editSelectedCourse = () => {
  if (selectedCourse.value) {
    editCourse(selectedCourse.value)
    showDetailDialog.value = false
  }
}

const manageCourseContent = () => {
  if (selectedCourse.value) {
    router.push({
      name: 'TeacherCourseContent',
      params: { id: selectedCourse.value.id }
    })
  }
}
</script>

<style scoped>
.courses-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.course-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #ebeef5;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.course-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.course-info {
  margin-bottom: 20px;
}

.course-code,
.course-subject {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 13px;
}

.course-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f5f7fa;
}

.create-time {
  color: #c0c4cc;
  font-size: 12px;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
}

/* 课程详情样式 */
.course-detail {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.detail-item span {
  color: #303133;
  flex: 1;
}

.course-description {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  color: #606266;
  line-height: 1.6;
  margin: 0;
  min-height: 60px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .course-card {
    padding: 20px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-item label {
    min-width: auto;
    margin-right: 0;
  }
}
</style>