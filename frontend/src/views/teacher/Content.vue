<template>
  <div class="content-management">
    <div class="page-header">
      <h2>教学内容管理</h2>
      <p>管理和生成课程教学内容</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：课程列表 -->
      <el-col :span="8">
        <el-card title="课程列表" class="course-list-card">
          <template #header>
            <div class="card-header">
              <span>课程列表</span>
              <el-button type="primary" size="small" @click="refreshCourses">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <el-scrollbar height="600px">
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else>
              <div
                v-for="course in courses"
                :key="course.id"
                class="course-item"
                :class="{ active: selectedCourse?.id === course.id }"
                @click="selectCourse(course)"
              >
                <div class="course-info">
                  <h4>{{ course.courseName }}</h4>
                  <p>{{ course.subject }} | {{ course.description }}</p>
                  <div class="course-meta">
                    <el-tag size="small" type="info">{{
                      course.status === 1 ? "进行中" : "已结束"
                    }}</el-tag>
                    <span class="create-time">{{
                      formatDate(course.createTime)
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>

      <!-- 右侧：内容管理 -->
      <el-col :span="16">
        <el-card v-if="!selectedCourse" class="empty-state">
          <div class="empty-content">
            <el-icon size="64" color="#c0c4cc"><Document /></el-icon>
            <h3>请选择课程</h3>
            <p>选择左侧课程以管理教学内容</p>
          </div>
        </el-card>

        <div v-else>
          <!-- 课程信息 -->
          <el-card class="course-info-card">
            <template #header>
              <div class="card-header">
                <span>{{ selectedCourse.courseName }}</span>
                <el-button type="primary" @click="showContentDialog = true">
                  <el-icon><Plus /></el-icon>
                  新增内容
                </el-button>
              </div>
            </template>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="课程名称">{{
                selectedCourse.courseName
              }}</el-descriptions-item>
              <el-descriptions-item label="学科">{{
                selectedCourse.subject
              }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag
                  :type="selectedCourse.status === 1 ? 'success' : 'info'"
                >
                  {{ selectedCourse.status === 1 ? "进行中" : "已结束" }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">{{
                formatDate(selectedCourse.createTime)
              }}</el-descriptions-item>
              <el-descriptions-item label="课程描述" :span="2">{{
                selectedCourse.description
              }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 教学内容列表 -->
          <el-card class="content-list-card">
            <template #header>
              <div class="card-header">
                <span>教学内容</span>
                <div>
                  <el-button 
                    type="success" 
                    @click="generateWithAI"
                    :loading="checkingAIService"
                    :disabled="checkingAIService"
                  >
                    <el-icon v-if="!checkingAIService"><MagicStick /></el-icon>
                    {{ checkingAIService ? '检查AI服务中...' : 'AI生成内容' }}
                  </el-button>
                </div>
              </div>
            </template>

            <el-table :data="teachingContents" v-loading="contentLoading">
              <el-table-column label="章节名称">
                <template #default="{ row }">
                  {{ row.chapterName || "未设置" }}
                </template>
              </el-table-column>
              <el-table-column label="内容类型">
                <template #default="{ row }">
                  <el-tag :type="getContentTypeColor(row.contentType)">
                    {{ getContentTypeName(row.contentType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="难度等级">
                <template #default="{ row }">
                  <el-rate
                    v-model="row.difficultyLevel"
                    disabled
                    show-score
                    :max="5"
                    :value="row.difficultyLevel || 0"
                  />
                </template>
              </el-table-column>
              <el-table-column label="创建时间">
                <template #default="{ row }">
                  {{ formatDate(row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="viewContent(row)"
                    >查看</el-button
                  >
                  <el-button
                    type="warning"
                    size="small"
                    @click="editContent(row)"
                    >编辑</el-button
                  >
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteContent(row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 新增/编辑内容对话框 -->
    <el-dialog
      v-model="showContentDialog"
      :title="editingContent ? '编辑教学内容' : '新增教学内容'"
      width="800px"
    >
      <el-form
        :model="contentForm"
        :rules="contentRules"
        ref="contentFormRef"
        label-width="100px"
      >
        <el-form-item label="章节名称" prop="chapterName">
          <el-input
            v-model="contentForm.chapterName"
            placeholder="请输入章节名称"
          />
        </el-form-item>

        <el-form-item label="标题" prop="title">
          <el-input v-model="contentForm.title" placeholder="请输入内容标题" />
        </el-form-item>

        <el-form-item label="内容类型" prop="contentType">
          <el-select
            v-model="contentForm.contentType"
            placeholder="请选择内容类型"
          >
            <el-option label="知识讲解" :value="1" />
            <el-option label="实训练习" :value="2" />
            <el-option label="指导说明" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="难度等级" prop="difficulty">
          <el-rate v-model="contentForm.difficulty" show-score />
        </el-form-item>

        <el-form-item label="教学内容" prop="content">
          <MdEditor
            v-model="contentForm.content"
            style="height: 400px"
            :toolbars="[
              'bold',
              'underline',
              'italic',
              '-',
              'title',
              'strikethrough',
              'sub',
              'sup',
              'quote',
              'unorderedList',
              'orderedList',
              '-',
              'codeRow',
              'code',
              'link',
              'image',
              'table',
              'mermaid',
              '-',
              'save',
              'preview',
            ]"
            placeholder="请输入教学内容，支持 Markdown 格式，或使用 AI 生成"
          />
        </el-form-item>

        <el-form-item label="学习目标">
          <el-input
            v-model="contentForm.learningObjectives"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="时间分配（分钟）" prop="timeAllocation">
          <el-input-number
            v-model="contentForm.timeAllocation"
            :min="1"
            :max="120"
            label="时间分配"
            placeholder="请输入时间分配（分钟）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showContentDialog = false">取消</el-button>
        <el-button type="primary" @click="saveContent" :loading="saving"
          >保存</el-button
        >
      </template>
    </el-dialog>

    <!-- AI生成内容对话框 -->
    <el-dialog v-model="showAIDialog" title="AI生成教学内容" width="600px">
      <el-form :model="aiForm" label-width="100px">
        <el-form-item label="章节名称">
          <el-input v-model="aiForm.chapterName" placeholder="请输入章节名称" />
        </el-form-item>

        <el-form-item label="标题">
          <el-input v-model="aiForm.title" placeholder="请输入教学内容标题" />
        </el-form-item>

        <el-form-item label="内容类型">
          <el-select v-model="aiForm.contentType">
            <el-option label="知识讲解" :value="1" />
            <el-option label="实训练习" :value="2" />
            <el-option label="指导说明" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="详细要求">
          <el-input
            v-model="aiForm.requirements"
            type="textarea"
            :rows="4"
            placeholder="请描述具体的内容要求，AI将根据此要求生成教学内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAIDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="generateContent"
          :loading="aiGenerating"
        >
          <el-icon><MagicStick /></el-icon>
          生成内容
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看内容对话框 -->
    <el-dialog v-model="showViewDialog" title="教学内容详情" width="800px">
      <div v-if="currentContent">
        <h3>{{ currentContent.title }}</h3>
        <el-divider></el-divider>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="章节名称">{{
            currentContent.chapterName
          }}</el-descriptions-item>
          <el-descriptions-item label="内容类型">
            <el-tag :type="getContentTypeColor(currentContent.contentType)">
              {{ getContentTypeName(currentContent.contentType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="难度等级">
            <el-rate
              v-model="currentContent.difficultyLevel"
              disabled
              show-score
              :max="5"
            />
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{
            formatDate(currentContent.createTime)
          }}</el-descriptions-item>
          <el-descriptions-item label="时间分配（分钟）">{{
            currentContent.timeAllocation
          }}</el-descriptions-item>
        </el-descriptions>

        <el-divider></el-divider>

        <h4>教学内容</h4>
        <MdPreview
          :modelValue="currentContent.content"
          class="content-preview"
        />

        <h4>学习目标</h4>
        <MdPreview
          :modelValue="currentContent.learningObjectives"
          class="objectives-preview"
        />
      </div>

      <template #footer>
        <el-button @click="showViewDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getCourses } from "@/api/teacher";
import {
  getTeachingContents,
  createTeachingContent,
  updateTeachingContent,
  deleteTeachingContent,
} from "@/api/teacher";
import { generateTeachingContent } from "@/api/teacher";
import { testAIConnection } from "@/api/ai";
import { formatDate } from "@/utils/format";
import { MdEditor, MdPreview } from "md-editor-v3";
import "md-editor-v3/lib/style.css";

// 响应式数据
const loading = ref(false);
const contentLoading = ref(false);
const saving = ref(false);
const aiGenerating = ref(false);
const aiServiceAvailable = ref(true);
const currentContent = ref(null);
const showViewDialog = ref(false);
const checkingAIService = ref(false); // 添加AI服务检查状态

// 检查 AI 服务状态
const checkAIService = async () => {
  checkingAIService.value = true;
  try {
    await testAIConnection();
    aiServiceAvailable.value = true;
    ElMessage.success("AI服务连接正常");
  } catch (error) {
    aiServiceAvailable.value = false;
    ElMessage.warning("AI服务暂时不可用，将使用基础模板生成内容");
  } finally {
    checkingAIService.value = false;
  }
};

const courses = ref([]);
const selectedCourse = ref(null);
const teachingContents = ref([]);

const showContentDialog = ref(false);
const showAIDialog = ref(false);
const editingContent = ref(null);

const contentForm = reactive({
  chapterName: "", // 章节名称
  title: "", // 标题
  contentType: 1, // 内容类型
  difficulty: 3, // 难度等级（1-5）
  content: "", // 教学内容
  learningObjectives: "", // 学习目标
  timeAllocation: 60, // 时间分配（分钟）
});

const aiForm = reactive({
  chapterName: "",
  title: "", // 添加标题字段
  contentType: 1,
  requirements: "",
});

const contentRules = {
  chapterName: [{ required: true, message: "请输入章节名称", trigger: "blur" }],
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  contentType: [
    { required: true, message: "请选择内容类型", trigger: "change" },
  ],
  content: [{ required: true, message: "请输入教学内容", trigger: "blur" }],
};

const contentFormRef = ref();

// 方法
const refreshCourses = async () => {
  loading.value = true;
  try {
    const response = await getCourses();
    courses.value = response.data;
  } catch (error) {
    ElMessage.error("获取课程列表失败");
  } finally {
    loading.value = false;
  }
};

const selectCourse = async (course) => {
  selectedCourse.value = course;
  await loadTeachingContents();
};

const loadTeachingContents = async () => {
  if (!selectedCourse.value) return;

  contentLoading.value = true;
  try {
    const response = await getTeachingContents(selectedCourse.value.id);
    teachingContents.value = response.data;
  } catch (error) {
    ElMessage.error("获取教学内容失败");
  } finally {
    contentLoading.value = false;
  }
};

// 获取内容类型名称
const getContentTypeName = (type) => {
  const typeMap = {
    1: "知识讲解",
    2: "实训练习",
    3: "指导说明",
  };
  return typeMap[type] || "未知类型";
};

// 获取内容类型标签颜色
const getContentTypeColor = (type) => {
  const colorMap = {
    1: "primary",
    2: "success",
    3: "warning",
  };
  return colorMap[type] || "info";
};

const viewContent = (content) => {
  currentContent.value = content;
  showViewDialog.value = true;
};

const editContent = (content) => {
  editingContent.value = content;
  Object.assign(contentForm, {
    chapterName: content.chapterName || "",
    title: content.title || "",
    contentType: content.contentType,
    difficulty: content.difficultyLevel || 3,
    content: content.content || "",
    learningObjectives: content.learningObjectives || "",
    timeAllocation: content.timeAllocation || 60,
  });
  showContentDialog.value = true;
};

const deleteContent = async (content) => {
  try {
    await ElMessageBox.confirm("确认删除此教学内容吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await deleteTeachingContent(content.id);
    ElMessage.success("删除成功");
    await loadTeachingContents();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

const saveContent = async () => {
  if (!contentFormRef.value) return;

  await contentFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!selectedCourse.value) {
        ElMessage.warning("请先选择课程");
        return;
      }

      saving.value = true;
      try {
        const data = {
          courseId: selectedCourse.value.id,
          title: contentForm.title,
          chapterName: contentForm.chapterName,
          contentType: contentForm.contentType,
          content: contentForm.content,
          difficultyLevel: contentForm.difficulty,
          learningObjectives: contentForm.learningObjectives,
          timeAllocation: contentForm.timeAllocation,
        };

        if (editingContent.value) {
          await updateTeachingContent(editingContent.value.id, data);
          ElMessage.success("更新成功");
        } else {
          await createTeachingContent(data);
          ElMessage.success("创建成功");
        }

        showContentDialog.value = false;
        resetContentForm();
        await loadTeachingContents();
      } catch (error) {
        console.error("保存失败:", error.response?.data || error);
        ElMessage.error(error.response?.data?.message || "保存失败");
      } finally {
        saving.value = false;
      }
    }
  });
};

const resetContentForm = () => {
  Object.assign(contentForm, {
    chapterName: "",
    title: "",
    contentType: 1,
    difficulty: 3,
    content: "",
    learningObjectives: "",
    timeAllocation: 60,
  });
  editingContent.value = null;
};

const generateWithAI = async () => {
  if (!selectedCourse.value) {
    ElMessage.warning("请先选择课程");
    return;
  }

  // 显示检查AI服务的提示
  ElMessage.info("正在检查AI服务状态，请稍候...");
  
  // 检查 AI 服务状态
  await checkAIService();
  showAIDialog.value = true;
};

const generateContent = async () => {
  if (!aiForm.chapterName || !aiForm.contentType || !aiForm.title) {
    ElMessage.warning("请填写章节名称、标题和内容类型");
    return;
  }

  aiGenerating.value = true;
  try {
    const response = await generateTeachingContent({
      courseId: selectedCourse.value.id,
      chapterName: aiForm.chapterName,
      title: aiForm.title,
      contentType: aiForm.contentType,
      requirements: aiForm.requirements,
      timeAllocation: 60,
      difficultyLevel: 3,
    });

    if (response.data) {
      ElMessage.success("AI内容生成并保存成功");
      showAIDialog.value = false;
      // 刷新列表
      await loadTeachingContents();
    } else {
      ElMessage.warning("AI内容生成失败，请稍后重试");
    }
  } catch (error) {
    console.error("AI生成失败:", error.response?.data || error);
    ElMessage.error(error.response?.data?.message || "AI生成失败，请稍后重试");
  } finally {
    aiGenerating.value = false;
  }
};

// 生成基础模板
const generateBasicTemplate = (form) => {
  const typeMap = {
    1: "知识讲解",
    2: "实训练习",
    3: "指导说明",
  };

  return `# ${form.title}

## 章节名称
${form.chapterName}

## 内容类型
${typeMap[form.contentType]}

## 教学内容
${form.requirements || "请在此处编写教学内容..."}

## 学习目标
1. 理解并掌握相关概念
2. 能够应用所学知识解决实际问题
3. 培养学生的实践能力

## 重点难点
- 重点1
- 重点2
- 难点1
- 难点2

## 教学建议
1. 结合实例讲解
2. 注重互动与实践
3. 及时总结归纳
`;
};

onMounted(() => {
  refreshCourses();
  checkAIService(); // 初始检查 AI 服务状态
});
</script>

<style scoped>
.content-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-list-card {
  height: 700px;
}

.course-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.course-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.course-item.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.course-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.course-info p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.empty-state {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: #909399;
}

.empty-content p {
  margin: 0;
  color: #c0c4cc;
}

.course-info-card {
  margin-bottom: 20px;
}

.content-list-card {
  min-height: 400px;
}

.loading-container {
  padding: 20px;
}

.content-preview,
.objectives-preview {
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  margin-top: 10px;
}

.content-preview {
  margin: 16px 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.objectives-preview {
  margin: 16px 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #f8f9fa;
}

.md-editor-dark {
  --md-bk-color: #1e1e1e;
}
</style>