<template>
  <div class="teacher-grading">
    <div class="page-header">
      <h2>作业评分</h2>
      <p>对学生提交的主观题进行人工评分，AI已提供参考分数</p>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：筛选和统计 -->
      <el-col :span="6">
        <el-card class="filter-card">
          <template #header>
            <div class="card-header">
              <span>筛选条件</span>
            </div>
          </template>
          
          <el-form :model="filterForm" label-width="80px">
            <el-form-item label="课程">
              <el-select v-model="filterForm.courseId" placeholder="选择课程" @change="loadAnswers">
                <el-option label="全部课程" :value="null" />
                <el-option
                  v-for="course in courses"
                  :key="course.id"
                  :label="course.courseName"
                  :value="course.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="题目类型">
              <el-select v-model="filterForm.questionType" placeholder="选择类型" @change="loadAnswers">
                <el-option label="全部类型" :value="null" />
                <el-option label="简答题" :value="4" />
                <el-option label="编程题" :value="5" />
              
              </el-select>
            </el-form-item>

            <el-form-item label="评分状态">
              <el-select v-model="filterForm.gradingStatus" placeholder="选择状态" @change="loadAnswers">
                <el-option label="全部" :value="null" />
                <el-option label="待评分" :value="1" />
                <el-option label="已评分" :value="2" />
              </el-select>
            </el-form-item>
          </el-form>
          
          <!-- 统计信息 -->
          <div class="statistics">
            <h4>评分统计</h4>
            <div class="stat-item">
              <span class="label">待评分：</span>
              <span class="value pending">{{ statistics.pendingCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">已评分：</span>
              <span class="value graded">{{ statistics.gradedCount || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="label">总计：</span>
              <span class="value total">{{ statistics.totalCount || 0 }}</span>
            </div>
            <div class="progress-item">
              <span class="label">完成度：</span>
              <el-progress 
                :percentage="statistics.gradingProgress || 0" 
                :stroke-width="6"
                :show-text="false"
              />
              <span class="percentage">{{ (statistics.gradingProgress || 0).toFixed(1) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：评分列表和详情 -->
      <el-col :span="18">
        <!-- 答案列表 -->
        <el-card v-if="!currentAnswer" class="list-card">
          <template #header>
            <div class="card-header">
              <span>答案列表</span>
              <el-button @click="loadAnswers" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div v-if="answerList.length === 0" class="empty-state">
            <el-empty :image-size="120">
              <template #description>
                <div class="empty-description">
                  <p v-if="filterForm.gradingStatus === 1">
                    🎉 太棒了！所有答案都已评分完成
                  </p>
                  <p v-else-if="filterForm.gradingStatus === 2">
                    暂无已评分的答案记录
                  </p>
                  <p v-else>
                    暂无答案记录
                  </p>
                </div>
              </template>
              <template #default>
                <div v-if="filterForm.gradingStatus === 1" class="empty-actions">
                  <el-button type="primary" @click="switchToGradedView">
                    查看已评分答案
                  </el-button>
                  <el-button @click="switchToAllView">
                    查看所有答案
                  </el-button>
                </div>
                <div v-else-if="filterForm.gradingStatus === 2" class="empty-actions">
                  <el-button type="primary" @click="switchToPendingView">
                    查看待评分答案
                  </el-button>
                  <el-button @click="switchToAllView">
                    查看所有答案
                  </el-button>
                </div>
              </template>
            </el-empty>
          </div>
          
          <div v-else class="answer-list">
            <div 
              v-for="answer in answerList" 
              :key="answer.id"
              class="answer-item"
              :class="{ 'graded': answer.gradingStatus === 2 }"
              @click="selectAnswer(answer)"
            >
              <div class="answer-header">
                <div class="student-info">
                  <span class="student-name">{{ answer.studentName || '学生' }}</span>
                  <el-tag :type="getQuestionTypeColor(answer.questionType)">
                    {{ getQuestionTypeName(answer.questionType) }}
                  </el-tag>
                  <el-tag 
                    :type="getGradingStatusColor(answer.gradingStatus)"
                    size="small"
                  >
                    {{ getGradingStatusText(answer.gradingStatus) }}
                  </el-tag>
                </div>
                <div class="answer-meta">
                  <span class="course-name">{{ answer.courseName }}</span>
                  <span class="submit-time">{{ formatDate(answer.answerTime) }}</span>
                </div>
              </div>
              <div class="answer-preview">
                <div class="question-title">{{ answer.questionTitle }}</div>
                <div class="student-answer">{{ truncateText(answer.studentAnswer, 100) }}</div>
                <div class="score-info">
                  <span v-if="answer.aiScore" class="ai-score">AI参考分: {{ answer.aiScore }}分</span>
                  <span v-if="answer.teacherScore" class="teacher-score">教师评分: {{ answer.teacherScore }}分</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div v-if="answerList.length > 0" class="pagination">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.size"
              :total="pagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadAnswers"
              @current-change="loadAnswers"
            />
          </div>
        </el-card>

        <!-- 评分详情 -->
        <el-card v-else class="grading-card">
          <template #header>
            <div class="card-header">
              <span>评分详情</span>
              <div class="header-actions">
                <el-button @click="previousAnswer" :disabled="currentAnswerIndex === 0">
                  <el-icon><ArrowLeft /></el-icon>
                  上一个
                </el-button>
                <el-button @click="nextAnswer" :disabled="currentAnswerIndex >= answerList.length - 1">
                  下一个
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
                <el-button @click="backToList">
                  <el-icon><Back /></el-icon>
                  返回列表
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="grading-content">
            <!-- 题目信息 -->
            <div class="question-section">
              <h3>题目信息</h3>
              <div class="question-info">
                <div class="info-row">
                  <span class="label">课程：</span>
                  <span class="value">{{ currentAnswerDetail.courseName }}</span>
                </div>
                <div class="info-row">
                  <span class="label">题目类型：</span>
                  <el-tag :type="getQuestionTypeColor(currentAnswerDetail.questionType)">
                    {{ getQuestionTypeName(currentAnswerDetail.questionType) }}
                  </el-tag>
                </div>
                <div class="info-row">
                  <span class="label">难度：</span>
                  <el-tag :type="getDifficultyColor(currentAnswerDetail.difficultyLevel)">
                    {{ getDifficultyText(currentAnswerDetail.difficultyLevel) }}
                  </el-tag>
                </div>
                <div class="info-row">
                  <span class="label">分值：</span>
                  <span class="value">{{ currentAnswerDetail.questionScore || 0 }}分</span>
                </div>
              </div>
              <div class="question-content">
                <h4>题目内容：</h4>
                <div class="content-text">{{ currentAnswerDetail.questionContent }}</div>
              </div>
              <div class="standard-answer">
                <h4>标准答案：</h4>
                <div class="answer-text">{{ currentAnswerDetail.correctAnswer }}</div>
              </div>
            </div>

            <!-- 学生信息和答案 -->
            <div class="student-section">
              <h3>学生答案</h3>
              <div class="student-info">
                <div class="info-row">
                  <span class="label">学生：</span>
                  <span class="value">{{ currentAnswerDetail.studentName }}</span>
                </div>
                <div class="info-row">
                  <span class="label">提交时间：</span>
                  <span class="value">{{ formatDate(currentAnswerDetail.answerTime) }}</span>
                </div>
                <div class="info-row">
                  <span class="label">AI参考分：</span>
                  <span class="value ai-score">{{ currentAnswerDetail.aiScore || 0 }}分</span>
                </div>
              </div>
              <div class="student-answer">
                <h4>学生答案：</h4>
                <div class="answer-text">{{ currentAnswerDetail.studentAnswer }}</div>
              </div>
              <div v-if="currentAnswerDetail.aiFeedback" class="ai-feedback">
                <h4>AI分析：</h4>
                <div class="feedback-text">{{ currentAnswerDetail.aiFeedback }}</div>
                <!-- 突出显示相似度信息 -->
                <div v-if="getSimilarityInfo(currentAnswerDetail.aiFeedback)" class="similarity-highlight">
                  <el-alert
                    :title="getSimilarityInfo(currentAnswerDetail.aiFeedback).title"
                    :description="getSimilarityInfo(currentAnswerDetail.aiFeedback).description"
                    :type="getSimilarityInfo(currentAnswerDetail.aiFeedback).type"
                    show-icon
                    :closable="false"
                  />
                </div>
              </div>
            </div>

            <!-- 教师评分表单 -->
            <div class="grading-section">
              <h3>教师评分</h3>
              <el-form :model="gradingForm" label-width="100px">
                <el-form-item label="评分结果">
                  <el-radio-group v-model="gradingForm.isCorrect" :disabled="currentAnswerDetail.gradingStatus === 2">
                    <el-radio :value="1">正确</el-radio>
                    <el-radio :value="2">部分正确</el-radio>
                    <el-radio :label="0">错误</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="得分">
                  <el-input-number 
                    v-model="gradingForm.teacherScore" 
                    :min="0" 
                    :max="currentAnswerDetail.questionScore || 100"
                    :precision="1"
                    :disabled="currentAnswerDetail.gradingStatus === 2"
                    controls-position="right"
                  />
                  <span class="score-hint">/ {{ currentAnswerDetail.questionScore || 0 }}分</span>
                  <span class="ai-reference">（AI参考分：{{ currentAnswerDetail.aiScore || 0 }}分）</span>
                </el-form-item>
                
                <el-form-item label="教师反馈">
                  <el-input
                    v-model="gradingForm.teacherFeedback"
                    type="textarea"
                    :rows="4"
                    :disabled="currentAnswerDetail.gradingStatus === 2"
                    :placeholder="currentAnswerDetail.gradingStatus === 2 ? '已评分' : '请输入对学生答案的评价和建议...'"
                  />
                </el-form-item>
                
                <el-form-item label="改进建议">
                  <el-input
                    v-model="gradingForm.improvementSuggestion"
                    type="textarea"
                    :rows="3"
                    :disabled="currentAnswerDetail.gradingStatus === 2"
                    :placeholder="currentAnswerDetail.gradingStatus === 2 ? '已评分' : '请输入改进建议...'"
                  />
                </el-form-item>
                
                <!-- 显示已评分信息 -->
                <div v-if="currentAnswerDetail.gradingStatus === 2" class="graded-info">
                  <el-alert
                    title="评分已完成"
                    type="success"
                    :closable="false"
                    show-icon
                  >
                    <template #default>
                      <p><strong>教师评分：</strong>{{ currentAnswerDetail.teacherScore }}分</p>
                      <p><strong>评分时间：</strong>{{ formatDate(currentAnswerDetail.teacherGradingTime) }}</p>
                    </template>
                  </el-alert>
                </div>
                
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="submitGrading" 
                    :loading="submitting"
                    :disabled="currentAnswerDetail.gradingStatus === 2"
                  >
                    <el-icon><Check /></el-icon>
                    {{ currentAnswerDetail.gradingStatus === 2 ? '已评分' : '提交评分' }}
                  </el-button>
                  <el-button 
                    @click="resetGradingForm"
                    :disabled="currentAnswerDetail.gradingStatus === 2"
                  >
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                  <el-button v-if="currentAnswerDetail.gradingStatus === 2" type="info" disabled>
                    <el-icon><Check /></el-icon>
                    评分已完成
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/format'
import { 
  Check, Refresh, ArrowLeft, ArrowRight, Back, Edit
} from '@element-plus/icons-vue'
import {
  getTeacherCourses,
  getAnswersForGrading,
  getAnswerDetail,
  submitTeacherGrading,
  getGradingStatistics
} from '@/api/teacher'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const courses = ref([])
const answerList = ref([])
const currentAnswer = ref(null)
const currentAnswerDetail = ref({})
const currentAnswerIndex = ref(0)
const statistics = ref({})

// 表单数据
const filterForm = reactive({
  courseId: null,
  questionType: null,
  gradingStatus: 1 // 默认显示待评分
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const gradingForm = reactive({
  isCorrect: 1,
  teacherScore: 0,
  teacherFeedback: '',
  improvementSuggestion: ''
})

// 生命周期
onMounted(() => {
  loadCourses()
  loadAnswers()
  loadStatistics()
})

// 方法
const loadCourses = async () => {
  try {
    const response = await getTeacherCourses()
    courses.value = response.data || []
  } catch (error) {
    console.error('Failed to load courses:', error)
    ElMessage.error('加载课程失败')
  }
}

const loadAnswers = async () => {
  loading.value = true
  try {
    const response = await getAnswersForGrading({
      courseId: filterForm.courseId,
      questionType: filterForm.questionType,
      gradingStatus: filterForm.gradingStatus,
      current: pagination.current,
      size: pagination.size
    })
    
    if (response.data) {
      answerList.value = Array.isArray(response.data) ? response.data : []
      pagination.total = answerList.value.length
      
      // 如果当前筛选条件是"待评分"但没有数据，且这是首次加载，自动切换到显示所有答案
      if (answerList.value.length === 0 && filterForm.gradingStatus === 1 && pagination.current === 1) {
        ElMessage.info('暂无待评分答案，已自动切换到显示所有答案')
        filterForm.gradingStatus = null
        // 重新加载数据
        setTimeout(() => {
          loadAnswers()
        }, 100)
        return
      }
    } else {
      answerList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('Failed to load answers:', error)
    ElMessage.error('加载答案列表失败')
    answerList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await getGradingStatistics({ courseId: filterForm.courseId })
    statistics.value = response.data || {
      pendingCount: 0,
      gradedCount: 0,
      totalCount: 0,
      gradingProgress: 0
    }
  } catch (error) {
    console.error('Failed to load statistics:', error)
  }
}

const selectAnswer = async (answer) => {
  try {
    currentAnswer.value = answer
    currentAnswerIndex.value = answerList.value.findIndex(a => a.id === answer.id)
    
    const response = await getAnswerDetail(answer.id)
    currentAnswerDetail.value = response.data || answer
    
    // 重置或加载评分表单 - 使用从后端获取的详细信息
    const detailData = response.data || answer
    
    // 检查评分状态 - 后端返回的是数据库字段名
    const isGraded = detailData.grading_status === 2 || detailData.gradingStatus === 2
    
    if (isGraded) {
      // 已评分，加载现有评分
      gradingForm.teacherScore = detailData.teacher_score || detailData.teacherScore || 0
      gradingForm.teacherFeedback = detailData.teacher_feedback || detailData.teacherFeedback || ''
      gradingForm.improvementSuggestion = detailData.improvement_suggestion || detailData.improvementSuggestion || ''
      gradingForm.isCorrect = detailData.is_correct || detailData.isCorrect || 1
      
      // 确保 currentAnswerDetail 包含正确的评分状态（统一使用驼峰命名）
      currentAnswerDetail.value.gradingStatus = 2
      currentAnswerDetail.value.teacherScore = gradingForm.teacherScore
      currentAnswerDetail.value.teacherFeedback = gradingForm.teacherFeedback
      currentAnswerDetail.value.improvementSuggestion = gradingForm.improvementSuggestion
      currentAnswerDetail.value.teacherGradingTime = detailData.teacher_grading_time || detailData.teacherGradingTime
    } else {
      // 未评分，重置表单并设置AI参考分
      resetGradingForm()
      gradingForm.teacherScore = detailData.ai_score || detailData.aiScore || 0
      currentAnswerDetail.value.gradingStatus = detailData.grading_status || detailData.gradingStatus || 0
    }
  } catch (error) {
    console.error('Failed to load answer detail:', error)
    ElMessage.error('加载答案详情失败')
  }
}

const submitGrading = async () => {
  if (!gradingForm.teacherFeedback.trim()) {
    ElMessage.warning('请输入教师反馈')
    return
  }
  
  // 确认对话框
  try {
    await ElMessageBox.confirm(
      `确认提交评分吗？\n\n学生：${currentAnswerDetail.value.studentName}\n得分：${gradingForm.teacherScore}分\n评分结果：${gradingForm.isCorrect === 1 ? '正确' : gradingForm.isCorrect === 2 ? '部分正确' : '错误'}\n\n提交后将无法修改！`,
      '确认评分',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )
  } catch {
    return // 用户取消
  }
  
  submitting.value = true
  try {
    await submitTeacherGrading({
      answerId: currentAnswer.value.id,
      teacherScore: gradingForm.teacherScore,
      isCorrect: gradingForm.isCorrect,
      teacherFeedback: gradingForm.teacherFeedback,
      improvementSuggestion: gradingForm.improvementSuggestion
    })
    
    ElMessage.success('评分提交成功')
    
    // 更新当前答案状态
    currentAnswer.value.gradingStatus = 2
    currentAnswer.value.teacherScore = gradingForm.teacherScore
    currentAnswer.value.teacherFeedback = gradingForm.teacherFeedback
    currentAnswer.value.improvementSuggestion = gradingForm.improvementSuggestion
    
    // 更新当前答案详情
    currentAnswerDetail.value.gradingStatus = 2
    currentAnswerDetail.value.teacherScore = gradingForm.teacherScore
    currentAnswerDetail.value.teacherFeedback = gradingForm.teacherFeedback
    currentAnswerDetail.value.improvementSuggestion = gradingForm.improvementSuggestion
    
    // 更新列表中的答案状态
    const answerInList = answerList.value.find(a => a.id === currentAnswer.value.id)
    if (answerInList) {
      answerInList.gradingStatus = 2
      answerInList.teacherScore = gradingForm.teacherScore
      answerInList.teacherFeedback = gradingForm.teacherFeedback
      answerInList.improvementSuggestion = gradingForm.improvementSuggestion
    }
    
    // 更新统计信息
    loadStatistics()
    
    // 根据筛选条件决定是否移除答案
    if (filterForm.gradingStatus === 1) {
      // 如果当前筛选条件是"待评分"，从列表中移除已评分的答案
      const currentIndex = currentAnswerIndex.value
      answerList.value = answerList.value.filter(a => a.id !== currentAnswer.value.id)
      
      // 自动跳转到下一个答案或返回列表
      if (answerList.value.length > 0) {
        // 如果还有答案，选择下一个（或当前位置的答案）
        const nextIndex = currentIndex < answerList.value.length ? currentIndex : answerList.value.length - 1
        selectAnswer(answerList.value[nextIndex])
      } else {
        // 没有更多答案，返回列表
        backToList()
        ElMessage.info('所有答案已评分完成')
      }
    } else {
      // 如果筛选条件是"全部"或"已评分"，保持在当前答案，只更新状态
      ElMessage.success('评分完成！您可以继续查看其他答案或返回列表')
    }
    
    ElMessage.success('评分已提交，学生可以查看最终成绩')
  } catch (error) {
    console.error('Failed to submit grading:', error)
    ElMessage.error('评分提交失败')
  } finally {
    submitting.value = false
  }
}

const resetGradingForm = () => {
  gradingForm.isCorrect = 1
  gradingForm.teacherScore = currentAnswerDetail.value.aiScore || 0
  gradingForm.teacherFeedback = ''
  gradingForm.improvementSuggestion = ''
}

const editGrading = () => {
  ElMessage.info('您可以修改评分，修改后需要重新提交')
}

const previousAnswer = () => {
  if (currentAnswerIndex.value > 0) {
    selectAnswer(answerList.value[currentAnswerIndex.value - 1])
  }
}

const nextAnswer = () => {
  if (currentAnswerIndex.value < answerList.value.length - 1) {
    selectAnswer(answerList.value[currentAnswerIndex.value + 1])
  }
}

const backToList = () => {
  currentAnswer.value = null
  currentAnswerDetail.value = {}
  currentAnswerIndex.value = 0
}

// 工具函数
const getQuestionTypeName = (type) => {
  const names = {
    1: '单选题',
    2: '多选题', 
    3: '填空题',
    4: '简答题',
    5: '编程题',
  }
  return names[type] || '未知'
}

const getQuestionTypeColor = (type) => {
  const colors = {
    1: 'success',
    2: 'warning',
    3: 'info',
    4: 'primary',
    5: 'danger',
    6: 'warning'
  }
  return colors[type] || 'info'
}

const getGradingStatusText = (status) => {
  const texts = {
    0: '未评分',
    1: '待评分',
    2: '已评分'
  }
  return texts[status] || '未知'
}

const getGradingStatusColor = (status) => {
  const colors = {
    0: 'info',
    1: 'warning',
    2: 'success'
  }
  return colors[status] || 'info'
}

const getDifficultyColor = (level) => {
  const colors = { 1: 'success', 2: 'warning', 3: 'danger' }
  return colors[level] || 'info'
}

const getDifficultyText = (level) => {
  const texts = { 1: '简单', 2: '中等', 3: '困难' }
  return texts[level] || '未知'
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 切换视图的方法
const switchToGradedView = () => {
  filterForm.gradingStatus = 2
  pagination.current = 1
  loadAnswers()
}

const switchToPendingView = () => {
  filterForm.gradingStatus = 1
  pagination.current = 1
  loadAnswers()
}

const switchToAllView = () => {
  filterForm.gradingStatus = null
  pagination.current = 1
  loadAnswers()
}

// 提取相似度信息的函数
const getSimilarityInfo = (aiFeedback) => {
  if (!aiFeedback) return null
  
  // 使用正则表达式提取相似度百分比
  const similarityMatch = aiFeedback.match(/答案相似度：(\d+\.?\d*)%/)
  if (!similarityMatch) return null
  
  const similarity = parseFloat(similarityMatch[1])
  
  let type = 'info'
  let title = '相似度分析'
  let description = `答案与标准答案的相似度为 ${similarity}%`
  
  if (similarity >= 80) {
    type = 'success'
    title = `🎉 高相似度 (${similarity}%)`
    description = '学生答案与标准答案高度相似，质量优秀，建议给予高分。'
  } else if (similarity >= 60) {
    type = 'warning'
    title = `✅ 中等相似度 (${similarity}%)`
    description = '学生答案基本正确，但可能缺少部分要点，建议适当扣分并给予指导。'
  } else if (similarity >= 40) {
    type = 'warning'
    title = `⚠️ 较低相似度 (${similarity}%)`
    description = '学生答案部分正确，但存在明显不足，建议重点复核并给予详细反馈。'
  } else {
    type = 'error'
    title = `❌ 低相似度 (${similarity}%)`
    description = '学生答案与标准答案差距较大，建议仔细评分并提供改进建议。'
  }
  
  return { type, title, description }
}
</script>

<style scoped>
.teacher-grading {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.filter-card, .list-card, .grading-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics {
  margin-top: 20px;
}

.statistics h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item .label {
  color: #606266;
}

.stat-item .value.pending {
  color: #e6a23c;
  font-weight: bold;
}

.stat-item .value.graded {
  color: #67c23a;
  font-weight: bold;
}

.stat-item .value.total {
  color: #303133;
  font-weight: bold;
}

.progress-item {
  margin-top: 15px;
}

.progress-item .label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
}

.progress-item .percentage {
  margin-left: 10px;
  color: #303133;
  font-weight: bold;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.answer-list {
  max-height: 600px;
  overflow-y: auto;
}

.answer-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.answer-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.answer-item.graded {
  background-color: #f0f9ff;
  border-color: #67c23a;
}

.answer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.student-name {
  font-weight: bold;
  color: #303133;
}

.answer-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.course-name {
  color: #606266;
  font-size: 12px;
}

.submit-time {
  color: #909399;
  font-size: 12px;
}

.answer-preview {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.question-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.student-answer {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.score-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.ai-score {
  color: #409eff;
  font-weight: bold;
}

.teacher-score {
  color: #67c23a;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.grading-content {
  max-height: 80vh;
  overflow-y: auto;
}

.question-section, .student-section, .grading-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.question-section:last-child, .student-section:last-child, .grading-section:last-child {
  border-bottom: none;
}

.question-section h3, .student-section h3, .grading-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.question-info, .student-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-row .label {
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-row .value {
  color: #303133;
}

.info-row .value.ai-score {
  color: #409eff;
  font-weight: bold;
}

.question-content, .standard-answer, .student-answer, .ai-feedback {
  margin-bottom: 15px;
}

.question-content h4, .standard-answer h4, .student-answer h4, .ai-feedback h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.content-text, .answer-text, .feedback-text {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  line-height: 1.6;
  color: #495057;
  white-space: pre-wrap;
}

.ai-feedback .feedback-text {
  background: #e8f4fd;
  border-color: #409eff;
  color: #409eff;
}

.score-hint {
  margin-left: 8px;
  color: #909399;
}

.ai-reference {
  margin-left: 15px;
  color: #409eff;
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.graded-info {
  margin: 15px 0;
}

.graded-info .el-alert {
  margin-bottom: 0;
}

.graded-info p {
  margin: 5px 0;
  color: #67c23a;
}

.graded-info strong {
  color: #303133;
}

.empty-description {
  margin-bottom: 20px;
}

.empty-description p {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.empty-actions .el-button {
  min-width: 120px;
}

/* 相似度高亮显示样式 */
.similarity-highlight {
  margin-top: 15px;
  padding: 10px;
  border-radius: 6px;
}

.similarity-highlight .el-alert {
  border: none;
  padding: 12px 16px;
}

.similarity-highlight .el-alert--success {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-left: 4px solid #52c41a;
}

.similarity-highlight .el-alert--warning {
  background: linear-gradient(135deg, #fffbf0 0%, #fff7e6 100%);
  border-left: 4px solid #faad14;
}

.similarity-highlight .el-alert--error {
  background: linear-gradient(135deg, #fff2f0 0%, #ffece6 100%);
  border-left: 4px solid #ff4d4f;
}

.similarity-highlight .el-alert__title {
  font-weight: bold;
  font-size: 16px;
}

.similarity-highlight .el-alert__description {
  margin-top: 8px;
  line-height: 1.5;
}
</style>