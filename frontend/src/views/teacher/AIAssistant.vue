<template>
  <div class="ai-assistant">
    <div class="page-header">
      <h2>AI教学助手</h2>
      <p>利用AI技术生成教学内容、题目和分析报告</p>
    </div>

    <el-row :gutter="20">
      <!-- 功能选择卡片 -->
      <el-col :span="4">
        <el-card class="function-card">
          <template #header>
            <div class="card-header">
              <span>AI功能</span>
            </div>
          </template>

          <div class="function-list">
            <div class="function-item" :class="{ active: activeFunction === 'content' }"
              @click="activeFunction = 'content'">
              <el-icon>
                <EditPen />
              </el-icon>
              <span>教学内容生成</span>
            </div>

            <div class="function-item" :class="{ active: activeFunction === 'question' }"
              @click="activeFunction = 'question'">
              <el-icon>
                <QuestionFilled />
              </el-icon>
              <span>题目生成</span>
            </div>

            <div class="function-item" :class="{ active: activeFunction === 'analysis' }"
              @click="activeFunction = 'analysis'">
              <el-icon>
                <DataAnalysis />
              </el-icon>
              <span>学情数据分析</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 主要工作区 -->
      <el-col :span="20">
        <!-- 教学内容生成 -->
        <el-card v-if="activeFunction === 'content'" class="work-card">
          <template #header>
            <div class="card-header">
              <span>教学内容生成</span>
            </div>
          </template>

          <el-form :model="contentForm" label-width="120px">
            <el-form-item label="选择课程">
              <el-select v-model="contentForm.courseId" placeholder="请选择课程">
                <el-option v-for="course in courses" :key="course.id" :label="course.courseName" :value="course.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="章节名称">
              <el-input v-model="contentForm.chapterName" placeholder="请输入章节名称" />
            </el-form-item>

            <el-form-item label="内容类型">
              <el-select v-model="contentForm.contentType" placeholder="选择内容类型">
                <el-option label="知识讲解" :value="1" />
                <el-option label="实训练习" :value="2" />
                <el-option label="指导说明" :value="3" />
              </el-select>
            </el-form-item>

            <el-form-item label="课程大纲">
              <el-input v-model="contentForm.courseOutline" type="textarea" :rows="4" placeholder="请输入课程大纲或相关背景信息" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleGenerateContent" :loading="generating"
                :disabled="!contentForm.courseOutline || !contentForm.chapterName">
                <el-icon>
                  <Magic />
                </el-icon>
                {{ generating ? 'AI生成中...' : '生成教学内容' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div v-if="generatedContent" class="generated-content">
            <h4>生成的教学内容</h4>
            <div class="content-preview">{{ generatedContent }}</div>
            <div class="content-actions">
              <el-button @click="copyContent">复制内容</el-button>
              <el-button type="primary" @click="saveGeneratedContent">保存为教学内容</el-button>
            </div>
          </div>
        </el-card>
        <!-- 题目生成 -->
        <el-card v-if="activeFunction === 'question'" class="work-card">
          <template #header>
            <div class="card-header">
              <span>AI题目生成</span>
            </div>
          </template>

          <el-form :model="questionForm" label-width="120px">
            <el-form-item label="选择课程" required>
              <el-select v-model="questionForm.courseId" placeholder="请选择课程">
                <el-option v-for="course in courses" :key="course.id" :label="course.courseName" :value="course.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="教学内容来源" required>
              <el-radio-group v-model="questionForm.contentSource" @change="handleContentSourceChange">
                <el-radio label="select">选择已有内容</el-radio>
                <el-radio label="input">手动输入</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="教学内容" required v-if="questionForm.contentSource === 'select'" v-loading="loading">
              <div class="content-select-wrapper">
                <el-select v-model="questionForm.selectedContentId" placeholder="请选择教学内容" @change="handleContentSelect"
                  filterable :disabled="!questionForm.courseId" clearable style="width: calc(100% - 80px);">
                  <el-option v-for="content in teachingContents" :key="content.id" :label="content.title"
                    :value="content.id">
                    <div class="content-option">
                      <div class="content-title">{{ content.title }}</div>
                      <div class="content-info">
                        <span>{{ content.chapterName }}</span>
                        <span>{{ getContentTypeName(content.contentType) }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
                <el-button @click="refreshTeachingContents" :loading="loading" :disabled="!questionForm.courseId"
                  style="margin-left: 8px;" title="刷新教学内容">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                </el-button>
              </div>
              <div class="form-item-tip" v-if="!questionForm.courseId">
                请先选择课程
              </div>
              <div class="form-item-tip" v-if="questionForm.courseId && teachingContents.length === 0 && !loading">
                <el-text type="warning">该课程暂无教学内容，请先创建教学内容或选择手动输入</el-text>
                <el-button type="text" @click="refreshTeachingContents" style="margin-left: 8px;">点击重试</el-button>
              </div>
              <div class="form-item-tip" v-if="questionForm.courseId && teachingContents.length > 0">
                <el-text type="info">找到 {{ teachingContents.length }} 条教学内容</el-text>
              </div>
            </el-form-item>

            <el-form-item label="教学内容" required v-else>
              <el-input v-model="questionForm.teachingContent" type="textarea" :rows="4" placeholder="请输入教学内容或知识点" />
            </el-form-item>

            <el-form-item label="题目类型" required>
              <el-select v-model="questionForm.questionType" placeholder="选择题目类型">
                <el-option label="选择题（单选）" value="SINGLE_CHOICE" />
                <el-option label="选择题（多选）" value="MULTIPLE_CHOICE" />
                <el-option label="填空题" value="FILL_BLANK" />
                <el-option label="简答题" value="SHORT_ANSWER" />
                <el-option label="编程题" value="PROGRAMMING" />

              </el-select>
            </el-form-item>

            <el-form-item label="难度等级">
              <el-radio-group v-model="questionForm.difficulty">
                <el-radio :label="1">简单</el-radio>
                <el-radio :label="2">中等</el-radio>
                <el-radio :label="3">困难</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="题目数量">
              <el-input-number v-model="questionForm.count" :min="1" :max="6" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleGenerateQuestions" :loading="generating"
                :disabled="!questionForm.teachingContent || !questionForm.courseId">
                <el-icon>
                  <Magic />
                </el-icon>
                {{ generating ? 'AI生成中...' : '生成题目' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div v-if="generatedQuestions.length > 0" class="generated-content">
            <h4>生成的题目</h4>
            <div class="questions-preview">
              <div v-for="(question, index) in generatedQuestions" :key="index" class="question-item">
                <div class="question-header">
                  <span>第 {{ index + 1 }} 题（{{ getQuestionTypeName(question.questionType) }}）</span>
                  <span class="difficulty">难度：{{ getDifficultyName(question.difficulty) }}</span>
                </div>
                <div class="question-content">{{ question.content }}</div>
                <template v-if="question.options">
                  <div class="question-options">
                    <div v-for="(option, key) in question.options" :key="key" class="option-item">
                      {{ key }}. {{ option }}
                    </div>
                  </div>
                </template>
                <div class="question-answer" v-if="question.correctAnswer">
                  <strong>参考答案：</strong>
                  <template v-if="isChoiceQuestion(question.questionType)">
                    {{ formatChoiceAnswer(question.correctAnswer) }}
                  </template>
                  <template v-else-if="question.questionType === 'FILL_BLANK'">
                    {{ formatFillBlankAnswer(question.correctAnswer) }}
                  </template>
                  <template v-else>
                    {{ question.correctAnswer }}
                  </template>
                </div>
                <div class="question-analysis">
                  <strong>答案解析：</strong>{{ question.answerAnalysis }}
                </div>
              </div>
            </div>
            <div class="content-actions">
              <el-button @click="copyQuestions">复制题目</el-button>
              <el-button type="primary" @click="saveGeneratedQuestions">保存到题库</el-button>
            </div>
          </div>
        </el-card>
        <!-- 学情数据分析 -->
        <div v-if="activeFunction === 'analysis'" class="analysis-container">
          <!-- 分析模式选择 -->
          <div class="analysis-mode-selector">
            <div class="mode-header">
              <div class="header-title">
                <el-icon class="header-icon">
                  <TrendCharts />
                </el-icon>
                <span>智能学情分析</span>
                <el-tag type="success" size="small">AI驱动</el-tag>
              </div>
            </div>

            <el-tabs v-model="analysisMode" class="analysis-tabs" @tab-change="handleAnalysisModeChange">
              <el-tab-pane label="课程整体分析" name="course">
                <template #label>
                  <div class="tab-label">
                    <el-icon>
                      <PieChart />
                    </el-icon>
                    <span>课程整体</span>
                  </div>
                </template>
              </el-tab-pane>
              <el-tab-pane label="个体学生分析" name="individual">
                <template #label>
                  <div class="tab-label">
                    <el-icon>
                      <User />
                    </el-icon>
                    <span>个体分析</span>
                  </div>
                </template>
              </el-tab-pane>
              <el-tab-pane label="答案深度分析" name="answer">
                <template #label>
                  <div class="tab-label">
                    <el-icon>
                      <DataAnalysis />
                    </el-icon>
                    <span>答案分析</span>
                  </div>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>
          <!-- 课程整体分析 -->
          <el-card v-if="analysisMode === 'course'" class="analysis-content-card">
            <template #header>
              <div class="analysis-card-header">
                <el-icon>
                  <PieChart />
                </el-icon>
                <span>课程整体分析</span>
              </div>
            </template>

            <!-- 课程选择和操作区域 -->
            <div class="course-selection-section">
              <el-row :gutter="20" align="middle">
                <el-col :span="12">
                  <div class="selection-group">
                    <label class="selection-label">选择分析课程</label>
                    <el-select v-model="courseAnalysisForm.courseId" placeholder="请选择要分析的课程" size="large"
                      style="width: 100%" @change="onCourseChange">
                      <el-option v-for="course in courses" :key="course.id" :label="course.courseName"
                        :value="course.id">
                        <div class="course-option">
                          <span class="course-name">{{ course.courseName }}</span>
                          <span style="margin:0 8px;">|</span>
                          <span class="course-info">{{ course.subject || '计算机科学' }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="action-buttons">
                    <el-button type="primary" size="large" @click="analyzeCourse" :loading="analyzing"
                      :disabled="!courseAnalysisForm.courseId" class="primary-btn">
                      <el-icon>
                        <TrendCharts />
                      </el-icon>
                      {{ analyzing ? 'AI分析中...' : '开始分析' }}
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 课程整体分析结果 -->
            <div v-if="courseOverallAnalysis" class="course-analysis-results">
              <!-- 核心指标卡片 -->
              <div class="metrics-grid">
                <div class="metric-card primary">
                  <div class="metric-icon">
                    <el-icon>
                      <User />
                    </el-icon>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ courseOverallAnalysis.totalStudents }}</div>
                    <div class="metric-label">参与学生</div>
                  </div>
                </div>

                <div class="metric-card success">
                  <div class="metric-icon">
                    <el-icon>
                      <CircleCheck />
                    </el-icon>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ courseOverallAnalysis.overallAccuracy?.toFixed(1) }}%</div>
                    <div class="metric-label">整体正确率</div>
                  </div>
                </div>

                <div class="metric-card warning">
                  <div class="metric-icon">
                    <el-icon>
                      <Star />
                    </el-icon>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ courseOverallAnalysis.avgScore?.toFixed(1) }}</div>
                    <div class="metric-label">平均分数</div>
                  </div>
                </div>

                <div class="metric-card info">
                  <div class="metric-icon">
                    <el-icon>
                      <EditPen />
                    </el-icon>
                  </div>
                  <div class="metric-content">
                    <div class="metric-value">{{ courseOverallAnalysis.totalAnswers }}</div>
                    <div class="metric-label">总答题数</div>
                  </div>
                </div>
              </div>

              <!-- AI智能洞察 -->
              <div class="ai-insights-card">
                <div class="insights-header">
                  <div class="insights-title-wrapper">
                    <div class="insights-icon-wrapper">
                      <el-icon class="insights-icon">
                        <Brain />
                      </el-icon>
                    </div>
                    <div class="insights-title-content">
                      <span class="insights-title">AI智能洞察</span>
                      <el-tag type="success" size="small" class="insights-tag">
                        <el-icon>
                          <Lightning />
                        </el-icon>
                        实时分析
                      </el-tag>
                    </div>
                  </div>
                </div>
                <div class="insights-content">
                  <div class="insight-item">
                    <div class="insight-icon-wrapper">
                      <el-icon class="insight-icon">
                        <TrendCharts />
                      </el-icon>
                    </div>
                    <div class="insight-text-wrapper">
                      <div class="insight-text">{{ courseOverallAnalysis.aiInsights }}</div>
                      <div class="insight-animation"></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学生表现分布 -->
              <div class="performance-distribution">
                <div class="section-header">
                  <div class="header-content">
                    <div class="header-icon-wrapper">
                      <el-icon class="header-icon">
                        <PieChart />
                      </el-icon>
                    </div>
                    <div class="header-text">
                      <h3 class="header-title">学生表现分布</h3>
                      <p class="header-subtitle">基于学生答题正确率的表现等级统计</p>
                    </div>
                  </div>
                  <div class="header-badge">
                    <el-tag type="info" size="small">实时统计</el-tag>
                  </div>
                </div>
                <div class="distribution-content">
                  <div class="distribution-visual">
                    <div class="performance-stats">
                      <div class="stat-item excellent">
                        <div class="stat-icon">
                          <el-icon>
                            <Trophy />
                          </el-icon>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ courseOverallAnalysis.performanceDistribution?.excellentPercent ||
                            0 }}%
                          </div>
                          <div class="stat-label">优秀 ({{ courseOverallAnalysis.performanceDistribution?.excellent || 0
                          }}人)
                          </div>
                        </div>
                      </div>
                      <div class="stat-item good">
                        <div class="stat-icon">
                          <el-icon>
                            <Star />
                          </el-icon>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ courseOverallAnalysis.performanceDistribution?.goodPercent || 0 }}%
                          </div>
                          <div class="stat-label">良好 ({{ courseOverallAnalysis.performanceDistribution?.good || 0 }}人)
                          </div>
                        </div>
                      </div>
                      <div class="stat-item average">
                        <div class="stat-icon">
                          <el-icon>
                            <CircleCheck />
                          </el-icon>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ courseOverallAnalysis.performanceDistribution?.averagePercent || 0
                          }}%
                          </div>
                          <div class="stat-label">中等 ({{ courseOverallAnalysis.performanceDistribution?.average || 0
                          }}人)
                          </div>
                        </div>
                      </div>
                      <div class="stat-item needs-improvement">
                        <div class="stat-icon">
                          <el-icon>
                            <Warning />
                          </el-icon>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{
                            courseOverallAnalysis.performanceDistribution?.needsImprovementPercent ||
                            0 }}%</div>
                          <div class="stat-label">待提高 ({{
                            courseOverallAnalysis.performanceDistribution?.needsImprovement || 0
                          }}人)</div>
                        </div>
                      </div>
                    </div>
                    <div class="distribution-bar">
                      <div class="bar-segment excellent"
                        :style="{ width: (courseOverallAnalysis.performanceDistribution?.excellentPercent || 0) + '%' }">
                      </div>
                      <div class="bar-segment good"
                        :style="{ width: (courseOverallAnalysis.performanceDistribution?.goodPercent || 0) + '%' }">
                      </div>
                      <div class="bar-segment average"
                        :style="{ width: (courseOverallAnalysis.performanceDistribution?.averagePercent || 0) + '%' }">
                      </div>
                      <div class="bar-segment needs-improvement"
                        :style="{ width: (courseOverallAnalysis.performanceDistribution?.needsImprovementPercent || 0) + '%' }">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 知识点掌握分析 -->
              <div class="knowledge-analysis">
                <div class="section-header">
                  <div class="header-content">
                    <div class="header-icon-wrapper">
                      <el-icon class="header-icon">
                        <DataBoard />
                      </el-icon>
                    </div>
                    <div class="header-text">
                      <h3 class="header-title">知识点掌握分析</h3>
                      <p class="header-subtitle">各知识点的掌握情况与教学建议</p>
                    </div>
                  </div>
                  <div class="header-badge">
                    <el-tag type="warning" size="small">智能分析</el-tag>
                  </div>
                </div>
                <div class="knowledge-grid">
                  <div v-for="knowledge in courseOverallAnalysis.knowledgePointAnalyses" :key="knowledge.knowledgePoint"
                    class="knowledge-card" :class="getKnowledgeCardClass(knowledge.avgAccuracy)">
                    <div class="knowledge-header">
                      <span class="knowledge-name">{{ knowledge.knowledgePoint }}</span>
                      <el-tag :type="getAccuracyTagType(knowledge.avgAccuracy)" size="small">
                        {{ knowledge.avgAccuracy?.toFixed(1) }}%
                      </el-tag>
                    </div>
                    <div class="knowledge-stats">
                      <div class="stat-item">
                        <span class="stat-label">题目数量</span>
                        <span class="stat-value">{{ knowledge.totalQuestions }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">难度等级</span>
                        <span class="stat-value">{{ knowledge.difficultyLevel }}</span>
                      </div>
                    </div>
                    <div class="knowledge-suggestion">
                      <el-icon>
                        <Lightbulb />
                      </el-icon>
                      <span>{{ knowledge.teachingSuggestion }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学生排名 -->
              <div class="student-rankings">
                <div class="section-header">
                  <div class="header-content">
                    <div class="header-icon-wrapper">
                      <el-icon class="header-icon">
                        <Trophy />
                      </el-icon>
                    </div>
                    <div class="header-text">
                      <h3 class="header-title">学生表现排名</h3>
                      <p class="header-subtitle">基于平均分和正确率的综合排名</p>
                    </div>
                  </div>
                  <div class="header-badge">
                    <el-tag type="success" size="small">TOP 10</el-tag>
                  </div>
                </div>
                <div class="rankings-table">
                  <el-table :data="courseOverallAnalysis.studentRankings" style="width: 100%">
                    <el-table-column prop="ranking" label="排名" width="80">
                      <template #default="scope">
                        <div class="ranking-badge" :class="getRankingClass(scope.row.ranking)">
                          {{ scope.row.ranking }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="studentName" label="学生姓名" />
                    <el-table-column prop="avgScore" label="平均分" width="100">
                      <template #default="scope">
                        <span class="score-value">{{ scope.row.avgScore?.toFixed(1) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="accuracy" label="正确率" width="100">
                      <template #default="scope">
                        <span class="accuracy-value">{{ scope.row.accuracy?.toFixed(1) }}%</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="performanceLevel" label="表现等级" width="120">
                      <template #default="scope">
                        <el-tag :type="getPerformanceTagType(scope.row.performanceLevel)">
                          {{ scope.row.performanceLevel }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150">
                      <template #default="scope">
                        <el-button size="small" @click="viewStudentDetail(scope.row)"
                          :loading="analyzing && currentAnalyzingStudent === scope.row.studentId" type="primary" link>
                          <el-icon>
                            <View />
                          </el-icon>
                          详细分析
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>

              <!-- 教学建议 -->
              <div class="teaching-suggestions">
                <div class="section-header">
                  <div class="header-content">
                    <div class="header-icon-wrapper">
                      <el-icon class="header-icon">
                        <Guide />
                      </el-icon>
                    </div>
                    <div class="header-text">
                      <h3 class="header-title">AI教学建议</h3>
                      <p class="header-subtitle">基于学情分析的个性化教学建议</p>
                    </div>
                  </div>
                  <div class="header-badge">
                    <el-tag type="primary" size="small">AI生成</el-tag>
                  </div>
                </div>
                <div class="suggestions-content">
                  <div class="suggestion-text">{{ courseOverallAnalysis.teachingSuggestions }}</div>
                </div>
              </div>
            </div>
          </el-card>
          <!-- 答案深度分析 -->
          <el-card v-if="analysisMode === 'answer'" class="analysis-content-card">
            <template #header>
              <div class="analysis-card-header">
                <div class="header-left">
                  <el-icon>
                    <DataAnalysis />
                  </el-icon>
                  <span>答案深度分析</span>
                  <el-tag type="warning" size="small">AI驱动</el-tag>
                </div>
              </div>
            </template>

            <!-- 课程选择和数据加载 -->
            <div class="answer-selection-section">
              <el-row :gutter="20" align="middle">
                <el-col :span="8">
                  <div class="selection-group">
                    <label class="selection-label">选择分析课程</label>
                    <el-select v-model="answerAnalysisForm.courseId" placeholder="请选择课程" size="large"
                      style="width: 100%" @change="loadStudentAnswers">
                      <el-option v-for="course in courses" :key="course.id" :label="course.courseName"
                        :value="course.id">
                        <div class="course-option">
                          <span class="course-name">{{ course.courseName }}</span>
                          <span style="margin:0 8px;">|</span>
                          <span class="course-info">{{ course.subject || '计算机科学' }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="selection-group">
                    <label class="selection-label">筛选条件</label>
                    <el-select v-model="answerAnalysisForm.filterType" placeholder="选择筛选条件" size="large"
                      style="width: 100%" @change="filterStudentAnswers">
                      <el-option label="全部答案" value="all" />
                      <el-option label="错误答案" value="incorrect" />
                      <el-option label="需要改进" value="needs_improvement" />
                      <el-option label="主观题答案" value="subjective" />
                    </el-select>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="action-buttons">
                    <el-button type="primary" size="large" @click="loadStudentAnswers" :loading="loadingAnswers"
                      :disabled="!answerAnalysisForm.courseId" class="primary-btn">
                      <el-icon>
                        <Refresh />
                      </el-icon>
                      {{ loadingAnswers ? '加载中...' : '加载答案' }}
                    </el-button>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 学生答案列表 -->
            <div v-if="studentAnswers.length > 0" class="answers-list-section">
              <div class="section-header">
                <div class="header-content">
                  <div class="header-icon-wrapper">
                    <el-icon class="header-icon">
                      <Document />
                    </el-icon>
                  </div>
                  <div class="header-text">
                    <h3 class="header-title">学生答案列表</h3>
                    <p class="header-subtitle">选择需要AI深度分析的答案</p>
                  </div>
                </div>
                <div class="header-badge">
                  <el-tag type="info" size="small">共 {{ studentAnswers.length }} 条</el-tag>
                </div>
              </div>

              <div class="answers-table">
                <el-table :data="filteredStudentAnswers" style="width: 100%" max-height="600">
                  <el-table-column prop="studentName" label="学生姓名" width="120" />
                  <el-table-column prop="questionTitle" label="题目" width="200">
                    <template #default="scope">
                      <el-tooltip :content="scope.row.questionContent" placement="top">
                        <span class="question-title-ellipsis">{{ scope.row.questionTitle }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column prop="studentAnswer" label="学生答案" min-width="250">
                    <template #default="scope">
                      <div class="answer-preview">
                        <span class="answer-text">{{ scope.row.studentAnswer?.substring(0, 100) }}{{
                          scope.row.studentAnswer?.length > 100 ? '...' : '' }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="isCorrect" label="正确性" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.isCorrect ? 'success' : 'danger'" size="small">
                        {{ scope.row.isCorrect ? '正确' : '错误' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="score" label="得分" width="80">
                    <template #default="scope">
                      <span class="score-value">{{ scope.row.score }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="difficultyLevel" label="难度" width="80">
                    <template #default="scope">
                      <el-tag :type="getDifficultyTagType(scope.row.difficultyLevel)" size="small">
                        {{ getDifficultyName(scope.row.difficultyLevel) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button size="small" type="primary" @click="analyzeSelectedAnswer(scope.row)"
                        :loading="analyzing && currentAnalyzingAnswer === scope.row.id">
                        <el-icon>
                          <DataAnalysis />
                        </el-icon>
                        AI分析
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- 无数据提示 -->
            <div v-else-if="answerAnalysisForm.courseId && !loadingAnswers" class="no-data-tip">
              <el-empty description="暂无学生答案数据">
                <el-button type="primary" @click="loadStudentAnswers">重新加载</el-button>
              </el-empty>
            </div>
          </el-card>
          <!-- AI分析结果 -->
          <el-card v-if="currentAnswerAnalysis" class="analysis-result-card">
            <template #header>
              <div class="analysis-card-header">
                <div class="header-left">
                  <el-icon>
                    <Brain />
                  </el-icon>
                  <span>AI深度分析结果</span>
                  <el-tag type="success" size="small">智能分析</el-tag>
                </div>
              </div>
            </template>

            <div class="answer-analysis-result">
              <!-- 答案基本信息 -->
              <div class="answer-info-section">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-card">
                      <h4>题目信息</h4>
                      <div class="info-item">
                        <span class="label">题目：</span>
                        <span>{{ currentAnswerAnalysis.questionTitle }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">学生：</span>
                        <span>{{ currentAnswerAnalysis.studentName }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">得分：</span>
                        <span class="score-value">{{ currentAnswerAnalysis.score }}</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-card">
                      <h4>答案对比</h4>
                      <div class="answer-comparison">
                        <div class="correct-answer">
                          <strong>标准答案：</strong>
                          <p>{{ currentAnswerAnalysis.correctAnswer }}</p>
                        </div>
                        <div class="student-answer">
                          <strong>学生答案：</strong>
                          <p>{{ currentAnswerAnalysis.studentAnswer }}</p>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- AI分析内容 -->
              <div class="ai-analysis-content">
                <div class="analysis-section">
                  <h4>
                    <el-icon>
                      <Lightning />
                    </el-icon>
                    AI智能分析
                  </h4>
                  <div class="analysis-markdown-content">
                    <MdPreview :modelValue="currentAnswerAnalysis.analysis || ''" :theme="'light'"
                      :previewTheme="'github'" :codeTheme="'github'" :showCodeRowNumber="true" />
                  </div>
                </div>

                <div class="analysis-section" v-if="currentAnswerAnalysis.personalizedAnalysis">
                  <h4>
                    <el-icon>
                      <User />
                    </el-icon>
                    个性化建议
                  </h4>
                  <div class="analysis-markdown-content">
                    <MdPreview :modelValue="currentAnswerAnalysis.personalizedAnalysis || ''" :theme="'light'"
                      :previewTheme="'github'" :codeTheme="'github'" :showCodeRowNumber="true" />
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="analysis-actions">
                <el-button @click="copyAnalysisResult">
                  <el-icon>
                    <CopyDocument />
                  </el-icon>
                  复制分析结果
                </el-button>
              </div>
            </div>
          </el-card>
          <!-- 个体学生分析 -->
          <el-card v-if="analysisMode === 'individual'" class="analysis-content-card">
            <template #header>
              <div class="analysis-card-header">
                <div class="header-left">
                  <el-icon>
                    <User />
                  </el-icon>
                  <span>个体学生分析</span>
                  <span v-if="singleStudentReport" class="student-name-tag">
                    - {{ singleStudentReport.basicInfo?.studentName }}
                  </span>
                </div>
                <div class="header-right">
                  <el-button v-if="singleStudentReport" size="small" @click="backToCourseAnalysis" type="info">
                    <el-icon>
                      <ArrowLeft />
                    </el-icon>
                    返回上级
                  </el-button>
                  <el-button v-if="!singleStudentReport" size="small" @click="viewAnalysisHistory" type="info">
                    <el-icon>
                      <Clock />
                    </el-icon>
                    历史记录
                  </el-button>
                </div>
              </div>
            </template>
            <!-- 来源提示 -->
            <div v-if="singleStudentReport" class="analysis-source-tip">
              <el-alert title="学生详情分析" :description="`正在查看 ${singleStudentReport.basicInfo?.studentName} 的详细学习分析报告`"
                type="info" :closable="false" show-icon>
              </el-alert>
            </div>
            <!-- 学生选择和操作区域 -->
            <div v-if="!singleStudentReport" class="student-selection-section">
              <el-row :gutter="20" align="middle">
                <el-col :span="10">
                  <div class="selection-group">
                    <label class="selection-label">选择分析课程</label>
                    <el-select v-model="singleAnalysisForm.courseId" placeholder="请选择课程" @change="loadStudents"
                      style="width: 100%" size="large">
                      <el-option v-for="course in courses" :key="course.id" :label="course.courseName"
                        :value="course.id" />
                    </el-select>
                  </div>
                </el-col>
                <el-col :span="10">
                  <div class="selection-group">
                    <label class="selection-label">选择分析学生</label>
                    <el-select v-model="singleAnalysisForm.studentId" placeholder="请选择学生" filterable
                      :loading="loadingStudents" style="width: 100%" size="large">
                      <el-option v-for="student in students" :key="student.id" :label="student.realName"
                        :value="student.id" />
                    </el-select>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="action-buttons">
                    <el-button type="primary" size="large" @click="analyzeSingleStudent" :loading="analyzing"
                      :disabled="!singleAnalysisForm.courseId || !singleAnalysisForm.studentId" class="primary-btn">
                      <el-icon>
                        <DataAnalysis />
                      </el-icon>
                      {{ analyzing ? 'AI分析中...' : '开始分析' }}
                    </el-button>
                  </div>
                </el-col>
              </el-row>

              <!-- 分析维度选择 -->
              <div class="analysis-dimensions-section">
                <label class="dimension-label">分析维度选择</label>
                <el-checkbox-group v-model="singleAnalysisForm.analysisDimensions" class="dimension-checkboxes">
                  <el-checkbox label="performance">学习表现</el-checkbox>
                  <el-checkbox label="knowledge">知识掌握</el-checkbox>
                  <el-checkbox label="errors">错误分析</el-checkbox>
                  <el-checkbox label="behavior">学习行为</el-checkbox>
                  <el-checkbox label="ai_diagnosis">AI诊断</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
            <!-- 复制分析结果 -->
            <div class="content-actions" style="margin-top: 20px;">
              <el-button @click="copyAnalysis">
                <el-icon>
                  <CopyDocument />
                </el-icon>
                复制分析结果
              </el-button>

            </div>
          </el-card>
          <!-- 批量学生分析 -->
          <div v-if="analysisMode === 'batch'">
            <el-form :model="batchAnalysisForm" label-width="120px">
              <el-form-item label="选择课程" required>
                <el-select v-model="batchAnalysisForm.courseId" placeholder="请选择课程" @change="loadStudentsForBatch">
                  <el-option v-for="course in courses" :key="course.id" :label="course.courseName" :value="course.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="选择学生" required>
                <el-select v-model="batchAnalysisForm.studentIds" placeholder="请选择学生（可多选）" multiple filterable
                  :loading="loadingStudents">
                  <el-option v-for="student in students" :key="student.id" :label="student.realName"
                    :value="student.id" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="analyzeBatchStudents" :loading="analyzing"
                  :disabled="!batchAnalysisForm.courseId || !batchAnalysisForm.studentIds.length">
                  <el-icon>
                    <DataAnalysis />
                  </el-icon>
                  {{ analyzing ? 'AI批量分析中...' : '批量分析' }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 分析结果展示 -->
          <div v-if="analysisResult" class="analysis-results">
            <el-divider content-position="left">
              <h3>分析结果</h3>
            </el-divider>

            <!-- 单个学生分析结果 -->
            <div v-if="singleStudentReport" class="single-analysis-result">

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-card class="result-card">
                    <template #header>
                      <span>基本信息</span>
                    </template>
                    <div class="info-item">
                      <span class="label">学生姓名：</span>
                      <span>{{ singleStudentReport.basicInfo?.studentName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">总答题数：</span>
                      <span>{{ singleStudentReport.basicInfo?.totalAnswers }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">总练习数：</span>
                      <span>{{ singleStudentReport.basicInfo?.totalPractices }}</span>
                    </div>
                  </el-card>
                </el-col>

                <el-col :span="12">
                  <el-card class="result-card">
                    <template #header>
                      <span>学习表现</span>
                    </template>
                    <div class="info-item">
                      <span class="label">整体正确率：</span>
                      <span class="performance-value">{{ singleStudentReport.performance?.overallAccuracy?.toFixed(1)
                      }}%</span>
                    </div>
                    <div class="info-item">
                      <span class="label">平均分：</span>
                      <span class="performance-value">{{ singleStudentReport.performance?.avgTeacherScore?.toFixed(1)
                      }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">表现等级：</span>
                      <el-tag :type="getPerformanceTagType(singleStudentReport.performance?.performanceLevel)">
                        {{ singleStudentReport.performance?.performanceLevel }}
                      </el-tag>
                    </div>
                  </el-card>
                </el-col>
              </el-row>

              <!-- 知识点掌握情况 -->
              <el-card class="result-card" style="margin-top: 20px;">
                <template #header>
                  <span>知识点掌握情况</span>
                </template>
                <el-table :data="singleStudentReport.knowledgeMastery" style="width: 100%">
                  <el-table-column prop="knowledgePoint" label="知识点" />
                  <el-table-column prop="totalQuestions" label="总题数" width="100" />
                  <el-table-column prop="correctQuestions" label="正确数" width="100" />
                  <el-table-column prop="accuracyRate" label="正确率" width="120">
                    <template #default="scope">
                      {{ scope.row.accuracyRate?.toFixed(1) }}%
                    </template>
                  </el-table-column>
                  <el-table-column prop="masteryDescription" label="掌握程度" width="120">
                    <template #default="scope">
                      <el-tag :type="getMasteryTagType(scope.row.masteryLevel)">
                        {{ scope.row.masteryDescription }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>

              <!-- AI智能诊断 -->
              <el-card class="result-card" style="margin-top: 20px;" v-if="singleStudentReport.aiDiagnosis">
                <template #header>
                  <span>AI智能诊断</span>
                </template>
                <div class="ai-diagnosis">
                  <div class="diagnosis-section">
                    <h4>整体评估</h4>
                    <p>{{ singleStudentReport.aiDiagnosis.overallAssessment }}</p>
                  </div>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <div class="diagnosis-section">
                        <h4>主要优势</h4>
                        <ul>
                          <li v-for="strength in singleStudentReport.aiDiagnosis.strengths" :key="strength">
                            {{ strength }}
                          </li>
                        </ul>
                      </div>
                    </el-col>

                    <el-col :span="12">
                      <div class="diagnosis-section">
                        <h4>需要改进</h4>
                        <ul>
                          <li v-for="weakness in singleStudentReport.aiDiagnosis.weaknesses" :key="weakness">
                            {{ weakness }}
                          </li>
                        </ul>
                      </div>
                    </el-col>
                  </el-row>

                  <div class="diagnosis-section">
                    <h4>个性化建议</h4>
                    <ol>
                      <li v-for="suggestion in singleStudentReport.aiDiagnosis.personalizedSuggestions"
                        :key="suggestion">
                        {{ suggestion }}
                      </li>
                    </ol>
                  </div>

                  <div class="diagnosis-section">
                    <h4>下一步学习计划</h4>
                    <p>{{ singleStudentReport.aiDiagnosis.nextStepPlan }}</p>
                  </div>
                </div>
              </el-card>

              <!-- 统一操作按钮区域 -->
              <div class="analysis-actions-unified"
                style="margin-top: 24px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                <el-space :size="16">
                  <el-button @click="exportToTxt" type="primary" size="large" :disabled="!singleStudentReport">
                    <el-icon>
                      <Document />
                    </el-icon>
                    导出TXT报告
                  </el-button>
                  <el-button @click="exportToExcel" type="success" size="large"
                    :disabled="!singleStudentReport?.analysisResultId">
                    <el-icon>
                      <Download />
                    </el-icon>
                    导出Excel报告
                  </el-button>
                  <el-button @click="viewAnalysisHistory" type="info" size="large">
                    <el-icon>
                      <Clock />
                    </el-icon>
                    查看历史记录
                  </el-button>
                </el-space>
                <div style="margin-top: 12px; color: #909399; font-size: 12px;">
                  <el-icon>
                    <InfoFilled />
                  </el-icon>
                  分析数据已自动保存，支持TXT文本和Excel表格两种格式导出
                </div>
              </div>
            </div>
          </div>
          <!-- 批量分析结果 -->
          <div v-if="analysisMode === 'batch' && batchAnalysisResults.length > 0" class="batch-analysis-result">
            <el-table :data="batchAnalysisResults" style="width: 100%">
              <el-table-column prop="basicInfo.studentName" label="学生姓名" />
              <el-table-column prop="performance.overallAccuracy" label="正确率" width="120">
                <template #default="scope">
                  {{ scope.row.performance?.overallAccuracy?.toFixed(1) }}%
                </template>
              </el-table-column>
              <el-table-column prop="performance.avgTeacherScore" label="平均分" width="120">
                <template #default="scope">
                  {{ scope.row.performance?.avgTeacherScore?.toFixed(1) }}
                </template>
              </el-table-column>
              <el-table-column prop="performance.performanceLevel" label="表现等级" width="120">
                <template #default="scope">
                  <el-tag :type="getPerformanceTagType(scope.row.performance?.performanceLevel)">
                    {{ scope.row.performance?.performanceLevel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" @click="viewDetailReport(scope.row)">
                    查看详情
                  </el-button>
                  <el-button size="small" type="primary" @click="generateStudentReport(scope.row)">
                    生成报告
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 答案分析结果 -->
          <div v-if="analysisMode === 'answer' && currentAnswerAnalysis" class="answer-analysis-result">
            <el-card class="analysis-detail-card">
              <template #header>
                <div class="analysis-detail-header">
                  <div class="header-left">
                    <el-icon>
                      <DataAnalysis />
                    </el-icon>
                    <span>AI深度分析结果</span>
                    <el-tag type="success" size="small">智能分析</el-tag>
                  </div>
                  <div class="header-actions">
                    <el-button size="small" @click="copyAnalysisResult">
                      <el-icon>
                        <CopyDocument />
                      </el-icon>
                      复制结果
                    </el-button>
                    <el-button size="small" type="primary" @click="exportAnalysisToTxt">
                      <el-icon>
                        <Download />
                      </el-icon>
                      导出TXT
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 题目信息区域 -->
              <div class="question-info-section">
                <div class="section-header">
                  <div class="header-icon-wrapper">
                    <el-icon class="header-icon">
                      <QuestionFilled />
                    </el-icon>
                  </div>
                  <div class="header-text">
                    <h3 class="header-title">题目信息</h3>
                    <p class="header-subtitle">完整的题目内容与相关信息</p>
                  </div>
                </div>

                <div class="question-detail-card">
                  <div class="question-meta">
                    <div class="meta-item">
                      <span class="meta-label">题目类型：</span>
                      <el-tag :type="getQuestionTypeTagType(currentAnswerAnalysis.questionType)" size="small">
                        {{ getQuestionTypeName(currentAnswerAnalysis.questionType) }}
                      </el-tag>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">难度等级：</span>
                      <el-tag :type="getDifficultyTagType(currentAnswerAnalysis.difficulty)" size="small">
                        {{ getDifficultyName(currentAnswerAnalysis.difficulty) }}
                      </el-tag>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">知识点：</span>
                      <span class="meta-value">{{ currentAnswerAnalysis.knowledgePoint || '未分类' }}</span>
                    </div>
                  </div>

                  <div class="question-content-wrapper">
                    <div class="content-label">
                      <el-icon>
                        <EditPen />
                      </el-icon>
                      <span>题目内容</span>
                    </div>
                    <div class="question-content" v-html="renderMarkdown(currentAnswerAnalysis.questionContent)"></div>
                  </div>

                  <!-- 选择题选项 -->
                  <div v-if="currentAnswerAnalysis.options && Object.keys(currentAnswerAnalysis.options).length > 0"
                    class="question-options-wrapper">
                    <div class="content-label">
                      <el-icon>
                        <List />
                      </el-icon>
                      <span>选项</span>
                    </div>
                    <div class="question-options">
                      <div v-for="(option, key) in currentAnswerAnalysis.options" :key="key" class="option-item"
                        :class="{ 'correct-option': isCorrectOption(key, currentAnswerAnalysis.correctAnswer) }">
                        <span class="option-key">{{ key }}.</span>
                        <span class="option-text" v-html="renderMarkdown(option)"></span>
                        <el-icon v-if="isCorrectOption(key, currentAnswerAnalysis.correctAnswer)" class="correct-icon">
                          <CircleCheck />
                        </el-icon>
                      </div>
                    </div>
                  </div>

                  <!-- 标准答案 -->
                  <div class="correct-answer-wrapper">
                    <div class="content-label">
                      <el-icon>
                        <CircleCheck />
                      </el-icon>
                      <span>标准答案</span>
                    </div>
                    <div class="correct-answer" v-html="renderMarkdown(formatCorrectAnswer(currentAnswerAnalysis))">
                    </div>
                  </div>
                </div>
              </div>

              <!-- 学生答案区域 -->
              <div class="student-answer-section">
                <div class="section-header">
                  <div class="header-icon-wrapper">
                    <el-icon class="header-icon">
                      <User />
                    </el-icon>
                  </div>
                  <div class="header-text">
                    <h3 class="header-title">学生答案</h3>
                    <p class="header-subtitle">{{ currentAnswerAnalysis.studentName }} 的作答情况</p>
                  </div>
                </div>

                <div class="student-answer-card">
                  <div class="answer-meta">
                    <div class="meta-item">
                      <span class="meta-label">学生姓名：</span>
                      <span class="meta-value">{{ currentAnswerAnalysis.studentName }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">答题时间：</span>
                      <span class="meta-value">{{ formatDateTime(currentAnswerAnalysis.answerTime) }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">答题结果：</span>
                      <el-tag :type="currentAnswerAnalysis.isCorrect ? 'success' : 'danger'" size="small">
                        {{ currentAnswerAnalysis.isCorrect ? '正确' : '错误' }}
                      </el-tag>
                    </div>
                    <div class="meta-item" v-if="currentAnswerAnalysis.score !== undefined">
                      <span class="meta-label">得分：</span>
                      <span class="meta-value score-value">{{ currentAnswerAnalysis.score }} 分</span>
                    </div>
                  </div>

                  <div class="student-answer-content">
                    <div class="content-label">
                      <el-icon>
                        <Edit />
                      </el-icon>
                      <span>学生答案</span>
                    </div>
                    <div class="answer-text"
                      :class="{ 'correct-answer-text': currentAnswerAnalysis.isCorrect, 'incorrect-answer-text': !currentAnswerAnalysis.isCorrect }"
                      v-html="renderMarkdown(currentAnswerAnalysis.studentAnswer)">
                    </div>
                  </div>
                </div>
              </div>

              <!-- AI智能分析区域 -->
              <div class="ai-analysis-section">
                <div class="section-header">
                  <div class="header-icon-wrapper">
                    <el-icon class="header-icon">
                      <Lightning />
                    </el-icon>
                  </div>
                  <div class="header-text">
                    <h3 class="header-title">AI智能分析</h3>
                    <p class="header-subtitle">基于深度学习的答案分析与建议</p>
                  </div>
                  <div class="header-badge">
                    <el-tag type="primary" size="small">AI驱动</el-tag>
                  </div>
                </div>

                <div class="ai-analysis-content">
                  <!-- 综合分析 -->
                  <div class="analysis-block">
                    <div class="block-header">
                      <el-icon>
                        <TrendCharts />
                      </el-icon>
                      <span>综合分析</span>
                    </div>
                    <div class="analysis-text markdown-content" v-html="renderMarkdown(currentAnswerAnalysis.analysis)">
                    </div>
                  </div>

                  <!-- 错误分析 -->
                  <div v-if="!currentAnswerAnalysis.isCorrect && currentAnswerAnalysis.errorAnalysis"
                    class="analysis-block error-analysis">
                    <div class="block-header">
                      <el-icon>
                        <Warning />
                      </el-icon>
                      <span>错误分析</span>
                    </div>
                    <div class="analysis-text markdown-content"
                      v-html="renderMarkdown(currentAnswerAnalysis.errorAnalysis)">
                    </div>
                  </div>

                  <!-- 知识点分析 -->
                  <div v-if="currentAnswerAnalysis.knowledgeAnalysis" class="analysis-block">
                    <div class="block-header">
                      <el-icon>
                        <DataBoard />
                      </el-icon>
                      <span>知识点掌握分析</span>
                    </div>
                    <div class="analysis-text markdown-content"
                      v-html="renderMarkdown(currentAnswerAnalysis.knowledgeAnalysis)"></div>
                  </div>

                  <!-- 个性化建议 -->
                  <div v-if="currentAnswerAnalysis.personalizedAnalysis" class="analysis-block suggestion-analysis">
                    <div class="block-header">
                      <el-icon>
                        <Lightbulb />
                      </el-icon>
                      <span>个性化建议</span>
                    </div>
                    <div class="analysis-text markdown-content"
                      v-html="renderMarkdown(currentAnswerAnalysis.personalizedAnalysis)"></div>
                  </div>

                  <!-- 改进方向 -->
                  <div v-if="currentAnswerAnalysis.improvementSuggestions" class="analysis-block improvement-analysis">
                    <div class="block-header">
                      <el-icon>
                        <Guide />
                      </el-icon>
                      <span>改进方向</span>
                    </div>
                    <div class="analysis-text markdown-content"
                      v-html="renderMarkdown(currentAnswerAnalysis.improvementSuggestions)"></div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>


        </div>
      </el-col>
    </el-row>
  </div>

  <!-- 分析历史记录对话框 -->
  <el-dialog v-model="historyDialogVisible" title="分析历史记录" :before-close="() => historyDialogVisible = false"
    :close-on-click-modal="false" :close-on-press-escape="false">
    <el-table :data="analysisHistory" style="width: 100%">
      <el-table-column prop="typeName" label="分析类型" />
      <el-table-column prop="targetName" label="分析对象" />
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="exportHistoryToExcel(scope.row.id)" type="success">
            <el-icon>
              <Download />
            </el-icon>
            导出Excel
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="historyDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
/* 学情数据分析样式优化 */
.analysis-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  min-height: 600px;
}

/* 个体分析头部样式 */
.analysis-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.student-name-tag {
  color: #409eff;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 分析来源提示 */
.analysis-source-tip {
  margin-bottom: 20px;
}

.analysis-source-tip .el-alert {
  border-radius: 8px;
}

/* 答案分析样式 */
.answer-selection-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.answers-list-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-title-ellipsis {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.answer-preview {
  max-height: 60px;
  overflow: hidden;
}

.answer-text {
  line-height: 1.4;
  color: #666;
}

.analysis-result-card {
  margin-top: 20px;
}

.answer-info-section {
  margin-bottom: 20px;
}

.info-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
}

.info-card h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.answer-comparison {
  margin-top: 12px;
}

.correct-answer,
.student-answer {
  margin-bottom: 12px;
}

.correct-answer p,
.student-answer p {
  margin: 4px 0 0 0;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #409eff;
  font-size: 14px;
  line-height: 1.4;
}

.student-answer p {
  border-left-color: #67c23a;
}

.ai-analysis-content {
  margin-bottom: 20px;
}

.analysis-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-text {
  line-height: 1.6;
  color: #666;
  white-space: pre-wrap;
}

.analysis-markdown-content {
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.analysis-markdown-content :deep(.md-editor-preview) {
  padding: 16px;
  background: #fafbfc;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
}

.analysis-markdown-content :deep(.md-editor-preview h1),
.analysis-markdown-content :deep(.md-editor-preview h2),
.analysis-markdown-content :deep(.md-editor-preview h3),
.analysis-markdown-content :deep(.md-editor-preview h4),
.analysis-markdown-content :deep(.md-editor-preview h5),
.analysis-markdown-content :deep(.md-editor-preview h6) {
  color: #2c3e50;
  margin-top: 16px;
  margin-bottom: 8px;
}

.analysis-markdown-content :deep(.md-editor-preview p) {
  margin-bottom: 12px;
  color: #555;
}

.analysis-markdown-content :deep(.md-editor-preview ul),
.analysis-markdown-content :deep(.md-editor-preview ol) {
  margin-bottom: 12px;
  padding-left: 20px;
}

.analysis-markdown-content :deep(.md-editor-preview li) {
  margin-bottom: 4px;
  color: #555;
}

.analysis-markdown-content :deep(.md-editor-preview blockquote) {
  border-left: 4px solid #409eff;
  background: #f0f9ff;
  padding: 12px 16px;
  margin: 12px 0;
  color: #2c3e50;
}

.analysis-markdown-content :deep(.md-editor-preview code) {
  background: #f1f2f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #e83e8c;
}

.analysis-markdown-content :deep(.md-editor-preview pre) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
}

.analysis-markdown-content :deep(.md-editor-preview pre code) {
  background: transparent;
  padding: 0;
  color: inherit;
}

.analysis-markdown-content :deep(.md-editor-preview table) {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

.analysis-markdown-content :deep(.md-editor-preview th),
.analysis-markdown-content :deep(.md-editor-preview td) {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.analysis-markdown-content :deep(.md-editor-preview th) {
  background: #f8f9fa;
  font-weight: 600;
}

.no-data-tip {
  text-align: center;
  padding: 40px 20px;
}

/* 学生选择区域样式 */
.student-selection-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.selection-group {
  margin-bottom: 16px;
}

.selection-label {
  display: block;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  font-size: 14px;
}

.analysis-dimensions-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.dimension-label {
  display: block;
  font-weight: 500;
  color: #606266;
  margin-bottom: 12px;
  font-size: 14px;
}

.dimension-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* 答案分析区域样式 */
.answer-analysis-section {
  padding: 0;
}

.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.student-answer-section {
  margin: 20px 0;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.analysis-actions {
  text-align: center;
  padding: 20px 0;
}

.primary-btn {
  padding: 12px 32px;
  font-size: 16px;
}

.analysis-mode-selector {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mode-header {
  margin-bottom: 20px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-icon {
  font-size: 20px;
  color: #409eff;
}

.analysis-tabs {
  margin-top: 16px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.analysis-content-card {
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.analysis-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.course-selection-section {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.selection-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selection-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: center;
}

.primary-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
}

.individual-analysis-form {
  padding: 20px 0;
}

.analysis-dimensions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dimension-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.dimension-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.analysis-actions {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 12px;
}

/* 指标卡片样式 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-card.primary {
  border-left: 4px solid #409eff;
}

.metric-card.success {
  border-left: 4px solid #67c23a;
}

.metric-card.warning {
  border-left: 4px solid #e6a23c;
}

.metric-card.info {
  border-left: 4px solid #909399;
}

.content-select-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.form-item-tip {
  margin-top: 8px;
  font-size: 13px;
}
</style>

<script setup>
import { ref, reactive, onMounted, watch, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { MdPreview } from 'md-editor-v3'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import 'md-editor-v3/lib/style.css'
import { getToken } from '@/utils/auth'
import { getCourses, createTeachingContent, getTeachingContents, batchSaveQuestions } from '@/api/teacher'
import {
  generateContent,
  generateQuestions as aiGenerateQuestions
} from '@/api/ai'
import {
  getComprehensiveReport,
  batchAnalyzeStudents,
  intelligentAnswerAnalysis,
  getCourseStudents,
  analyzeCourseOverall,
  analyzeStudentIndividual,
  getQuickInsights,
  generateAnalysisReport,
  exportAnalysisData,
  getStudentAnswers
} from '@/api/analytics'

const activeFunction = ref('content')
const generating = ref(false)
const loading = ref(false)
const courses = ref([])

const generatedContent = ref('')
const generatedQuestions = ref('')
const analysisResult = ref('')

const teachingContents = ref([])

const contentForm = reactive({
  courseId: '',
  chapterName: '',
  contentType: '',
  courseOutline: '', // 添加 courseOutline 字段
  title: '',         // 添加 title 字段
  difficulty: 2,     // 添加默认难度
  timeAllocation: 60 // 添加默认时间分配
})

const questionForm = reactive({
  courseId: '',
  contentSource: 'input', // 默认手动输入
  selectedContentId: null,
  teachingContent: '',
  questionType: 'SINGLE_CHOICE',  // 默认选择单选题
  difficulty: 1,
  count: 5
})

// 学情分析相关数据
const analysisMode = ref('single')
const analyzing = ref(false)
const currentAnalyzingStudent = ref(null)
const students = ref([])
const loadingStudents = ref(false)
const singleStudentReport = ref(null)
const batchAnalysisResults = ref([])

const singleAnalysisForm = reactive({
  courseId: '',
  studentId: '',
  analysisDimensions: ['performance', 'knowledge', 'errors', 'ai_diagnosis']
})

const batchAnalysisForm = reactive({
  courseId: '',
  studentIds: []
})

const answerAnalysisForm = reactive({
  courseId: '',
  filterType: 'all'
})

// 答案分析相关数据
const studentAnswers = ref([])
const filteredStudentAnswers = ref([])
const loadingAnswers = ref(false)
const currentAnswerAnalysis = ref(null)
const currentAnalyzingAnswer = ref(null)

// 课程整体分析相关数据
const courseAnalysisForm = reactive({
  courseId: ''
})

const courseOverallAnalysis = ref(null)

// 配置marked
marked.setOptions({
  highlight: function (code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) { }
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// Markdown渲染方法
const renderMarkdown = (text) => {
  if (!text) return ''
  return marked(text)
}

// 格式化标准答案
const formatCorrectAnswer = (analysis) => {
  if (!analysis.correctAnswer) return '无标准答案'

  if (analysis.questionType === 'SINGLE_CHOICE' || analysis.questionType === 'MULTIPLE_CHOICE') {
    return `**${analysis.correctAnswer}**`
  } else if (analysis.questionType === 'FILL_BLANK') {
    return `**${analysis.correctAnswer}**`
  } else {
    return analysis.correctAnswer
  }
}

// 判断是否为正确选项
const isCorrectOption = (optionKey, correctAnswer) => {
  if (!correctAnswer) return false
  return correctAnswer.includes(optionKey)
}

// 获取题目类型标签类型
const getQuestionTypeTagType = (type) => {
  const typeMap = {
    'SINGLE_CHOICE': 'primary',
    'MULTIPLE_CHOICE': 'success',
    'FILL_BLANK': 'warning',
    'SHORT_ANSWER': 'info',
    'PROGRAMMING': 'danger'
  }
  return typeMap[type] || 'info'
}


// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '未知时间'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 导出分析结果为TXT
const exportAnalysisToTxt = () => {
  if (!currentAnswerAnalysis.value) {
    ElMessage.warning('没有可导出的分析结果')
    return
  }

  const analysis = currentAnswerAnalysis.value
  let content = `AI深度分析结果报告\n`
  content += `生成时间：${new Date().toLocaleString('zh-CN')}\n`
  content += `${'='.repeat(50)}\n\n`

  // 题目信息
  content += `【题目信息】\n`
  content += `题目类型：${getQuestionTypeName(analysis.questionType)}\n`
  content += `难度等级：${getDifficultyName(analysis.difficulty)}\n`
  content += `知识点：${analysis.knowledgePoint || '未分类'}\n`
  content += `题目内容：${analysis.questionContent}\n`

  // 选择题选项
  if (analysis.options && Object.keys(analysis.options).length > 0) {
    content += `\n选项：\n`
    Object.entries(analysis.options).forEach(([key, option]) => {
      const isCorrect = isCorrectOption(key, analysis.correctAnswer)
      content += `${key}. ${option}${isCorrect ? ' ✓' : ''}\n`
    })
  }

  content += `标准答案：${analysis.correctAnswer}\n\n`

  // 学生答案
  content += `【学生答案】\n`
  content += `学生姓名：${analysis.studentName}\n`
  content += `答题时间：${formatDateTime(analysis.answerTime)}\n`
  content += `答题结果：${analysis.isCorrect ? '正确' : '错误'}\n`
  if (analysis.score !== undefined) {
    content += `得分：${analysis.score} 分\n`
  }
  content += `学生答案：${analysis.studentAnswer}\n\n`

  // AI分析
  content += `【AI智能分析】\n`
  content += `综合分析：\n${analysis.analysis}\n\n`

  if (!analysis.isCorrect && analysis.errorAnalysis) {
    content += `错误分析：\n${analysis.errorAnalysis}\n\n`
  }

  if (analysis.knowledgeAnalysis) {
    content += `知识点掌握分析：\n${analysis.knowledgeAnalysis}\n\n`
  }

  if (analysis.personalizedAnalysis) {
    content += `个性化建议：\n${analysis.personalizedAnalysis}\n\n`
  }

  if (analysis.improvementSuggestions) {
    content += `改进方向：\n${analysis.improvementSuggestions}\n\n`
  }

  // 创建下载
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `AI分析结果_${analysis.studentName}_${new Date().toISOString().slice(0, 10)}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('分析结果已导出为TXT文件')
}

const loadCourses = async () => {
  try {
    const response = await getCourses()
    courses.value = response.data
  } catch (error) {
    console.error('Failed to load courses:', error)
  }
}

const handleGenerateContent = async () => {
  if (!contentForm.courseOutline || !contentForm.chapterName) {
    ElMessage.warning('请填写课程大纲和章节名称')
    return
  }

  generating.value = true
  try {
    const params = {
      courseOutline: contentForm.courseOutline,
      chapterName: contentForm.chapterName,
      contentType: contentForm.contentType || 1  // 默认使用1（知识讲解）
    }

    const response = await generateContent(params)
    generatedContent.value = response.data
    ElMessage.success('教学内容生成成功')
  } catch (error) {
    console.error('生成失败:', error.response?.data || error)
    ElMessage.error(error.response?.data?.message || '生成失败，请重试')
  } finally {
    generating.value = false
  }
}

const handleGenerateQuestions = async () => {
  if (!questionForm.teachingContent || !questionForm.courseId) {
    ElMessage.warning('请填写教学内容并选择课程')
    return
  }

  generating.value = true
  try {
    const response = await aiGenerateQuestions({
      teachingContent: questionForm.teachingContent,
      questionType: questionForm.questionType,
      difficulty: parseInt(questionForm.difficulty),
      count: parseInt(questionForm.count),
      courseId: questionForm.courseId
    })

    // 检查不同的响应格式
    let questions = null
    if (response.data) {
      questions = Array.isArray(response.data) ? response.data : [response.data]
    } else if (response.code === 200 && response.result) {
      questions = Array.isArray(response.result) ? response.result : [response.result]
    } else if (Array.isArray(response)) {
      questions = response
    }

    if (questions && questions.length > 0) {
      generatedQuestions.value = questions
      ElMessage.success(`题目生成成功，共生成 ${questions.length} 道题目`)
    } else {
      ElMessage.error(response.message || '生成失败，返回数据为空')
    }
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '生成失败，请重试')
  } finally {
    generating.value = false
  }
}

// 学情分析相关方法
const loadStudents = async () => {
  if (!singleAnalysisForm.courseId) return

  loadingStudents.value = true
  try {
    const response = await getCourseStudents(singleAnalysisForm.courseId)
    students.value = response.data || []
  } catch (error) {
    console.error('Failed to load students:', error)
    ElMessage.error('加载学生列表失败')
  } finally {
    loadingStudents.value = false
  }
}

const loadStudentsForBatch = async () => {
  if (!batchAnalysisForm.courseId) return

  loadingStudents.value = true
  try {
    const response = await getCourseStudents(batchAnalysisForm.courseId)
    students.value = response.data || []
  } catch (error) {
    console.error('Failed to load students:', error)
    ElMessage.error('加载学生列表失败')
  } finally {
    loadingStudents.value = false
  }
}

const analyzeSingleStudent = async () => {
  analyzing.value = true
  try {
    const response = await getComprehensiveReport(
      singleAnalysisForm.studentId,
      singleAnalysisForm.courseId
    )
    singleStudentReport.value = response.data
    // 设置分析结果摘要
    analysisResult.value = `学生 ${response.data.basicInfo?.studentName} 的综合分析已完成`

    // 检查是否有分析结果ID，表示数据已保存
    if (response.data.analysisResultId) {
      ElMessage.success('学生分析完成，数据已自动更新保存')
    } else {
      ElMessage.success('学生分析完成并保存')
    }
  } catch (error) {
    ElMessage.error('学生分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}




const analyzeAnswer = async () => {
  analyzing.value = true
  try {
    const response = await intelligentAnswerAnalysis({
      courseId: answerAnalysisForm.courseId,
      questionContent: answerAnalysisForm.questionContent,
      correctAnswer: answerAnalysisForm.correctAnswer,
      studentAnswer: answerAnalysisForm.studentAnswer,
      studentId: answerAnalysisForm.studentId
    })
    analysisResult.value = response.data
    ElMessage.success('答案分析完成')
  } catch (error) {
    console.error('Failed to analyze answer:', error)
    ElMessage.error('答案分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

const generateReport = async () => {
  if (!singleStudentReport.value) {
    ElMessage.warning('请先进行学生分析')
    return
  }

  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成分析报告...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await fetch('/api/teacher/analytics/generate-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify({
        type: 'student_individual',
        studentId: singleAnalysisForm.value?.studentId || singleStudentReport.value?.basicInfo?.studentId,
        courseId: singleAnalysisForm.value?.courseId || singleStudentReport.value?.basicInfo?.courseId
      })
    })

    const result = await response.json()
    loading.close()

    if (result.code === 200) {
      ElMessage.success('分析报告生成成功')
      // 保存分析结果ID，用于后续导出
      if (result.data.analysisResultId) {
        singleStudentReport.value.analysisResultId = result.data.analysisResultId
      }
    } else {
      ElMessage.error(result.message || '生成报告失败')
    }
  } catch (error) {
    console.error('生成报告失败:', error)
    ElMessage.error('生成报告失败，请重试')
  }
}

const viewDetailReport = (studentReport) => {
  // 查看详细报告的逻辑
  singleStudentReport.value = studentReport
  analysisMode.value = 'single'
  ElMessage.info('已切换到详细报告视图')
}

const generateStudentReport = (studentReport) => {
  // 为单个学生生成报告
  ElMessage.success(`正在为 ${studentReport.basicInfo?.studentName} 生成报告`)
}

// 保存分析结果
const saveAnalysisResult = async () => {
  if (!singleStudentReport.value) {
    ElMessage.warning('没有可保存的分析结果')
    return
  }

  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在保存分析结果...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 从分析表单中获取学生ID和课程ID
    const studentId = singleAnalysisForm.value?.studentId
    const courseId = singleAnalysisForm.value?.courseId

    if (!studentId || !courseId) {
      loading.close()
      ElMessage.error('缺少必要的学生ID或课程ID信息，请重新选择学生和课程进行分析')
      return
    }

    console.log('保存分析结果请求参数:', {
      type: 'student_individual',
      studentId: studentId,
      courseId: courseId
    })

    const response = await fetch('/api/teacher/analytics/generate-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify({
        type: 'student_individual',
        studentId: studentId,
        courseId: courseId
      })
    })

    console.log('保存响应状态:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      loading.close()
      console.error('保存API响应错误:', errorText)
      ElMessage.error(`保存失败 (HTTP ${response.status}): ${errorText}`)
      return
    }

    const result = await response.json()
    loading.close()

    console.log('保存API响应结果:', result)

    if (result.code === 200) {
      ElMessage.success('分析结果保存成功')
      // 保存分析结果ID
      if (result.data && result.data.analysisResultId) {
        singleStudentReport.value.analysisResultId = result.data.analysisResultId
      }
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存分析结果失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 导出Excel功能
const exportToExcel = async () => {
  if (!singleStudentReport.value?.analysisResultId) {
    ElMessage.warning('请先保存分析结果')
    return
  }

  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在导出Excel文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await fetch(`/api/teacher/analytics/export/excel/${singleStudentReport.value.analysisResultId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    })

    loading.close()

    if (response.ok) {
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = '学生分析报告.xlsx'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''))
        }
      }

      // 下载文件
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      ElMessage.success('Excel文件导出成功')
    } else {
      ElMessage.error('导出失败，请重试')
    }
  } catch (error) {
    console.error('导出Excel失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 导出TXT功能
const exportToTxt = async () => {
  if (!singleStudentReport.value) {
    ElMessage.warning('请先进行学生分析')
    return
  }

  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成TXT文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 格式化分析数据为文本
    const txtContent = formatAnalysisToText(singleStudentReport.value)

    // 创建Blob对象
    const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' })

    // 生成文件名
    const studentName = singleStudentReport.value.basicInfo?.studentName || '未知学生'
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `${studentName}_学习分析报告_${timestamp}.txt`

    // 下载文件
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)

    loading.close()
    ElMessage.success('TXT文件导出成功')
  } catch (error) {
    console.error('导出TXT失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 格式化分析数据为文本格式
const formatAnalysisToText = (analysisData) => {
  let content = ''

  // 标题
  content += '='.repeat(50) + '\n'
  content += '          学生学习分析报告\n'
  content += '='.repeat(50) + '\n\n'

  // 基本信息
  if (analysisData.basicInfo) {
    content += '【基本信息】\n'
    content += '-'.repeat(30) + '\n'
    content += `学生姓名：${analysisData.basicInfo.studentName || '未知'}\n`
    content += `总答题数：${analysisData.basicInfo.totalAnswers || 0}\n`
    content += `总练习数：${analysisData.basicInfo.totalPractices || 0}\n\n`
  }

  // 学习表现
  if (analysisData.performance) {
    content += '【学习表现】\n'
    content += '-'.repeat(30) + '\n'
    content += `整体正确率：${analysisData.performance.overallAccuracy?.toFixed(1) || 0}%\n`
    content += `平均分数：${analysisData.performance.avgTeacherScore?.toFixed(1) || 0}\n`
    content += `表现等级：${analysisData.performance.performanceLevel || '未知'}\n\n`
  }

  // 知识点掌握情况
  if (analysisData.knowledgeMastery && analysisData.knowledgeMastery.length > 0) {
    content += '【知识点掌握情况】\n'
    content += '-'.repeat(30) + '\n'
    analysisData.knowledgeMastery.forEach((mastery, index) => {
      content += `${index + 1}. ${mastery.knowledgePoint}\n`
      content += `   总题数：${mastery.totalQuestions || 0}\n`
      content += `   正确数：${mastery.correctQuestions || 0}\n`
      content += `   正确率：${mastery.accuracyRate?.toFixed(1) || 0}%\n`
      content += `   掌握程度：${mastery.masteryDescription || '未知'}\n\n`
    })
  }

  // AI诊断
  if (analysisData.aiDiagnosis) {
    content += '【AI智能诊断】\n'
    content += '-'.repeat(30) + '\n'

    if (analysisData.aiDiagnosis.overallAssessment) {
      content += '整体评估：\n'
      content += `${analysisData.aiDiagnosis.overallAssessment}\n\n`
    }

    if (analysisData.aiDiagnosis.strengths && analysisData.aiDiagnosis.strengths.length > 0) {
      content += '主要优势：\n'
      analysisData.aiDiagnosis.strengths.forEach((strength, index) => {
        content += `${index + 1}. ${strength}\n`
      })
      content += '\n'
    }

    if (analysisData.aiDiagnosis.weaknesses && analysisData.aiDiagnosis.weaknesses.length > 0) {
      content += '需要改进：\n'
      analysisData.aiDiagnosis.weaknesses.forEach((weakness, index) => {
        content += `${index + 1}. ${weakness}\n`
      })
      content += '\n'
    }

    if (analysisData.aiDiagnosis.personalizedSuggestions && analysisData.aiDiagnosis.personalizedSuggestions.length > 0) {
      content += '个性化建议：\n'
      analysisData.aiDiagnosis.personalizedSuggestions.forEach((suggestion, index) => {
        content += `${index + 1}. ${suggestion}\n`
      })
      content += '\n'
    }

    if (analysisData.aiDiagnosis.nextStepPlan) {
      content += '下一步学习计划：\n'
      content += `${analysisData.aiDiagnosis.nextStepPlan}\n\n`
    }
  }

  // 生成时间
  content += '='.repeat(50) + '\n'
  content += `报告生成时间：${new Date().toLocaleString('zh-CN')}\n`
  content += '='.repeat(50) + '\n'

  return content
}

// 查看分析历史
const analysisHistory = ref([])
const historyDialogVisible = ref(false)

const viewAnalysisHistory = async () => {
  try {

    const response = await fetch('/api/teacher/analytics/history', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    })


    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text()
      console.error('API响应错误:', errorText)
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    // 检查响应是否为JSON格式
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      const responseText = await response.text()
      console.error('非JSON响应内容:', responseText)
      throw new Error('服务器返回了非JSON格式的响应，可能是API路径错误或服务器错误')
    }

    const result = await response.json()

    if (result.code === 200) {
      analysisHistory.value = result.data || []
      historyDialogVisible.value = true
      ElMessage.success(`获取到 ${result.data?.length || 0} 条历史记录`)
    } else {
      ElMessage.error(result.message || '获取历史记录失败')
    }
  } catch (error) {
    console.error('获取分析历史失败:', error)
    if (error.message.includes('非JSON格式')) {
      ElMessage.error('API接口返回格式错误，请检查后端服务状态')
    } else if (error.message.includes('HTTP 404')) {
      ElMessage.error('API接口不存在，请检查后端路由配置')
    } else if (error.message.includes('HTTP 401')) {
      ElMessage.error('认证失败，请重新登录')
    } else if (error.message.includes('HTTP 403')) {
      ElMessage.error('权限不足，请检查用户角色')
    } else {
      ElMessage.error('获取历史记录失败：' + error.message)
    }
  }
}

// 从历史记录导出Excel
const exportHistoryToExcel = async (analysisResultId) => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在导出Excel文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await fetch(`/teacher/analytics/export/excel/${analysisResultId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    })

    loading.close()

    if (response.ok) {
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = '学生分析报告.xlsx'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''))
        }
      }

      // 下载文件
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      ElMessage.success('Excel文件导出成功')
    } else {
      ElMessage.error('导出失败，请重试')
    }
  } catch (error) {
    console.error('导出Excel失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const getPerformanceTagType = (level) => {
  switch (level) {
    case '优秀': return 'success'
    case '良好': return 'primary'
    case '中等': return 'warning'
    case '待提高': return 'danger'
    case '一般': return 'warning'
    case '需改进': return 'danger'
    default: return 'info'
  }
}

const getKnowledgeCardClass = (accuracy) => {
  if (accuracy >= 85) return 'excellent';
  if (accuracy >= 70) return 'good';
  if (accuracy >= 60) return 'average';
  return 'needs-improvement';
}

const getAccuracyTagType = (accuracy) => {
  if (accuracy >= 85) return 'success';
  if (accuracy >= 70) return 'primary';
  if (accuracy >= 60) return 'warning';
  return 'danger';
}

const getRankingClass = (ranking) => {
  if (ranking === 1) return 'top-1'
  if (ranking <= 3) return 'top-3'
  return 'normal'
}

const viewStudentDetail = async (student) => {
  try {
    analyzing.value = true
    currentAnalyzingStudent.value = student.studentId
    ElMessage.info(`正在获取 ${student.studentName} 的详细分析...`)

    // 获取学生的详细分析数据
    const response = await analyzeStudentIndividual(
      student.studentId,
      courseAnalysisForm.value.courseId,
      ['performance', 'knowledge', 'learning_path', 'ai_diagnosis']
    )

    // 设置分析结果
    singleStudentReport.value = response.data

    // 更新单个学生分析表单
    singleAnalysisForm.value.courseId = courseAnalysisForm.value.courseId
    singleAnalysisForm.value.studentId = student.studentId

    // 切换到个体分析模式
    analysisMode.value = 'individual'

    ElMessage.success(`${student.studentName} 的详细分析已加载`)
  } catch (error) {
    console.error('获取学生详情失败:', error)
    ElMessage.error('获取学生详细分析失败，请重试')
  } finally {
    analyzing.value = false
    currentAnalyzingStudent.value = null
  }
}

const backToCourseAnalysis = () => {
  // 清除当前分析结果，返回到学生选择界面
  singleStudentReport.value = null
  analysisResult.value = ''
  ElMessage.info('已返回学生选择界面')
}

// 加载学生答案数据
const loadStudentAnswers = async () => {
  if (!answerAnalysisForm.courseId) {
    ElMessage.warning('请先选择课程')
    return
  }

  try {
    loadingAnswers.value = true

    // 调用后端API获取学生答案数据
    const response = await getStudentAnswers(answerAnalysisForm.courseId)

    if (response.data) {
      studentAnswers.value = response.data
      filterStudentAnswers()
      ElMessage.success(`加载了 ${response.data.length} 条学生答案`)
    } else {
      ElMessage.error('加载学生答案失败')
    }
  } catch (error) {
    console.error('加载学生答案失败:', error)
    ElMessage.error('加载学生答案失败，请重试')
  } finally {
    loadingAnswers.value = false
  }
}

// 筛选学生答案
const filterStudentAnswers = () => {
  const filterType = answerAnalysisForm.filterType

  switch (filterType) {
    case 'incorrect':
      filteredStudentAnswers.value = studentAnswers.value.filter(answer => !answer.isCorrect)
      break
    case 'needs_improvement':
      filteredStudentAnswers.value = studentAnswers.value.filter(answer =>
        answer.score < 60 || (!answer.isCorrect && answer.questionType === 4) // 主观题低分
      )
      break
    case 'subjective':
      filteredStudentAnswers.value = studentAnswers.value.filter(answer => answer.questionType === 4)
      break
    default:
      filteredStudentAnswers.value = studentAnswers.value
  }
}

// 分析单个学生答案
const analyzeSelectedAnswer = async (answerData) => {
  try {
    analyzing.value = true
    currentAnalyzingAnswer.value = answerData.id
    ElMessage.info(`正在分析 ${answerData.studentName} 的答案...`)

    const response = await intelligentAnswerAnalysis({
      courseId: answerAnalysisForm.courseId,
      questionContent: answerData.questionContent,
      correctAnswer: answerData.correctAnswer,
      studentAnswer: answerData.studentAnswer,
      studentId: answerData.studentId
    })

    // 设置分析结果
    currentAnswerAnalysis.value = {
      ...answerData,
      analysis: response.data.analysis,
      personalizedAnalysis: response.data.personalizedAnalysis
    }

    ElMessage.success('AI分析完成')
  } catch (error) {
    console.error('答案分析失败:', error)
    ElMessage.error('AI分析失败，请重试')
  } finally {
    analyzing.value = false
    currentAnalyzingAnswer.value = null
  }
}

// 获取难度标签类型
const getDifficultyTagType = (level) => {
  switch (level) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

// 复制分析结果
const copyAnalysisResult = () => {
  if (!currentAnswerAnalysis.value) return

  const result = `题目：${currentAnswerAnalysis.value.questionTitle}
学生：${currentAnswerAnalysis.value.studentName}
学生答案：${currentAnswerAnalysis.value.studentAnswer}
标准答案：${currentAnswerAnalysis.value.correctAnswer}

AI分析：
${currentAnswerAnalysis.value.analysis}

${currentAnswerAnalysis.value.personalizedAnalysis ? '个性化建议：\n' + currentAnswerAnalysis.value.personalizedAnalysis : ''}`

  navigator.clipboard.writeText(result).then(() => {
    ElMessage.success('分析结果已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}



const getMasteryTagType = (level) => {
  switch (level) {
    case 3: return 'success'  // 精通
    case 2: return 'primary'  // 熟练掌握
    case 1: return 'warning'  // 初步掌握
    case 0: return 'danger'   // 未掌握
    default: return 'info'
  }
}





const analyzeBatchStudents = async () => {
  analyzing.value = true
  try {
    const response = await batchAnalyzeStudents(
      batchAnalysisForm.studentIds,
      batchAnalysisForm.courseId
    )
    batchAnalysisResults.value = response.data || []
    analysisResult.value = `成功分析 ${batchAnalysisResults.value.length} 名学生`
    ElMessage.success('批量分析完成')
  } catch (error) {
    console.error('Failed to batch analyze students:', error)
    ElMessage.error('批量分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}




const exportAnalysis = () => {
  if (!analysisResult.value) {
    ElMessage.warning('没有可导出的分析结果')
    return
  }

  // 导出分析结果的逻辑
  const content = JSON.stringify(analysisResult.value, null, 2)
  const blob = new Blob([content], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `学情分析报告_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('分析报告已导出')
}

const copyContent = () => {
  if (generatedContent.value) {
    navigator.clipboard.writeText(generatedContent.value)
    ElMessage.success('已复制到剪贴板')
  }
}

const copyQuestions = () => {
  if (generatedQuestions.value) {
    navigator.clipboard.writeText(generatedQuestions.value)
    ElMessage.success('已复制到剪贴板')
  }
}

const copyAnalysis = () => {
  if (singleStudentReport.value) {
    // 格式化学生分析报告
    const report = formatStudentAnalysisReport(singleStudentReport.value)
    navigator.clipboard.writeText(report)
    ElMessage.success('分析报告已复制到剪贴板')
  } else if (analysisResult.value) {
    navigator.clipboard.writeText(typeof analysisResult.value === 'string' ? analysisResult.value : JSON.stringify(analysisResult.value, null, 2))
    ElMessage.success('已复制到剪贴板')
  } else {
    ElMessage.warning('没有可复制的分析结果')
  }
}

// 格式化学生分析报告
const formatStudentAnalysisReport = (report) => {
  let formattedReport = ''

  // 标题
  formattedReport += '='.repeat(50) + '\n'
  formattedReport += '学生学习分析报告\n'
  formattedReport += '='.repeat(50) + '\n\n'

  // 基本信息
  if (report.basicInfo) {
    formattedReport += '【基本信息】\n'
    formattedReport += `学生姓名：${report.basicInfo.studentName || '未知'}\n`
    formattedReport += `总答题数：${report.basicInfo.totalAnswers || 0}\n`
    formattedReport += `总练习数：${report.basicInfo.totalPractices || 0}\n\n`
  }

  // 学习表现
  if (report.performance) {
    formattedReport += '【学习表现】\n'
    formattedReport += `整体正确率：${report.performance.overallAccuracy?.toFixed(1) || 0}%\n`
    formattedReport += `平均分数：${report.performance.avgTeacherScore?.toFixed(1) || 0}\n`
    formattedReport += `表现等级：${report.performance.performanceLevel || '未评级'}\n\n`
  }

  // 知识点掌握情况
  if (report.knowledgeMastery && report.knowledgeMastery.length > 0) {
    formattedReport += '【知识点掌握情况】\n'
    report.knowledgeMastery.forEach((mastery, index) => {
      formattedReport += `${index + 1}. ${mastery.knowledgePoint}\n`
      formattedReport += `   总题数：${mastery.totalQuestions}，正确数：${mastery.correctQuestions}\n`
      formattedReport += `   正确率：${mastery.accuracyRate?.toFixed(1)}%，掌握程度：${mastery.masteryDescription}\n\n`
    })
  }

  // AI诊断
  if (report.aiDiagnosis) {
    formattedReport += '【AI智能诊断】\n'
    if (report.aiDiagnosis.overallAssessment) {
      formattedReport += `整体评估：${report.aiDiagnosis.overallAssessment}\n\n`
    }

    if (report.aiDiagnosis.strengths && report.aiDiagnosis.strengths.length > 0) {
      formattedReport += '主要优势：\n'
      report.aiDiagnosis.strengths.forEach((strength, index) => {
        formattedReport += `${index + 1}. ${strength}\n`
      })
      formattedReport += '\n'
    }

    if (report.aiDiagnosis.weaknesses && report.aiDiagnosis.weaknesses.length > 0) {
      formattedReport += '需要改进：\n'
      report.aiDiagnosis.weaknesses.forEach((weakness, index) => {
        formattedReport += `${index + 1}. ${weakness}\n`
      })
      formattedReport += '\n'
    }

    if (report.aiDiagnosis.personalizedSuggestions && report.aiDiagnosis.personalizedSuggestions.length > 0) {
      formattedReport += '个性化建议：\n'
      report.aiDiagnosis.personalizedSuggestions.forEach((suggestion, index) => {
        formattedReport += `${index + 1}. ${suggestion}\n`
      })
      formattedReport += '\n'
    }

    if (report.aiDiagnosis.nextStepPlan) {
      formattedReport += `下一步学习计划：${report.aiDiagnosis.nextStepPlan}\n\n`
    }
  }

  // 报告生成时间
  formattedReport += '='.repeat(50) + '\n'
  formattedReport += `报告生成时间：${new Date().toLocaleString()}\n`
  formattedReport += '='.repeat(50)

  return formattedReport
}

const saveGeneratedContent = async () => {
  if (!generatedContent.value || !contentForm.courseId) {
    ElMessage.warning('请先生成内容并选择课程')
    return
  }

  try {
    await createTeachingContent({
      courseId: contentForm.courseId,
      chapterName: contentForm.chapterName,
      contentType: contentForm.contentType || 1,
      content: generatedContent.value,
      aiGenerated: 1,
      title: contentForm.chapterName // 使用章节名称作为标题
    })
    ElMessage.success('内容已保存')
  } catch (error) {
    console.error('Failed to save content:', error)
    ElMessage.error('保存失败，请重试')
  }
}

const saveGeneratedQuestions = async () => {
  if (!generatedQuestions.value || !questionForm.courseId) {
    ElMessage.warning('请先生成题目并选择课程')
    return
  }

  try {
    const processedQuestions = generatedQuestions.value.map(q => ({
      ...q,
      courseId: questionForm.courseId,
      aiGenerated: 1,  // 使用整数1而不是true
      title: q.content.length > 50 ? q.content.substring(0, 50) + '...' : q.content // 使用题目内容的前50个字符作为标题
    }))

    await batchSaveQuestions(processedQuestions)

    // 使用Element Plus的Dialog来显示成功信息
    ElMessageBox.alert(
      h('div', { class: 'save-summary' }, [
        h('h4', '题目保存成功！'),
        h('p', `共保存 ${processedQuestions.length} 道题目到题库：`),
        h('ul', processedQuestions.map((q, i) =>
          h('li', `${i + 1}. ${q.title}`)
        )),
        h('p', { class: 'tip' }, '提示：您可以在题库管理中查看和编辑这些题目')
      ]),
      '保存成功',
      {
        confirmButtonText: '确定',
        customClass: 'question-save-dialog'
      }
    )
  } catch (error) {
    console.error('Failed to save questions:', error)
    ElMessage.error('保存失败，请重试')
  }
}



const loadTeachingContents = async (courseId) => {
  if (!courseId) {
    teachingContents.value = []
    return
  }

  loading.value = true

  try {
    const response = await getTeachingContents(courseId)
    if (response.code === 200 && response.data) {
      teachingContents.value = response.data
    } else {
      ElMessage.warning('获取教学内容失败：' + (response.message || '未知错误'))
      teachingContents.value = []
    }
  } catch (error) {
    ElMessage.error('加载教学内容失败，请重试')
    teachingContents.value = []
  } finally {
    loading.value = false
  }
}

const handleContentSourceChange = (value) => {
  if (value === 'select' && questionForm.courseId) {
    loadTeachingContents(questionForm.courseId)
  } else if (value === 'input') {
    questionForm.selectedContentId = null
    questionForm.teachingContent = ''
  }
}

const refreshTeachingContents = () => {
  if (questionForm.courseId) {
    loadTeachingContents(questionForm.courseId)
  } else {
    ElMessage.warning('请先选择课程')
  }
}

// 学情分析相关函数
const handleAnalysisModeChange = (mode) => {
  analysisMode.value = mode
  // 清空之前的分析结果
  courseOverallAnalysis.value = null
  analysisResult.value = ''
  singleStudentReport.value = null
}

const handleQuickAction = (command) => {
  switch (command) {
    case 'insights':
      quickInsights()
      break
    case 'report':
      generateCourseReport()
      break
    case 'export':
      exportData()
      break
  }
}

const onCourseChange = (courseId) => {
  // 课程变化时清空之前的分析结果
  courseOverallAnalysis.value = null
  analysisResult.value = ''
}

// 课程整体分析
const analyzeCourse = async () => {
  if (!courseAnalysisForm.courseId) {
    ElMessage.warning('请先选择课程')
    return
  }

  analyzing.value = true
  try {
    const response = await analyzeCourseOverall(courseAnalysisForm.courseId)
    if (response.code === 200 && response.data) {
      courseOverallAnalysis.value = response.data
      ElMessage.success('课程分析完成')
    } else {
      ElMessage.error(response.message || '分析失败')
    }
  } catch (error) {
    console.error('Failed to analyze course:', error)
    ElMessage.error('分析失败，请重试')
  } finally {
    analyzing.value = false
  }
}

// 快速洞察
const quickInsights = async () => {
  if (!courseAnalysisForm.courseId) {
    ElMessage.warning('请先选择课程')
    return
  }

  try {
    const response = await getQuickInsights(courseAnalysisForm.courseId)
    if (response.code === 200 && response.data) {
      ElMessage.success('快速洞察获取成功')
      // 可以在这里显示洞察结果
      console.log('Quick insights:', response.data)
    } else {
      ElMessage.error(response.message || '获取洞察失败')
    }
  } catch (error) {
    console.error('Failed to get quick insights:', error)
    ElMessage.error('获取洞察失败，请重试')
  }
}

// 生成课程报告
const generateCourseReport = async () => {
  if (!courseOverallAnalysis.value) {
    ElMessage.warning('请先进行课程分析')
    return
  }

  try {
    const response = await generateAnalysisReport({
      type: 'course',
      courseId: courseAnalysisForm.courseId,
      data: courseOverallAnalysis.value
    })
    if (response.code === 200) {
      ElMessage.success('报告生成成功')
    } else {
      ElMessage.error(response.message || '报告生成失败')
    }
  } catch (error) {
    console.error('Failed to generate report:', error)
    ElMessage.error('报告生成失败，请重试')
  }
}

// 导出数据
const exportData = async () => {
  if (!courseOverallAnalysis.value) {
    ElMessage.warning('请先进行课程分析')
    return
  }

  try {
    const response = await exportAnalysisData({
      type: 'course',
      courseId: courseAnalysisForm.courseId
    })

    // 处理文件下载
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `课程分析数据_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('Failed to export data:', error)
    ElMessage.error('数据导出失败，请重试')
  }
}

watch(() => questionForm.courseId, (newVal) => {
  if (newVal && questionForm.contentSource === 'select') {
    questionForm.selectedContentId = null
    questionForm.teachingContent = ''
    loadTeachingContents(newVal)
  } else if (!newVal) {
    teachingContents.value = []
  }
}, { immediate: false })


const handleContentSelect = (contentId) => {
  const selectedContent = teachingContents.value.find(item => item.id === contentId)
  if (selectedContent) {
    questionForm.teachingContent = selectedContent.content
  }
}



const getDifficultyName = (level) => {
  const levels = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return levels[level] || '未知难度'
}

const getQuestionTypeName = (type) => {
  const types = {
    'SINGLE_CHOICE': '单选题',
    'MULTIPLE_CHOICE': '多选题',
    'FILL_BLANK': '填空题',
    'SHORT_ANSWER': '简答题',
    'PROGRAMMING': '编程题'
  }
  return types[type] || '未知类型'
}

const formatChoiceAnswer = (answer) => {
  if (Array.isArray(answer)) {
    return answer.join(', ')
  }
  return answer || ''
}



const getContentTypeName = (type) => {
  const types = {
    1: '知识讲解',
    2: '实训练习',
    3: '指导说明'
  }
  return types[type] || '未知类型'
}

const isChoiceQuestion = (type) => {
  return type === 'SINGLE_CHOICE' || type === 'MULTIPLE_CHOICE'
}


const formatFillBlankAnswer = (answer) => {
  if (!answer) return ''
  return answer.split('|||').join('；')
}

onMounted(() => {
  loadCourses()
})
</script>

<style scoped>
.ai-assistant {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.function-card {
  height: 100%;
}

.function-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.function-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.function-item:hover {
  background-color: #f5f7fa;
}

.function-item.active {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.work-card {
  min-height: 500px;
}

/* AI洞察卡片美化 */
.ai-insights-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 28px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.ai-insights-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.insights-header {
  position: relative;
  z-index: 1;
}

.insights-title-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.insights-icon-wrapper {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.insights-icon {
  font-size: 24px;
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

.insights-title-content {
  flex: 1;
}

.insights-title {
  font-size: 18px;
  font-weight: 700;
  display: block;
  margin-bottom: 4px;
}

.insights-tag {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  backdrop-filter: blur(10px);
}

.insights-content {
  position: relative;
  z-index: 1;
  margin-top: 20px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.insight-icon-wrapper {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.insight-icon {
  font-size: 20px;
}

.insight-text-wrapper {
  flex: 1;
  position: relative;
}

.insight-text {
  line-height: 1.7;
  font-size: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.insight-animation {
  position: absolute;
  bottom: -4px;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    width: 0%;
  }

  50% {
    width: 100%;
  }

  100% {
    width: 0%;
  }
}

/* 学生表现分布美化 */
.performance-distribution {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.distribution-visual {
  margin-top: 20px;
}

.performance-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-item.excellent {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
}

.stat-item.good {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}

.stat-item.average {
  background: linear-gradient(135deg, #45b7d1, #96c93d);
  color: white;
}

.stat-item.needs-improvement {
  background: linear-gradient(135deg, #f39c12, #d35400);
  color: white;
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 4px;
  color: #ffffff; 
}

.distribution-bar {
  height: 12px;
  border-radius: 6px;
  display: flex;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bar-segment {
  height: 100%;
  transition: all 0.6s ease;
  position: relative;
}

.bar-segment.excellent {
  background: linear-gradient(90deg, #ff6b6b, #ee5a24);
}

.bar-segment.good {
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.bar-segment.average {
  background: linear-gradient(90deg, #45b7d1, #96c93d);
}

.bar-segment.needs-improvement {
  background: linear-gradient(90deg, #f39c12, #d35400);
}

.bar-segment::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: barShine 2s infinite;
}

@keyframes barShine {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* 美化的标题样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  margin-bottom: 24px;
  border-bottom: 2px solid #f0f2f5;
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 1px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon-wrapper {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: transform 0.3s ease;
}

.header-icon-wrapper:hover {
  transform: scale(1.05);
}

.header-icon {
  font-size: 24px;
  color: white;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.4;
}

.header-badge {
  display: flex;
  align-items: center;
}

/* 各个分析模块的特殊样式 */
.performance-distribution {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
}

.knowledge-analysis {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
}

.student-rankings {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
}

.teaching-suggestions {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
}

.suggestions-content {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f8ff 100%);
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.suggestion-text {
  font-size: 15px;
  line-height: 1.7;
  color: #2c3e50;
}

.generated-content {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.content-preview,
.questions-preview,
.analysis-preview {
  margin: 16px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  white-space: pre-wrap;
}

.content-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.question-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-weight: bold;
}

.difficulty {
  color: #909399;
}

.question-content {
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.question-options {
  margin: 12px 0;
}

.option-item {
  margin: 8px 0;
}

.question-answer,
.question-analysis {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #e4e7ed;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.content-option {
  padding: 8px 0;
}

.content-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.content-info {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 8px;
}

.save-summary {
  font-size: 14px;
  color: #333;
}

.save-summary h4 {
  margin: 0 0 8px 0;
  font-weight: 600;
}

.save-summary p {
  margin: 4px 0;
}

.save-summary ul {
  margin: 8px 0;
  padding-left: 20px;
  list-style-type: disc;
}

.save-summary .tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 学情分析相关样式 */
.analysis-results {
  margin-top: 20px;
}

.single-analysis-result {
  margin-top: 20px;
}

.result-card {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.performance-value {
  font-weight: 600;
  color: #409eff;
  font-size: 16px;
}

.ai-diagnosis {
  padding: 16px 0;
}

.diagnosis-section {
  margin-bottom: 24px;
}

.diagnosis-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.diagnosis-section p {
  margin: 8px 0;
  line-height: 1.6;
  color: #606266;
}

.diagnosis-section ul,
.diagnosis-section ol {
  margin: 8px 0;
  padding-left: 20px;
}

.diagnosis-section li {
  margin: 6px 0;
  line-height: 1.5;
  color: #606266;
}

.batch-analysis-result {
  margin-top: 20px;
}

.answer-analysis-result {
  margin-top: 20px;
}

/* AI深度分析结果样式 */
.analysis-detail-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.analysis-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-detail-header .header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.analysis-detail-header .header-actions {
  display: flex;
  gap: 8px;
}

/* 题目信息区域 */
.question-info-section {
  margin-bottom: 24px;
}

.question-detail-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.question-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #dee2e6;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-label {
  font-weight: 500;
  color: #6c757d;
}

.meta-value {
  color: #495057;
}

.content-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  font-size: 14px;
}

.question-content-wrapper,
.question-options-wrapper,
.correct-answer-wrapper {
  margin-bottom: 20px;
}

.question-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  line-height: 1.6;
}

.question-options {
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  overflow: hidden;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.2s;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item.correct-option {
  background: #f0f9ff;
  border-left: 4px solid #0ea5e9;
}

.option-key {
  font-weight: 600;
  margin-right: 8px;
  color: #6b7280;
}

.option-text {
  flex: 1;
  line-height: 1.5;
}

.correct-icon {
  color: #10b981;
  margin-left: 8px;
}

.correct-answer {
  background: #f0fdf4;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #bbf7d0;
  font-weight: 500;
}

/* 学生答案区域 */
.student-answer-section {
  margin-bottom: 24px;
}

.student-answer-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.answer-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #dee2e6;
}

.score-value {
  font-weight: 600;
  color: #f59e0b;
}

.student-answer-content {
  margin-top: 16px;
}

.answer-text {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  line-height: 1.6;
  min-height: 60px;
}

.correct-answer-text {
  border-left: 4px solid #10b981;
  background: #f0fdf4;
}

.incorrect-answer-text {
  border-left: 4px solid #ef4444;
  background: #fef2f2;
}

/* AI智能分析区域 */
.ai-analysis-section {
  margin-bottom: 24px;
}

.ai-analysis-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analysis-block {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.analysis-block:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.block-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  font-size: 15px;
}

.analysis-text {
  line-height: 1.7;
  color: #4b5563;
}

/* 不同类型分析块的样式 */
.error-analysis {
  border-left: 4px solid #ef4444;
  background: #fef2f2;
}

.error-analysis .block-header {
  color: #dc2626;
}

.suggestion-analysis {
  border-left: 4px solid #3b82f6;
  background: #eff6ff;
}

.suggestion-analysis .block-header {
  color: #2563eb;
}

.improvement-analysis {
  border-left: 4px solid #10b981;
  background: #f0fdf4;
}

.improvement-analysis .block-header {
  color: #059669;
}

/* Markdown内容样式 */
.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 1.5em;
}

.markdown-content h2 {
  font-size: 1.3em;
}

.markdown-content h3 {
  font-size: 1.1em;
}

.markdown-content p {
  margin: 8px 0;
  line-height: 1.7;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-content li {
  margin: 4px 0;
  line-height: 1.6;
}

.markdown-content code {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #e11d48;
}

.markdown-content pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.markdown-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 16px;
  margin: 12px 0;
  color: #64748b;
  font-style: italic;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content th {
  background: #f8fafc;
  font-weight: 600;
}

.markdown-content strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content em {
  font-style: italic;
  color: #4b5563;
}

.analysis-preview {
  margin: 16px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  white-space: pre-wrap;
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
}

.content-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-assistant {
    padding: 10px;
  }

  .el-row {
    flex-direction: column;
  }

  .function-card,
  .work-card {
    margin-bottom: 20px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-item .label {
    margin-bottom: 4px;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 数据可视化样式 */
.chart-container {
  height: 300px;
  margin: 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>
/*
统一操作按钮区域样式
*/
.analysis-actions-unified {
background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
border: 1px solid #e4e7ed;
border-radius: 12px;
padding: 24px;
margin-top: 24px;
text-align: center;
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.analysis-actions-unified .el-button {
min-width: 140px;
height: 44px;
font-size: 14px;
font-weight: 500;
border-radius: 8px;
transition: all 0.3s ease;
}

.analysis-actions-unified .el-button:hover {
transform: translateY(-2px);
box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 优化学生选择区域样式 */
.student-selection-section {
background: white;
border-radius: 12px;
padding: 24px;
margin-bottom: 24px;
box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
border: 1px solid #e4e7ed;
}

.selection-group {
margin-bottom: 16px;
}

.selection-label {
display: block;
margin-bottom: 8px;
font-weight: 500;
color: #303133;
font-size: 14px;
}

/* 分析维度选择样式 */
.analysis-dimensions-section {
margin-top: 20px;
padding: 16px;
background: #f8f9fa;
border-radius: 8px;
border: 1px solid #e9ecef;
}

.dimension-label {
display: block;
margin-bottom: 12px;
font-weight: 500;
color: #495057;
font-size: 14px;
}

.dimension-checkboxes {
display: flex;
flex-wrap: wrap;
gap: 16px;
}

.dimension-checkboxes .el-checkbox {
margin-right: 0;
}

/* 移除重复的操作按钮样式 */
.analysis-actions {
display: none !important;
}

/* 优化卡片头部样式 */
.analysis-card-header {
display: flex;
justify-content: space-between;
align-items: center;
width: 100%;
}

.header-left {
display: flex;
align-items: center;
gap: 8px;
font-size: 16px;
font-weight: 600;
}

.student-name-tag {
color: #409eff;
font-weight: 500;
background: #ecf5ff;
padding: 4px 8px;
border-radius: 4px;
font-size: 14px;
}

.header-right {
display: flex;
align-items: center;
gap: 8px;
}