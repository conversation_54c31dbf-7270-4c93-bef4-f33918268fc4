<template>
  <el-dialog
    :title="isEdit ? '编辑AI模型' : '添加新AI模型'"
    v-model="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
      class="model-form"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-form-item label="模型名称" prop="modelName">
            <el-input v-model="form.modelName" placeholder="输入模型显示名称" />
          </el-form-item>

          <el-form-item label="提供商" prop="provider">
            <el-select v-model="form.provider" placeholder="选择AI提供商" style="width: 100%">
              <el-option
                v-for="provider in providers"
                :key="provider"
                :label="provider"
                :value="provider"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="API端点" prop="apiEndpoint">
            <el-input v-model="form.apiEndpoint" placeholder="输入API端点URL" />
          </el-form-item>

          <el-form-item label="API密钥" prop="apiKey">
            <el-input
              v-model="form.apiKey"
              placeholder="输入API密钥"
              type="password"
              show-password
            />
          </el-form-item>

          <el-form-item label="模型ID" prop="modelId">
            <el-input v-model="form.modelId" placeholder="输入模型标识符" />
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="输入模型描述（可选）"
            />
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="高级设置" name="advanced">
          <el-form-item label="最大Token数" prop="maxTokens">
            <el-input-number
              v-model="form.maxTokens"
              :min="1"
              :max="32000"
              :step="100"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="温度" prop="temperature">
            <el-slider
              v-model="form.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              show-input
              :format-tooltip="value => value.toFixed(1)"
            />
          </el-form-item>

          <el-form-item label="超时时间(秒)" prop="timeoutSeconds">
            <el-input-number
              v-model="form.timeoutSeconds"
              :min="1"
              :max="300"
              :step="5"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="设为默认模型">
            <el-switch v-model="form.isDefault" />
          </el-form-item>

          <el-form-item label="立即激活">
            <el-switch v-model="form.isActive" />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <div class="test-connection-section" v-if="activeTab === 'basic'">
      <el-button
        type="primary"
        plain
        @click="testConnection"
        :loading="testing"
        :disabled="!canTest"
      >
        测试连接
      </el-button>
      <div v-if="testResult" class="test-result" :class="testResult.success ? 'success' : 'error'">
        <el-icon :class="testResult.success ? 'success-icon' : 'error-icon'">
          <component :is="testResult.success ? 'CircleCheck' : 'CircleClose'" />
        </el-icon>
        <span>{{ testResult.message }}</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="saving">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createModel, updateModel, testModelConnectionCustom } from '@/api/aiModel'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  model: {
    type: Object,
    default: null
  },
  providers: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'saved'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 当前标签页
const activeTab = ref('basic')

// 加载状态
const saving = ref(false)
const testing = ref(false)
const testResult = ref(null)

// 表单数据
const form = reactive({
  modelName: '',
  provider: '',
  apiEndpoint: '',
  apiKey: '',
  modelId: '',
  description: '',
  maxTokens: 2048,
  temperature: 0.7,
  timeoutSeconds: 30,
  isDefault: false,
  isActive: false
})

// 表单验证规则
const rules = {
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { max: 100, message: '模型名称不能超过100个字符', trigger: 'blur' }
  ],
  provider: [
    { required: true, message: '请选择提供商', trigger: 'change' }
  ],
  apiEndpoint: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { max: 255, message: 'API端点不能超过255个字符', trigger: 'blur' }
  ],
  modelId: [
    { required: true, message: '请输入模型ID', trigger: 'blur' },
    { max: 100, message: '模型ID不能超过100个字符', trigger: 'blur' }
  ],
  maxTokens: [
    { required: true, message: '请输入最大Token数', trigger: 'blur' },
    { type: 'number', min: 1, max: 32000, message: '最大Token数必须在1-32000之间', trigger: 'blur' }
  ],
  temperature: [
    { required: true, message: '请设置温度', trigger: 'blur' },
    { type: 'number', min: 0, max: 2, message: '温度必须在0-2之间', trigger: 'blur' }
  ],
  timeoutSeconds: [
    { required: true, message: '请输入超时时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '超时时间必须在1-300秒之间', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.model)
const canTest = computed(() => {
  return form.provider && form.apiEndpoint && form.modelId
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置为默认值
  form.modelName = ''
  form.provider = ''
  form.apiEndpoint = ''
  form.apiKey = ''
  form.modelId = ''
  form.description = ''
  form.maxTokens = 2048
  form.temperature = 0.7
  form.timeoutSeconds = 30
  form.isDefault = false
  form.isActive = false
  
  testResult.value = null
  activeTab.value = 'basic'
}

// 监听model变化，更新表单
watch(() => props.model, (newVal) => {
  if (newVal) {
    Object.keys(form).forEach(key => {
      if (newVal[key] !== undefined) {
        form[key] = newVal[key]
      }
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 测试连接
const testConnection = async () => {
  if (!canTest.value) {
    ElMessage.warning('请先填写提供商、API端点和模型ID')
    return
  }
  
  testing.value = true
  testResult.value = null
  
  try {
    const testData = { ...form }
    const response = await testModelConnectionCustom(testData)
    
    if (response.code === 200) {
      const result = response.data
      testResult.value = {
        success: result.success,
        message: result.success 
          ? `连接成功！响应时间: ${result.responseTime}ms` 
          : `连接失败: ${result.message}`
      }
    } else {
      testResult.value = {
        success: false,
        message: '测试请求失败'
      }
    }
  } catch (error) {
    testResult.value = {
      success: false,
      message: `连接测试错误: ${error.message}`
    }
  } finally {
    testing.value = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请正确填写所有必填字段')
      return
    }
    
    saving.value = true
    
    try {
      const data = { ...form }
      let response
      
      if (isEdit.value) {
        response = await updateModel(props.model.id, data)
      } else {
        response = await createModel(data)
      }
      
      if (response.code === 200) {
        ElMessage.success(isEdit.value ? '模型更新成功' : '模型添加成功')
        dialogVisible.value = false
        emit('saved')
      } else {
        ElMessage.error(response.message || '操作失败')
      }
    } catch (error) {
      ElMessage.error(`保存失败: ${error.message}`)
    } finally {
      saving.value = false
    }
  })
}
</script>

<style scoped>
.model-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.test-connection-section {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.test-result {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
}

.test-result.success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.test-result.error {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>