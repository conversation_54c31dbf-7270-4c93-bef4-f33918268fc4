<template>
  <el-dialog
    title="模型连接测试结果"
    v-model="dialogVisible"
    width="700px"
  >
    <div class="test-results-container">
      <el-table
        :data="results"
        stripe
        style="width: 100%"
      >
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-icon :class="row.success ? 'success-icon' : 'error-icon'" size="20">
              <component :is="row.success ? 'CircleCheck' : 'CircleClose'" />
            </el-icon>
          </template>
        </el-table-column>
        
        <el-table-column prop="modelId" label="模型 ID" width="80" />
        
        <el-table-column label="结果">
          <template #default="{ row }">
            <div class="result-message">
              <span>{{ row.message }}</span>
              <span v-if="row.responseTime" class="response-time">
                Response time: {{ row.responseTime }}ms
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-if="results.length === 0" class="no-results">
        No test results available
      </div>
      
      <div class="summary" v-if="results.length > 0">
        <div class="summary-item">
          <span>Total:</span>
          <strong>{{ results.length }}</strong>
        </div>
        <div class="summary-item success">
          <span>Success:</span>
          <strong>{{ successCount }}</strong>
        </div>
        <div class="summary-item error">
          <span>Failed:</span>
          <strong>{{ failedCount }}</strong>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">Close</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  results: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// Dialog visibility
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// Computed statistics
const successCount = computed(() => {
  return props.results.filter(result => result.success).length
})

const failedCount = computed(() => {
  return props.results.filter(result => !result.success).length
})
</script>

<style scoped>
.test-results-container {
  max-height: 60vh;
  overflow-y: auto;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.result-message {
  display: flex;
  flex-direction: column;
}

.response-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.no-results {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.summary {
  display: flex;
  gap: 20px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item.success strong {
  color: #67c23a;
}

.summary-item.error strong {
  color: #f56c6c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>