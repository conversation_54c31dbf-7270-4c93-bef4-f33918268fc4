<template>
  <div class="model-management">
    <!-- Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">AI模型管理</h1>
        <p class="page-description">管理AI模型，测试连接，配置活跃模型</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog" icon="Plus">
          添加新模型
        </el-button>
        <el-button @click="testAllModels" :loading="testingAll" icon="Connection">
          测试所有模型
        </el-button>
      </div>
    </div>

    <!-- Filters -->
    <div class="filters">
      <el-form :inline="true" :model="filters" class="filter-form">
        <el-form-item label="提供商">
          <el-select 
            v-model="filters.provider" 
            placeholder="选择提供商" 
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="provider in providerOptions"
              :key="provider.value"
              :label="provider.label"
              :value="provider.value"
            >
              <div class="provider-option">
                <div class="provider-info">
                  <span class="provider-name">{{ provider.label }}</span>
                  <el-tag 
                    :type="provider.tagType" 
                    size="small" 
                    class="provider-tag"
                  >
                    {{ provider.description }}
                  </el-tag>
                </div>
                <span class="provider-count">{{ provider.count }} 个模型</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="filters.isActive" 
            placeholder="选择状态" 
            clearable
            style="width: 180px"
          >
            <el-option 
              :value="true"
            >
              <div class="status-option" >
                <el-icon class="status-icon active"><CircleCheck /></el-icon>
                <div class="status-info" >
                  <span class="status-name">活跃模型</span>
                  <span class="status-desc">当前正在使用的模型</span>
                </div>
              </div>
            </el-option>
            <el-option 
              :value="false"
            >
              <div class="status-option">
                <el-icon class="status-icon inactive"><CircleClose /></el-icon>
                <div class="status-info">
                  <span class="status-name">非活跃模型</span>
                  <span class="status-desc">暂未启用的模型</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadModels" icon="Search">搜索</el-button>
          <el-button @click="resetFilters" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- Model List Table -->
    <div class="table-container">
      <el-table
        :data="models"
        v-loading="loading"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="modelName" label="模型名称" sortable="custom">
          <template #default="{ row }">
            <div class="model-info">
              <div class="model-name">
                <strong>{{ row.modelName }}</strong>
                <div class="model-badges">
                  <el-tag v-if="row.isActive" type="success" size="small">当前使用  </el-tag>
                  <el-tag v-if="row.isDefault" type="primary" size="small">默认</el-tag>
                </div>
              </div>
              <div class="model-id">模型ID：{{ row.modelId }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="provider" label="提供商" width="240">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)" size="small">
              {{ row.provider }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="连接状态" width="240">
          <template #default="{ row }">
            <div class="connection-status">
              <el-icon :class="getConnectionStatusClass(row.connectionStatus)">
                <component :is="getConnectionStatusIcon(row.connectionStatus)" />
              </el-icon>
              <span>{{ getConnectionStatusText(row.connectionStatus) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="lastTestTime" label="最后测试" width="240">
          <template #default="{ row }">
            <span v-if="row.lastTestTime">
              {{ formatDateTime(row.lastTestTime) }}
            </span>
            <span v-else class="text-muted">从未测试</span>
          </template>
        </el-table-column>

        <!-- Optimized Actions Column -->
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <!-- Primary Action: Test Connection -->
              <el-button
                size="small"
                @click="testConnection(row)"
                :loading="testingModels[row.id]"
                icon="Connection"
                :type="row.connectionStatus === 1 ? 'success' : 'primary'"
              >
                {{ row.connectionStatus === 1 ? '重测' : '测试' }}
              </el-button>
              
              <!-- Quick Action: Activate (only show if not active) -->
              <el-button
                v-if="!row.isActive"
                size="small"
                type="success"
                @click="setActive(row)"
                icon="Switch"
                plain
              >
                激活
              </el-button>
              
              <!-- More Actions Dropdown -->
              <el-dropdown @command="handleCommand" trigger="click">
                <el-button size="small" icon="More" circle />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      :command="{ action: 'edit', row }"
                      icon="Edit"
                    >
                      编辑模型
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="!row.isDefault"
                      :command="{ action: 'setDefault', row }"
                      icon="Star"
                    >
                      设为默认
                    </el-dropdown-item>
                    <el-dropdown-item 
                      v-if="row.isActive"
                      :command="{ action: 'deactivate', row }"
                      icon="SwitchButton"
                    >
                      取消激活
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="{ action: 'duplicate', row }"
                      icon="CopyDocument"
                    >
                      复制模型
                    </el-dropdown-item>
                    <el-dropdown-item 
                      divided
                      :command="{ action: 'delete', row }"
                      :disabled="row.isActive || row.isDefault"
                      icon="Delete"
                    >
                      删除模型
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadModels"
          @current-change="loadModels"
        />
      </div>
    </div>

    <!-- Add/Edit Model Dialog -->
    <ModelDialog
      v-model="dialogVisible"
      :model="currentModel"
      :providers="providers"
      @saved="handleModelSaved"
    />

    <!-- Connection Test Results Dialog -->
    <TestResultsDialog
      v-model="testResultsVisible"
      :results="testResults"
    />

    <!-- Error Display Dialog -->
    <ErrorDialog
      v-model="errorDialogVisible"
      :error-data="currentError"
      :model-name="currentErrorModel"
      :retrying="testingModels[currentErrorModelId]"
      @retry="retryFailedModel"
      @select-model="selectAlternativeModel"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getModelList,
  deleteModel as deleteModelApi,
  setActiveModel,
  setDefaultModel,
  testModelConnection,
  testAllModels as testAllModelsApi,
  getProviders
} from '@/api/aiModel'
import ModelDialog from './components/ModelDialog.vue'
import TestResultsDialog from './components/TestResultsDialog.vue'
import ErrorDialog from '@/components/ErrorDialog.vue'

// Reactive data
const loading = ref(false)
const testingAll = ref(false)
const models = ref([])
const providers = ref([])
const testingModels = ref({})

// Computed properties
const providerOptions = computed(() => {
  const providerCounts = {}
  
  // Count models for each provider
  models.value.forEach(model => {
    const provider = model.provider || 'unknown'
    providerCounts[provider] = (providerCounts[provider] || 0) + 1
  })
  
  // Generate provider options with descriptions and counts
  return providers.value.map(provider => {
    const count = providerCounts[provider] || 0
    const providerInfo = getProviderInfo(provider)
    
    return {
      value: provider,
      label: provider,
      description: providerInfo.description,
      tagType: providerInfo.tagType,
      count: count
    }
  }).filter(option => option.count > 0) // Only show providers that have models
})

const filters = reactive({
  provider: '',
  isActive: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const dialogVisible = ref(false)
const currentModel = ref(null)

const testResultsVisible = ref(false)
const testResults = ref([])

const errorDialogVisible = ref(false)
const currentError = ref({})
const currentErrorModel = ref('')
const currentErrorModelId = ref(null)

// Methods
const loadModels = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...filters
    }
    
    const response = await getModelList(params)
    if (response.code === 200) {
      models.value = response.data.records
      pagination.total = response.data.total
    }
  } catch (error) {
    ElMessage.error('加载模型失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadProviders = async () => {
  try {
    const response = await getProviders()
    if (response.code === 200) {
      providers.value = response.data
    }
  } catch (error) {
    console.error('Failed to load providers:', error)
  }
}

const showAddDialog = () => {
  currentModel.value = null
  dialogVisible.value = true
}

const editModel = (model) => {
  currentModel.value = { ...model }
  dialogVisible.value = true
}

const deleteModel = async (model) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${model.modelName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deleteModelApi(model.id)
    if (response.code === 200) {
      ElMessage.success('模型删除成功')
      loadModels()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模型失败: ' + error.message)
    }
  }
}

const setActive = async (model) => {
  try {
    const response = await setActiveModel(model.id)
    if (response.code === 200) {
      ElMessage.success(`模型 "${model.modelName}" 已激活`)
      loadModels()
    }
  } catch (error) {
    ElMessage.error('激活模型失败: ' + error.message)
  }
}

const setDefault = async (model) => {
  try {
    const response = await setDefaultModel(model.id)
    if (response.code === 200) {
      ElMessage.success(`模型 "${model.modelName}" 已设为默认`)
      loadModels()
    }
  } catch (error) {
    ElMessage.error('设置默认模型失败: ' + error.message)
  }
}

const duplicateModel = (model) => {
  const duplicatedModel = {
    ...model,
    id: null,
    modelName: model.modelName + ' (副本)',
    isActive: false,
    isDefault: false
  }
  currentModel.value = duplicatedModel
  dialogVisible.value = true
}

// Handle dropdown command
const handleCommand = ({ action, row }) => {
  switch (action) {
    case 'edit':
      editModel(row)
      break
    case 'setDefault':
      setDefault(row)
      break
    case 'deactivate':
      // Implement deactivate logic
      ElMessage.info('取消激活功能待实现')
      break
    case 'duplicate':
      duplicateModel(row)
      break
    case 'delete':
      deleteModel(row)
      break
  }
}

const testConnection = async (model) => {
  testingModels.value[model.id] = true
  try {
    const response = await testModelConnection(model.id)
    if (response.code === 200) {
      const result = response.data
      if (result.success) {
        ElMessage.success(`模型 "${model.modelName}" 连接测试成功`)
      } else {
        // Show formatted error dialog instead of simple message
        if (result.formattedError) {
          currentError.value = result.formattedError
          currentErrorModel.value = model.modelName
          currentErrorModelId.value = model.id
          errorDialogVisible.value = true
        } else {
          ElMessage.error(`模型 "${model.modelName}" 连接测试失败: ${result.message}`)
        }
      }
      loadModels() // Refresh to show updated connection status
    }
  } catch (error) {
    // Handle network or other errors
    const formattedError = {
      errorInfo: {
        title: 'Connection Failed',
        message: error.message || 'Network error occurred',
        errorType: 'NETWORK_ERROR',
        timestamp: new Date().toISOString(),
        modelName: model.modelName,
        provider: model.provider
      },
      troubleshooting: {
        suggestions: [
          'Check your internet connection',
          'Verify the model endpoint is accessible',
          'Try again in a few moments'
        ]
      }
    }
    
    currentError.value = formattedError
    currentErrorModel.value = model.modelName
    currentErrorModelId.value = model.id
    errorDialogVisible.value = true
  } finally {
    testingModels.value[model.id] = false
  }
}

const testAllModels = async () => {
  testingAll.value = true
  try {
    const response = await testAllModelsApi()
    if (response.code === 200) {
      testResults.value = response.data
      testResultsVisible.value = true
      loadModels() // Refresh to show updated connection statuses
    }
  } catch (error) {
    ElMessage.error('批量测试失败: ' + error.message)
  } finally {
    testingAll.value = false
  }
}

const resetFilters = () => {
  filters.provider = ''
  filters.isActive = null
  pagination.current = 1
  loadModels()
}

const handleSortChange = ({ prop, order }) => {
  // Implement sorting if needed
  loadModels()
}

const handleModelSaved = () => {
  dialogVisible.value = false
  loadModels()
}

const retryFailedModel = () => {
  if (currentErrorModelId.value) {
    const model = models.value.find(m => m.id === currentErrorModelId.value)
    if (model) {
      errorDialogVisible.value = false
      testConnection(model)
    }
  }
}

const selectAlternativeModel = (modelName) => {
  ElMessage.info(`Suggested alternative model: ${modelName}`)
  // You can implement logic to switch to alternative model here
}

// Utility methods
const getProviderTagType = (provider) => {
  const types = {
    openrouter: 'primary',
    openai: 'success',
    anthropic: 'warning',
    ollama: 'info',
    google: 'danger'
  }
  return types[provider?.toLowerCase()] || 'default'
}

const getProviderInfo = (provider) => {
  const providerInfoMap = {
    openrouter: {
      description: '多模型聚合',
      tagType: 'primary'
    },
    openai: {
      description: 'GPT系列',
      tagType: 'success'
    },
    anthropic: {
      description: 'Claude系列',
      tagType: 'warning'
    },
    ollama: {
      description: '本地部署',
      tagType: 'info'
    },
    google: {
      description: 'Gemini系列',
      tagType: 'danger'
    }
  }
  
  return providerInfoMap[provider?.toLowerCase()] || {
    description: '其他',
    tagType: 'default'
  }
}

const getConnectionStatusClass = (status) => {
  switch (status) {
    case 1: return 'status-success'
    case 2: return 'status-error'
    default: return 'status-unknown'
  }
}

const getConnectionStatusIcon = (status) => {
  switch (status) {
    case 1: return 'CircleCheck'
    case 2: return 'CircleClose'
    default: return 'QuestionFilled'
  }
}

const getConnectionStatusText = (status) => {
  switch (status) {
    case 1: return '已连接'
    case 2: return '连接失败'
    default: return '未知'
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString()
}

// Lifecycle
onMounted(() => {
  loadProviders()
  loadModels()
})
</script>

<style scoped>
.model-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-form {
  margin: 0;
}

/* Enhanced filter form styles */
.filter-form .el-form-item {
  margin-right: 24px;
  margin-bottom: 0;
}

.filter-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.model-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.model-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.model-badges {
  display: flex;
  gap: 4px;
}

.model-id {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-success {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.status-unknown {
  color: #909399;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-container {
  padding: 16px;
  display: flex;
  justify-content: center;
}

.text-muted {
  color: #909399;
}

/* Custom dropdown styles */
.provider-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  width: 100%;
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.provider-name {
  font-weight: 500;
  color: #303133;
}

.provider-tag {
  margin-left: 4px;
}

.provider-count {
  font-size: 12px;
  color: #909399;
  flex-shrink: 0;
}

/* Fixed status option alignment */
.status-option {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 0;
  width: 100%;
}

.status-icon {
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.status-icon.active {
  color: #67c23a;
}

.status-icon.inactive {
  color: #f56c6c;
}

.status-info {
  display: flex;
  justify-content: space-between; /* 两端对齐 */
  gap: 36px;
  flex: 1;
  min-width: 0;
  white-space: nowrap;

}

.status-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  line-height: 1.2;
}

.status-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

/* Responsive design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .filters {
    padding: 12px;
  }
  
  .filter-form .el-form-item {
    margin-bottom: 12px;
  }
}
</style>