<template>
  <div class="notification-management">
    <div class="page-header">
      <h2>通知管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        发布通知
      </el-button>
    </div>

    <!-- 通知列表 -->
    <el-card>
      <div class="filter-bar">
        <el-form :model="queryForm" inline>
          <el-form-item label="通知类型">
            <el-select v-model="queryForm.type" placeholder="请选择" style="width: 120px">
              <el-option label="全部类型" :value="null" />
              <el-option label="系统通知" :value="1" />
              <el-option label="课程通知" :value="2" />
              <el-option label="作业通知" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="目标角色">
            <el-select v-model="queryForm.targetRole" placeholder="请选择" style="width: 120px">
              <el-option label="全部角色" :value="null" />
              <el-option label="所有用户" value="ALL" />
              <el-option label="教师" value="TEACHER" />
              <el-option label="学生" value="STUDENT" />
              <el-option label="管理员" value="ADMIN" />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级">
            <el-select v-model="queryForm.priority" placeholder="请选择" style="width: 120px">
              <el-option label="全部优先级" :value="null" />
              <el-option label="普通" :value="1" />
              <el-option label="重要" :value="2" />
              <el-option label="紧急" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchNotifications">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="notifications" v-loading="loading">
        <el-table-column prop="title" label="标题" min-width="120" />
        <el-table-column prop="content" label="内容" min-width="250" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.type === 1">系统通知</el-tag>
            <el-tag v-else-if="row.type === 2" type="success">课程通知</el-tag>
            <el-tag v-else-if="row.type === 3" type="warning">作业通知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="target_role" label="目标角色" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.target_role === 'ALL'" type="info">所有用户</el-tag>
            <el-tag v-else-if="row.target_role === 'TEACHER'" type="success">教师</el-tag>
            <el-tag v-else-if="row.target_role === 'STUDENT'" type="warning">学生</el-tag>
            <el-tag v-else-if="row.target_role === 'ADMIN'" type="danger">管理员</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.priority === 1">普通</el-tag>
            <el-tag v-else-if="row.priority === 2" type="warning">重要</el-tag>
            <el-tag v-else-if="row.priority === 3" type="danger">紧急</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDetail(row)">查看</el-button>
            <el-button type="text" size="small" @click="editNotification(row)">编辑</el-button>
            <el-button 
              type="text" 
              size="small" 
              style="color: #f56c6c"
              @click="deleteNotification(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchNotifications"
          @current-change="fetchNotifications"
        />
      </div>
    </el-card>

    <!-- 创建/编辑通知对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑通知' : '发布通知'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="notificationForm" :rules="formRules" ref="formRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="notificationForm.title" placeholder="请输入通知标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="notificationForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入通知内容"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="notificationForm.type" placeholder="请选择通知类型" style="width: 100%">
            <el-option label="系统通知" :value="1" />
            <el-option label="课程通知" :value="2" />
            <el-option label="作业通知" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标角色" prop="targetRole">
          <el-select v-model="notificationForm.targetRole" placeholder="请选择目标角色" style="width: 100%">
            <el-option label="所有用户" value="ALL" />
            <el-option label="教师" value="TEACHER" />
            <el-option label="学生" value="STUDENT" />
            <el-option label="管理员" value="ADMIN" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="notificationForm.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="普通" :value="1" />
            <el-option label="重要" :value="2" />
            <el-option label="紧急" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="过期时间">
          <el-date-picker
            v-model="notificationForm.expireTime"
            type="datetime"
            placeholder="选择过期时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '发布' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 通知详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="通知详情" width="500px">
      <div v-if="currentNotification">
        <div class="detail-item">
          <label>标题：</label>
          <span>{{ currentNotification.title }}</span>
        </div>
        <div class="detail-item">
          <label>内容：</label>
          <p>{{ currentNotification.content }}</p>
        </div>
        <div class="detail-item">
          <label>类型：</label>
          <el-tag v-if="currentNotification.type === 1">系统通知</el-tag>
          <el-tag v-else-if="currentNotification.type === 2" type="success">课程通知</el-tag>
          <el-tag v-else-if="currentNotification.type === 3" type="warning">作业通知</el-tag>
        </div>
        <div class="detail-item">
          <label>目标角色：</label>
          <el-tag v-if="currentNotification.target_role === 'ALL'" type="info">所有用户</el-tag>
          <el-tag v-else-if="currentNotification.target_role === 'TEACHER'" type="success">教师</el-tag>
          <el-tag v-else-if="currentNotification.target_role === 'STUDENT'" type="warning">学生</el-tag>
          <el-tag v-else-if="currentNotification.target_role === 'ADMIN'" type="danger">管理员</el-tag>
        </div>
        <div class="detail-item">
          <label>优先级：</label>
          <el-tag v-if="currentNotification.priority === 1">普通</el-tag>
          <el-tag v-else-if="currentNotification.priority === 2" type="warning">重要</el-tag>
          <el-tag v-else-if="currentNotification.priority === 3" type="danger">紧急</el-tag>
        </div>
        <div class="detail-item">
          <label>创建时间：</label>
          <span>{{ formatTime(currentNotification.create_time) }}</span>
        </div>
        <div class="detail-item" v-if="currentNotification.expire_time">
          <label>过期时间：</label>
          <span>{{ formatTime(currentNotification.expire_time) }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  publishNotification, 
  getAllNotifications,
  deleteNotification as deleteNotificationApi
} from '@/api/notification'
import { formatTime } from '@/utils/format'

const loading = ref(false)
const notifications = ref([])
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const currentNotification = ref(null)
const formRef = ref()

const queryForm = reactive({
  type: null,
  targetRole: null,
  priority: null
})

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const notificationForm = reactive({
  title: '',
  content: '',
  type: 1,
  targetRole: 'ALL',
  priority: 1,
  expireTime: null
})

const formRules = {
  title: [
    { required: true, message: '请输入通知标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入通知内容', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  targetRole: [
    { required: true, message: '请选择目标角色', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 获取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...queryForm
    }
    const response = await getAllNotifications(params)
    if (response.code === 200) {
      notifications.value = response.data.notifications || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取通知列表失败:', error)
    ElMessage.error('获取通知列表失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key] = null
  })
  pagination.page = 1
  fetchNotifications()
}

// 显示创建对话框
const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑通知
const editNotification = (row) => {
  isEdit.value = true
  Object.assign(notificationForm, {
    id: row.id,
    title: row.title,
    content: row.content,
    type: row.type,
    targetRole: row.target_role,
    priority: row.priority,
    expireTime: row.expire_time
  })
  dialogVisible.value = true
}

// 查看详情
const viewDetail = (row) => {
  currentNotification.value = row
  detailDialogVisible.value = true
}

// 删除通知
const deleteNotification = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteNotificationApi(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      fetchNotifications()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const response = await publishNotification(notificationForm)
    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '发布成功')
      dialogVisible.value = false
      fetchNotifications()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(notificationForm, {
    title: '',
    content: '',
    type: 1,
    targetRole: 'ALL',
    priority: 1,
    expireTime: null
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  fetchNotifications()
})
</script>

<style scoped>
.notification-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.filter-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.detail-item {
  margin-bottom: 15px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 10px;
}

.detail-item p {
  margin: 5px 0;
  line-height: 1.6;
  color: #303133;
}
</style>