<template>
  <div class="admin-dashboard">
    <div class="page-header">
      <h2>管理员仪表板</h2>
      <p>系统运营数据概览</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <el-icon>
                <User />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ dashboardData.totalUsers || 0 }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon teacher-icon">
              <el-icon>
                <Avatar />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ dashboardData.totalTeachers || 0 }}</div>
              <div class="stat-label">教师数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon student-icon">
              <el-icon>
                <Reading />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ dashboardData.totalStudents || 0 }}</div>
              <div class="stat-label">学生数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon course-icon">
              <el-icon>
                <Notebook />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ dashboardData.totalCourses || 0 }}</div>
              <div class="stat-label">课程数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 使用统计区域 -->
    <el-row :gutter="20" class="usage-stats-section">
      <!-- 教师使用统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>教师使用统计</span>
              <el-button size="small" @click="loadTeacherUsageStats">
                <el-icon>
                  <Refresh />
                </el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <div class="usage-stats-content">
            <!-- 当日统计 -->
            <div class="stats-period">
              <h4>当日统计</h4>
              <el-row :gutter="15">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ teacherStats.today?.activeTeachers || 0 }}</div>
                    <div class="stat-desc">活跃教师</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ teacherStats.today?.totalUsage || 0 }}</div>
                    <div class="stat-desc">使用次数</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ formatDuration(teacherStats.today?.totalDuration || 0) }}</div>
                    <div class="stat-desc">使用时长</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 本周统计 -->
            <div class="stats-period">
              <h4>本周统计</h4>
              <el-row :gutter="15">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ teacherStats.thisWeek?.activeTeachers || 0 }}</div>
                    <div class="stat-desc">活跃教师</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ teacherStats.thisWeek?.totalUsage || 0 }}</div>
                    <div class="stat-desc">使用次数</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ formatDuration(teacherStats.thisWeek?.totalDuration || 0) }}</div>
                    <div class="stat-desc">使用时长</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 活跃板块 -->
            <div class="active-modules">
              <h4>活跃板块排行</h4>
              <div class="modules-list">
                <div v-for="(module, index) in teacherStats.activeModules?.slice(0, 5) || []" :key="module.module_name"
                  class="module-item">
                  <div class="module-rank">{{ index + 1 }}</div>
                  <div class="module-info">
                    <div class="module-name">{{ module.module_name }}</div>
                    <div class="module-usage">{{ module.total_access }}次使用</div>
                  </div>
                  <div class="module-users">{{ module.unique_users }}人</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 学生使用统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学生使用统计</span>
              <el-button size="small" @click="loadStudentUsageStats">
                <el-icon>
                  <Refresh />
                </el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <div class="usage-stats-content">
            <!-- 当日统计 -->
            <div class="stats-period">
              <h4>当日统计</h4>
              <el-row :gutter="15">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ studentStats.today?.activeStudents || 0 }}</div>
                    <div class="stat-desc">活跃学生</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ studentStats.today?.totalUsage || 0 }}</div>
                    <div class="stat-desc">使用次数</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ formatDuration(studentStats.today?.totalDuration || 0) }}</div>
                    <div class="stat-desc">使用时长</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 本周统计 -->
            <div class="stats-period">
              <h4>本周统计</h4>
              <el-row :gutter="15">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ studentStats.thisWeek?.activeStudents || 0 }}</div>
                    <div class="stat-desc">活跃学生</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ studentStats.thisWeek?.totalUsage || 0 }}</div>
                    <div class="stat-desc">使用次数</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ formatDuration(studentStats.thisWeek?.totalDuration || 0) }}</div>
                    <div class="stat-desc">使用时长</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 活跃板块 -->
            <div class="active-modules">
              <h4>活跃板块排行</h4>
              <div class="modules-list">
                <div v-for="(module, index) in studentStats.activeModules?.slice(0, 5) || []" :key="module.module_name"
                  class="module-item">
                  <div class="module-rank">{{ index + 1 }}</div>
                  <div class="module-info">
                    <div class="module-name">{{ module.module_name }}</div>
                    <div class="module-usage">{{ module.total_access }}次使用</div>
                  </div>
                  <div class="module-users">{{ module.unique_users }}人</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 用户增长趋势 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
              <el-radio-group v-model="userGrowthPeriod" size="small" @change="loadUserGrowthData">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="userGrowthChart" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 活跃用户分布 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>活跃用户分布</span>
            </div>
          </template>
          <div ref="activeUsersChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 教学效率统计 -->
    <el-row :gutter="20" class="efficiency-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>教学效率统计</span>
              <el-button size="small" @click="refreshEfficiencyData">
                <el-icon>
                  <Refresh />
                </el-icon>
                刷新数据
              </el-button>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="efficiency-item">
                <h4>备课效率</h4>
                <div class="efficiency-value">
                  {{ dashboardData.avgPreparationTime || '45分钟' }}/课
                </div>
                <div class="efficiency-status">
                  <el-tag :type="getEfficiencyTagType(dashboardData.avgPreparationTime, 60)" size="small">
                    {{ getEfficiencyStatus(dashboardData.avgPreparationTime, 60) }}
                  </el-tag>
                </div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="efficiency-item">
                <h4>练习设计效率</h4>
                <div class="efficiency-value">
                  {{ dashboardData.avgExerciseTime || '35分钟' }}/套
                </div>
                <div class="efficiency-status">
                  <el-tag :type="getEfficiencyTagType(dashboardData.avgExerciseTime, 45)" size="small">
                    {{ getEfficiencyStatus(dashboardData.avgExerciseTime, 45) }}
                  </el-tag>
                </div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="efficiency-item">
                <h4>教学效率指数</h4>
                <div class="efficiency-value">
                  {{ dashboardData.efficiencyIndex || 0 }}分
                </div>
                <div class="efficiency-status">
                  <el-tag :type="getIndexTagType(dashboardData.efficiencyIndex)" size="small">
                    {{ getIndexStatus(dashboardData.efficiencyIndex) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 课程优化建议 -->
          <div v-if="dashboardData.optimizationSuggestions && dashboardData.optimizationSuggestions.length > 0"
            class="optimization-suggestions">
            <h4>课程优化建议</h4>
            <div class="suggestions-list">
              <div v-for="(suggestion, index) in dashboardData.optimizationSuggestions" :key="index"
                class="suggestion-item" :class="'priority-' + suggestion.priority">
                <div class="suggestion-type">{{ suggestion.type }}</div>
                <div class="suggestion-content">{{ suggestion.content }}</div>
                <el-tag :type="getSuggestionTagType(suggestion.priority)" size="small">
                  {{ suggestion.priority }}优先级
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-section">
      <el-col>
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近登录用户</span>
            </div>
          </template>

          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column prop="realName" label="姓名" />
            <el-table-column prop="role" label="角色">
              <template #default="{ row }">
                <el-tag :type="getRoleTagType(row.role)" size="small">
                  {{ getRoleText(row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastLoginTime" label="最后登录" />
          </el-table>
        </el-card>
      </el-col>


    </el-row>

    <!-- 发布通知对话框 -->
    <el-dialog v-model="showNotificationDialog" title="发布系统通知" width="600px">
      <el-form :model="notificationForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="notificationForm.title" placeholder="请输入通知标题" />
        </el-form-item>
        <el-form-item label="内容">
          <el-input v-model="notificationForm.content" type="textarea" :rows="4" placeholder="请输入通知内容" />
        </el-form-item>
        <el-form-item label="目标用户">
          <el-select v-model="notificationForm.targetRole" placeholder="选择目标用户">
            <el-option label="所有用户" value="ALL" />
            <el-option label="教师" value="TEACHER" />
            <el-option label="学生" value="STUDENT" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showNotificationDialog = false">取消</el-button>
        <el-button type="primary" @click="publishSystemNotification">发布</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getDashboardOverview,
  getUsageTrends,
  getActiveUsersStats,
  getUsers,
  getTeacherUsageStats,
  getStudentUsageStats,
  getComprehensiveStats
} from '@/api/admin'
import {
  getLatestSystemNotifications,
  publishNotification
} from '@/api/notification'
import { formatDate } from '@/utils/format'

const loading = ref(false)
const showNotificationDialog = ref(false)
const userGrowthPeriod = ref('month')

const userGrowthChart = ref()
const activeUsersChart = ref()
let userGrowthChartInstance = null
let activeUsersChartInstance = null

const dashboardData = ref({})
const recentUsers = ref([])
const notifications = ref([])
const teacherStats = ref({})
const studentStats = ref({})

const notificationForm = reactive({
  title: '',
  content: '',
  targetRole: 'ALL'
})

const loadDashboardData = async () => {
  loading.value = true
  try {
    const response = await getDashboardOverview()
    dashboardData.value = response.data
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    ElMessage.error('加载仪表板数据失败')
  } finally {
    loading.value = false
  }
}

const loadUserGrowthData = async () => {
  try {
    const response = await getUsageTrends({ period: userGrowthPeriod.value })
    renderUserGrowthChart(response.data)
  } catch (error) {
    console.error('Failed to load user growth data:', error)
  }
}

const loadActiveUsersData = async () => {
  try {
    const response = await getActiveUsersStats()
    renderActiveUsersChart(response.data)
  } catch (error) {
    console.error('Failed to load active users data:', error)
  }
}

const loadRecentUsers = async () => {
  try {
    const response = await getUsers({ limit: 10 })
    // Get recent users from the response
    if (response.data && response.data.records) {
      recentUsers.value = response.data.records.slice(0, 10)
    } else {
      recentUsers.value = []
    }
  } catch (error) {
    console.error('Failed to load recent users:', error)
    recentUsers.value = []
  }
}

const loadNotifications = async () => {
  try {
    const response = await getLatestSystemNotifications({ limit: 5 })
    notifications.value = response.data || []
  } catch (error) {
    console.error('Failed to load notifications:', error)
    notifications.value = []
  }
}

const renderUserGrowthChart = (data) => {
  if (!userGrowthChartInstance) {
    userGrowthChartInstance = echarts.init(userGrowthChart.value)
  }

  // Ensure data exists
  const dates = data?.dates || []
  const newUsers = data?.newUsers || []
  const activeUsers = data?.activeUsers || []

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ' + param.value + '<br/>'
        })
        return result
      }
    },
    legend: {
      data: ['新增用户', '活跃用户']
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45,
        formatter: function (value) {
          // Format date for display
          return value.substring(5) // Show MM-DD
        }
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1
    },
    series: [
      {
        name: '新增用户',
        type: 'line',
        data: newUsers,
        smooth: true,
        itemStyle: { color: '#409eff' },
        lineStyle: { color: '#409eff' }
      },
      {
        name: '活跃用户',
        type: 'line',
        data: activeUsers,
        smooth: true,
        itemStyle: { color: '#67c23a' },
        lineStyle: { color: '#67c23a' }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }

  userGrowthChartInstance.setOption(option, true)
}

const renderActiveUsersChart = (data) => {
  if (!activeUsersChartInstance) {
    activeUsersChartInstance = echarts.init(activeUsersChart.value)
  }

  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '用户分布',
        type: 'pie',
        radius: '50%',
        data: [
          { value: data.teachers || 0, name: '教师' },
          { value: data.students || 0, name: '学生' },
          { value: data.admins || 0, name: '管理员' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  activeUsersChartInstance.setOption(option)
}

const refreshEfficiencyData = () => {
  loadDashboardData()
  ElMessage.success('数据已刷新')
}

const getEfficiencyTrendClass = (trend) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

const getEfficiencyTrendIcon = (trend) => {
  if (trend > 0) return 'ArrowUp'
  if (trend < 0) return 'ArrowDown'
  return 'Minus'
}

const getRoleTagType = (role) => {
  const types = {
    'ADMIN': 'danger',
    'TEACHER': 'warning',
    'STUDENT': 'success'
  }
  return types[role] || ''
}

const getRoleText = (role) => {
  const texts = {
    'ADMIN': '管理员',
    'TEACHER': '教师',
    'STUDENT': '学生'
  }
  return texts[role] || role
}

const getSuggestionTagType = (priority) => {
  const types = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return types[priority] || 'info'
}

const getEfficiencyTagType = (timeStr, standardTime) => {
  const time = parseInt(timeStr)
  if (time <= standardTime * 0.8) return 'success'  // 优秀
  if (time <= standardTime) return 'warning'        // 良好
  return 'danger'                                   // 需改进
}

const getEfficiencyStatus = (timeStr, standardTime) => {
  const time = parseInt(timeStr)
  if (time <= standardTime * 0.8) return '优秀'
  if (time <= standardTime) return '良好'
  return '需改进'
}

const getIndexTagType = (indexStr) => {
  const index = parseFloat(indexStr)
  if (index >= 85) return 'success'    // 优秀
  if (index >= 70) return 'warning'    // 良好
  return 'danger'                      // 需改进
}

const getIndexStatus = (indexStr) => {
  const index = parseFloat(indexStr)
  if (index >= 85) return '优秀'
  if (index >= 70) return '良好'
  return '需改进'
}

const loadTeacherUsageStats = async () => {
  try {
    const response = await getTeacherUsageStats()
    teacherStats.value = response.data || {}
  } catch (error) {
    console.error('Failed to load teacher usage stats:', error)
    ElMessage.error('加载教师使用统计失败')
  }
}

const loadStudentUsageStats = async () => {
  try {
    const response = await getStudentUsageStats()
    studentStats.value = response.data || {}
  } catch (error) {
    console.error('Failed to load student usage stats:', error)
    ElMessage.error('加载学生使用统计失败')
  }
}

const formatDuration = (seconds) => {
  if (!seconds || seconds === 0) return '0分钟'

  const totalSeconds = parseInt(seconds)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const remainingSeconds = totalSeconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return `${remainingSeconds}秒`
  }
}

const publishSystemNotification = async () => {
  if (!notificationForm.title || !notificationForm.content) {
    ElMessage.warning('请填写完整的通知信息')
    return
  }

  try {
    const notificationData = {
      title: notificationForm.title,
      content: notificationForm.content,
      targetRole: notificationForm.targetRole,
      type: 1, // 系统通知
      priority: 1 // 普通优先级
    }

    await publishNotification(notificationData)
    ElMessage.success('通知发布成功')
    showNotificationDialog.value = false

    // 重置表单
    notificationForm.title = ''
    notificationForm.content = ''
    notificationForm.targetRole = 'ALL'

    // 重新加载通知列表
    loadNotifications()
  } catch (error) {
    console.error('Failed to publish notification:', error)
    ElMessage.error('发布通知失败')
  }
}

onMounted(async () => {
  await loadDashboardData()
  await loadRecentUsers()
  await loadNotifications()
  await loadTeacherUsageStats()
  await loadStudentUsageStats()

  await nextTick()
  loadUserGrowthData()
  loadActiveUsersData()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    userGrowthChartInstance?.resize()
    activeUsersChartInstance?.resize()
  })
})
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: -10px;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: #409eff;
}

.teacher-icon {
  background: #e6a23c;
}

.student-icon {
  background: #67c23a;
}

.course-icon {
  background: #f56c6c;
}

.stat-info {
 
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #ffff;
  margin-top: 8px;
}

.charts-section {
  margin-bottom: 20px;
}

.efficiency-section {
  margin-bottom: 20px;
}

.efficiency-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.efficiency-item h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.efficiency-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 10px;
}

.efficiency-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-stable {
  color: #909399;
}

.activity-section {
  margin-bottom: 20px;
}

.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content h5 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 14px;
}

.notification-content p {
  margin: 0;
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  margin-left: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.usage-stats-section {
  margin-bottom: 20px;
}

.usage-stats-content {
  padding: 10px 0;
}

.stats-period {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.stats-period:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.stats-period h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.stat-item {
  text-align: center;
  padding: 15px 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-desc {
  font-size: 12px;
  color: #909399;
}

.active-modules h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.modules-list {
  max-height: 200px;
  overflow-y: auto;
}

.module-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.module-item:last-child {
  border-bottom: none;
}

.module-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-right: 15px;
}

.module-item:nth-child(1) .module-rank {
  background: #f56c6c;
}

.module-item:nth-child(2) .module-rank {
  background: #e6a23c;
}

.module-item:nth-child(3) .module-rank {
  background: #67c23a;
}

.module-info {
  flex: 1;
}

.module-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 2px;
}

.module-usage {
  font-size: 12px;
  color: #909399;
}

.module-users {
  font-size: 12px;
  color: #606266;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
}

.optimization-suggestions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.optimization-suggestions h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.suggestions-list {
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  border-left: 4px solid #e4e7ed;
}

.suggestion-item.priority-高 {
  border-left-color: #f56c6c;
  background: #fef0f0;
}

.suggestion-item.priority-中 {
  border-left-color: #e6a23c;
  background: #fdf6ec;
}

.suggestion-item.priority-低 {
  border-left-color: #909399;
  background: #f4f4f5;
}

.suggestion-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.suggestion-content {
  flex: 1;
  font-size: 14px;
  color: #303133;
  margin-right: 10px;
}
</style>