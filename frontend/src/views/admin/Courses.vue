<template>
  <div class="admin-courses-container">
    <div class="page-header">
      <div class="header-left">
        <h2>课程管理</h2>
        <p>查看和管理所有教师的课程信息</p>
      </div>
    </div>

  
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="课程名称">
          <el-input 
            v-model="searchForm.courseName" 
            placeholder="请输入课程名称" 
            clearable
            @keyup.enter="loadCourses"
          />
        </el-form-item>
        <el-form-item label="学科">
          <el-select 
            v-model="searchForm.subject" 
            placeholder="选择学科" 
            clearable
            style="width: 200px"
          >
            <el-option label="全部学科" value="" />
            <el-option 
              v-for="subject in subjectOptions" 
              :key="subject" 
              :label="subject" 
              :value="subject" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="授课教师">
          <el-select 
            v-model="searchForm.teacherName" 
            placeholder="选择授课教师" 
            clearable
            style="width: 200px"
          >
            <el-option label="全部教师" value="" />
            <el-option 
              v-for="teacher in teacherOptions" 
              :key="teacher" 
              :label="teacher" 
              :value="teacher" 
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadCourses">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            添加课程
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-table v-loading="loading"
      :data="courses"
      style="width: 100%;"
    border
    >
      <el-table-column prop="courseCode" label="课程编码" width="120" />
      <el-table-column prop="courseName" label="课程名称" />
      <el-table-column prop="subject" label="学科" width="180" />
      <el-table-column prop="teacherName" label="授课教师" width="120" />
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="250">
        <template #default="{ row }">
          <div class="status-cell">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              active-text="正常"
              inactive-text="禁用"
              :class="{ 'is-disabled': row.status === 0 }"
              @change="handleStatusChange(row.id, $event)"
            />
            <el-tag
              :type="row.status === 1 ? 'success' : 'danger'"
              size="small"
              class="status-tag"
            >
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              type="primary"
              size="small"
              @click="viewCourseDetails(row)"
            >
              详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="showAssignTeacherDialog(row)"
            >
              分配教师
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteCourse(row)"
            >
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="showDetailsDialog"
      title="课程详情"
      width="600px"
    >
      <div v-if="selectedCourse" class="course-details">
        <div class="detail-item">
          <span class="label">课程名称：</span>
          <span>{{ selectedCourse.courseName }}</span>
        </div>
        <div class="detail-item">
          <span class="label">课程编码：</span>
          <span>{{ selectedCourse.courseCode }}</span>
        </div>
        <div class="detail-item">
          <span class="label">学科名称：</span>
          <span>{{ selectedCourse.subject }}</span>
        </div>
        <div class="detail-item">
          <span class="label">授课教师：</span>
          <span>{{ selectedCourse.teacherName }}</span>
        </div>
        <div class="detail-item">
          <span class="label">创建时间：</span>
          <span>{{ formatDate(selectedCourse.createTime) }}</span>
        </div>
        <div class="detail-item">
          <span class="label">课程描述：</span>
          <p class="description">{{ selectedCourse.description }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 创建课程对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="添加课程"
      width="600px"
    >
      <el-form
        ref="courseFormRef"
        :model="courseForm"
        :rules="courseRules"
        label-width="100px"
      >
        <el-form-item label="课程名称" prop="courseName">
          <el-input 
            v-model="courseForm.courseName" 
            placeholder="请输入课程名称"
          />
        </el-form-item>
        
        <el-form-item label="课程编码" prop="courseCode">
          <el-input 
            v-model="courseForm.courseCode" 
            placeholder="请输入课程编码"
            @blur="validateCourseCode"
            :suffix-icon="codeValidating ? 'Loading' : ''"
          />
          <div v-if="codeValidationMessage" :class="['validation-message', codeValidationStatus]">
            {{ codeValidationMessage }}
          </div>
        </el-form-item>
        
        <el-form-item label="学科" prop="subject">
          <el-input 
            v-model="courseForm.subject" 
            placeholder="请输入学科名称"
          />
        </el-form-item>
        
        <el-form-item label="授课教师" prop="teacherId">
          <el-select 
            v-model="courseForm.teacherId" 
            placeholder="选择授课教师" 
            style="width: 100%"
          >
            <el-option 
              v-for="teacher in allTeachers" 
              :key="teacher.id" 
              :label="teacher.realName" 
              :value="teacher.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程描述" prop="description">
          <el-input 
            v-model="courseForm.description" 
            type="textarea"
            :rows="4"
            placeholder="请输入课程描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCourse" :loading="saving">
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 分配教师对话框 -->
    <el-dialog
      v-model="showAssignDialog"
      title="分配教师"
      width="500px"
    >
      <div v-if="assignCourse" class="assign-content">
        <div class="course-info">
          <h4>课程信息</h4>
          <p><strong>课程名称：</strong>{{ assignCourse.courseName }}</p>
          <p><strong>课程编码：</strong>{{ assignCourse.courseCode }}</p>
          <p><strong>当前教师：</strong>{{ assignCourse.teacherName || '未分配' }}</p>
        </div>
        
        <el-form 
          ref="assignFormRef"
          :model="assignForm"
          :rules="assignRules"
          label-width="100px"
        >
          <el-form-item label="选择教师" prop="teacherId">
            <el-select 
              v-model="assignForm.teacherId" 
              placeholder="请选择教师" 
              style="width: 100%"
              filterable
            >
              <el-option 
                v-for="teacher in allTeachers" 
                :key="teacher.id" 
                :label="`${teacher.realName} (${teacher.username})`" 
                :value="teacher.id"
              >
                <span>{{ teacher.realName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ teacher.email }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showAssignDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAssignment" :loading="assigning">
          {{ assigning ? '分配中...' : '确认分配' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { 
  getCourses, 
  deleteCourse, 
  changeCourseStatus, 
  createCourse, 
  createCourseWithTeacher,
  getTeachers,
  getTeachersForCourse,
  assignTeacherToCourse,
  getCoursesWithDetails,
  checkCourseCodeExists
} from '@/api/admin'
import { formatDate } from '@/utils/format'

const loading = ref(false)
const courses = ref([])
const showDetailsDialog = ref(false)
const selectedCourse = ref(null)
const subjectOptions = ref([])
const teacherOptions = ref([])
const allTeachers = ref([])
const showCreateDialog = ref(false)
const courseFormRef = ref()
const saving = ref(false)

// 课程编码验证相关
const codeValidating = ref(false)
const codeValidationMessage = ref('')
const codeValidationStatus = ref('') // 'success' or 'error'

// 教师分配相关
const showAssignDialog = ref(false)
const assignCourse = ref(null)
const assignFormRef = ref()
const assigning = ref(false)

const courseForm = reactive({
  courseName: '',
  courseCode: '',
  subject: '',
  teacherId: '',
  description: ''
})

const courseRules = {
  courseName: [
    { required: true, message: '请输入课程名称', trigger: 'blur' }
  ],
  courseCode: [
    { required: true, message: '请输入课程编码', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入学科', trigger: 'blur' }
  ],
  teacherId: [
    { required: true, message: '请选择授课教师', trigger: 'change' }
  ]
}

const searchForm = reactive({
  courseName: '',
  subject: '',
  teacherName: ''
})

// 教师分配表单
const assignForm = reactive({
  courseId: '',
  teacherId: ''
})

const assignRules = {
  teacherId: [
    { required: true, message: '请选择教师', trigger: 'change' }
  ]
}

onMounted(() => {
  loadCourses()
  loadFilterOptions()
  loadTeachers()
})

const loadCourses = async () => {
  loading.value = true
  try {
    const params = {}
    // 只传递非空的过滤参数
    if (searchForm.courseName) params.courseName = searchForm.courseName
    if (searchForm.subject) params.subject = searchForm.subject
    if (searchForm.teacherName) params.teacherName = searchForm.teacherName
    
    const res = await getCourses(params)
    courses.value = res.data
  } catch (error) {
    console.error('Failed to load courses:', error)
    ElMessage.error('获取课程列表失败')
  } finally {
    loading.value = false
  }
}

const loadFilterOptions = async () => {
  try {
    // 获取所有课程数据来构建过滤选项
    const res = await getCourses()
    const allCourses = res.data
    
    // 提取唯一的学科选项
    const subjects = [...new Set(allCourses.map(course => course.subject).filter(Boolean))]
    subjectOptions.value = subjects.sort()
    
    // 提取唯一的教师选项（只显示已分配课程的教师）
    const teachers = [...new Set(allCourses.map(course => course.teacherName).filter(Boolean))]
    teacherOptions.value = teachers.sort()
  } catch (error) {
    console.error('Failed to load filter options:', error)
  }
}

const loadTeachers = async () => {
  try {
    const res = await getTeachers()
    allTeachers.value = res.data.records || res.data
  } catch (error) {
    console.error('Failed to load teachers:', error)
  }
}

const saveCourse = async () => {
  if (!courseFormRef.value) return
  
  await courseFormRef.value.validate(async (valid) => {
    if (valid) {
      // 检查课程编码是否重复
      if (codeValidationStatus.value === 'error') {
        ElMessage.error('课程编码已存在，请修改后再保存')
        return
      }
      
      saving.value = true
      try {
        await createCourse(courseForm)
        ElMessage.success('课程创建成功')
        showCreateDialog.value = false
        resetCourseForm()
        loadCourses()
        loadFilterOptions()
      } catch (error) {
        console.error('Failed to create course:', error)
        if (error.response && error.response.data && error.response.data.message) {
          ElMessage.error(error.response.data.message)
        } else {
          ElMessage.error('创建课程失败')
        }
      } finally {
        saving.value = false
      }
    }
  })
}

const resetCourseForm = () => {
  courseForm.courseName = ''
  courseForm.courseCode = ''
  courseForm.subject = ''
  courseForm.teacherId = ''
  courseForm.description = ''
  resetValidation()
}

const resetSearch = () => {
  searchForm.courseName = ''
  searchForm.subject = ''
  searchForm.teacherName = ''
  loadCourses()
}

const viewCourseDetails = (course) => {
  selectedCourse.value = course
  showDetailsDialog.value = true
}

const handleDeleteCourse = (course) => {
  ElMessageBox.confirm(
    `确认删除课程"${course.courseName}"吗？此操作不可恢复`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteCourse(course.id)
      ElMessage.success('课程删除成功')
      loadCourses()
    } catch (error) {
      console.error('Failed to delete course:', error)
      ElMessage.error('删除课程失败')
    }
  })
}

const handleStatusChange = async (id, status) => {
  try {
    await changeCourseStatus(id, { status })
    ElMessage.success('课程状态修改成功')
  } catch (error) {
    // 如果失败，回滚状态
    const course = courses.value.find(c => c.id === id)
    if (course) {
      course.status = status === 1 ? 0 : 1
    }
    ElMessage.error('课程状态修改失败')
  }
}

// 教师分配相关方法
const showAssignTeacherDialog = (course) => {
  assignCourse.value = course
  assignForm.courseId = course.id
  assignForm.teacherId = course.teacherId || ''
  showAssignDialog.value = true
}

const saveAssignment = async () => {
  if (!assignFormRef.value) return
  
  await assignFormRef.value.validate(async (valid) => {
    if (valid) {
      assigning.value = true
      try {
        await assignTeacherToCourse({
          courseId: assignForm.courseId,
          teacherId: assignForm.teacherId
        })
        ElMessage.success('教师分配成功')
        showAssignDialog.value = false
        resetAssignForm()
        loadCourses()
        loadFilterOptions()
      } catch (error) {
        console.error('Failed to assign teacher:', error)
        ElMessage.error('教师分配失败')
      } finally {
        assigning.value = false
      }
    }
  })
}

const resetAssignForm = () => {
  assignForm.courseId = ''
  assignForm.teacherId = ''
  assignCourse.value = null
}

// 验证课程编码是否重复
const validateCourseCode = async () => {
  const courseCode = courseForm.courseCode.trim()
  
  // 清空之前的验证信息
  codeValidationMessage.value = ''
  codeValidationStatus.value = ''
  
  if (!courseCode) {
    return
  }
  
  codeValidating.value = true
  
  try {
    const res = await checkCourseCodeExists(courseCode)
    const exists = res.data
    
    if (exists) {
      codeValidationMessage.value = '课程编码已存在，请修改'
      codeValidationStatus.value = 'error'
    } else {
      codeValidationMessage.value = '课程编码可用'
      codeValidationStatus.value = 'success'
    }
  } catch (error) {
    console.error('Failed to check course code:', error)
    codeValidationMessage.value = '验证课程编码失败'
    codeValidationStatus.value = 'error'
  } finally {
    codeValidating.value = false
  }
}

// 重置课程表单时清空验证信息
const resetValidation = () => {
  codeValidationMessage.value = ''
  codeValidationStatus.value = ''
  codeValidating.value = false
}
</script>

<style lang="scss" scoped>
.admin-courses-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    .header-left {
      h2 {
        margin: 0;
        font-size: 24px;
      }

      p {
        margin: 8px 0 0;
        color: #666;
      }
    }
  }

  .course-details {
    .detail-item {
      margin-bottom: 16px;

      .label {
        font-weight: bold;
        margin-right: 8px;
      }

      .description {
        margin: 8px 0;
        white-space: pre-wrap;
      }
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .status-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-tag {
      margin-left: 8px;
    }
  }

  .assign-content {
    .course-info {
      background-color: #f5f7fa;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 16px;
      }
      
      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
        
        strong {
          color: #303133;
        }
      }
    }
  }

  .validation-message {
    margin-top: 5px;
    font-size: 12px;
    line-height: 1;
    
    &.success {
      color: #67c23a;
    }
    
    &.error {
      color: #f56c6c;
    }
  }
}
</style>
