<template>
  <div class="admin-users">
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统中的所有用户账号</p>
    </div>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input 
            v-model="searchForm.username" 
            placeholder="请输入用户名" 
            clearable
            @keyup.enter="loadUsers"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input 
            v-model="searchForm.realName" 
            placeholder="请输入真实姓名" 
            clearable
            @keyup.enter="loadUsers"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="选择角色" style="width: 120px">
            <el-option label="全部角色" value="" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="教师" value="TEACHER" />
            <el-option label="学生" value="STUDENT" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="正常" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadUsers">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <el-table :data="users" v-loading="loading" stripe>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleUserStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="160" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewUser(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editUser(row)">编辑</el-button>
            <el-dropdown @command="(command) => handleUserAction(command, row)">
              <el-button size="small" type="info">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
                  <el-dropdown-item command="viewRoles">查看角色</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除用户</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadUsers"
          @current-change="loadUsers"
        />
      </div>
    </el-card>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingUser ? '编辑用户' : '新增用户'"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="userForm.username" 
            placeholder="请输入用户名"
            :disabled="!!editingUser"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!editingUser">
          <el-input 
            v-model="userForm.password" 
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="选择角色" style="width: 100%">
            <el-option label="管理员" value="ADMIN" />
            <el-option label="教师" value="TEACHER" />
            <el-option label="学生" value="STUDENT" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          {{ saving ? '保存中...' : '保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="showViewDialog" title="用户详情" width="600px">
      <div v-if="viewingUser">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ viewingUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ viewingUser.username }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ viewingUser.realName }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ viewingUser.email }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ viewingUser.phone }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="getRoleTagType(viewingUser.role)">
              {{ getRoleText(viewingUser.role) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewingUser.status === 1 ? 'success' : 'danger'">
              {{ viewingUser.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ viewingUser.createTime }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ viewingUser.lastLoginTime || '从未登录' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog v-model="showResetPasswordDialog" title="重置密码" width="400px">
      <el-form :model="resetPasswordForm" label-width="80px">
        <el-form-item label="新密码">
          <el-input 
            v-model="resetPasswordForm.newPassword" 
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showResetPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="resetUserPassword">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户角色对话框 -->
    <el-dialog v-model="showRolesDialog" title="用户角色" width="400px">
      <div v-if="userRoles.length > 0">
        <el-tag 
          v-for="role in userRoles" 
          :key="role.id"
          :type="getRoleTagType(role.roleCode)"
          class="role-tag"
        >
          {{ role.roleName }}
        </el-tag>
      </div>
      <div v-else>
        <el-empty description="该用户暂无角色" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getUsers, 
  getUser, 
  createUser, 
  updateUser, 
  deleteUser,
  changeUserStatus as updateUserStatus,
  resetPassword,
  // getUserRoles - 暂时注释，API中没有这个函数
} from '@/api/admin'

const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const showViewDialog = ref(false)
const showResetPasswordDialog = ref(false)
const showRolesDialog = ref(false)
const editingUser = ref(null)
const viewingUser = ref(null)
const userFormRef = ref()

const users = ref([])
const userRoles = ref([])

const searchForm = reactive({
  username: '',
  realName: '',
  role: '',
  status: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const userForm = reactive({
  username: '',
  password: '',
  realName: '',
  email: '',
  phone: '',
  role: ''
})

const resetPasswordForm = reactive({
  newPassword: ''
})

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await getUsers(params)
    users.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('Failed to load users:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.username = ''
  searchForm.realName = ''
  searchForm.role = ''
  searchForm.status = ''
  pagination.current = 1
  loadUsers()
}

const viewUser = async (user) => {
  try {
    const response = await getUser(user.id)
    viewingUser.value = response.data
    showViewDialog.value = true
  } catch (error) {
    console.error('Failed to load user details:', error)
    ElMessage.error('加载用户详情失败')
  }
}

const editUser = (user) => {
  editingUser.value = user
  Object.assign(userForm, {
    username: user.username,
    realName: user.realName,
    email: user.email,
    phone: user.phone,
    role: user.role
  })
  showCreateDialog.value = true
}

const saveUser = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true
      try {
        if (editingUser.value) {
          await updateUser(editingUser.value.id, userForm)
          ElMessage.success('用户更新成功')
        } else {
          await createUser(userForm)
          ElMessage.success('用户创建成功')
        }
        showCreateDialog.value = false
        resetForm()
        loadUsers()
      } catch (error) {
        console.error('Failed to save user:', error)
        ElMessage.error('保存用户失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const handleUserStatusChange = async (user) => {
  try {
    await updateUserStatus(user.id, { status: user.status })
    ElMessage.success(`用户${user.status === 1 ? '启用' : '禁用'}成功`)
  } catch (error) {
    console.error('Failed to change user status:', error)
    ElMessage.error('修改用户状态失败')
    // 恢复原状态
    user.status = user.status === 1 ? 0 : 1
  }
}

const handleUserAction = (command, user) => {
  switch (command) {
    case 'resetPassword':
      resetPasswordForm.newPassword = ''
      editingUser.value = user
      showResetPasswordDialog.value = true
      break
    case 'viewRoles':
      loadUserRoles(user.id)
      break
    case 'delete':
      deleteUserConfirm(user)
      break
  }
}

const resetUserPassword = async () => {
  if (!resetPasswordForm.newPassword) {
    ElMessage.warning('请输入新密码')
    return
  }
  
  try {
    await resetPassword(editingUser.value.id, resetPasswordForm.newPassword)
    ElMessage.success('密码重置成功')
    showResetPasswordDialog.value = false
  } catch (error) {
    console.error('Failed to reset password:', error)
    ElMessage.error('重置密码失败')
  }
}

const loadUserRoles = async (userId) => {
  try {
    // 暂时模拟数据，因为API中没有getUserRoles函数
    userRoles.value = [{ id: 1, roleName: '默认角色', roleCode: 'STUDENT' }]
    showRolesDialog.value = true
  } catch (error) {
    console.error('Failed to load user roles:', error)
    ElMessage.error('加载用户角色失败')
  }
}

const deleteUserConfirm = (user) => {
  ElMessageBox.confirm(
    `确认删除用户 "${user.realName}" 吗？此操作不可恢复！`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteUser(user.id)
      ElMessage.success('用户删除成功')
      loadUsers()
    } catch (error) {
      console.error('Failed to delete user:', error)
      ElMessage.error('删除用户失败')
    }
  })
}

const getRoleTagType = (role) => {
  const types = {
    'ADMIN': 'danger',
    'TEACHER': 'warning',
    'STUDENT': 'success'
  }
  return types[role] || ''
}

const getRoleText = (role) => {
  const texts = {
    'ADMIN': '管理员',
    'TEACHER': '教师',
    'STUDENT': '学生'
  }
  return texts[role] || role
}

const resetForm = () => {
  editingUser.value = null
  Object.assign(userForm, {
    username: '',
    password: '',
    realName: '',
    email: '',
    phone: '',
    role: ''
  })
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.admin-users {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.role-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>