<template>
  <div class="system-settings">
    <div class="page-header">
      <h2>系统设置</h2>
      <p>管理系统的各项配置参数</p>
    </div>

    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>验证码设置</span>
        </div>
      </template>

      <div class="settings-content">
        <el-form :model="captchaSettings" label-width="120px">
          <el-form-item label="验证码开关">
            <el-switch
              v-model="captchaSettings.enabled"
              @change="toggleCaptcha"
              :loading="switchLoading"
              active-text="开启"
              inactive-text="关闭"
            />
            <div class="setting-desc">
              开启后，用户登录时需要输入验证码
            </div>
          </el-form-item>

          <el-form-item label="验证码长度" v-if="captchaSettings.enabled">
            <el-input-number
              v-model="captchaSettings.length"
              :min="3"
              :max="6"
              @change="updateCaptchaLength"
              :disabled="lengthLoading"
            />
            <div class="setting-desc">
              验证码字符长度，建议3-6位
            </div>
          </el-form-item>

          <el-form-item label="过期时间" v-if="captchaSettings.enabled">
            <el-input-number
              v-model="captchaSettings.expireMinutes"
              :min="1"
              :max="60"
              @change="updateExpireMinutes"
              :disabled="expireLoading"
            />
            <span class="unit">分钟</span>
            <div class="setting-desc">
              验证码的有效期，超时后需要重新获取
            </div>
          </el-form-item>
        </el-form>

        <!-- 验证码预览 -->
        <div v-if="captchaSettings.enabled" class="captcha-preview">
          <h4>验证码预览</h4>
          <div class="preview-container">
            <div class="captcha-demo">
              <el-input
                placeholder="请输入验证码"
                style="width: 200px; margin-right: 10px;"
                disabled
              />
              <div class="captcha-image-demo" @click="refreshPreview">
                <img v-if="previewImage" :src="previewImage" alt="验证码预览" />
                <div v-else class="loading-text">点击生成预览</div>
              </div>
            </div>
            <el-button @click="refreshPreview" :loading="previewLoading">
              刷新预览
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="settings-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>登录页设置</span>
        </div>
      </template>
      <div class="settings-content">
        <el-form label-width="160px">
          <el-form-item label="显示演示账号">
            <el-switch v-model="loginSettings.showDemoAccounts" :loading="loginSwitchLoading" @change="toggleShowDemoAccounts" active-text="显示" inactive-text="隐藏"/>
            <div class="setting-desc">控制登录页面下方是否展示演示账号快捷填充区</div>
          </el-form-item>
        </el-form>
        <el-form-item label="演示账号列表">
          <el-input
            type="textarea"
            v-model="demoAccountsText"
            :rows="5"
            placeholder='请输入JSON数组，如: [{"label":"管理员","username":"admin","password":"admin123"}]'
          />
          <div class="setting-desc">JSON数组，元素包含 label/username/password，用于登录页演示账号显示与一键填充</div>
          <el-button type="primary" style="margin-top: 8px" :loading="demoSaveLoading" @click="saveDemoAccounts">保存</el-button>
        </el-form-item>

      </div>
    </el-card>


    <!-- 其他系统设置可以在这里添加 -->
    <el-card class="settings-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>其他设置</span>
        </div>
      </template>
      <div class="settings-content">
        <el-empty description="更多系统设置功能开发中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import  { getLoginPageSettings, toggleLoginDemoAccounts, getLoginDemoAccounts, saveLoginDemoAccounts } from '@/api/admin-login'


const demoAccountsText = ref('')
const demoSaveLoading = ref(false)

const loadDemoAccounts = async () => {
  try {
    const res = await getLoginDemoAccounts()
    demoAccountsText.value = JSON.stringify(res.data, null, 2)
  } catch (e) {
    console.error('加载演示账号列表失败:', e)
  }
}

const saveDemoAccounts = async () => {
  demoSaveLoading.value = true
  try {
    // 校验 JSON
    const parsed = JSON.parse(demoAccountsText.value || '[]')
    if (!Array.isArray(parsed)) throw new Error('必须为JSON数组')
    await saveLoginDemoAccounts(JSON.stringify(parsed))
    ElMessage.success('演示账号列表已保存')
  } catch (e) {
    console.error('保存演示账号失败:', e)
    ElMessage.error('保存失败：' + (e.message || ''))
  } finally {
    demoSaveLoading.value = false
  }
}



const loginSettings = ref({ showDemoAccounts: true })
const loginSwitchLoading = ref(false)

const loadLoginSettings = async () => {
  try {
    const res = await getLoginPageSettings()
    loginSettings.value.showDemoAccounts = !!res.data?.showDemoAccounts
  } catch (e) {
    console.error('加载登录页设置失败:', e)
  }
}

const toggleShowDemoAccounts = async (enabled) => {
  loginSwitchLoading.value = true
  try {
    await toggleLoginDemoAccounts(enabled)
    ElMessage.success(enabled ? '已显示演示账号' : '已隐藏演示账号')
  } catch (e) {
    console.error('切换演示账号开关失败:', e)
    ElMessage.error('操作失败')
    loginSettings.value.showDemoAccounts = !enabled
  } finally {
    loginSwitchLoading.value = false
  }
}

import {
  getCaptchaSettings,
  toggleCaptchaEnabled,
  updateCaptchaLength as updateLengthApi,
  updateCaptchaExpireMinutes as updateExpireApi
} from '@/api/admin'
import { getCaptcha } from '@/api/captcha'

const captchaSettings = ref({
  enabled: false,
  length: 4,
  expireMinutes: 5
})

const switchLoading = ref(false)
const lengthLoading = ref(false)
const expireLoading = ref(false)
const previewLoading = ref(false)
const previewImage = ref('')

// 加载设置
const loadSettings = async () => {
  try {
    const response = await getCaptchaSettings()
    captchaSettings.value = response.data

    if (captchaSettings.value.enabled) {
      refreshPreview()
    }
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.error('加载设置失败')
  }
}

// 切换验证码开关
const toggleCaptcha = async (enabled) => {
  switchLoading.value = true
  try {
    await toggleCaptchaEnabled(enabled)
    ElMessage.success(enabled ? '验证码功能已开启' : '验证码功能已关闭')

    if (enabled) {
      refreshPreview()
    } else {
      previewImage.value = ''
    }
  } catch (error) {
    console.error('切换验证码开关失败:', error)
    ElMessage.error('操作失败')
    // 恢复开关状态
    captchaSettings.value.enabled = !enabled
  } finally {
    switchLoading.value = false
  }
}

// 更新验证码长度
const updateCaptchaLength = async (length) => {
  if (!length || length < 3 || length > 6) return

  lengthLoading.value = true
  try {
    await updateLengthApi(length)
    ElMessage.success('验证码长度已更新')
    refreshPreview()
  } catch (error) {
    console.error('更新验证码长度失败:', error)
    ElMessage.error('更新失败')
  } finally {
    lengthLoading.value = false
  }
}

// 更新过期时间
const updateExpireMinutes = async (minutes) => {
  if (!minutes || minutes < 1) return

  expireLoading.value = true
  try {
    await updateExpireApi(minutes)
    ElMessage.success('验证码过期时间已更新')
  } catch (error) {
    console.error('更新过期时间失败:', error)
    ElMessage.error('更新失败')
  } finally {
    expireLoading.value = false
  }
}

// 刷新验证码预览
const refreshPreview = async () => {
  if (!captchaSettings.value.enabled) return

  previewLoading.value = true
  try {
    // 生成一个临时的sessionId用于预览
    const previewSessionId = 'preview_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    const response = await getCaptcha(previewSessionId)
    previewImage.value = response.data.image
  } catch (error) {
    console.error('生成验证码预览失败:', error)
    ElMessage.error('生成预览失败')
  } finally {
    previewLoading.value = false
  }
}

onMounted(() => {
  loadSettings()
  loadLoginSettings()
  loadDemoAccounts()
})
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.settings-content {
  padding: 20px 0;
}

.setting-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.unit {
  margin-left: 8px;
  color: #606266;
  font-size: 14px;
}

.captcha-preview {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.captcha-preview h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.preview-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.captcha-demo {
  display: flex;
  align-items: center;
}

.captcha-image-demo {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.captcha-image-demo:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.captcha-image-demo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 3px;
}

.loading-text {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.el-form-item {
  margin-bottom: 25px;
}

.el-switch {
  margin-right: 10px;
}
</style>