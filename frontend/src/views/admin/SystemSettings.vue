<template>
  <div class="system-settings">
    <div class="page-header">
      <h2>系统设置</h2>
      <p>管理系统的各项配置参数</p>
    </div>

    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>验证码设置</span>
        </div>
      </template>

      <div class="settings-content">
        <el-form :model="captchaSettings" label-width="120px">
          <el-form-item label="验证码开关">
            <el-switch
              v-model="captchaSettings.enabled"
              @change="toggleCaptcha"
              :loading="switchLoading"
              active-text="开启"
              inactive-text="关闭"
            />
            <div class="setting-desc">
              开启后，用户登录时需要输入验证码
            </div>
          </el-form-item>

          <el-form-item label="验证码长度" v-if="captchaSettings.enabled">
            <el-input-number
              v-model="captchaSettings.length"
              :min="3"
              :max="6"
              @change="updateCaptchaLength"
              :disabled="lengthLoading"
            />
            <div class="setting-desc">
              验证码字符长度，建议3-6位
            </div>
          </el-form-item>

          <el-form-item label="过期时间" v-if="captchaSettings.enabled">
            <el-input-number
              v-model="captchaSettings.expireMinutes"
              :min="1"
              :max="60"
              @change="updateExpireMinutes"
              :disabled="expireLoading"
            />
            <span class="unit">分钟</span>
            <div class="setting-desc">
              验证码的有效期，超时后需要重新获取
            </div>
          </el-form-item>
        </el-form>

        <!-- 验证码预览 -->
        <div v-if="captchaSettings.enabled" class="captcha-preview">
          <h4>验证码预览</h4>
          <div class="preview-container">
            <div class="captcha-demo">
              <el-input
                placeholder="请输入验证码"
                style="width: 200px; margin-right: 10px;"
                disabled
              />
              <div class="captcha-image-demo" @click="refreshPreview">
                <img v-if="previewImage" :src="previewImage" alt="验证码预览" />
                <div v-else class="loading-text">点击生成预览</div>
              </div>
            </div>
            <el-button @click="refreshPreview" :loading="previewLoading">
              刷新预览
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="settings-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>登录页设置</span>
        </div>
      </template>
      <div class="settings-content">
        <el-form label-width="160px">
          <el-form-item label="显示演示账号">
            <el-switch v-model="loginSettings.showDemoAccounts" :loading="loginSwitchLoading" @change="toggleShowDemoAccounts" active-text="显示" inactive-text="隐藏"/>
            <div class="setting-desc">控制登录页面下方是否展示演示账号快捷填充区</div>
          </el-form-item>
        </el-form>
        <el-form-item label="演示账号管理">
          <div class="demo-accounts-management">
            <div class="management-header">
              <el-button type="primary" @click="showAddDialog = true" icon="Plus">添加账号</el-button>
              <el-button type="success" @click="initDemoAccountsHandler" :loading="initLoading" icon="Download">初始化默认账号</el-button>
              <el-button type="info" @click="saveDemoAccounts" :loading="demoSaveLoading" icon="Check">保存配置</el-button>
            </div>

            <el-table :data="demoAccountsList" style="width: 100%; margin-top: 16px;" border>
              <el-table-column prop="label" label="用户类型" width="120">
                <template #default="scope">
                  <el-select v-model="scope.row.label" placeholder="选择类型" size="small" @change="updateTagType(scope.row)">
                    <el-option label="管理员" value="管理员" />
                    <el-option label="教师" value="教师" />
                    <el-option label="学生" value="学生" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="username" label="用户名" width="150">
                <template #default="scope">
                  <el-input v-model="scope.row.username" placeholder="登录用户名" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="password" label="密码" width="150">
                <template #default="scope">
                  <el-input v-model="scope.row.password" placeholder="登录密码" size="small" show-password />
                </template>
              </el-table-column>
              <el-table-column prop="tagType" label="标签类型" width="120">
                <template #default="scope">
                  <el-select v-model="scope.row.tagType" placeholder="选择类型" size="small">
                    <el-option label="默认" value="info">
                      <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span>默认</span>
                        <el-tag type="info" size="small">示例</el-tag>
                      </div>
                    </el-option>
                    <el-option label="成功" value="success">
                      <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span>成功</span>
                        <el-tag type="success" size="small">示例</el-tag>
                      </div>
                    </el-option>
                    <el-option label="警告" value="warning">
                      <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span>警告</span>
                        <el-tag type="warning" size="small">示例</el-tag>
                      </div>
                    </el-option>
                    <el-option label="危险" value="danger">
                      <div style="display: flex; align-items: center; justify-content: space-between;">
                        <span>危险</span>
                        <el-tag type="danger" size="small">示例</el-tag>
                      </div>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="预览" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.tagType || 'info'" size="small">{{ scope.row.label || '未命名' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button type="danger" size="small" @click="removeDemoAccount(scope.$index)" icon="Delete" />
                </template>
              </el-table-column>
            </el-table>

            <div class="setting-desc" style="margin-top: 12px;">
              管理登录页面显示的演示账号，用户可以一键填充这些账号信息进行快速登录
            </div>
          </div>
        </el-form-item>

      </div>
    </el-card>


    <!-- 其他系统设置可以在这里添加 -->
    <el-card class="settings-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>其他设置</span>
        </div>
      </template>
      <div class="settings-content">
        <el-empty description="更多系统设置功能开发中..." />
      </div>
    </el-card>

    <!-- 添加演示账号对话框 -->
    <el-dialog v-model="showAddDialog" title="添加演示账号" width="500px">
      <el-form :model="newDemoAccount" label-width="80px">
        <el-form-item label="用户类型" required>
          <el-select v-model="newDemoAccount.label" placeholder="选择用户类型" style="width: 100%" @change="updateNewAccountTagType">
            <el-option label="管理员" value="管理员" />
            <el-option label="教师" value="教师" />
            <el-option label="学生" value="学生" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户名" required>
          <el-input v-model="newDemoAccount.username" placeholder="登录用户名" />
        </el-form-item>
        <el-form-item label="密码" required>
          <el-input v-model="newDemoAccount.password" placeholder="登录密码" show-password />
        </el-form-item>
        <el-form-item label="标签类型">
          <div style="display: flex; align-items: center; gap: 12px;">
            <el-select v-model="newDemoAccount.tagType" placeholder="选择标签类型" style="flex: 1;">
              <el-option label="默认" value="info">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span>默认</span>
                  <el-tag type="info" size="small">示例</el-tag>
                </div>
              </el-option>
              <el-option label="成功" value="success">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span>成功</span>
                  <el-tag type="success" size="small">示例</el-tag>
                </div>
              </el-option>
              <el-option label="警告" value="warning">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span>警告</span>
                  <el-tag type="warning" size="small">示例</el-tag>
                </div>
              </el-option>
              <el-option label="危险" value="danger">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span>危险</span>
                  <el-tag type="danger" size="small">示例</el-tag>
                </div>
              </el-option>
            </el-select>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="font-size: 12px; color: #666;">预览：</span>
              <el-tag :type="newDemoAccount.tagType || 'info'" size="small">
                {{ newDemoAccount.label || '用户类型' }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="addDemoAccount">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import  { getLoginPageSettings, toggleLoginDemoAccounts, getLoginDemoAccounts, saveLoginDemoAccounts, initDemoAccounts } from '@/api/admin-login'

// 演示账号管理
const demoAccountsList = ref([])
const demoSaveLoading = ref(false)
const initLoading = ref(false)
const showAddDialog = ref(false)
const newDemoAccount = ref({
  label: '',
  username: '',
  password: '',
  tagType: 'info'
})

const loadDemoAccounts = async () => {
  try {
    const res = await getLoginDemoAccounts()
    if (Array.isArray(res.data)) {
      demoAccountsList.value = res.data
    } else {
      demoAccountsList.value = []
    }
  } catch (e) {
    console.error('加载演示账号列表失败:', e)
    demoAccountsList.value = []
  }
}

const saveDemoAccounts = async () => {
  demoSaveLoading.value = true
  try {
    // 验证数据
    for (let i = 0; i < demoAccountsList.value.length; i++) {
      const account = demoAccountsList.value[i]
      if (!account.label || !account.username || !account.password) {
        ElMessage.error(`第${i + 1}行数据不完整，请填写完整信息`)
        return
      }
    }

    await saveLoginDemoAccounts(JSON.stringify(demoAccountsList.value))
    ElMessage.success('演示账号配置已保存')
  } catch (e) {
    console.error('保存演示账号失败:', e)
    ElMessage.error('保存失败：' + (e.message || ''))
  } finally {
    demoSaveLoading.value = false
  }
}

// 初始化演示账号
const initDemoAccountsHandler = async () => {
  initLoading.value = true
  try {
    await initDemoAccounts()
    ElMessage.success('演示账号初始化成功')
    await loadDemoAccounts() // 重新加载列表
  } catch (e) {
    console.error('初始化演示账号失败:', e)
    ElMessage.error('初始化失败：' + (e.message || ''))
  } finally {
    initLoading.value = false
  }
}

// 根据用户类型自动设置标签颜色
const getTagTypeByLabel = (label) => {
  switch (label) {
    case '管理员':
      return 'danger'
    case '教师':
      return 'warning'
    case '学生':
      return 'success'
    default:
      return 'info'
  }
}

// 更新标签类型
const updateTagType = (row) => {
  row.tagType = getTagTypeByLabel(row.label)
}

// 更新新账号的标签类型
const updateNewAccountTagType = () => {
  newDemoAccount.value.tagType = getTagTypeByLabel(newDemoAccount.value.label)
}

// 更新新账号的标签类型
const updateNewAccountTagType = () => {
  newDemoAccount.value.tagType = getTagTypeByLabel(newDemoAccount.value.label)
}

// 添加演示账号
const addDemoAccount = () => {
  if (!newDemoAccount.value.label || !newDemoAccount.value.username || !newDemoAccount.value.password) {
    ElMessage.error('请填写完整的账号信息')
    return
  }

  // 检查用户名是否重复
  if (demoAccountsList.value.some(account => account.username === newDemoAccount.value.username)) {
    ElMessage.error('用户名已存在')
    return
  }

  // 自动设置标签类型
  if (!newDemoAccount.value.tagType) {
    newDemoAccount.value.tagType = getTagTypeByLabel(newDemoAccount.value.label)
  }

  demoAccountsList.value.push({ ...newDemoAccount.value })

  // 重置表单
  newDemoAccount.value = {
    label: '',
    username: '',
    password: '',
    tagType: 'info'
  }
  showAddDialog.value = false
  ElMessage.success('账号已添加')
}

// 删除演示账号
const removeDemoAccount = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个演示账号吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    demoAccountsList.value.splice(index, 1)
    ElMessage.success('账号已删除')
  } catch {
    // 用户取消删除
  }
}



const loginSettings = ref({ showDemoAccounts: true })
const loginSwitchLoading = ref(false)

const loadLoginSettings = async () => {
  try {
    const res = await getLoginPageSettings()
    loginSettings.value.showDemoAccounts = !!res.data?.showDemoAccounts
  } catch (e) {
    console.error('加载登录页设置失败:', e)
  }
}

const toggleShowDemoAccounts = async (enabled) => {
  loginSwitchLoading.value = true
  try {
    await toggleLoginDemoAccounts(enabled)
    ElMessage.success(enabled ? '已显示演示账号' : '已隐藏演示账号')
  } catch (e) {
    console.error('切换演示账号开关失败:', e)
    ElMessage.error('操作失败')
    loginSettings.value.showDemoAccounts = !enabled
  } finally {
    loginSwitchLoading.value = false
  }
}

import {
  getCaptchaSettings,
  toggleCaptchaEnabled,
  updateCaptchaLength as updateLengthApi,
  updateCaptchaExpireMinutes as updateExpireApi
} from '@/api/admin'
import { getCaptcha } from '@/api/captcha'

const captchaSettings = ref({
  enabled: false,
  length: 4,
  expireMinutes: 5
})

const switchLoading = ref(false)
const lengthLoading = ref(false)
const expireLoading = ref(false)
const previewLoading = ref(false)
const previewImage = ref('')

// 加载设置
const loadSettings = async () => {
  try {
    const response = await getCaptchaSettings()
    captchaSettings.value = response.data

    if (captchaSettings.value.enabled) {
      refreshPreview()
    }
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.error('加载设置失败')
  }
}

// 切换验证码开关
const toggleCaptcha = async (enabled) => {
  switchLoading.value = true
  try {
    await toggleCaptchaEnabled(enabled)
    ElMessage.success(enabled ? '验证码功能已开启' : '验证码功能已关闭')

    if (enabled) {
      refreshPreview()
    } else {
      previewImage.value = ''
    }
  } catch (error) {
    console.error('切换验证码开关失败:', error)
    ElMessage.error('操作失败')
    // 恢复开关状态
    captchaSettings.value.enabled = !enabled
  } finally {
    switchLoading.value = false
  }
}

// 更新验证码长度
const updateCaptchaLength = async (length) => {
  if (!length || length < 3 || length > 6) return

  lengthLoading.value = true
  try {
    await updateLengthApi(length)
    ElMessage.success('验证码长度已更新')
    refreshPreview()
  } catch (error) {
    console.error('更新验证码长度失败:', error)
    ElMessage.error('更新失败')
  } finally {
    lengthLoading.value = false
  }
}

// 更新过期时间
const updateExpireMinutes = async (minutes) => {
  if (!minutes || minutes < 1) return

  expireLoading.value = true
  try {
    await updateExpireApi(minutes)
    ElMessage.success('验证码过期时间已更新')
  } catch (error) {
    console.error('更新过期时间失败:', error)
    ElMessage.error('更新失败')
  } finally {
    expireLoading.value = false
  }
}

// 刷新验证码预览
const refreshPreview = async () => {
  if (!captchaSettings.value.enabled) return

  previewLoading.value = true
  try {
    // 生成一个临时的sessionId用于预览
    const previewSessionId = 'preview_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    const response = await getCaptcha(previewSessionId)
    previewImage.value = response.data.image
  } catch (error) {
    console.error('生成验证码预览失败:', error)
    ElMessage.error('生成预览失败')
  } finally {
    previewLoading.value = false
  }
}

onMounted(() => {
  loadSettings()
  loadLoginSettings()
  loadDemoAccounts()
})
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.settings-content {
  padding: 20px 0;
}

.setting-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.unit {
  margin-left: 8px;
  color: #606266;
  font-size: 14px;
}

.captcha-preview {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.captcha-preview h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.preview-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.captcha-demo {
  display: flex;
  align-items: center;
}

.captcha-image-demo {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.captcha-image-demo:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.captcha-image-demo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 3px;
}

.loading-text {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.el-form-item {
  margin-bottom: 25px;
}

.el-switch {
  margin-right: 10px;
}

.demo-accounts-management {
  width: 100%;
}

.management-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.management-header .el-button {
  flex-shrink: 0;
}
</style>