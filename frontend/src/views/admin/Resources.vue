<template>
  <div class="admin-resources">
    <div class="page-header">
      <h2>资源管理</h2>
      <p>管理系统中的教学资源和知识库文件</p>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" class="resource-tabs" @tab-change="handleTabChange">
      <el-tab-pane label="知识库管理" name="knowledge">
        <div class="knowledge-management">
          <!-- 搜索和筛选 -->
          <el-card class="search-card">
            <el-form :model="searchForm" inline>
              <el-form-item label="关键词">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="搜索文件名或内容"
                  clearable
                  @keyup.enter="searchKnowledge"
                />
              </el-form-item>
              <el-form-item label="学科">
                <el-select
                  v-model="searchForm.subject"
                  placeholder="选择学科"
                  style="width: 150px"
                >
                  <el-option label="全部学科" value="" />
                  <el-option label="计算机科学" value="计算机科学" />
                  <el-option label="数学" value="数学" />
                  <el-option label="物理" value="物理" />
                  <el-option label="化学" value="化学" />
                  <el-option label="英语" value="英语" />
                </el-select>
              </el-form-item>
              <el-form-item label="文件类型">
                <el-select
                  v-model="searchForm.fileType"
                  placeholder="选择文件类型"
                  style="width: 150px"
                >
                  <el-option label="全部类型" value="" />
                  <el-option label="PDF" value="pdf" />
                  <el-option label="Word" value="doc" />
                  <el-option label="PowerPoint" value="ppt" />
                  <el-option label="文本" value="txt" />
                  <el-option label="图片" value="image" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchKnowledge">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetSearch">重置</el-button>
                <el-button type="success" @click="showUploadDialog = true">
                  <el-icon><Upload /></el-icon>
                  上传文件
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 知识库文件列表 -->
          <el-card>
            <template #header>
              <div class="card-header">
                <span>知识库文件</span>
                <div class="header-actions">
                  <el-button size="small" @click="loadKnowledgeFiles">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button
                    size="small"
                    type="primary"
                    @click="exportKnowledgeBaseDocx"
                  >
                    <el-icon><Download /></el-icon>
                    导出知识库
                  </el-button>
                </div>
              </div>
            </template>

            <el-table :data="knowledgeFiles" v-loading="loading" stripe>
              <el-table-column type="selection" width="55" />
              <el-table-column prop="fileName" label="文件名" min-width="200">
                <template #default="{ row }">
                  <div class="file-info">
                    <el-icon class="file-icon">
                      <component :is="getFileIcon(row.fileType)" />
                    </el-icon>
                    <span>{{ row.fileName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="subject" label="学科" width="120" />
              <el-table-column prop="courseName" label="关联课程" width="150" />
              <el-table-column prop="fileSize" label="文件大小" width="100">
                <template #default="{ row }">
                  {{ formatFileSize(row.fileSize) }}
                </template>
              </el-table-column>
              <el-table-column prop="tags" label="标签" width="200">
                <template #default="{ row }">
                  <el-tag
                    v-for="tag in (row.tags || '')
                      .split(',')
                      .filter((t) => t.trim())"
                    :key="tag"
                    size="small"
                    class="tag-item"
                  >
                    {{ tag.trim() }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="aiProcessed" label="AI处理" width="100">
                <template #default="{ row }">
                  <el-tag
                    :type="row.aiProcessed ? 'success' : 'info'"
                    size="small"
                  >
                    {{ row.aiProcessed ? "已处理" : "未处理" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="uploadTime" label="上传时间" width="180" />
              <el-table-column
                prop="downloadCount"
                label="下载次数"
                width="100"
              />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <div class="action-buttons">
                    <el-button size="small" @click="viewFileContent(row)">
                      <el-icon><View /></el-icon>
                      查看
                    </el-button>
                    <el-dropdown @command="(command) => handleActionCommand(command, row)">
                      <el-button size="small" type="primary">
                        更多操作
                        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="download">
                            <el-icon><Download /></el-icon>
                            下载文件
                          </el-dropdown-item>
                          <el-dropdown-item command="export">
                            <el-icon><Document /></el-icon>
                            导出文件
                          </el-dropdown-item>
                          <el-dropdown-item command="edit">
                            <el-icon><Edit /></el-icon>
                            编辑信息
                          </el-dropdown-item>
                          <el-dropdown-item command="delete" divided>
                            <el-icon><Delete /></el-icon>
                            <span style="color: #f56c6c;">删除文件</span>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination">
              <el-pagination
                v-model:current-page="pagination.current"
                v-model:page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadKnowledgeFiles"
                @current-change="loadKnowledgeFiles"
              />
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="课程资源" name="courses">
        <div class="course-resources">
          <el-card class="course-selector">
            <el-form inline>
              <el-form-item label="选择课程">
                <el-select
                  v-model="selectedCourseId"
                  placeholder="请选择要查看的课程"
                  @change="loadCourseResources"
                  style="width: 200px"
                >
                  <el-option
                    v-for="course in courses"
                    :key="course.id"
                    :label="course.courseName"
                    :value="course.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="exportCourseResourcesDocx"
                  :disabled="!selectedCourseId"
                >
                  <el-icon><Download /></el-icon>
                  导出课程资源
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <el-card v-if="selectedCourseId">
            <template #header>
              <span>课程资源列表</span>
            </template>
            <el-table
              :data="courseResources"
              v-loading="loadingCourseResources"
            >
              <el-table-column prop="title" label="资源标题" />
              <el-table-column prop="type" label="资源类型">
                <template #default="{ row }">
                  <el-tag :type="getResourceTypeColor(row.type)">
                    {{ getResourceTypeText(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="teacherName" label="授课教师" width="120" />
              <el-table-column prop="createTime" label="创建时间" />
              <el-table-column prop="updateTime" label="更新时间" />
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button size="small" @click="viewResourceContent(row)">
                    查看内容
                  </el-button>
                  <el-button
                    size="small"
                    type="primary"
                    @click="exportSingleResourceContent(row)"
                  >
                    导出
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="资源统计" name="statistics">
        <div class="resource-statistics">
          <el-row :gutter="20">
            <!-- 文件类型统计 -->
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>文件类型分布</span>
                </template>
                <div ref="fileTypeChart" style="height: 300px"></div>
              </el-card>
            </el-col>

            <!-- 使用统计 -->
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>资源使用统计</span>
                </template>
                <div ref="usageChart" style="height: 300px"></div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 详细统计表格 -->
          <el-card class="stats-table">
            <template #header>
              <span>详细统计信息</span>
            </template>
            <el-table :data="statisticsData" stripe>
              <el-table-column prop="subject" label="学科" />
              <el-table-column prop="fileCount" label="文件数量" />
              <el-table-column prop="totalSize" label="总大小">
                <template #default="{ row }">
                  {{ formatFileSize(row.totalSize) }}
                </template>
              </el-table-column>
              <el-table-column prop="downloadCount" label="总下载次数" />
              <el-table-column prop="avgDownload" label="平均下载次数" />
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 文件上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传知识库文件" width="600px">
      <el-form :model="uploadForm" label-width="100px">
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
            drag
            multiple
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word、PowerPoint、文本文件，单个文件不超过 50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="学科">
          <el-select v-model="uploadForm.subject" placeholder="选择学科">
            <el-option label="计算机科学" value="计算机科学" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="英语" value="英语" />
          </el-select>
        </el-form-item>

        <el-form-item label="关联课程">
          <el-select
            v-model="uploadForm.courseId"
            placeholder="选择课程（可选）"
            clearable
          >
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.courseName"
              :value="course.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="标签">
          <el-input
            v-model="uploadForm.tags"
            placeholder="输入标签，用逗号分隔"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="3"
            placeholder="文件描述（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cancelUpload">取消</el-button>
        <el-button type="primary" @click="uploadFiles" :loading="uploading">
          {{ uploading ? "上传中..." : "上传" }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 文件编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑文件信息" width="600px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="文件名">
          <el-input v-model="editForm.fileName" />
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="editForm.subject">
            <el-option label="计算机科学" value="计算机科学" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="英语" value="英语" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联课程">
          <el-select v-model="editForm.courseId" clearable>
            <el-option
              v-for="course in courses"
              :key="course.id"
              :label="course.courseName"
              :value="course.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editForm.tags" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editForm.description" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateFile">保存</el-button>
      </template>
    </el-dialog>

    <!-- 文件内容查看对话框 -->
    <el-dialog v-model="showViewDialog" title="文件详情" width="900px">
      <div v-if="viewingFile" v-loading="loadingFileDetails">
        <el-tabs v-model="activeViewTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="文件名">{{
                viewingFile.fileName
              }}</el-descriptions-item>
              <el-descriptions-item label="文件大小">{{
                formatFileSize(viewingFile.fileSize)
              }}</el-descriptions-item>
              <el-descriptions-item label="学科">{{
                viewingFile.subject
              }}</el-descriptions-item>
              <el-descriptions-item label="关联课程">{{
                viewingFile.courseName || "无"
              }}</el-descriptions-item>
              <el-descriptions-item label="上传时间">{{
                viewingFile.uploadTime
              }}</el-descriptions-item>
              <el-descriptions-item label="下载次数">{{
                viewingFile.downloadCount
              }}</el-descriptions-item>
              <el-descriptions-item label="AI处理状态">
                <el-tag
                  :type="viewingFile.aiProcessed ? 'success' : 'info'"
                  size="small"
                >
                  {{ viewingFile.aiProcessed ? "已处理" : "未处理" }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="标签" :span="2">
                <el-tag
                  v-for="tag in (viewingFile.tags || '')
                    .split(',')
                    .filter((t) => t.trim())"
                  :key="tag"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ viewingFile.description || "无描述" }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 文件内容 -->
          <el-tab-pane label="文件内容" name="content">
            <el-card>
              <div class="file-content">
                <pre v-if="fileContent">{{ fileContent }}</pre>
                <div v-else class="no-content">暂无可显示的文件内容</div>
              </div>
            </el-card>
          </el-tab-pane>

          <!-- AI分析 -->
          <el-tab-pane label="AI分析" name="ai" v-if="viewingFile.aiProcessed">
            <div v-if="aiAnalysisData">
              <el-card class="analysis-card" v-if="aiAnalysisData.summary">
                <template #header>
                  <span>文档摘要</span>
                </template>
                <div class="summary-content">
                  {{ aiAnalysisData.summary }}
                </div>
              </el-card>

              <el-card class="analysis-card" v-if="aiAnalysisData.keyPoints">
                <template #header>
                  <span>关键知识点</span>
                </template>
                <div class="key-points-content">
                  {{ aiAnalysisData.keyPoints }}
                </div>
              </el-card>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-card class="analysis-card" v-if="aiAnalysisData.smartTags">
                    <template #header>
                      <span>智能标签</span>
                    </template>
                    <div class="smart-tags-content">
                      {{ aiAnalysisData.smartTags }}
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="12">
                  <el-card class="analysis-card">
                    <template #header>
                      <span>文档统计</span>
                    </template>
                    <el-descriptions :column="1" size="small">
                      <el-descriptions-item label="难度等级">
                        <el-tag
                          :type="getDifficultyColor(aiAnalysisData.difficulty)"
                        >
                          {{ aiAnalysisData.difficulty || "未知" }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="字符数">{{
                        aiAnalysisData.characterCount || 0
                      }}</el-descriptions-item>
                      <el-descriptions-item label="词数">{{
                        aiAnalysisData.wordCount || 0
                      }}</el-descriptions-item>
                      <el-descriptions-item label="AI模型">{{
                        aiAnalysisData.aiModel || "未知"
                      }}</el-descriptions-item>
                      <el-descriptions-item label="处理耗时"
                        >{{
                          aiAnalysisData.processingTime || 0
                        }}s</el-descriptions-item
                      >
                    </el-descriptions>
                  </el-card>
                </el-col>
              </el-row>

              <!-- 提取文本 -->
              <el-card class="analysis-card" v-if="aiAnalysisData.extractedText">
                <template #header>
                  <div class="card-header">
                    <span>提取的文本内容</span>
                    <el-button size="small" @click="copyExtractedText">
                      <el-icon><CopyDocument /></el-icon>
                      复制文本
                    </el-button>
                  </div>
                </template>
                <div class="extracted-text-content">
                  <pre>{{ aiAnalysisData.extractedText }}</pre>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 资源内容查看对话框 -->
    <el-dialog v-model="showResourceDialog" title="资源内容" width="900px">
      <div v-if="viewingResource">
        <el-descriptions :column="2" border class="resource-info">
          <el-descriptions-item label="标题">{{
            viewingResource.title
          }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{
            getResourceTypeText(viewingResource.type)
          }}</el-descriptions-item>
          <el-descriptions-item label="授课教师">{{
            viewingResource.teacherName
          }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{
            viewingResource.createTime
          }}</el-descriptions-item>
        </el-descriptions>

        <el-card class="content-card">
          <template #header>
            <span>内容详情</span>
          </template>
          <div class="resource-content">
            <pre v-if="viewingResource.content">{{ viewingResource.content }}</pre>
            <div v-else class="no-content">暂无内容</div>
          </div>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.admin-resources {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.resource-tabs {
  margin-top: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.course-selector {
  margin-bottom: 20px;
}

.resource-statistics {
  padding: 20px 0;
}

.stats-table {
  margin-top: 20px;
}

.file-content {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.file-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.no-content {
  text-align: center;
  color: #909399;
  padding: 40px;
}

.analysis-card {
  margin-bottom: 16px;
}

.summary-content,
.key-points-content,
.smart-tags-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
}

.extracted-text-content {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.extracted-text-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.resource-info {
  margin-bottom: 20px;
}

.content-card {
  margin-top: 20px;
}

.resource-content {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.resource-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-resources {
    padding: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";
import * as echarts from "echarts";
import {
  getKnowledgeFiles,
  searchKnowledgeFiles,
  uploadKnowledgeFile,
  updateKnowledgeFile,
  deleteKnowledgeFile,
  downloadKnowledgeFile,
  getFileTypeStatistics,
  getResourceUsageStatistics,
  exportCourseResources,
  exportKnowledgeBase,
  exportSingleResource,
  getCoursesForResources,
  getKnowledgeFileDetails,
  getCourseResources,
} from "@/api/admin";

const activeTab = ref("knowledge");
const activeViewTab = ref("basic");
const loading = ref(false);
const loadingCourseResources = ref(false);
const loadingFileDetails = ref(false);
const uploading = ref(false);
const showUploadDialog = ref(false);
const showEditDialog = ref(false);
const showViewDialog = ref(false);
const showResourceDialog = ref(false);

const uploadRef = ref();
const fileTypeChart = ref();
const usageChart = ref();
let fileTypeChartInstance = null;
let usageChartInstance = null;

const knowledgeFiles = ref([]);
const courses = ref([]);
const courseResources = ref([]);
const statisticsData = ref([]);
const fileList = ref([]);
const viewingFile = ref(null);
const viewingResource = ref(null);
const selectedCourseId = ref("");
const fileContent = ref("");
const aiAnalysisData = ref(null);

const searchForm = reactive({
  keyword: "",
  subject: "",
  fileType: "",
});

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

const uploadForm = reactive({
  subject: "",
  courseId: "",
  tags: "",
  description: "",
});

const editForm = reactive({
  id: "",
  fileName: "",
  subject: "",
  courseId: "",
  tags: "",
  description: "",
});

const loadKnowledgeFiles = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      keyword: searchForm.keyword,
      subject: searchForm.subject,
      fileType: searchForm.fileType,
    };
    
    // 过滤掉空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key];
      }
    });
    
    console.log('Loading knowledge files with params:', params);
    
    const response = await getKnowledgeFiles(params);
    knowledgeFiles.value = response.data.records;
    pagination.total = response.data.total;
  } catch (error) {
    console.error("Failed to load knowledge files:", error);
    ElMessage.error("加载知识库文件失败");
  } finally {
    loading.value = false;
  }
};

const searchKnowledge = async () => {
  // 重置分页到第一页
  pagination.current = 1;
  loadKnowledgeFiles();
};

const resetSearch = () => {
  searchForm.keyword = "";
  searchForm.subject = "";
  searchForm.fileType = "";
  pagination.current = 1;
  loadKnowledgeFiles();
};

const loadCourses = async () => {
  try {
    const response = await getCoursesForResources();
    courses.value = response.data || [];
  } catch (error) {
    console.error("Failed to load courses:", error);
    ElMessage.error("加载课程列表失败");
  }
};

const loadCourseResources = async () => {
  if (!selectedCourseId.value) return;

  loadingCourseResources.value = true;
  try {
    const response = await getCourseResources(selectedCourseId.value);
    courseResources.value = response.data;
  } catch (error) {
    console.error("Failed to load course resources:", error);
    ElMessage.error("加载课程资源失败");
  } finally {
    loadingCourseResources.value = false;
  }
};

const loadStatistics = async () => {
  try {
    const [fileTypeResponse, usageResponse] = await Promise.all([
      getFileTypeStatistics(),
      getResourceUsageStatistics(),
    ]);

    await nextTick();
    
    if (fileTypeResponse.data && Array.isArray(fileTypeResponse.data)) {
      renderFileTypeChart(fileTypeResponse.data);
    }
    
    if (usageResponse.data) {
      renderUsageChart(usageResponse.data);
      statisticsData.value = usageResponse.data.details || [];
    }
  } catch (error) {
    console.error("Failed to load statistics:", error);
    ElMessage.error("加载统计数据失败");
  }
};

const renderFileTypeChart = (data) => {
  if (!fileTypeChartInstance) {
    fileTypeChartInstance = echarts.init(fileTypeChart.value);
  }

  const option = {
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [
      {
        name: "文件类型",
        type: "pie",
        radius: "50%",
        data: data.map((item) => ({
          value: item.count,
          name: item.fileType,
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };

  fileTypeChartInstance.setOption(option);
};

const renderUsageChart = (data) => {
  if (!usageChartInstance) {
    usageChartInstance = echarts.init(usageChart.value);
  }

  const option = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["下载次数", "文件数量"],
    },
    xAxis: {
      type: "category",
      data: data.subjects || [],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "下载次数",
        type: "bar",
        data: data.downloads || [],
      },
      {
        name: "文件数量",
        type: "line",
        data: data.counts || [],
      },
    ],
  };

  usageChartInstance.setOption(option);
};

const handleFileChange = (file, uploadFileList) => {
  fileList.value = uploadFileList;
};

const uploadFiles = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning("请选择要上传的文件");
    return;
  }

  uploading.value = true;
  try {
    for (const file of fileList.value) {
      const formData = new FormData();
      formData.append("file", file.raw);
      formData.append("subject", uploadForm.subject);
      formData.append("courseId", uploadForm.courseId);
      formData.append("tags", uploadForm.tags);
      formData.append("description", uploadForm.description);

      await uploadKnowledgeFile(formData);
    }

    ElMessage.success("文件上传成功");
    showUploadDialog.value = false;
    fileList.value = [];
    resetUploadForm();
    loadKnowledgeFiles();
  } catch (error) {
    console.error("Failed to upload files:", error);
    ElMessage.error("文件上传失败");
  } finally {
    uploading.value = false;
  }
};

const viewFileContent = async (file) => {
  viewingFile.value = file;
  showViewDialog.value = true;
  activeViewTab.value = "basic";
  
  // Load detailed file information
  loadingFileDetails.value = true;
  try {
    const response = await getKnowledgeFileDetails(file.id);
    if (response.data) {
      viewingFile.value = { ...file, ...response.data };
      fileContent.value = response.data.content || "";
      
      if (response.data.aiProcessed) {
        aiAnalysisData.value = {
          summary: response.data.summary,
          keyPoints: response.data.keyPoints,
          smartTags: response.data.smartTags,
          extractedText: response.data.extractedText,
          structuredData: response.data.structuredData,
          difficulty: response.data.difficulty,
          wordCount: response.data.wordCount,
          characterCount: response.data.characterCount,
          aiModel: response.data.aiModel,
          processingTime: response.data.processingTime,
        };
      }
    }
  } catch (error) {
    console.error("Failed to load file details:", error);
    ElMessage.error("加载文件详情失败");
  } finally {
    loadingFileDetails.value = false;
  }
};

const viewResourceContent = (resource) => {
  viewingResource.value = resource;
  showResourceDialog.value = true;
};

const editFile = (file) => {
  Object.assign(editForm, file);
  showEditDialog.value = true;
};

const updateFile = async () => {
  try {
    await updateKnowledgeFile(editForm.id, editForm);
    ElMessage.success("文件信息更新成功");
    showEditDialog.value = false;
    loadKnowledgeFiles();
  } catch (error) {
    console.error("Failed to update file:", error);
    ElMessage.error("更新失败");
  }
};

const deleteFile = (file) => {
  ElMessageBox.confirm("确认删除这个文件吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await deleteKnowledgeFile(file.id);
      ElMessage.success("删除成功");
      loadKnowledgeFiles();
    } catch (error) {
      console.error("Failed to delete file:", error);
      ElMessage.error("删除失败");
    }
  });
};

const handleActionCommand = (command, row) => {
  switch (command) {
    case 'download':
      downloadFile(row);
      break;
    case 'export':
      exportSingleFile(row);
      break;
    case 'edit':
      editFile(row);
      break;
    case 'delete':
      deleteFile(row);
      break;
  }
};

const downloadFile = async (file) => {
  try {
    const response = await downloadKnowledgeFile(file.id);
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = file.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success("文件下载成功");
  } catch (error) {
    console.error("Failed to download file:", error);
    ElMessage.error("下载失败");
  }
};

const exportKnowledgeBaseDocx = async () => {
  try {
    ElMessage.info("正在导出知识库，请稍候...");
    const response = await exportKnowledgeBase();
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `AI知识库整理_${new Date().toISOString().slice(0, 10)}.docx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success("知识库导出成功");
  } catch (error) {
    console.error("Failed to export knowledge base:", error);
    ElMessage.error("导出失败");
  }
};

const exportCourseResourcesDocx = async () => {
  if (!selectedCourseId.value) {
    ElMessage.warning("请先选择课程");
    return;
  }

  try {
    ElMessage.info("正在导出课程资源，请稍候...");
    const response = await exportCourseResources(selectedCourseId.value);
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    
    // Get course name for filename
    const course = courses.value.find(c => c.id === selectedCourseId.value);
    const courseName = course ? course.courseName : "课程";
    link.download = `${courseName}_课程资源_${new Date().toISOString().slice(0, 10)}.docx`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success("课程资源导出成功");
  } catch (error) {
    console.error("Failed to export course resources:", error);
    ElMessage.error("导出失败");
  }
};

const exportSingleFile = async (file) => {
  try {
    ElMessage.info("正在导出文件，请稍候...");
    const response = await exportSingleResource(file.id, "knowledge");
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${file.fileName}_详情.docx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success("文件导出成功");
  } catch (error) {
    console.error("Failed to export file:", error);
    ElMessage.error("导出失败");
  }
};

const exportSingleResourceContent = async (resource) => {
  try {
    ElMessage.info("正在导出资源，请稍候...");
    const response = await exportSingleResource(resource.id, resource.type);
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `${resource.title}_${getResourceTypeText(resource.type)}.docx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success("资源导出成功");
  } catch (error) {
    console.error("Failed to export resource:", error);
    ElMessage.error("导出失败");
  }
};

const cancelUpload = () => {
  showUploadDialog.value = false;
  fileList.value = [];
  resetUploadForm();
};

const resetUploadForm = () => {
  uploadForm.subject = "";
  uploadForm.courseId = "";
  uploadForm.tags = "";
  uploadForm.description = "";
};

const handleTabChange = (tabName) => {
  if (tabName === "statistics") {
    nextTick(() => {
      loadStatistics();
    });
  }
};

const copyExtractedText = () => {
  if (aiAnalysisData.value?.extractedText) {
    navigator.clipboard.writeText(aiAnalysisData.value.extractedText);
    ElMessage.success("文本已复制到剪贴板");
  }
};

const getFileIcon = (fileType) => {
  const iconMap = {
    pdf: "Document",
    doc: "Document",
    docx: "Document",
    ppt: "Document",
    pptx: "Document",
    txt: "Document",
    md: "Document",
    default: "Document",
  };
  return iconMap[fileType] || iconMap.default;
};

const getResourceTypeColor = (type) => {
  const colorMap = {
    knowledge: "primary",
    teaching_content: "success",
    default: "info",
  };
  return colorMap[type] || colorMap.default;
};

const getResourceTypeText = (type) => {
  const textMap = {
    knowledge: "知识库文件",
    teaching_content: "教学内容",
    default: "未知类型",
  };
  return textMap[type] || textMap.default;
};

const getDifficultyColor = (difficulty) => {
  const colorMap = {
    简单: "success",
    中等: "warning",
    困难: "danger",
    default: "info",
  };
  return colorMap[difficulty] || colorMap.default;
};

const formatFileSize = (size) => {
  if (!size || size === 0) return "0 B";
  
  const units = ["B", "KB", "MB", "GB"];
  let unitIndex = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024;
    unitIndex++;
  }
  
  return `${fileSize.toFixed(2)} ${units[unitIndex]}`;
};

onMounted(() => {
  loadKnowledgeFiles();
  loadCourses();
});
</script>
<style scoped>
.admin-resources {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.resource-tabs {
  margin-top: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 4px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.course-selector {
  margin-bottom: 20px;
}

.stats-table {
  margin-top: 20px;
}

.analysis-card {
  margin-bottom: 16px;
}

.summary-content,
.key-points-content,
.smart-tags-content {
  line-height: 1.6;
  color: #606266;
}

.extracted-text-content {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.extracted-text-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.file-content {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.file-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.no-content {
  text-align: center;
  color: #909399;
  padding: 40px;
  font-style: italic;
}

.resource-info {
  margin-bottom: 20px;
}

.content-card {
  margin-top: 16px;
}

.resource-content {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.resource-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.text-gray-500 {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-resources {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>