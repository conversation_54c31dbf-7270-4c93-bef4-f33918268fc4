import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/notifications',
    name: 'NotificationCenter',
    component: () => import('@/views/NotificationCenter.vue'),
    meta: { requiresAuth: true }
  },
  // 教师路由
  {
    path: '/teacher',
    component: () => import('@/layout/TeacherLayout.vue'),
    meta: { requiresAuth: true, roles: ['TEACHER', 'ADMIN'] },
    children: [
      {
        path: '',
        redirect: '/teacher/courses'
      },
      {
        path: 'courses',
        name: 'TeacherCourses',
        component: () => import('@/views/teacher/Courses.vue')
      },
      {
        path: 'courses/:id/content',
        name: 'TeacherCourseContent',
        component: () => import('@/views/teacher/Content.vue')
      },
      {
        path: 'content',
        name: 'TeacherContent',
        component: () => import('@/views/teacher/Content.vue')
      },
      {
        path: 'ai-assistant',
        name: 'TeacherAI',
        component: () => import('@/views/teacher/AIAssistant.vue')
      },
      {
        path: 'grading',
        name: 'TeacherGrading',
        component: () => import('@/views/teacher/Grading.vue')
      }
    ]
  },
  // 学生路由
  {
    path: '/student',
    component: () => import('@/layout/StudentLayout.vue'),
    meta: { requiresAuth: true, roles: ['STUDENT', 'ADMIN'] },
    children: [
      {
        path: '',
        redirect: '/student/courses'
      },
      {
        path: 'courses',
        name: 'StudentCourses',
        component: () => import('@/views/student/Courses.vue')
      },
      {
        path: 'learning',
        name: 'StudentLearning',
        component: () => import('@/views/student/Learning.vue')
      },
      {
        path: 'practice',
        name: 'StudentPractice',
        component: () => import('@/views/student/Practice.vue')
      },
      {
        path: 'records',
        name: 'StudentRecords',
        component: () => import('@/views/student/Records.vue')
      },
      {
        path: 'ai-assistant',
        name: 'StudentAI',
        component: () => import('@/views/student/AIAssistant.vue')
      }
    ]
  },
  // 管理员路由
  {
    path: '/admin',
    component: () => import('@/layout/AdminLayout.vue'),
    meta: { requiresAuth: true, roles: ['ADMIN'] },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue')
      },
      {
        path: 'courses',
        name: 'AdminCourses',
        component: () => import('@/views/admin/Courses.vue')
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/Users.vue')
      },
      {
        path: 'resources',
        name: 'AdminResources',
        component: () => import('@/views/admin/Resources.vue')
      },
      {
        path: 'notifications',
        name: 'AdminNotifications',
        component: () => import('@/views/admin/NotificationManagement.vue')
      },
      {
        path: 'models',
        name: 'AdminModels',
        component: () => import('@/views/admin/ModelManagement.vue')
      },
      {
        path: 'settings',
        name: 'AdminSystemSettings',
        component: () => import('@/views/admin/SystemSettings.vue')
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const userStore = useUserStore()
  const token = userStore.token
  // 如果访问登录页面且已登录，重定向到仪表板
  if (to.path === '/login' && token) {
    next('/dashboard')
    return
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!token) {
      next('/login')
      return
    }
    
    // 检查用户信息
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        console.error('Failed to get user info in route guard:', error)
        userStore.logout()
        next('/login')
        return
      }
    }
    
    // 检查角色权限
    if (to.meta.roles && !to.meta.roles.includes(userStore.userInfo.role)) {
      next('/dashboard')
      return
    }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router