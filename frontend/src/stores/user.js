import { defineStore } from 'pinia'
import { login, register, getUserInfo, updateUserProfile } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    userInfo: null,
    avatarTimestamp: Date.now()
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.userInfo?.role === 'ADMIN',
    isTeacher: (state) => state.userInfo?.role === 'TEACHER',
    isStudent: (state) => state.userInfo?.role === 'STUDENT',
    avatarUrl: (state) => {
      if (!state.userInfo?.avatar) {
      
        return ''
      }
      // 直接使用后端返回的路径，通过Vite代理访问
      const finalUrl = '/api' + state.userInfo.avatar + '?t=' + state.avatarTimestamp
    
      return finalUrl
    }
  },

  actions: {
    // 登录
    async login(loginForm) {
      try {
        const response = await login(loginForm)
        const { token, userId, username, realName, role } = response.data
        
        this.token = token
        this.userInfo = { userId, username, realName, role }
        
        setToken(token)
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 注册
    async register(registerForm) {
      try {
        const response = await register(registerForm)
        return response
      } catch (error) {
        throw error
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getUserInfo()
        this.userInfo = response.data
        return response
      } catch (error) {
        console.error('Failed to get user info:', error)
        this.logout()
        throw error
      }
    },

    // 更新用户信息
    async updateUserInfo(userInfo) {
      try {
        const response = await updateUserProfile(userInfo)
        this.userInfo = response.data
        this.avatarTimestamp = Date.now() // 更新头像时间戳
        return response
      } catch (error) {
        throw error
      }
    },

    // 更新头像时间戳
    updateAvatarTimestamp() {
      this.avatarTimestamp = Date.now()
    },

    // 初始化用户信息
    async initUserInfo() {
      if (this.token && !this.userInfo) {
        try {
          await this.getUserInfo()
        } catch (error) {
          this.logout()
        }
      }
    },

    // 登出
    logout() {
      this.token = null
      this.userInfo = null
      removeToken()
    },

    // 根据角色获取默认路由 - 登录后统一跳转到Dashboard
    getDefaultRoute() {
      if (!this.userInfo) {
        console.log('用户信息为空，返回登录页')
        return '/login'
      }
      
      // 所有用户登录后都先到Dashboard
      console.log('用户登录后跳转到Dashboard')
      return '/dashboard'
    },

    // 根据角色获取工作台路由 - 从Dashboard进入后台时使用
    getWorkspaceRoute() {
      if (!this.userInfo) {
        console.log('用户信息为空，返回登录页')
        return '/login'
      }
      
      switch (this.userInfo.role) {
        case 'ADMIN':
          console.log('跳转到管理员面板')
          return '/admin/dashboard'
        case 'TEACHER':
          console.log('跳转到教师面板')
          return '/teacher/courses'
        case 'STUDENT':
          console.log('跳转到学生面板')
          return '/student/courses'
        default:
          console.log('默认跳转到仪表板')
          return '/dashboard'
      }
    }
  }
})