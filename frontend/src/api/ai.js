import request from '@/utils/request'

// 基于知识库回答问题
export function answerQuestionWithKnowledge(data) {
  return request({
    url: '/ai/enhanced/answer',
    method: 'post',
    data
  })
}

// 搜索相关知识库内容
export function searchKnowledge(params) {
  return request({
    url: '/ai/enhanced/search',
    method: 'get',
    params
  })
}

// 基于知识库生成练习题
export function generateQuestionsFromKnowledge(data) {
  return request({
    url: '/ai/enhanced/generate-questions',
    method: 'post',
    data,
    timeout: 180000 // AI生成题目需要更长时间，设置为3分钟
  })
}

// 分析学生答案
export function analyzeStudentAnswer(data) {
  return request({
    url: '/ai/enhanced/analyze-answer',
    method: 'post',
    data
  })
}

// 推荐学习资源
export function recommendLearningResources(params) {
  return request({
    url: '/ai/enhanced/recommend-resources',
    method: 'get',
    params
  })
}

// 生成个性化学习计划
export function generateStudyPlan(data) {
  return request({
    url: '/ai/enhanced/study-plan',
    method: 'post',
    data
  })
}

// 获取知识库统计信息
export function getKnowledgeStatistics() {
  return request({
    url: '/ai/enhanced/knowledge-stats',
    method: 'get'
  })
}

// 获取知识文件内容
export function getKnowledgeFileContent(knowledgeId) {
  return request({
    url: `/ai/enhanced/knowledge-content/${knowledgeId}`,
    method: 'get'
  })
}

// 保存问答记录
export function saveQuestionRecord(data) {
  return request({
    url: '/ai/enhanced/save-question',
    method: 'post',
    data
  })
}

// 测试AI连接状态
export function testAIConnection() {
  return request({
    url: '/ai/test',
    method: 'get'
  })
}

// 原有的AI功能
export function generateContent(data) {
  return request({
    url: '/ai/generate-content',
    method: 'post',
    data
  })
}

export function generateQuestions(data) {
  return request({
    url: '/ai/generate-questions',
    method: 'post',
    data,
    timeout: 180000 // AI生成题目需要更长时间，设置为3分钟
  })
}

export function answerQuestion(data) {
  return request({
    url: '/ai/answer-question',
    method: 'post',
    data
  })
}

export function analyzeAnswer(data) {
  return request({
    url: '/ai/analyze-answer',
    method: 'post',
    data
  })
}