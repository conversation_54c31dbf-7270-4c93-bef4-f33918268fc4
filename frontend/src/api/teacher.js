import request from '@/utils/request'

// 获取我的课程列表
export function getCourses() {
  return request({
    url: '/teacher/courses',
    method: 'get'
  })
}

// 创建课程
export function createCourse(data) {
  return request({
    url: '/teacher/courses',
    method: 'post',
    data
  })
}

// 更新课程
export function updateCourse(id, data) {
  return request({
    url: `/teacher/courses/${id}`,
    method: 'put',
    data
  })
}

// 删除课程
export function deleteCourse(id) {
  return request({
    url: `/teacher/courses/${id}`,
    method: 'delete'
  })
}

// 获取课程教学内容列表
export function getTeachingContents(courseId) {
  return request({
    url: `/teacher/content/course/${courseId}`,
    method: 'get'
  })
}

// 创建教学内容
export function createTeachingContent(data) {
  return request({
    url: '/teacher/content',
    method: 'post',
    data
  })
}

// 更新教学内容
export function updateTeachingContent(id, data) {
  return request({
    url: `/teacher/content/${id}`,
    method: 'put',
    data
  })
}

// 删除教学内容
export function deleteTeachingContent(id) {
  return request({
    url: `/teacher/content/${id}`,
    method: 'delete'
  })
}

// 保存题目到题库
export function saveQuestionToBank(data) {
  return request({
    url: '/teacher/exam-question',
    method: 'post',
    data
  })
}

// 批量保存题目到题库
export function batchSaveQuestions(data) {
  return request({
    url: '/teacher/exam-questions/batch',
    method: 'post',
    data
  })
}

// 使用AI生成教学内容
export function generateTeachingContent(data) {
  return request({
    url: '/teacher/content/generate',
    method: 'post',
    data
  })
}
// ==================== 评分相关API ====================

// 获取待评分答案列表
export function getAnswersForGrading(params) {
  return request({
    url: '/teacher/grading/pending',
    method: 'get',
    params
  })
}

// 获取答案详情
export function getAnswerDetail(answerId) {
  return request({
    url: `/teacher/grading/answer/${answerId}`,
    method: 'get'
  })
}

// 提交教师评分
export function submitTeacherGrading(data) {
  return request({
    url: '/teacher/grading/submit',
    method: 'post',
    data: {
      answerId: data.answerId,
      score: data.teacherScore,
      isCorrect: data.isCorrect,
      feedback: data.teacherFeedback,
      improvementSuggestion: data.improvementSuggestion
    }
  })
}

// 获取评分统计信息
export function getGradingStatistics(params) {
  return request({
    url: '/teacher/grading/statistics',
    method: 'get',
    params
  })
}

// 批量评分
export function batchGrading(data) {
  return request({
    url: '/teacher/grading/batch',
    method: 'post',
    data
  })
}

// 获取教师课程列表（用于评分筛选）
export function getTeacherCourses() {
  return request({
    url: '/teacher/courses',
    method: 'get'
  })
}