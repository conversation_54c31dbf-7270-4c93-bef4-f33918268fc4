import request from '@/utils/request'

// 课程浏览
export function getAllCourses() {
  return request({
    url: '/student/courses',
    method: 'get'
  })
}

export function getCourse(id) {
  return request({
    url: `/student/courses/${id}`,
    method: 'get'
  })
}

export function getCourseContent(courseId) {
  return request({
    url: `/student/courses/${courseId}/content`,
    method: 'get'
  })
}

// 学习助手
export function askQuestion(data) {
  return request({
    url: '/student/learning/ask',
    method: 'post',
    data
  })
}

export function getQuestionHistory(params) {
  return request({
    url: '/student/learning/questions',
    method: 'get',
    params
  })
}

export function getRecentQuestions(params) {
  return request({
    url: '/student/learning/questions/recent',
    method: 'get',
    params
  })
}

export function rateAnswer(answerId, params) {
  return request({
    url: `/student/learning/answer/${answerId}/rate`,
    method: 'post',
    params
  })
}

// 练习系统
export function generatePractice(data) {
  return request({
    url: '/student/practice/generate',
    method: 'post',
    data
  })
}

export function startPractice(data) {
  return request({
    url: '/student/practice/start',
    method: 'post',
    data
  })
}

export function submitAnswer(data) {
  return request({
    url: '/student/practice/answer',
    method: 'post',
    data
  })
}

export function completePractice(practiceId) {
  return request({
    url: `/student/practice/${practiceId}/complete`,
    method: 'post'
  })
}

export function getPracticeHistory(params) {
  return request({
    url: '/student/practice/history',
    method: 'get',
    params
  })
}

export function getPracticeStatistics(params) {
  return request({
    url: '/student/practice/statistics',
    method: 'get',
    params
  })
}

export function getWrongQuestions(params) {
  return request({
    url: '/student/practice/wrong-questions',
    method: 'get',
    params
  })
}

// 学生课程相关 - 添加别名和缺失的函数
export const getStudentCourses = getAllCourses // 为 getAllCourses 创建别名
export const getCourses = getAllCourses // 为 AIAssistant.vue 提供 getCourses 别名

export function getCourseContents(courseId) {
  return request({
    url: `/student/courses/${courseId}/content`,
    method: 'get'
  })
}

// 练习相关 - 修正API端点
export function generatePracticeQuestions(data) {
  return request({
    url: '/student/practice/generate',
    method: 'post',
    data
  })
}

export function submitPracticeAnswers(data) {
  return request({
    url: '/student/practice/answer',
    method: 'post',
    data
  })
}

// 学习记录和统计 - 修正API端点
export function getStudentStats() {
  return request({
    url: '/student/practice/statistics',
    method: 'get'
  })
}

export function getStudentRecords(params) {
  return request({
    url: '/student/practice/history',
    method: 'get',
    params
  })
}

// 获取练习记录详情
export function getPracticeRecord(practiceId) {
  return request({
    url: `/student/practice/${practiceId}`,
    method: 'get'
  })
}

// 学习记录相关
export function getLearningStats(params) {
  return request({
    url: '/student/learning/stats',
    method: 'get',
    params
  })
}

export function getLearningRecords(params) {
  return request({
    url: '/student/learning/records',
    method: 'get',
    params
  })
}
