import request from '@/utils/request'

// 用户登录
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/me',
    method: 'get'
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 检查验证码是否启用
export function checkCaptchaEnabled() {
  return request({
    url: '/auth/captcha/enabled',
    method: 'get'
  })
}

// 获取验证码
export function getCaptcha() {
  return request({
    url: '/auth/captcha/generate',
    method: 'get'
  })
}

// 获取登录页演示账号配置
export function getLoginDemoConfig() {
  return request({
    url: '/auth/login/demo-config',
    method: 'get'
  })
}


// 验证验证码
export function verifyCaptcha(data) {
  return request({
    url: '/auth/captcha/verify',
    method: 'post',
    data
  })
}

// 更新用户资料
export function updateUserProfile(data) {
  return request({
    url: '/auth/profile',
    method: 'put',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/auth/change-password',
    method: 'put',
    data
  })
}

// 上传头像
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/auth/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
