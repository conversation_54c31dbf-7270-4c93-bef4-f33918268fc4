import request from '@/utils/request'

// 获取登录页相关设置（管理员）
export function getLoginPageSettings() {
  return request({
    url: '/admin/system-settings/login',
    method: 'get'
  })
}

// 切换登录页演示账号展示开关（管理员）
export function toggleLoginDemoAccounts(enabled) {
  return request({
    url: '/admin/system-settings/login/demo-accounts/toggle',
    method: 'post',
    data: { enabled }
  })
}


// 获取登录页演示账号列表
export function getLoginDemoAccounts() {
  return request({
    url: '/admin/system-settings/login/demo-accounts',
    method: 'get'
  })
}

// 保存登录页演示账号列表（传JSON数组字符串）
export function saveLoginDemoAccounts(jsonArray) {
  return request({
    url: '/admin/system-settings/login/demo-accounts',
    method: 'post',
    data: jsonArray,
    headers: { 'Content-Type': 'application/json' }
  })
}
