import request from '@/utils/request'

// 获取验证码
export function getCaptcha(sessionId) {
  return request({
    url: '/auth/captcha/generate',
    method: 'get',
    params: { sessionId }
  })
}

// 验证验证码
export function verifyCaptcha(sessionId, captcha) {
  return request({
    url: '/auth/captcha/verify',
    method: 'post',
    data: { sessionId, captcha }
  })
}

// 检查验证码是否启用
export function isCaptchaEnabled() {
  return request({
    url: '/auth/captcha/enabled',
    method: 'get'
  })
}
