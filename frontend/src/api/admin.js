import request from '@/utils/request'

// 统计大屏
export function getDashboardOverview() {
  return request({
    url: '/admin/dashboard/overview',
    method: 'get'
  })
}

export function getUsageStatistics(params) {
  return request({
    url: '/admin/dashboard/usage',
    method: 'get',
    params
  })
}

export function getUsageTrends(params) {
  return request({
    url: '/admin/dashboard/trends',
    method: 'get',
    params
  })
}

export function getActiveUsersStats(params) {
  return request({
    url: '/admin/dashboard/active-users',
    method: 'get',
    params
  })
}

export function getTeacherEfficiencyStats(params) {
  return request({
    url: '/admin/dashboard/teacher-efficiency',
    method: 'get',
    params
  })
}

export function getCoursePerformanceStats(params) {
  return request({
    url: '/admin/dashboard/course-performance',
    method: 'get',
    params
  })
}

// 用户管理
export function getUsers(params) {
  return request({
    url: '/admin/users',
    method: 'get',
    params
  })
}

export function getUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'get'
  })
}

export function createUser(data) {
  return request({
    url: '/admin/users',
    method: 'post',
    data
  })
}

export function updateUser(id, data) {
  return request({
    url: `/admin/users/${id}`,
    method: 'put',
    data
  })
}

export function deleteUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'delete'
  })
}

export function changeUserStatus(id, params) {
  return request({
    url: `/admin/users/${id}/status`,
    method: 'put',
    params
  })
}

export function resetPassword(id, newPassword) {
  return request({
    url: `/admin/users/${id}/reset-password`,
    method: 'put',
    params: {
      newPassword: newPassword
    }
  })
}

export function searchUsers(params) {
  return request({
    url: '/admin/users/search',
    method: 'get',
    params
  })
}

// 资源管理
export function uploadKnowledgeFile(data) {
  return request({
    url: '/admin/resources/knowledge/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

export function getKnowledgeFiles(params) {
  return request({
    url: '/admin/resources/knowledge',
    method: 'get',
    params
  })
}

export function downloadKnowledgeFile(id) {
  return request({
    url: `/admin/resources/knowledge/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

export function updateKnowledgeFile(id, data) {
  return request({
    url: `/admin/resources/knowledge/${id}`,
    method: 'put',
    data
  })
}

export function deleteKnowledgeFile(id) {
  return request({
    url: `/admin/resources/knowledge/${id}`,
    method: 'delete'
  })
}

export function getFileTypeStatistics() {
  return request({
    url: '/admin/resources/statistics/file-types',
    method: 'get'
  })
}

export function getResourceUsageStatistics() {
  return request({
    url: '/admin/resources/statistics/usage',
    method: 'get'
  })
}

export function exportCourseResources(courseId) {
  return request({
    url: `/admin/resources/export/course/${courseId}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 导出知识库为DOCX
export function exportKnowledgeBase() {
  return request({
    url: '/admin/resources/export/knowledge',
    method: 'get',
    responseType: 'blob'
  })
}

// 导出单个资源为DOCX
export function exportSingleResource(resourceId, type) {
  return request({
    url: `/admin/resources/export/resource/${resourceId}`,
    method: 'get',
    params: { type },
    responseType: 'blob'
  })
}

export function searchKnowledgeFiles(params) {
  return request({
    url: '/admin/resources/knowledge/search',
    method: 'get',
    params
  })
}

export function getKnowledgeFilesBySubject(subject) {
  return request({
    url: `/admin/resources/knowledge/subject/${subject}`,
    method: 'get'
  })
}

export function getKnowledgeFilesByCourse(courseId) {
  return request({
    url: `/admin/resources/knowledge/course/${courseId}`,
    method: 'get'
  })
}

export function getKnowledgeFilesByTags(params) {
  return request({
    url: '/admin/resources/knowledge/tags',
    method: 'get',
    params
  })
}

// 获取课程列表（用于资源管理页面）
export function getCoursesForResources() {
  return request({
    url: '/admin/resources/courses',
    method: 'get'
  })
}

// 获取知识库文件详情（包含AI分析数据）
export function getKnowledgeFileDetails(id) {
  return request({
    url: `/admin/resources/knowledge/${id}`,
    method: 'get'
  })
}

// 获取课程资源
export function getCourseResources(courseId) {
  return request({
    url: `/admin/resources/course/${courseId}/resources`,
    method: 'get'
  })
}

// 课程管理
export function getCourses(params) {
  return request({
    url: '/admin/courses',
    method: 'get',
    params: {
      ...params,
      includeDisabled: true // 包含禁用的课程
    }
  })
}

export function getCourse(id) {
  return request({
    url: `/admin/courses/${id}`,
    method: 'get'
  })
}

export function createCourse(data) {
  return request({
    url: '/admin/courses',
    method: 'post',
    data
  })
}

export function updateCourse(id, data) {
  return request({
    url: `/admin/courses/${id}`,
    method: 'put',
    data
  })
}

export function deleteCourse(id) {
  return request({
    url: `/admin/courses/${id}`,
    method: 'delete'
  })
}

export function changeCourseStatus(id, params) {
  return request({
    url: `/admin/courses/${id}/status`,
    method: 'put',
    params
  })
}

// 教师使用统计
export function getTeacherUsageStats() {
  return request({
    url: '/admin/dashboard/teacher-usage',
    method: 'get'
  })
}

// 学生使用统计
export function getStudentUsageStats() {
  return request({
    url: '/admin/dashboard/student-usage',
    method: 'get'
  })
}

// 综合统计数据
export function getComprehensiveStats() {
  return request({
    url: '/admin/dashboard/comprehensive-stats',
    method: 'get'
  })
}

// 获取验证码设置
export function getCaptchaSettings() {
  return request({
    url: '/admin/system-settings/captcha',
    method: 'get'
  })
}

// 切换验证码开关
export function toggleCaptchaEnabled(enabled) {
  return request({
    url: '/admin/system-settings/captcha/toggle',
    method: 'post',
    data: { enabled }
  })
}

// 更新验证码长度
export function updateCaptchaLength(length) {
  return request({
    url: '/admin/system-settings/captcha/length',
    method: 'post',
    data: { length }
  })
}

// 更新验证码过期时间
export function updateCaptchaExpireMinutes(expireMinutes) {
  return request({
    url: '/admin/system-settings/captcha/expire-minutes',
    method: 'post',
    data: { expireMinutes }
  })
}

// 获取所有系统设置
export function getAllSystemSettings() {
  return request({
    url: '/admin/system-settings/all',
    method: 'get'
  })
}

// 教师管理相关API
export function getTeachers(params) {
  return request({
    url: '/admin/courses/teachers',
    method: 'get',
    params
  })
}

export function getTeachersForCourse(courseId) {
  return request({
    url: `/admin/courses/${courseId}/teachers`,
    method: 'get'
  })
}

export function assignTeacherToCourse(data) {
  return request({
    url: '/admin/courses/assign-teacher',
    method: 'put',
    data
  })
}

// 课程管理扩展API
export function getCoursesWithDetails(params) {
  return request({
    url: '/admin/courses/detailed',
    method: 'get',
    params
  })
}

export function checkCourseCodeExists(courseCode) {
  return request({
    url: '/admin/courses/check-code',
    method: 'get',
    params: { courseCode }
  })
}

export function createCourseWithTeacher(data) {
  return request({
    url: '/admin/courses/with-teacher',
    method: 'post',
    data
  })
}

// 用户管理相关API
export function getUserList(params) {
  return request({
    url: '/admin/users',
    method: 'get',
    params
  })
}

// 课程管理相关API
export function getCourseList(params) {
  return request({
    url: '/admin/courses',
    method: 'get',
    params
  })
}

// 统计数据相关API
export function getDashboardStats() {
  return request({
    url: '/admin/dashboard/stats',
    method: 'get'
  })
}

export function getSystemHealth() {
  return request({
    url: '/admin/system/health',
    method: 'get'
  })
}
