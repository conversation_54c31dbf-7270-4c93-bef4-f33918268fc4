import request from '@/utils/request'

// 获取课程学生列表
export function getCourseStudents(courseId) {
  return request({
    url: `/teacher/analytics/course-students/${courseId}`,
    method: 'get'
  })
}

// 获取综合分析报告
export function getComprehensiveReport(studentId, courseId) {
  return request({
    url: '/teacher/analytics/comprehensive-report',
    method: 'get',
    params: {
      studentId,
      courseId
    }
  })
}

// 批量分析学生
export function batchAnalyzeStudents(studentIds, courseId) {
  return request({
    url: '/teacher/analytics/batch-analyze',
    method: 'post',
    data: {
      studentIds,
      courseId
    }
  })
}

// 智能答案分析
export function intelligentAnswerAnalysis(data) {
  return request({
    url: '/teacher/analytics/answer-analysis',
    method: 'post',
    data: {
      courseId: data.courseId,
      questionContent: data.questionContent,
      correctAnswer: data.correctAnswer,
      studentAnswer: data.studentAnswer,
      studentId: data.studentId
    }
  })
}

// 课程整体分析
export function analyzeCourseOverall(courseId) {
  return request({
    url: `/teacher/analytics/course-overall/${courseId}`,
    method: 'get'
  })
}

// 学生个体分析
export function analyzeStudentIndividual(studentId, courseId, dimensions = []) {
  return request({
    url: '/teacher/analytics/student-individual',
    method: 'post',
    data: {
      studentId,
      courseId,
      analysisDimensions: dimensions
    }
  })
}

// 获取学习分析数据
export function getLearningAnalytics(params) {
  return request({
    url: '/teacher/analytics/learning-data',
    method: 'get',
    params
  })
}

// 生成分析报告
export function generateAnalysisReport(data) {
  return request({
    url: '/teacher/analytics/generate-report',
    method: 'post',
    data
  })
}

// 导出分析数据
export function exportAnalysisData(params) {
  return request({
    url: '/teacher/analytics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取快速洞察
export function getQuickInsights(courseId) {
  return request({
    url: `/teacher/analytics/quick-insights/${courseId}`,
    method: 'get'
  })
}

// 获取学生答案数据用于深度分析
export function getStudentAnswers(courseId) {
  return request({
    url: `/teacher/analytics/student-answers/${courseId}`,
    method: 'get'
  })
}