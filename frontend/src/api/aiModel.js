import request from '@/utils/request'

// AI Model Management API

/**
 * Get paginated list of AI models
 */
export function getModelList(params) {
  return request({
    url: '/admin/ai-models',
    method: 'get',
    params
  })
}

/**
 * Get all active models
 */
export function getActiveModels() {
  return request({
    url: '/admin/ai-models/active',
    method: 'get'
  })
}

/**
 * Get model by ID
 */
export function getModelById(id) {
  return request({
    url: `/admin/ai-models/${id}`,
    method: 'get'
  })
}

/**
 * Get current active model
 */
export function getActiveModel() {
  return request({
    url: '/admin/ai-models/current/active',
    method: 'get'
  })
}

/**
 * Get default model
 */
export function getDefaultModel() {
  return request({
    url: '/admin/ai-models/current/default',
    method: 'get'
  })
}

/**
 * Create new AI model
 */
export function createModel(data) {
  return request({
    url: '/admin/ai-models',
    method: 'post',
    data
  })
}

/**
 * Update AI model
 */
export function updateModel(id, data) {
  return request({
    url: `/admin/ai-models/${id}`,
    method: 'put',
    data
  })
}

/**
 * Delete AI model
 */
export function deleteModel(id) {
  return request({
    url: `/admin/ai-models/${id}`,
    method: 'delete'
  })
}

/**
 * Set model as active
 */
export function setActiveModel(id) {
  return request({
    url: `/admin/ai-models/${id}/activate`,
    method: 'post'
  })
}

/**
 * Set model as default
 */
export function setDefaultModel(id) {
  return request({
    url: `/admin/ai-models/${id}/set-default`,
    method: 'post'
  })
}

/**
 * Test model connection
 */
export function testModelConnection(id) {
  return request({
    url: `/admin/ai-models/${id}/test`,
    method: 'post'
  })
}

/**
 * Test model connection with custom parameters
 */
export function testModelConnectionCustom(data) {
  return request({
    url: '/admin/ai-models/test',
    method: 'post',
    data
  })
}

/**
 * Batch test all models
 */
export function testAllModels() {
  return request({
    url: '/admin/ai-models/test-all',
    method: 'post'
  })
}

/**
 * Get available providers
 */
export function getProviders() {
  return request({
    url: '/admin/ai-models/providers',
    method: 'get'
  })
}