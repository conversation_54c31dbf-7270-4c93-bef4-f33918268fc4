import request from '@/utils/request'

// 发布系统通知
export function publishNotification(data) {
  return request({
    url: '/notifications/publish',
    method: 'post',
    data
  })
}

// 获取用户通知列表
export function getUserNotifications(params) {
  return request({
    url: '/notifications/my',
    method: 'get',
    params
  })
}

// 获取未读通知数量
export function getUnreadNotificationCount() {
  return request({
    url: '/notifications/unread-count',
    method: 'get'
  })
}

// 标记通知为已读
export function markNotificationAsRead(id) {
  return request({
    url: `/notifications/${id}/read`,
    method: 'post'
  })
}

// 标记所有通知为已读
export function markAllNotificationsAsRead() {
  return request({
    url: '/notifications/read-all',
    method: 'post'
  })
}

// 获取最新系统通知
export function getLatestSystemNotifications(params) {
  return request({
    url: '/notifications/latest',
    method: 'get',
    params
  })
}

// 获取通知详情
export function getNotificationDetail(id) {
  return request({
    url: `/notifications/${id}`,
    method: 'get'
  })
}

// 删除通知
export function deleteNotification(id) {
  return request({
    url: `/notifications/${id}`,
    method: 'delete'
  })
}

// 管理员获取所有通知列表
export function getAllNotifications(params) {
  return request({
    url: '/notifications/admin/all',
    method: 'get',
    params
  })
}