export default {
  auth: {
    // 登录页面
    login: {
      title: '登录账号',
      username: '用户名',
      password: '密码',
      loginButton: '登录',
      loginLoading: '登录中...',
      noAccount: '还没有账号？',
      register: '立即注册',
      demoAccounts: '演示账号',
      admin: '管理员',
      teacher: '教师',
      student: '学生',
      usernameRequired: '请输入用户名',
      passwordRequired: '请输入密码',
      passwordLength: '密码长度不能少于3位',
      captcha: '请输入验证码',
      captchaRequired: '请输入验证码',
      captchaLength: '验证码长度为{n}位',
      loginSuccess: '登录成功'
    },
    // 注册页面
    register: {
      title: '创建账号',
      username: '用户名',
      realName: '真实姓名',
      email: '邮箱地址',
      phone: '手机号',
      password: '密码',
      confirmPassword: '确认密码',
      registerButton: '注册',
      registerLoading: '注册中...',
      hasAccount: '已有账号？',
      login: '立即登录',
      usernameRequired: '请输入用户名',
      usernameLength: '用户名长度在3到20个字符',
      realNameRequired: '请输入真实姓名',
      emailRequired: '请输入邮箱地址',
      emailInvalid: '请输入正确的邮箱地址',
      phoneInvalid: '请输入正确的手机号',
      passwordRequired: '请输入密码',
      passwordLength: '密码长度不能少于6位',
      confirmPasswordRequired: '请再次输入密码',
      passwordMismatch: '两次输入的密码不一致',
      registerSuccess: '注册成功，请登录'
    },
    // 公共部分
    common: {
      slogan: 'AI Education Forge - 智能教育锻造平台',
      subSlogan: '让AI赋能教育，让学习更智能更高效',
      language: '简体中文'
    }
  },
  error: {
    // Error types
    types: {
      RATE_LIMIT_EXCEEDED: '频率限制超出',
      AUTHENTICATION_ERROR: '认证错误',
      AUTHORIZATION_ERROR: '授权错误',
      BAD_REQUEST: '请求错误',
      INTERNAL_SERVER_ERROR: '内部服务器错误',
      BAD_GATEWAY: '网关错误',
      SERVICE_UNAVAILABLE: '服务不可用',
      GATEWAY_TIMEOUT: '网关超时',
      NETWORK_ERROR: '网络错误',
      UNKNOWN_ERROR: '未知错误'
    },
    
    // Error titles
    titles: {
      RATE_LIMIT_EXCEEDED: '频率限制超出',
      AUTHENTICATION_ERROR: '认证失败',
      AUTHORIZATION_ERROR: '访问被拒绝',
      BAD_REQUEST: '请求错误',
      INTERNAL_SERVER_ERROR: '内部服务器错误',
      BAD_GATEWAY: '网关错误',
      SERVICE_UNAVAILABLE: '服务不可用',
      GATEWAY_TIMEOUT: '网关超时',
      NETWORK_ERROR: '连接失败',
      UNKNOWN_ERROR: '连接失败'
    },
    
    // Common messages
    messages: {
      connectionFailed: '连接失败',
      unexpectedError: '发生了意外错误',
      modelNotFound: '模型未找到',
      networkError: '网络连接错误'
    },
    
    // Rate limit specific
    rateLimit: {
      title: '频率限制信息',
      limit: '限制',
      remaining: '剩余',
      resetTime: '重置时间',
      upgradeMessage: '添加10个积分以解锁每日1000次免费模型请求'
    },
    
    // Troubleshooting
    troubleshooting: {
      title: '故障排除建议',
      suggestions: {
        rateLimitExceeded: [
          '等待频率限制重置',
          '考虑升级您的计划以获得更高限制',
          '在您的应用程序中实施请求节流',
          '尝试使用具有更高限制的不同模型'
        ],
        authenticationError: [
          '检查您的API密钥是否正确',
          '验证您的账户是否处于活跃状态',
          '确保设置了正确的认证头'
        ],
        authorizationError: [
          '验证您是否有权限访问此模型',
          '检查您的账户是否有所需的订阅',
          '如果您认为这是错误，请联系支持'
        ],
        networkError: [
          '检查您的网络连接',
          '验证模型端点是否正确',
          '稍后再试'
        ]
      },
      alternativeModels: '推荐替代模型',
      documentation: '相关文档',
      documentationLinks: {
        limits: 'API限制说明',
        errors: '错误代码参考',
        authentication: '认证文档'
      }
    },
    
    // Technical details
    technical: {
      title: '技术详情',
      endpoint: '端点',
      userId: '用户ID',
      httpStatus: 'HTTP状态',
      metadata: '元数据',
      originalMessage: '原始消息',
      formattedJson: '格式化JSON',
      model: '模型',
      timestamp: '时间'
    },
    
    // Actions
    actions: {
      copyError: '复制错误',
      copyJson: '复制JSON',
      retry: '重试',
      retryTest: '重新测试',
      close: '关闭',
      selectModel: '选择模型'
    },
    
    // Success messages
    success: {
      errorCopied: '错误信息已复制到剪贴板',
      jsonCopied: 'JSON已复制到剪贴板'
    },
    
    // Error messages
    errors: {
      copyFailed: '复制失败'
    },
    
    // Dialog
    dialog: {
      title: '模型连接错误',
      titleWithModel: '模型连接错误 - {modelName}'
    }
  }
}