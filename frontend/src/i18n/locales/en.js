export default {
  auth: {
    // Login page
    login: {
      title: 'Login to Account',
      username: '<PERSON>rna<PERSON>',
      password: 'Password',
      loginButton: 'Login',
      loginLoading: 'Logging in...',
      noAccount: 'Don\'t have an account?',
      register: 'Register Now',
      demoAccounts: 'Demo Accounts',
      admin: 'Admin',
      teacher: 'Teacher',
      student: 'Student',
      usernameRequired: 'Please enter your username',
      passwordRequired: 'Please enter your password',
      passwordLength: 'Password must be at least 3 characters',
      captcha: 'Please enter captcha',
      captchaRequired: 'Please enter captcha',
      captchaLength: 'Captcha must be 4 characters',
      loginSuccess: 'Login successful'
    },
    // Register page
    register: {
      title: 'Create Account',
      username: 'Userna<PERSON>',
      realName: 'Real Name',
      email: 'Email Address',
      phone: 'Phone Number',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      registerButton: 'Register',
      registerLoading: 'Registering...',
      hasAccount: 'Already have an account?',
      login: 'Login Now',
      usernameRequired: 'Please enter a username',
      usernameLength: 'Userna<PERSON> must be between 3 and 20 characters',
      realNameRequired: 'Please enter your real name',
      emailRequired: 'Please enter your email address',
      emailInvalid: 'Please enter a valid email address',
      phoneInvalid: 'Please enter a valid phone number',
      passwordRequired: 'Please enter a password',
      passwordLength: 'Password must be at least 6 characters',
      confirmPasswordRequired: 'Please confirm your password',
      passwordMismatch: 'Passwords do not match',
      registerSuccess: 'Registration successful, please login'
    },
    // Common parts
    common: {
      slogan: 'AI Education Forge - Intelligent Education Platform',
      subSlogan: 'Empowering education with AI, making learning smarter and more efficient',
      language: 'English'
    }
  },
  error: {
    // Error types
    types: {
      RATE_LIMIT_EXCEEDED: 'Rate Limit Exceeded',
      AUTHENTICATION_ERROR: 'Authentication Error',
      AUTHORIZATION_ERROR: 'Authorization Error',
      BAD_REQUEST: 'Bad Request',
      INTERNAL_SERVER_ERROR: 'Internal Server Error',
      BAD_GATEWAY: 'Bad Gateway',
      SERVICE_UNAVAILABLE: 'Service Unavailable',
      GATEWAY_TIMEOUT: 'Gateway Timeout',
      NETWORK_ERROR: 'Network Error',
      UNKNOWN_ERROR: 'Unknown Error'
    },
    
    // Error titles
    titles: {
      RATE_LIMIT_EXCEEDED: 'Rate Limit Exceeded',
      AUTHENTICATION_ERROR: 'Authentication Failed',
      AUTHORIZATION_ERROR: 'Access Denied',
      BAD_REQUEST: 'Bad Request',
      INTERNAL_SERVER_ERROR: 'Internal Server Error',
      BAD_GATEWAY: 'Bad Gateway',
      SERVICE_UNAVAILABLE: 'Service Unavailable',
      GATEWAY_TIMEOUT: 'Gateway Timeout',
      NETWORK_ERROR: 'Connection Failed',
      UNKNOWN_ERROR: 'Connection Failed'
    },
    
    // Common messages
    messages: {
      connectionFailed: 'Connection failed',
      unexpectedError: 'An unexpected error occurred',
      modelNotFound: 'Model not found',
      networkError: 'Network connection error'
    },
    
    // Rate limit specific
    rateLimit: {
      title: 'Rate Limit Information',
      limit: 'Limit',
      remaining: 'Remaining',
      resetTime: 'Reset Time',
      upgradeMessage: 'Add 10 credits to unlock 1000 free model requests per day'
    },
    
    // Troubleshooting
    troubleshooting: {
      title: 'Troubleshooting Suggestions',
      suggestions: {
        rateLimitExceeded: [
          'Wait for the rate limit to reset',
          'Consider upgrading your plan for higher limits',
          'Implement request throttling in your application',
          'Try using a different model with higher limits'
        ],
        authenticationError: [
          'Check your API key is correct',
          'Verify your account is active',
          'Ensure proper authentication headers are set'
        ],
        authorizationError: [
          'Verify you have permission to access this model',
          'Check if your account has the required subscription',
          'Contact support if you believe this is an error'
        ],
        networkError: [
          'Check your internet connection',
          'Verify the model endpoint is correct',
          'Try again in a few moments'
        ]
      },
      alternativeModels: 'Recommended Alternative Models',
      documentation: 'Related Documentation',
      documentationLinks: {
        limits: 'API Limits Documentation',
        errors: 'Error Code Reference',
        authentication: 'Authentication Documentation'
      }
    },
    
    // Technical details
    technical: {
      title: 'Technical Details',
      endpoint: 'Endpoint',
      userId: 'User ID',
      httpStatus: 'HTTP Status',
      metadata: 'Metadata',
      originalMessage: 'Original Message',
      formattedJson: 'Formatted JSON',
      model: 'Model',
      timestamp: 'Time'
    },
    
    // Actions
    actions: {
      copyError: 'Copy Error',
      copyJson: 'Copy JSON',
      retry: 'Retry',
      retryTest: 'Retry Test',
      close: 'Close',
      selectModel: 'Select Model'
    },
    
    // Success messages
    success: {
      errorCopied: 'Error information copied to clipboard',
      jsonCopied: 'JSON copied to clipboard'
    },
    
    // Error messages
    errors: {
      copyFailed: 'Copy failed'
    },
    
    // Dialog
    dialog: {
      title: 'Model Connection Error',
      titleWithModel: 'Model Connection Error - {modelName}'
    }
  }
}