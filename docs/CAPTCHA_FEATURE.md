# 验证码登录功能说明

## 功能概述

本项目已集成验证码登录功能，管理员可以通过系统设置页面一键开关验证码功能，并配置相关参数。

## 功能特性

### 1. 验证码生成
- 支持数字和字母混合验证码
- 可配置验证码长度（3-8位）
- 自动生成干扰线和干扰点
- 支持点击刷新验证码

### 2. 验证码验证
- 不区分大小写验证
- 支持过期时间配置（1-60分钟）
- 一次性使用，验证后自动失效
- 自动清理过期验证码

### 3. 管理员控制
- 一键开关验证码功能
- 实时配置验证码长度
- 实时配置过期时间
- 系统设置页面统一管理

## 使用说明

### 管理员设置

1. 登录管理员账号
2. 进入"系统设置"页面
3. 在"验证码设置"区域进行配置：
   - **验证码开关**：控制是否启用验证码功能
   - **验证码长度**：设置验证码字符数量（3-8位）
   - **过期时间**：设置验证码有效期（1-60分钟）

### 用户登录

当验证码功能启用时：
1. 在登录页面输入用户名和密码
2. 输入验证码（不区分大小写）
3. 如果验证码看不清，点击验证码图片刷新
4. 点击登录按钮完成登录

## 技术实现

### 后端实现

#### 数据库表结构

**system_settings 表**
- `setting_key`: 设置键
- `setting_value`: 设置值
- `description`: 设置描述

**captcha_records 表**
- `session_id`: 会话ID
- `captcha_code`: 验证码
- `captcha_image`: 验证码图片（Base64）
- `expires_at`: 过期时间
- `used`: 是否已使用

#### 核心服务

**CaptchaService**
- `generateCaptcha()`: 生成验证码
- `verifyCaptcha()`: 验证验证码
- `isCaptchaEnabled()`: 检查是否启用
- `cleanExpiredCaptcha()`: 清理过期验证码

**SystemSettingsService**
- `getSettingValue()`: 获取设置值
- `updateSetting()`: 更新设置
- `toggleCaptchaEnabled()`: 切换验证码开关

#### API接口

**验证码相关**
- `GET /api/captcha/generate`: 生成验证码
- `POST /api/captcha/verify`: 验证验证码
- `GET /api/captcha/enabled`: 检查是否启用

**管理员设置**
- `GET /admin/system-settings/captcha`: 获取验证码设置
- `POST /admin/system-settings/captcha/toggle`: 切换开关
- `POST /admin/system-settings/captcha/length`: 设置长度
- `POST /admin/system-settings/captcha/expire-minutes`: 设置过期时间

### 前端实现

#### 登录页面
- 动态显示/隐藏验证码输入框
- 验证码图片点击刷新功能
- 表单验证集成

#### 管理员设置页面
- 实时开关控制
- 参数配置界面
- 设置状态反馈

## 配置说明

### 默认配置

```javascript
{
  "captcha_enabled": "1",        // 验证码开关：1-开启，0-关闭
  "captcha_length": "4",         // 验证码长度：4位
  "captcha_expire_minutes": "5"  // 过期时间：5分钟
}
```

### 自定义配置

管理员可以通过系统设置页面修改以下参数：

- **验证码开关**：启用/禁用验证码功能
- **验证码长度**：3-8位字符
- **过期时间**：1-60分钟

## 安全特性

1. **会话隔离**：每个会话独立的验证码
2. **一次性使用**：验证后立即失效
3. **自动过期**：超时自动清理
4. **干扰处理**：图片包含干扰线和干扰点
5. **大小写不敏感**：提升用户体验

## 维护说明

### 定时任务

系统每10分钟自动清理过期的验证码记录，保持数据库整洁。

### 缓存机制

系统设置采用5分钟缓存机制，减少数据库查询压力。

### 日志记录

关键操作都有详细的日志记录，便于问题排查。

## 故障排除

### 常见问题

1. **验证码不显示**
   - 检查后端服务是否正常
   - 查看浏览器控制台错误信息

2. **验证码验证失败**
   - 确认验证码未过期
   - 检查输入是否正确（不区分大小写）

3. **设置不生效**
   - 等待缓存刷新（最多5分钟）
   - 检查管理员权限

### 日志查看

```bash
# 查看验证码相关日志
grep "captcha" logs/application.log

# 查看系统设置相关日志
grep "system-settings" logs/application.log
```

## 扩展说明

如需扩展验证码功能，可以考虑：

1. 支持更多验证码类型（数学运算、图片识别等）
2. 增加验证码复杂度配置
3. 支持多语言验证码
4. 集成第三方验证码服务

---

**注意**：修改系统设置需要管理员权限，普通用户无法访问设置页面。