2025-08-11 17:25:10.911 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 21732 (E:\Java Project\AiEduForge\target\classes started by <PERSON><PERSON><PERSON><PERSON> in E:\Java Project\AiEduForge)
2025-08-11 17:25:10.913 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 17:25:10.913 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 17:25:11.609 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 17:25:11.637 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-08-11 17:25:11.716 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 17:25:12.105 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 17:25:12.117 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 17:25:12.118 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 17:25:12.166 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 17:25:12.166 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1218 ms
2025-08-11 17:25:12.221 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 17:25:12.326 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 17:25:12.371 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 17:25:12.396 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 17:25:12.585 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 17:25:12.607 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 17:25:12.836 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@674ed201
2025-08-11 17:25:12.838 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 17:25:12.882 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 17:25:12.897 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 17:25:13.140 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 17:25:13.145 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:25:13.247 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@138a952f'
2025-08-11 17:25:13.372 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 17:25:13.396 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 17:25:13.408 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 17:25:13.424 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 17:25:13.444 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 17:25:13.466 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 17:25:13.478 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 17:25:13.492 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 17:25:13.505 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 17:25:13.516 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 17:25:13.529 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 17:25:13.541 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 17:25:13.550 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 17:25:13.559 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 17:25:13.568 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 17:25:13.580 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 17:25:13.597 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 17:25:13.607 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 17:25:14.213 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 17:25:14.878 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 17:25:14.889 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.409 seconds (process running for 5.021)
2025-08-11 17:25:14.891 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 17:25:14.891 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 17:25:14.892 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 17:25:14.968 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 17:27:27.459 [http-nio-8080-exec-5] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 17:27:27.460 [http-nio-8080-exec-5] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 17:27:27.462 [http-nio-8080-exec-5] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-11 17:27:34.747 [http-nio-8080-exec-9] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:27:37.811 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:27:37.820 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:27:37.834 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:37.966 [http-nio-8080-exec-8] ERROR c.e.aieduforge.service.impl.StatisticsServiceImpl - Failed to record user activity
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-其他模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
### The error may exist in com/example/aieduforge/mapper/UsageStatisticsMapper.java (best guess)
### The error may involve com.example.aieduforge.mapper.UsageStatisticsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO usage_statistics  ( user_id, user_type, module_name, action_type, access_count, duration_seconds, stat_date, create_time, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-其他模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
; Duplicate entry '1-其他模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:254)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy112.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy113.insert(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy114.insert(Unknown Source)
	at com.example.aieduforge.service.impl.StatisticsServiceImpl.recordUserActivity(StatisticsServiceImpl.java:277)
	at com.example.aieduforge.interceptor.UserActivityInterceptor.afterCompletion(UserActivityInterceptor.java:124)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:176)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1186)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.example.aieduforge.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-其他模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:109)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:990)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:384)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy177.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy176.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy175.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 120 common frames omitted
2025-08-11 17:27:37.983 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:27:37.994 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:38.034 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:27:38.043 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:38.068 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:27:38.075 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:38.100 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:27:38.108 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:38.127 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:27:38.131 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:38.135 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:38.137 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:43.485 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:43.493 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:45.185 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:45.190 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:46.124 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:46.129 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:47.755 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:27:47.763 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:48.535 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:27:48.541 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:48.563 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:27:48.570 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:48.588 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:27:48.595 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:48.615 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:27:48.621 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:48.634 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:27:48.635 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:48.640 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:48.642 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:51.322 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:51.327 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:53.527 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:53.533 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:27:54.311 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:27:54.316 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:28:04.797 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:28:04.803 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:28:07.767 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:28:07.769 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:28:07.772 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:28:07.772 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:28:08.848 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users/1 -> 其他模块
2025-08-11 17:28:08.853 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:28:14.233 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: PUT /api/admin/users/8/status -> 其他模块
2025-08-11 17:28:14.238 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=更新操作
2025-08-11 17:28:15.785 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:28:15.785 [http-nio-8080-exec-9] ERROR c.e.aieduforge.service.impl.StatisticsServiceImpl - Failed to record user activity
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-课程模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
### The error may exist in com/example/aieduforge/mapper/UsageStatisticsMapper.java (best guess)
### The error may involve com.example.aieduforge.mapper.UsageStatisticsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO usage_statistics  ( user_id, user_type, module_name, action_type, access_count, duration_seconds, stat_date, create_time, update_time )  VALUES (  ?, ?, ?, ?, ?, ?, ?, ?, ?  )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-课程模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
; Duplicate entry '1-课程模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:254)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at jdk.proxy2/jdk.proxy2.$Proxy112.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy113.insert(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy114.insert(Unknown Source)
	at com.example.aieduforge.service.impl.StatisticsServiceImpl.recordUserActivity(StatisticsServiceImpl.java:277)
	at com.example.aieduforge.interceptor.UserActivityInterceptor.afterCompletion(UserActivityInterceptor.java:124)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:176)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1186)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.example.aieduforge.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry '1-课程模块-2025-08-11' for key 'usage_statistics.uk_user_module_date'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:109)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:990)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:384)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor37.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy3/jdk.proxy3.$Proxy177.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy176.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at jdk.internal.reflect.GeneratedMethodAccessor113.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy175.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 120 common frames omitted
2025-08-11 17:28:15.800 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:28:16.367 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:28:16.372 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:28:23.383 [http-nio-8080-exec-10] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher002
2025-08-11 17:28:23.390 [http-nio-8080-exec-10] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:28:24.863 [http-nio-8080-exec-1] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher002
2025-08-11 17:28:24.867 [http-nio-8080-exec-1] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:31:39.158 [http-nio-8080-exec-2] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:31:44.588 [http-nio-8080-exec-6] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:31:46.332 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:31:46.333 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:31:46.339 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.339 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.403 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:31:46.409 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.435 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:31:46.442 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.470 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:31:46.476 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.501 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:31:46.507 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.524 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:31:46.526 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:31:46.532 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:46.532 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:47.307 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:31:47.312 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:31:48.870 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: PUT /api/admin/users/8/status -> 其他模块
2025-08-11 17:31:48.875 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=更新操作
2025-08-11 17:31:50.282 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: PUT /api/admin/users/2/status -> 其他模块
2025-08-11 17:31:50.288 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=更新操作
2025-08-11 17:31:53.963 [http-nio-8080-exec-8] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:31:53.967 [http-nio-8080-exec-8] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:32:13.014 [http-nio-8080-exec-10] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:32:13.018 [http-nio-8080-exec-10] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:32:14.108 [http-nio-8080-exec-1] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:32:14.111 [http-nio-8080-exec-1] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:32:14.588 [http-nio-8080-exec-3] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:32:14.591 [http-nio-8080-exec-3] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:34:27.712 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 17:34:27.861 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 17:34:27.869 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:34:27.871 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 17:34:27.877 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 17:34:32.894 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 23176 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 17:34:32.896 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 17:34:32.896 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 17:34:33.585 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 17:34:33.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-08-11 17:34:33.694 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 17:34:34.107 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 17:34:34.118 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 17:34:34.118 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 17:34:34.170 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 17:34:34.170 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1237 ms
2025-08-11 17:34:34.240 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 17:34:34.361 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 17:34:34.410 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 17:34:34.438 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 17:34:34.653 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 17:34:34.677 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 17:34:34.923 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@10e8c7a2
2025-08-11 17:34:34.925 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 17:34:34.970 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 17:34:34.985 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 17:34:35.243 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 17:34:35.248 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:34:35.357 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@68f75a35'
2025-08-11 17:34:35.491 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 17:34:35.513 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 17:34:35.528 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 17:34:35.545 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 17:34:35.560 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 17:34:35.584 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 17:34:35.600 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 17:34:35.615 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 17:34:35.629 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 17:34:35.642 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 17:34:35.657 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 17:34:35.671 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 17:34:35.680 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 17:34:35.691 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 17:34:35.703 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 17:34:35.718 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 17:34:35.733 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 17:34:35.749 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 17:34:36.386 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 17:34:37.081 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 17:34:37.094 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.637 seconds (process running for 5.232)
2025-08-11 17:34:37.096 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 17:34:37.097 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 17:34:37.097 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 17:34:37.198 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 17:34:37.784 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 17:34:37.784 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 17:34:37.785 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-11 17:34:37.949 [http-nio-8080-exec-1] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:34:37.981 [http-nio-8080-exec-1] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 17:37:15.253 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 17:37:15.417 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 17:37:15.425 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:37:15.427 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 17:37:15.432 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 17:37:17.873 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 23044 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 17:37:17.875 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 17:37:17.876 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 17:37:18.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 17:37:18.635 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 JPA repository interfaces.
2025-08-11 17:37:18.730 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 17:37:19.223 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 17:37:19.235 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 17:37:19.235 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 17:37:19.283 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 17:37:19.283 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1366 ms
2025-08-11 17:37:19.340 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 17:37:19.459 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 17:37:19.510 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 17:37:19.539 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 17:37:19.770 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 17:37:19.801 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 17:37:20.077 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4d0abb23
2025-08-11 17:37:20.079 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 17:37:20.125 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 17:37:20.140 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 17:37:20.443 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 17:37:20.447 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:37:20.556 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@197180a5'
2025-08-11 17:37:20.711 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 17:37:20.741 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 17:37:20.760 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 17:37:20.784 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 17:37:20.800 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 17:37:20.830 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 17:37:20.845 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 17:37:20.863 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 17:37:20.879 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 17:37:20.895 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 17:37:20.908 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 17:37:20.926 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 17:37:20.935 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 17:37:20.948 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 17:37:20.960 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 17:37:20.976 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 17:37:20.989 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 17:37:21.002 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 17:37:21.657 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 17:37:22.332 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 17:37:22.344 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.91 seconds (process running for 5.571)
2025-08-11 17:37:22.347 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 17:37:22.347 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 17:37:22.347 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 17:37:22.416 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 17:37:26.091 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 17:37:26.091 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 17:37:26.092 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-11 17:37:26.263 [http-nio-8080-exec-1] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:37:26.294 [http-nio-8080-exec-1] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 该账号已被禁用，请联系管理员
2025-08-11 17:37:35.369 [http-nio-8080-exec-2] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:37:39.298 [http-nio-8080-exec-4] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:37:39.303 [http-nio-8080-exec-4] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 该账号已被禁用，请联系管理员
2025-08-11 17:37:40.743 [http-nio-8080-exec-5] INFO  com.example.aieduforge.controller.AuthController - User login attempt: teacher001
2025-08-11 17:37:40.748 [http-nio-8080-exec-5] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 该账号已被禁用，请联系管理员
2025-08-11 17:37:47.717 [http-nio-8080-exec-6] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:37:49.324 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:37:49.335 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:37:49.343 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.346 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.456 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:37:49.465 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.490 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:37:49.498 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.519 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:37:49.525 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.549 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:37:49.557 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.575 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:37:49.577 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:37:49.582 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:49.585 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:50.607 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:37:50.615 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:51.885 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: PUT /api/admin/users/2/status -> 其他模块
2025-08-11 17:37:51.890 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=更新操作
2025-08-11 17:37:55.400 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:55.402 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:55.418 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:57.323 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:37:57.331 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:37:58.049 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:58.049 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:58.061 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:58.868 [http-nio-8080-exec-4] INFO  c.e.aieduforge.controller.AdminResourceController - Getting knowledge files with filters - keyword: null, subject: null, fileType: null
2025-08-11 17:37:58.868 [http-nio-8080-exec-4] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Searching knowledge with filters - keyword: null, subject: null, fileType: null
2025-08-11 17:37:58.878 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 17:37:58.895 [http-nio-8080-exec-4] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Found 12 knowledge files matching filters
2025-08-11 17:37:58.936 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/resources/knowledge -> 其他模块
2025-08-11 17:37:58.942 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:01.404 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/admin/all -> 其他模块
2025-08-11 17:38:01.410 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:02.312 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 17:38:02.330 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 17:38:07.721 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=提交操作
2025-08-11 17:38:07.760 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 17:38:10.804 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 17:38:10.810 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:13.706 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:38:13.711 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:13.733 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:38:13.738 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:13.755 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:38:13.759 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:13.777 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:38:13.782 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:13.795 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:38:13.797 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:38:13.799 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:13.800 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:14.676 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 17:38:14.682 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:19.267 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:38:19.269 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:38:19.273 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:19.273 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:49.273 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:38:49.274 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:38:49.278 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:38:49.278 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:08.799 [http-nio-8080-exec-4] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:40:10.299 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:40:10.299 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:40:10.305 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.305 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.356 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:40:10.362 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.384 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:40:10.390 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.406 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:40:10.411 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.429 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:40:10.434 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.449 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:40:10.451 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:40:10.454 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.455 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:10.920 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 17:40:10.926 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:40:11.898 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 17:40:11.903 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:40:12.474 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 17:40:12.479 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:40:14.042 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 17:40:14.047 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:40:16.667 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 17:40:16.671 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:40:16.850 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 17:40:16.855 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:40:28.674 [http-nio-8080-exec-10] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:40:39.114 [http-nio-8080-exec-2] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:41:41.729 [http-nio-8080-exec-6] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:45:47.320 [http-nio-8080-exec-2] ERROR c.e.aieduforge.exception.GlobalExceptionHandler - Unexpected error occurred
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource auth/login/demo-config.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at com.example.aieduforge.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:67)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$4(HandlerMappingIntrospector.java:267)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-08-11 17:45:54.151 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 17:45:54.267 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 17:45:54.274 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:45:54.275 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 17:45:54.278 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 17:49:54.948 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 24280 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 17:49:54.949 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 17:49:54.950 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 17:49:55.625 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 17:49:55.655 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 JPA repository interfaces.
2025-08-11 17:49:55.729 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 17:49:56.112 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 17:49:56.122 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 17:49:56.122 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 17:49:56.174 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 17:49:56.174 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1187 ms
2025-08-11 17:49:56.223 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 17:49:56.323 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 17:49:56.370 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 17:49:56.396 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 17:49:56.595 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 17:49:56.619 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 17:49:56.844 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@10e8c7a2
2025-08-11 17:49:56.845 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 17:49:56.884 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 17:49:56.899 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 17:49:57.137 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 17:49:57.146 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:49:57.242 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@68f75a35'
2025-08-11 17:49:57.362 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 17:49:57.385 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 17:49:57.398 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 17:49:57.413 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 17:49:57.424 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 17:49:57.445 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 17:49:57.457 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 17:49:57.469 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 17:49:57.480 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 17:49:57.493 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 17:49:57.506 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 17:49:57.518 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 17:49:57.526 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 17:49:57.537 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 17:49:57.545 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 17:49:57.556 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 17:49:57.567 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 17:49:57.580 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 17:49:58.166 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 17:49:58.746 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 17:49:58.757 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.217 seconds (process running for 4.779)
2025-08-11 17:49:58.759 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 17:49:58.760 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 17:49:58.760 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 17:49:58.843 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 17:50:00.302 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 17:50:00.302 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 17:50:00.304 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-11 17:50:03.342 [http-nio-8080-exec-5] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:50:06.699 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:50:06.710 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:50:06.717 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.717 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.834 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:50:06.842 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.872 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:50:06.880 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.901 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:50:06.909 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.939 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:50:06.947 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.966 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:50:06.970 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:50:06.974 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:06.976 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:07.537 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 17:50:07.537 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 17:50:07.547 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:07.547 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:12.494 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 17:50:12.500 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:50:31.699 [http-nio-8080-exec-10] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 17:50:33.620 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:50:33.624 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:50:33.627 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.630 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.690 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:50:33.698 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.723 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:50:33.729 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.747 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:50:33.754 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.777 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:50:33.783 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.797 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:50:33.798 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:50:33.802 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:33.803 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:34.738 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 17:50:34.738 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 17:50:34.744 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:34.744 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:50:35.659 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 17:50:35.664 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:50:35.798 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 17:50:35.803 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:50:36.669 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 17:50:36.674 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:51:03.606 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:51:03.607 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:51:03.611 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:51:03.611 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:51:34.279 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:51:34.282 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:51:34.286 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:51:34.286 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:52:04.283 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:52:04.285 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:52:04.288 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:52:04.288 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:52:41.999 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:52:42.003 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:52:42.006 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:52:42.007 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:52:45.357 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 17:52:45.363 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:52:45.928 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 17:52:45.933 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:52:49.753 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 17:52:49.758 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:52:51.162 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 17:52:51.166 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 17:53:03.611 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:53:03.613 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:53:03.617 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:53:03.617 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:53:34.283 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:53:34.284 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:53:34.287 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:53:34.288 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:54:04.278 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:54:04.280 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:54:04.283 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:54:04.284 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:55:06.276 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:55:06.277 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:55:06.280 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:55:06.280 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:05.771 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:56:05.774 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:56:05.778 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:05.779 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:34.268 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:56:34.270 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:56:34.273 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:34.273 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:45.894 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:56:45.900 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:45.923 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:56:45.928 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:45.944 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:56:45.950 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:45.969 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:56:45.975 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:45.991 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:56:45.992 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:56:45.996 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:56:45.996 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:57:03.618 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:57:03.620 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:57:03.623 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:57:03.624 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:57:34.285 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:57:34.287 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:57:34.291 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:57:34.291 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:04.277 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:58:04.278 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:58:04.282 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:04.282 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.219 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:58:52.220 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:58:52.224 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.224 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.254 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:58:52.258 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.284 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:58:52.289 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.314 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:58:52.319 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.334 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:58:52.339 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.352 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:58:52.353 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:58:52.356 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:58:52.356 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:18.893 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 17:59:19.104 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 17:59:19.112 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:59:19.114 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 17:59:19.120 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 17:59:24.521 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 27516 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 17:59:24.523 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 17:59:24.524 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 17:59:25.280 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 17:59:25.308 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-08-11 17:59:25.387 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 17:59:25.800 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 17:59:25.819 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 17:59:25.819 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 17:59:25.870 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 17:59:25.871 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1303 ms
2025-08-11 17:59:25.928 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 17:59:26.054 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 17:59:26.103 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 17:59:26.130 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 17:59:26.340 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 17:59:26.367 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 17:59:26.617 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6159fb3c
2025-08-11 17:59:26.619 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 17:59:26.664 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 17:59:26.681 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 17:59:26.954 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 17:59:26.960 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 17:59:27.068 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4b360a82'
2025-08-11 17:59:27.204 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 17:59:27.228 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 17:59:27.246 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 17:59:27.264 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 17:59:27.285 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 17:59:27.307 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 17:59:27.321 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 17:59:27.335 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 17:59:27.351 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 17:59:27.368 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 17:59:27.380 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 17:59:27.393 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 17:59:27.401 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 17:59:27.412 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 17:59:27.421 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 17:59:27.434 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 17:59:27.447 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 17:59:27.458 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 17:59:28.051 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 17:59:28.679 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 17:59:28.691 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.635 seconds (process running for 5.262)
2025-08-11 17:59:28.693 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 17:59:28.694 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 17:59:28.694 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 17:59:28.763 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 17:59:33.752 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 17:59:33.752 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 17:59:33.753 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-11 17:59:34.307 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 17:59:34.332 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 17:59:34.334 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.342 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.454 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 17:59:34.464 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.497 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 17:59:34.505 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.529 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 17:59:34.537 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.562 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 17:59:34.569 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.588 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 17:59:34.591 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 17:59:34.595 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:34.598 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:35.248 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 17:59:35.248 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 17:59:35.256 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:35.256 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 17:59:36.303 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 17:59:36.309 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:00:00.013 [scheduling-1] INFO  c.example.aieduforge.service.ScheduledTaskService - 开始更新系统统计数据...
2025-08-11 18:00:00.013 [scheduling-1] INFO  c.example.aieduforge.service.ScheduledTaskService - 系统统计数据更新完成
2025-08-11 18:00:08.644 [http-nio-8080-exec-6] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:00:23.674 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 18:00:23.813 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 18:00:23.820 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:00:23.822 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 18:00:23.827 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 18:00:26.721 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 29108 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 18:00:26.723 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 18:00:26.724 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 18:00:27.406 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 18:00:27.435 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-08-11 18:00:27.512 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 18:00:27.909 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 18:00:27.919 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 18:00:27.920 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 18:00:27.964 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 18:00:27.965 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1199 ms
2025-08-11 18:00:28.015 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 18:00:28.123 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 18:00:28.168 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 18:00:28.193 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 18:00:28.398 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 18:00:28.422 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 18:00:28.650 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d8fd077
2025-08-11 18:00:28.652 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 18:00:28.692 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 18:00:28.707 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 18:00:28.960 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 18:00:28.964 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:00:29.070 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@50e0b472'
2025-08-11 18:00:29.197 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 18:00:29.221 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 18:00:29.235 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 18:00:29.252 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 18:00:29.264 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 18:00:29.288 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 18:00:29.301 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 18:00:29.315 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 18:00:29.329 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 18:00:29.342 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 18:00:29.356 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 18:00:29.369 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 18:00:29.378 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 18:00:29.389 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 18:00:29.398 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 18:00:29.413 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 18:00:29.425 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 18:00:29.441 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 18:00:30.154 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 18:00:30.855 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 18:00:30.868 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.595 seconds (process running for 5.224)
2025-08-11 18:00:30.870 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 18:00:30.871 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 18:00:30.871 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 18:00:30.973 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 18:00:44.220 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 18:00:44.221 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 18:00:44.222 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-11 18:00:46.193 [http-nio-8080-exec-3] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:00:48.170 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:00:48.180 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:00:48.187 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.187 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.303 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 18:00:48.310 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.337 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 18:00:48.345 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.365 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 18:00:48.372 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.393 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 18:00:48.400 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.415 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 18:00:48.419 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 18:00:48.423 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.426 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.838 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:00:48.838 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:00:48.844 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:48.844 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:00:49.956 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 18:00:49.962 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:13.972 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 18:01:13.979 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:15.469 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 18:01:15.475 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:16.970 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 18:01:16.977 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:17.621 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 18:01:17.627 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:18.134 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:01:18.137 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:01:18.141 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:18.141 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:19.984 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 18:01:19.989 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:22.187 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:01:22.200 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:01:22.793 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:01:22.793 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:01:22.800 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:22.800 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:36.059 [http-nio-8080-exec-8] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:01:37.706 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:01:37.708 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:01:37.711 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.714 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.776 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 18:01:37.781 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.805 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 18:01:37.811 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.827 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 18:01:37.833 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.849 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 18:01:37.854 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.869 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 18:01:37.872 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 18:01:37.875 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:37.876 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:48.564 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:01:48.564 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:01:48.571 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:48.571 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:01:50.533 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/length -> 其他模块
2025-08-11 18:01:50.538 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:51.249 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/expire-minutes -> 其他模块
2025-08-11 18:01:51.254 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:54.080 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/expire-minutes -> 其他模块
2025-08-11 18:01:54.090 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:01:59.094 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/expire-minutes -> 其他模块
2025-08-11 18:01:59.100 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:02:07.703 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:02:07.708 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:02:07.709 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:02:07.714 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:02:26.893 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 18:02:26.901 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:02:28.140 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 18:02:28.151 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:02:38.487 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:02:38.494 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:02:38.520 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:02:38.528 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:02:38.555 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 18:02:39.366 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 18:02:39.378 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:02:39.381 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 18:02:39.389 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 18:02:52.886 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 17052 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 18:02:52.888 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 18:02:52.889 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 18:02:53.647 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 18:02:53.677 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 JPA repository interfaces.
2025-08-11 18:02:53.768 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 18:02:54.221 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 18:02:54.233 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 18:02:54.233 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 18:02:54.284 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 18:02:54.285 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1346 ms
2025-08-11 18:02:54.345 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 18:02:54.466 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 18:02:54.516 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 18:02:54.544 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 18:02:54.759 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 18:02:54.784 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 18:02:55.051 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4d0abb23
2025-08-11 18:02:55.052 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 18:02:55.099 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 18:02:55.116 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 18:02:55.413 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 18:02:55.417 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:02:55.538 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@197180a5'
2025-08-11 18:02:55.684 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 18:02:55.711 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 18:02:55.727 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 18:02:55.750 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 18:02:55.772 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 18:02:55.802 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 18:02:55.820 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 18:02:55.838 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 18:02:55.858 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 18:02:55.877 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 18:02:55.891 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 18:02:55.914 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 18:02:55.925 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 18:02:55.941 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 18:02:55.956 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 18:02:55.974 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 18:02:55.992 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 18:02:56.008 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 18:02:56.794 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 18:02:57.561 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 18:02:57.579 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 5.157 seconds (process running for 5.803)
2025-08-11 18:02:57.582 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 18:02:57.583 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 18:02:57.583 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 18:02:57.692 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 18:02:57.728 [http-nio-8080-exec-3] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 18:02:57.728 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 18:02:57.729 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-11 18:03:10.579 [http-nio-8080-exec-6] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:03:12.492 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:03:12.500 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:03:12.509 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.510 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.622 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 18:03:12.630 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.658 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 18:03:12.665 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.685 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 18:03:12.692 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.715 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 18:03:12.721 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.740 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 18:03:12.744 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 18:03:12.747 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:12.750 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:13.188 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:03:13.188 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:03:13.195 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:13.195 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:43.285 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:03:43.287 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:03:43.291 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:03:43.291 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:04:13.277 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:04:13.280 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:04:13.284 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:04:13.285 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:04:43.284 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:04:43.286 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:04:43.291 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:04:43.293 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:05:13.277 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:05:13.280 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:05:13.283 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:05:13.284 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:05:43.273 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:05:43.275 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:05:43.278 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:05:43.278 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:06:19.039 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:06:19.041 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:06:19.044 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:06:19.045 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:06:43.284 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:06:43.286 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:06:43.290 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:06:43.290 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:07:13.283 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:07:13.286 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:07:13.289 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:07:13.291 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:07:18.614 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:07:18.614 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:07:18.622 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:07:18.622 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:07:43.279 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:07:43.282 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:07:43.286 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:07:43.286 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:13.282 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:08:13.284 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:08:13.286 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:13.287 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:18.011 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:08:18.011 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:08:18.035 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:18.035 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:42.447 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:08:42.450 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:08:42.454 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:42.455 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:08:43.116 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 18:08:43.121 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:08:44.328 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 18:08:44.333 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:09:13.283 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:09:13.285 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:09:13.290 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:09:13.290 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:09:43.271 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:09:43.271 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:09:43.277 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:09:43.277 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:10:13.271 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:10:13.271 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:10:13.274 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:10:13.274 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:10:42.446 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:10:42.447 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:10:42.451 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:10:42.451 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:11:13.270 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:11:13.271 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:11:13.275 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:11:13.275 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:11:43.271 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:11:43.273 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:11:43.276 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:11:43.276 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:12:31.992 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:12:31.994 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:12:31.999 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:12:32.000 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:12:36.284 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 18:12:36.456 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 18:12:36.465 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:12:36.468 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 18:12:36.474 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 18:12:41.913 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 27792 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 18:12:41.915 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 18:12:41.916 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 18:12:42.578 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 18:12:42.615 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 JPA repository interfaces.
2025-08-11 18:12:42.701 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 18:12:43.129 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 18:12:43.140 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 18:12:43.140 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 18:12:43.191 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 18:12:43.192 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1240 ms
2025-08-11 18:12:43.243 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 18:12:43.370 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 18:12:43.425 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 18:12:43.452 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 18:12:43.658 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 18:12:43.680 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 18:12:43.917 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4544ab46
2025-08-11 18:12:43.919 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 18:12:43.966 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 18:12:43.984 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 18:12:44.231 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 18:12:44.235 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:12:44.334 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@45017263'
2025-08-11 18:12:44.457 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 18:12:44.479 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 18:12:44.494 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 18:12:44.510 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 18:12:44.522 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 18:12:44.545 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 18:12:44.557 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 18:12:44.571 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 18:12:44.583 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 18:12:44.596 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 18:12:44.608 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 18:12:44.623 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 18:12:44.631 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 18:12:44.642 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 18:12:44.652 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 18:12:44.665 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 18:12:44.677 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 18:12:44.690 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 18:12:45.281 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 18:12:45.910 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 18:12:45.921 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.467 seconds (process running for 5.112)
2025-08-11 18:12:45.923 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 18:12:45.924 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 18:12:45.924 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 18:12:45.995 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 18:13:13.315 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 18:13:13.316 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 18:13:13.317 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-11 18:13:13.520 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:13:13.524 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:13:13.540 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:13:13.540 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:13:43.274 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:13:43.277 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:13:43.280 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:13:43.280 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:17:18.677 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:17:18.683 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:17:18.688 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:17:18.692 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:17:18.722 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:17:18.722 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:17:18.730 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:17:18.730 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:17:18.730 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:17:18.738 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:17:59.866 [http-nio-8080-exec-3] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:18:01.618 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:18:01.621 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:18:01.623 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.626 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.726 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 18:18:01.730 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.756 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 18:18:01.766 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.786 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 18:18:01.795 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.811 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 18:18:01.817 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.836 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 18:18:01.836 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 18:18:01.841 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:01.841 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:02.312 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:18:02.314 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:18:02.314 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:18:02.318 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:02.318 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:02.318 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:03.362 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/captcha/toggle -> 其他模块
2025-08-11 18:18:03.368 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:18:04.833 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts/toggle -> 其他模块
2025-08-11 18:18:04.839 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:18:17.821 [http-nio-8080-exec-1] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:18:17.897 [http-nio-8080-exec-1] WARN  c.e.aieduforge.exception.GlobalExceptionHandler - Business error: 用户名或密码错误
2025-08-11 18:18:22.151 [http-nio-8080-exec-2] INFO  com.example.aieduforge.controller.AuthController - User login attempt: admin
2025-08-11 18:18:23.689 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:18:23.691 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:18:23.695 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.695 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.741 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/users -> 其他模块
2025-08-11 18:18:23.747 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.771 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/latest -> 其他模块
2025-08-11 18:18:23.777 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.797 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/teacher-usage -> 其他模块
2025-08-11 18:18:23.802 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.820 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/student-usage -> 其他模块
2025-08-11 18:18:23.825 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.838 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/active-users -> 其他模块
2025-08-11 18:18:23.838 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/dashboard/trends -> 其他模块
2025-08-11 18:18:23.844 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:23.844 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:54.272 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:18:54.273 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:18:54.276 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:18:54.276 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:24.177 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:19:24.179 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:19:24.184 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:24.185 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:25.305 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:19:25.312 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:19:25.312 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:19:25.314 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:25.320 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:25.320 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:38.246 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:19:38.252 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:19:54.270 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:19:54.271 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:19:54.275 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:19:54.275 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:20:23.699 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:20:23.701 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:20:23.705 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:20:23.705 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:20:25.970 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:25.984 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:29.718 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:32.937 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:33.974 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:38.032 [http-nio-8080-exec-9] INFO  c.example.aieduforge.controller.AiModelController - Admin admin set AI model 2 as active
2025-08-11 18:20:38.038 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=提交操作
2025-08-11 18:20:38.063 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:39.601 [http-nio-8080-exec-10] INFO  c.example.aieduforge.controller.AiModelController - Admin admin set AI model 3 as active
2025-08-11 18:20:39.607 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=提交操作
2025-08-11 18:20:39.635 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:40.629 [http-nio-8080-exec-2] INFO  c.example.aieduforge.controller.AiModelController - Admin admin set AI model 1 as active
2025-08-11 18:20:40.635 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=提交操作
2025-08-11 18:20:40.663 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:20:50.527 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/admin/all -> 其他模块
2025-08-11 18:20:50.534 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:20:53.686 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:20:53.694 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:20:53.695 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:20:53.702 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:00.336 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/notifications/publish -> 其他模块
2025-08-11 18:21:00.342 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:21:00.374 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/admin/all -> 其他模块
2025-08-11 18:21:00.380 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:10.569 [http-nio-8080-exec-1] INFO  c.e.aieduforge.controller.AdminResourceController - Getting knowledge files with filters - keyword: null, subject: null, fileType: null
2025-08-11 18:21:10.569 [http-nio-8080-exec-1] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Searching knowledge with filters - keyword: null, subject: null, fileType: null
2025-08-11 18:21:10.581 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 18:21:10.586 [http-nio-8080-exec-1] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Found 12 knowledge files matching filters
2025-08-11 18:21:10.613 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/resources/knowledge -> 其他模块
2025-08-11 18:21:10.618 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:11.020 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/admin/all -> 其他模块
2025-08-11 18:21:11.025 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:23.680 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:21:23.682 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:21:23.685 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:23.685 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:49.052 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/notifications/publish -> 其他模块
2025-08-11 18:21:49.058 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:21:49.094 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/admin/all -> 其他模块
2025-08-11 18:21:49.101 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:53.684 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:21:53.686 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:21:53.690 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:53.690 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:53.711 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:21:53.716 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:21:55.304 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/notifications/20/read -> 其他模块
2025-08-11 18:21:55.309 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:21:59.369 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/notifications/21/read -> 其他模块
2025-08-11 18:21:59.373 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:22:06.875 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:22:06.882 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:21.141 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:22:21.141 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:22:21.141 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:22:21.149 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:21.149 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:21.148 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:23.683 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:22:23.687 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:22:23.689 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:23.689 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:29.576 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 18:22:29.659 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 18:22:29.666 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:22:29.666 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 18:22:29.670 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-11 18:22:34.152 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Starting AiEduForgeApplication using Java 17.0.9 with PID 18652 (E:\Java Project\AiEduForge\target\classes started by CuiHao in E:\Java Project\AiEduForge)
2025-08-11 18:22:34.153 [main] DEBUG com.example.aieduforge.AiEduForgeApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-11 18:22:34.154 [main] INFO  com.example.aieduforge.AiEduForgeApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-11 18:22:34.926 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-11 18:22:34.966 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 JPA repository interfaces.
2025-08-11 18:22:35.059 [main] DEBUG org.apache.ibatis.logging.LogFactory - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-08-11 18:22:35.474 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 18:22:35.485 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 18:22:35.486 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-11 18:22:35.542 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-11 18:22:35.542 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1351 ms
2025-08-11 18:22:35.611 [main] DEBUG c.e.aieduforge.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-08-11 18:22:35.733 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-11 18:22:35.776 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-08-11 18:22:35.800 [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-08-11 18:22:36.000 [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-11 18:22:36.023 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-11 18:22:36.252 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2052f095
2025-08-11 18:22:36.254 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-11 18:22:36.297 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-11 18:22:36.315 [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.1
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-08-11 18:22:36.581 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-11 18:22:36.587 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:22:36.684 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@6054ac20'
2025-08-11 18:22:36.820 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AiAnswerMapper.xml]'
2025-08-11 18:22:36.844 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AIKnowledgeDataMapper.xml]'
2025-08-11 18:22:36.859 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\AnalysisResultsMapper.xml]'
2025-08-11 18:22:36.877 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseMapper.xml]'
2025-08-11 18:22:36.890 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\CourseOutlineMapper.xml]'
2025-08-11 18:22:36.914 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamAnswerMapper.xml]'
2025-08-11 18:22:36.925 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\ExamQuestionMapper.xml]'
2025-08-11 18:22:36.938 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\KnowledgeBaseMapper.xml]'
2025-08-11 18:22:36.950 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\LearningAnalyticsMapper.xml]'
2025-08-11 18:22:36.964 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\PracticeRecordMapper.xml]'
2025-08-11 18:22:36.975 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\StudentQuestionMapper.xml]'
2025-08-11 18:22:36.989 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysNotificationMapper.xml]'
2025-08-11 18:22:36.996 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysRoleMapper.xml]'
2025-08-11 18:22:37.009 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\SysUserMapper.xml]'
2025-08-11 18:22:37.019 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingContentMapper.xml]'
2025-08-11 18:22:37.034 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\TeachingEfficiencyMapper.xml]'
2025-08-11 18:22:37.047 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UsageStatisticsMapper.xml]'
2025-08-11 18:22:37.060 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [E:\Java Project\AiEduForge\target\classes\mapper\UserNotificationMapper.xml]'
2025-08-11 18:22:37.663 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-11 18:22:38.271 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-11 18:22:38.277 [main] INFO  com.example.aieduforge.AiEduForgeApplication - Started AiEduForgeApplication in 4.601 seconds (process running for 5.254)
2025-08-11 18:22:38.283 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 开始清理过期验证码
2025-08-11 18:22:38.283 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL start create  ...  
2025-08-11 18:22:38.283 [main] DEBUG c.b.mybatisplus.autoconfigure.DdlApplicationRunner -   ...  DDL end create  ...  
2025-08-11 18:22:38.350 [scheduling-1] DEBUG com.example.aieduforge.task.CaptchaCleanupTask - 过期验证码清理完成
2025-08-11 18:22:41.257 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 18:22:41.257 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-11 18:22:41.260 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-11 18:22:41.749 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:22:41.772 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:41.777 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:22:41.785 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:22:41.785 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:22:41.788 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:41.796 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:41.798 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:41.799 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:22:41.806 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:22:49.058 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: POST /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:22:49.066 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=提交操作
2025-08-11 18:23:12.288 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:23:12.292 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:23:12.295 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:23:12.300 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:23:42.283 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:23:42.287 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:23:42.290 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:23:42.291 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:24:12.273 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:24:12.280 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:24:12.280 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:24:12.286 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:24:42.273 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:24:42.278 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:24:42.282 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:24:42.284 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:24:49.295 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:24:49.303 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:24:49.306 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:24:49.316 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:00.633 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:25:00.712 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:25:01.554 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:25:01.554 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:25:01.561 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:01.561 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:03.336 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:25:03.344 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:25:03.861 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:25:03.864 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:25:03.869 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:03.869 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:12.272 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:25:12.282 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:25:12.282 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:12.282 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:32.067 [http-nio-8080-exec-10] INFO  c.e.aieduforge.controller.AdminResourceController - Getting knowledge files with filters - keyword: null, subject: null, fileType: null
2025-08-11 18:25:32.068 [http-nio-8080-exec-10] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Searching knowledge with filters - keyword: null, subject: null, fileType: null
2025-08-11 18:25:32.081 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 18:25:32.087 [http-nio-8080-exec-10] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Found 12 knowledge files matching filters
2025-08-11 18:25:32.122 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/resources/knowledge -> 其他模块
2025-08-11 18:25:32.131 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:32.695 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:25:32.703 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:25:33.815 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:25:33.815 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:25:33.820 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:33.820 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:41.728 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:25:41.730 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:25:41.733 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:25:41.734 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:01.584 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:01.595 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:02.044 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:26:02.044 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:26:02.051 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:02.051 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:06.026 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:26:06.026 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:26:06.037 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:06.045 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:09.617 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:09.622 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:10.163 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:26:10.163 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:26:10.169 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:10.169 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:12.278 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:26:12.279 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:26:12.279 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:12.284 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:26.432 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:26.450 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:34.195 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:26:34.195 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:26:34.202 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:34.202 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:38.018 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:38.025 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:26:38.957 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:26:38.957 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:26:38.962 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:38.962 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:41.726 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:26:41.727 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:26:41.730 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:26:41.730 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:27:12.279 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:27:12.281 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:27:12.284 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:27:12.286 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:27:41.741 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:27:41.742 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:27:41.746 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:27:41.747 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:11.731 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:28:11.733 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:28:11.736 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:11.737 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:15.682 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:28:15.689 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:28:16.485 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/admin/all -> 其他模块
2025-08-11 18:28:16.492 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:17.263 [http-nio-8080-exec-9] INFO  c.e.aieduforge.controller.AdminResourceController - Getting knowledge files with filters - keyword: null, subject: null, fileType: null
2025-08-11 18:28:17.263 [http-nio-8080-exec-9] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Searching knowledge with filters - keyword: null, subject: null, fileType: null
2025-08-11 18:28:17.273 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=课程模块, action=页面访问
2025-08-11 18:28:17.280 [http-nio-8080-exec-9] INFO  c.e.a.service.impl.ResourceManagementServiceImpl - Found 12 knowledge files matching filters
2025-08-11 18:28:17.307 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/resources/knowledge -> 其他模块
2025-08-11 18:28:17.312 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:19.829 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/resources/knowledge/17 -> 其他模块
2025-08-11 18:28:19.852 [http-nio-8080-exec-1] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:28.462 [http-nio-8080-exec-3] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:28:28.467 [http-nio-8080-exec-2] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=AI助手, action=页面访问
2025-08-11 18:28:29.969 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:28:29.970 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:28:29.975 [http-nio-8080-exec-7] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:29.975 [http-nio-8080-exec-4] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:42.283 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/unread-count -> 其他模块
2025-08-11 18:28:42.286 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/notifications/my -> 其他模块
2025-08-11 18:28:42.288 [http-nio-8080-exec-6] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:28:42.289 [http-nio-8080-exec-5] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:29:01.296 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login/demo-accounts -> 其他模块
2025-08-11 18:29:01.296 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/login -> 其他模块
2025-08-11 18:29:01.296 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.interceptor.UserActivityInterceptor - Unclassified request: GET /api/admin/system-settings/captcha -> 其他模块
2025-08-11 18:29:01.304 [http-nio-8080-exec-9] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:29:01.304 [http-nio-8080-exec-10] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:29:01.304 [http-nio-8080-exec-8] DEBUG c.e.aieduforge.service.impl.StatisticsServiceImpl - User activity recorded: userId=1, module=其他模块, action=页面访问
2025-08-11 18:29:03.347 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-11 18:29:03.492 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-11 18:29:03.498 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-11 18:29:03.500 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-11 18:29:03.504 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
