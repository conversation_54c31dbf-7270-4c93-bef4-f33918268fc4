# AiEduForge 任务书  <span style="vertical-align:super;">© 2025</span>

<span style="font-size:1.4em; color:#3366CC; font-weight:bold; letter-spacing:2pt;">智能教育，赋能未来</span>

- ## 技术框架【简介，详见技术文档】：


​	**后端**:SpringBoot3.5.3+MySQL+JWT+SpringSecurity+Mybatis-Plus+MySql 等

​	**AI**:本地 Ollama 部署的模型：gemma3:1b

​	**前端**:Vue3+Element-Plus 等

​	**构建**:Maven+Vite

​		【详细】https://www.cnsoftbei.com/content-3-1095-1.html




```
    ___     _   ______    __      ______                   
   /   |   (_) / ____/___/ /_  __/ ____/___  _________ ____ 
  / /| |  / / / __/ / __  / / / / /_  / __ \/ ___/ __ `/ _ \
 / ___ | / / / /___/ /_/ / /_/ / __/ / /_/ / /  / /_/ /  __/
/_/  |_|/_/ /_____/\__,_/\__,_/_/    \____/_/   \__, /\________/
```

## 功能需求：

### 1.基础信息：

**名称**：基于开源 AI 大模型的教学实训智能体软件
**实现目标：** 本赛题旨在开发一个基于开源大模型的教学实训智能体软件，帮助教师生成课前备课设计、课后检测问答，提升效率与效果，提供学生全时在线练习与指导，实现教学相长。

**实用价值：** 解决传统实训教学中教师手动设计任务、批改效率低、学生缺乏针对性指导等问题，推动实训教学向智能化、自适应化转型，降低教学成本，提升学习效果。

**涉及技术：** 自然语言处理、大模型本地知识库技术、自动化评估技术等

**整体要求：** 基于开源的大模型技术，开发一个智能化教学实训智能体软件，实现教师智能备课设计、智能考核、学情数据分析与可视化、学生实时学习问答、资源推荐、练习纠错等，推动教育资源智能化升级。

### 2.三大用户功能：

#### （1）**教师侧：**

**备课与设计：** 根据所提供的本地课程大纲、课程知识库文档等自动设计教学内容，包括知识讲解、实训练习与指导、时间分布等。

**考核内容生成：** 根据教学内容自动生成考核题目及参考答案，考核题目种类可多样化，根据学科设计，如计算机类可设计相关编程题和答案

**学情数据分析：** 对学生提交的答案进行自动化检测，提供错误定位与修正建议。对学生整体数据进行分析，总结知识掌握情况与教学建议。

#### （2）**学生侧：**

**在线学习助手 ：** 对学生的提出的问题，结合教学内容进行解答;

**实时练习评测助手**：根据学生历史练习情况，以及学生的练习要求，生成随练题目，并对练习纠错

#### （3）**管理侧：**

**用户管理**：管理员/教师/学生等用户的基本管理课件

**资源管理**：按学科列表教师备课产生的课件、练习等资源，可以导出。

**大屏概览**：教师使用次数统计/活跃板块(当日/本周)·学生使用次数统计/活跃板块(当日/本周)

**教学效率指数**(备课与修正耗时、课后练习设计与修正耗时、课程优化方向(如：某学科通过率持续偏低)

**学生学习效果**(平均正确率趋势、知识点掌握情况，高频错误知识点等)
AI 教学助手答案分析栏目，整体优化，这里主要任务是

**学情数据分析**：对学生提交的答案进行自动化检测，提供错误定位与修正建议。对学生整体数据进行分析，总结知识掌握情况与教学建议。

