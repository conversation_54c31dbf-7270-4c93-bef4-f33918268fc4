# AiEduForge API接口文档

<span style="font-size:1.4em; color:#CC0000; font-weight:bold; letter-spacing:2pt;">注意,此项目版权已登记,不得盗用.</span>

## 基础说明

### 接口规范
- 基础路径: `http://localhost:8080`
- 请求方式: REST风格
- 数据格式: JSON
- 认证方式: JWT <PERSON> (在Header中携带 Authorization: Bearer {token})

### 响应格式
```json
{
  "code": 200,          // 状态码：200成功，其他表示失败
  "message": "success", // 提示信息
  "data": {}           // 响应数据
}
```

### 权限说明
- **ADMIN**: 管理员权限，可访问所有接口
- **TEACHER**: 教师权限，可访问教学相关接口
- **STUDENT**: 学生权限，可访问学习相关接口

## 1. 认证接口 (/auth)

### 1.1 用户登录
- 请求路径：POST /auth/login
- 请求参数：
```json
{
  "username": "string",
  "password": "string"
}
```
- 响应数据：
```json
{
  "token": "string",
  "userInfo": {
    "id": "number",
    "username": "string",
    "role": "string",
    "avatar": "string"
  }
}
```

### 1.2 用户注册
- 请求路径：POST /auth/register
- 请求参数：
```json
{
  "username": "string",
  "password": "string",
  "email": "string",
  "role": "string"
}
```

### 1.3 刷新Token
- 请求路径：POST /auth/refresh
- 请求头：需要携带原token
- 响应数据：
```json
{
  "token": "string"
}
```

## 2. 管理员接口 (/admin)

### 2.1 统计大屏

#### 2.1.1 总览数据
- 请求路径：GET /admin/dashboard/overview
- 响应数据：
```json
{
  "totalUsers": "number",
  "activeUsers": "number",
  "totalCourses": "number",
  "totalLearningTime": "number"
}
```

#### 2.1.2 使用统计
- 请求路径：GET /admin/dashboard/usage
- 请求参数：
```json
{
  "startDate": "string",
  "endDate": "string"
}
```

### 2.2 用户管理

#### 2.2.1 获取用户列表
- 请求路径：GET /admin/users
- 请求参数：
```json
{
  "page": "number",
  "size": "number",
  "role": "string?",
  "keyword": "string?"
}
```

#### 2.2.2 创建用户
- 请求路径：POST /admin/users
- 请求参数：
```json
{
  "username": "string",
  "password": "string",
  "email": "string",
  "role": "string",
  "status": "number"
}
```

#### 2.2.3 更新用户
- 请求路径：PUT /admin/users/{id}
- 请求参数：同创建用户

#### 2.2.4 删除用户
- 请求路径：DELETE /admin/users/{id}

## 3. 教师接口 (/teacher)

### 3.1 课程管理

#### 3.1.1 获取课程列表
- 请求路径：GET /teacher/courses
- 响应数据：
```json
{
  "total": "number",
  "list": [{
    "id": "number",
    "name": "string",
    "description": "string",
    "status": "number",
    "createTime": "string"
  }]
}
```

#### 3.1.2 创建课程
- 请求路径：POST /teacher/courses
- 请求参数：
```json
{
  "name": "string",
  "description": "string",
  "cover": "string",
  "status": "number"
}
```

### 3.2 教学内容

#### 3.2.1 获取教学内容
- 请求路径：GET /teacher/content/course/{courseId}

#### 3.2.2 创建教学内容
- 请求路径：POST /teacher/content
- 请求参数：
```json
{
  "courseId": "number",
  "title": "string",
  "content": "string",
  "type": "string"
}
```

## 4. AI接口 (/ai)

### 4.1 内容生成

#### 4.1.1 生成教学内容
- 请求路径：POST /ai/generate/content
- 请求参���：
```json
{
  "subject": "string",
  "topic": "string",
  "level": "string",
  "requirements": "string"
}
```

#### 4.1.2 生成试题
- 请求路径：POST /ai/generate/questions
- 请求参数：
```json
{
  "teachingContent": "string",
  "questionType": "string",
  "difficulty": "string",
  "count": "number",
  "courseId": "number"
}
```

### 4.2 智能分析

#### 4.2.1 分析学生答案
- 请求路径：POST /ai/analyze/student-answer
- 请求参数：
```json
{
  "questionId": "number",
  "studentAnswer": "string",
  "standardAnswer": "string"
}
```

## 5. 学生接口 (/student)

### 5.1 课程学习

#### 5.1.1 获取可选课程
- 请求路径：GET /student/courses/available
- 请求参数：
```json
{
  "page": "number",
  "size": "number",
  "keyword": "string?"
}
```

#### 5.1.2 选课
- 请求路径：POST /student/courses/select/{courseId}

### 5.2 学习记录

#### 5.2.1 获取学习进度
- 请求路径：GET /student/learning/progress/{courseId}

#### 5.2.2 提交练习答案
- 请求路径：POST /student/practice/submit
- 请求参数：
```json
{
  "questionId": "number",
  "answer": "string",
  "spendTime": "number"
}
```

## 6. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200    | 成功 |
| 400    | 请求参数错误 |
| 401    | 未授权 |
| 403    | 权限不足 |
| 404    | 资源不存在 |
| 500    | 服务器内部错误 |

## 7. 注意事项

1. 所有需要认证的接口必须在请求头中携带token
2. 分页接口默认page从1开始，size默认为10
3. 文件上传接口统一使用multipart/form-data格式
4. 时间格式统一使用ISO 8601标准：YYYY-MM-DDTHH:mm:ss.sssZ

## 8. 更新日志

### v1.0.0 (2025-07-13)
- 初始版本发布
- 完成基础CRUD接口
- 实现AI辅助教学相关接口

---

如有疑问，请联系技术支持团队：<EMAIL>

## 9. 知识库管理接口 (/knowledge)

### 9.1 知识点管理

#### 9.1.1 获取知识点列表
- 请求路径：GET /knowledge/points
- 请求参数：
```json
{
  "courseId": "number",
  "page": "number",
  "size": "number"
}
```
- 响应数据：
```json
{
  "total": "number",
  "list": [{
    "id": "number",
    "name": "string",
    "description": "string",
    "difficulty": "number",
    "parentId": "number"
  }]
}
```

### 9.2 课程大纲

#### 9.2.1 获取课程大纲
- 请求路径：GET /knowledge/outline/{courseId}
- 响应数据：
```json
{
  "id": "number",
  "courseId": "number",
  "chapters": [{
    "id": "number",
    "title": "string",
    "sections": [{
      "id": "number",
      "title": "string",
      "content": "string",
      "order": "number"
    }],
    "order": "number"
  }]
}
```

## 10. 学习分析接口 (/analytics)

### 10.1 学习效果分析

#### 10.1.1 获取个人学习报告
- 请求路径：GET /analytics/personal-report
- 请求参数：
```json
{
  "studentId": "number",
  "courseId": "number",
  "startDate": "string",
  "endDate": "string"
}
```
- 响应数据：
```json
{
  "totalTime": "number",
  "completionRate": "number",
  "correctRate": "number",
  "weakPoints": ["string"],
  "suggestions": ["string"]
}
```

### 10.2 教学效果分析

#### 10.2.1 获取班级学习概况
- 请求路径：GET /analytics/class-overview
- 请求参数：
```json
{
  "courseId": "number",
  "classId": "number"
}
```

## 11. 考试管理接口 (/exam)

### 11.1 试卷管理

#### 11.1.1 创建试卷
- 请求路径：POST /exam/papers
- 请求参数：
```json
{
  "title": "string",
  "courseId": "number",
  "totalScore": "number",
  "duration": "number",
  "startTime": "string",
  "endTime": "string",
  "questions": [{
    "id": "number",
    "score": "number",
    "type": "string",
    "difficulty": "string"
  }]
}
```

#### 11.1.2 获取试卷详情
- 请求路径：GET /exam/papers/{paperId}
- 响应数据：包含试卷完��信息和题目详情

### 11.2 考试记录

#### 11.2.1 提交试卷
- 请求路径：POST /exam/submit
- 请求参数：
```json
{
  "paperId": "number",
  "answers": [{
    "questionId": "number",
    "answer": "string",
    "spendTime": "number"
  }]
}
```

## 12. 系统监控接口 (/monitor)

### 12.1 系统状态
- 请求路径：GET /monitor/status
- ���应数据：
```json
{
  "cpuUsage": "number",
  "memoryUsage": "number",
  "diskUsage": "number",
  "onlineUsers": "number",
  "requestsPerMinute": "number"
}
```

### 12.2 操作日志
- 请求路径：GET /monitor/operation-logs
- 请求参数：
```json
{
  "startTime": "string",
  "endTime": "string",
  "operator": "string?",
  "type": "string?",
  "page": "number",
  "size": "number"
}
```

## 13. 文件管理接口 (/file)

### 13.1 文件上传
- 请求路径：POST /file/upload
- 请求格式：multipart/form-data
- 请求参数：
  - file: 文件对象
  - type: 文件类型(image/video/document)
  - moduleId: 所属模块ID
- 响应数据：
```json
{
  "fileId": "string",
  "url": "string",
  "size": "number",
  "type": "string"
}
```

### 13.2 文件删除
- 请求路径：DELETE /file/{fileId}

---




## 8. 通知系统接口 (/notifications)

### 8.1 获取用户通知
- 请求路径：GET /notifications/my
- 权限要求：需要登录
- 请求参数：
```json
{
  "page": "number",     // 页码，默认1
  "size": "number"      // 每页大小，默认10
}
```
- 响应数据：
```json
{
  "notifications": [{
    "id": "number",
    "title": "string",
    "content": "string",
    "type": "number",        // 1-系统通知，2-课程通知，3-作业通知
    "priority": "number",    // 1-普通，2-重要，3-紧急
    "is_read": "boolean",
    "read_time": "string",
    "create_time": "string"
  }],
  "total": "number",
  "page": "number",
  "size": "number",
  "totalPages": "number"
}
```

### 8.2 获取未读通知数量
- 请求路径：GET /notifications/unread-count
- 权限要求：需要登录
- 响应数据：
```json
{
  "data": "number"  // 未读通知数量
}
```

### 8.3 标记通知为已读
- 请求路径：POST /notifications/{id}/read
- 权限要求：需要登录
- 路径参数：id - 通知ID

### 8.4 标记所有通知为已读
- 请求路径：POST /notifications/read-all
- 权限要求：需要登录

### 8.5 发布通知（管理员）
- 请求路径：POST /notifications/publish
- 权限要求：ADMIN
- 请求参数：
```json
{
  "title": "string",
  "content": "string",
  "type": "number",        // 1-系统通知，2-课程通知，3-作业通知
  "targetRole": "string",  // ALL, TEACHER, STUDENT, ADMIN
  "priority": "number"     // 1-普通，2-重要，3-紧急
}
```

### 8.6 获取最新系统通知
- 请求路径：GET /notifications/latest
- 权限要求：需要登录
- 请求参数：
```json
{
  "limit": "number"  // 获取数量，默认5
}
```

### 8.7 获取通知详情
- 请求路径：GET /notifications/{id}
- 权限要求：需要登录
- 路径参数：id - 通知ID

### 8.8 删除通知（管理员）
- 请求路径：DELETE /notifications/{id}
- 权限要求：ADMIN
- 路径参数：id - 通知ID

### 8.9 获取所有通知（管理员）
- 请求路径：GET /notifications/admin/all
- 权限要求：ADMIN
- 请求参数：
```json
{
  "page": "number",
  "size": "number",
  "type": "number?",       // 通知类型筛选
  "targetRole": "string?", // 目标角色筛选
  "priority": "number?"    // 优先级筛选
}
```

## 9. 通知统计接口 (/notification-stats)

### 9.1 获取通知概览统计
- 请求路径：GET /notification-stats/overview
- 权限要求：ADMIN
- 响应数据：
```json
{
  "totalNotifications": "number",
  "todaySent": "number",
  "todayRead": "number",
  "todayReadRate": "number",
  "sentGrowthRate": "number",
  "readGrowthRate": "number",
  "onlineUsers": "number"
}
```

### 9.2 获取通知发送趋势
- 请求路径：GET /notification-stats/trend
- 权限要求：ADMIN
- 请求参数：
```json
{
  "days": "number"  // 统计天数，默认7天
}
```

### 9.3 获取通知类型分布
- 请求路径：GET /notification-stats/type-distribution
- 权限要求：ADMIN

### 9.4 获取用户阅读统计
- 请求路径：GET /notification-stats/read-stats
- 权限要求：ADMIN

### 9.5 获取WebSocket连接统计
- 请求路径：GET /notification-stats/websocket-stats
- 权限要求：ADMIN

## 10. AI增强接口 (/ai)

### 10.1 智能问答
- 请求路径：POST /ai/chat
- 权限要求：需要登录
- 请求参数：
```json
{
  "message": "string",
  "context": "string?",    // 上下文信息
  "courseId": "number?"    // 相关课程ID
}
```

### 10.2 生成教学内容
- 请求路径：POST /ai/generate/content
- 权限要求：TEACHER
- 请求参数：
```json
{
  "subject": "string",
  "topic": "string",
  "level": "string",
  "requirements": "string"
}
```

### 10.3 生成练习题目
- 请求路径：POST /ai/generate/questions
- 权限要求：TEACHER
- 请求参数：
```json
{
  "teachingContent": "string",
  "questionType": "string",
  "difficulty": "string",
  "count": "number",
  "courseId": "number"
}
```

### 10.4 分析学生答案
- 请求路径：POST /ai/analyze/answer
- 权限要求：TEACHER
- 请求参数：
```json
{
  "questionId": "number",
  "studentAnswer": "string",
  "standardAnswer": "string"
}
```

## 11. 系统健康检查接口 (/health)

### 11.1 系统状态检查
- 请求路径：GET /health/status
- 权限要求：无
- 响应数据：
```json
{
  "status": "string",      // UP/DOWN
  "timestamp": "string",
  "services": {
    "database": "string",  // UP/DOWN
    "redis": "string",     // UP/DOWN
    "ai": "string"         // UP/DOWN
  }
}
```

## 12. WebSocket接口

### 12.1 通知推送
- 连接地址：ws://localhost:8080/websocket/notification/{userId}
- 认证方式：URL参数传递用户ID
- 消息格式：
```json
{
  "type": "string",        // connect/notification/pong
  "message": "string",
  "data": {},
  "timestamp": "number"
}
```

### 12.2 心跳检测
- 客户端发送：
```json
{
  "type": "ping",
  "timestamp": "number"
}
```
- 服务端响应：
```json
{
  "type": "pong",
  "message": "心跳响应",
  "timestamp": "number"
}
```

## 13. 版本更新记录

### v1.0.0 (2025-07-19)
- 初始版本发布
- 完成基础CRUD接口
- 实现AI辅助教学相关接口
- 新增通知系统完整接口
- 新增WebSocket实时通信
- 新增系统监控和统计接口
- 完善权限控制和安全认证

#### 新增接口模块
1. **通知系统接口**：完整的通知管理功能
2. **WebSocket接口**：实时通信支持
3. **统计分析接口**：数据统计和分析
4. **AI增强接口**：智能教学辅助
5. **系统监控接口**：健康检查和状态监控

#### 主要特性
- 支持实时通知推送
- 完整的权限控制体系
- AI智能教学辅助
- 数据统计和分析
- 系统健康监控
- WebSocket长连接通信

---

© 2025 AiEduForge - 技术支持-独立开发-版权虽有：<EMAIL>(有需求可联系)