# AiEduForge 技术文档  <span style="vertical-align:super;">© 2025 AiEduForge</span>


```
    ___     _   ______    __      ______                   
   /   |   (_) / ____/___/ /_  __/ ____/___  _________ ____ 
  / /| |  / / / __/ / __  / / / / /_  / __ \/ ___/ __ `/ _ \
 / ___ | / / / /___/ /_/ / /_/ / __/ / /_/ / /  / /_/ /  __/
/_/  |_|/_/ /_____/\__,_/\__,_/_/    \____/_/   \__, /\________/
```

🚀 AI Education Forge - 智能教育锻造平台 🎓 

✨ 让AI赋能教育，让学习更智能更高效 ✨

🎯 智能助手 | 自动批改 | 个性学习 | 数据洞察 🎯

## 1. 技术栈详解

### 1.1 后端技术栈

- **核心框架**: Spring Boot 3.5.3

  - 提供自动配置、依赖注入等特性
  - 内嵌 Tomcat 服务器
  - 使用 Spring Security 进行安全认证
  - 支持 Java 17+特性

- **持久层**: MyBatis Plus 3.5.5

  - XML 配置方式，提供灵活的 SQL 映射
  - 动态 SQL 支持
  - 内置 CRUD 操作
  - 分页插件支持
  - 代码生成器

- **数据库**: MySQL 8.0

  - InnoDB 引擎
  - UTF8mb4 字符集
  - 优化索引设计
  - 支持 JSON 数据类型

- **安全认证**: JWT + Spring Security

  - JWT Token 认证
  - 角色权限控制
  - 接口权限拦截
  - 密码加密存储

- **实时通信**: WebSocket
  - 基于 Jakarta WebSocket API
  - 实时通知推送
  - 连接状态管理
  - 心跳检测机制

### 1.2 前端技术栈

- **框架**: Vue 3.0 + Vite 5.x

  - 组合式 API
  - Script Setup 语法
  - 响应式系统
  - 快速热重载

- **状态管理**: Pinia

  - 模块化状态管理
  - 持久化存储
  - 开发工具支持

- **路由**: Vue Router 4.x

  - 动态路由配置
  - 权限控制
  - 路由守卫
  - 懒加载支持

- **UI 组件**: Element Plus

  - 响应式布局
  - 主题定制
  - 组件按需加载
  - 国际化支持

- **HTTP 客户端**: Axios
  - 请求拦截器
  - 响应拦截器
  - 错误处理
  - 请求取消

### 1.3 AI 模块

- **AI 服务**: OpenRouter API

  - 使用 Google Gemma 3 模型 (gemma-3n-e2b-it:free)
  - 云端 API 调用，无需本地部署
  - 高性能响应和处理能力
  - 支持复杂提示词工程

- **AI 功能**:
  - 智能内容生成
  - 自动题目生成（多种题型支持）
  - 答案智能分析与评分
  - 学习建议推荐
  - 编程题自动生成与评估

## 2. 项目结构说明

### 2.1 后端结构

```
src/main/java/com/example/aieduforge/
├── common/          # 公共工具类和响应格式
├── config/          # 配置类(安全、数据库、WebSocket等)
├── controller/      # 控制器层
│   ├── admin/       # 管理员相关控制器
│   ├── AuthController.java        # 认证控制器
│   ├── AIController.java          # AI功能控制器
│   ├── NotificationController.java # 通知控制器
│   └── ...          # 其他业务控制器
├── service/         # 服务层
│   ├── impl/        # 服务实现类
│   ├── AIService.java             # AI服务接口
│   ├── NotificationService.java   # 通知服务接口
│   └── ...          # 其他业务服务
├── mapper/          # MyBatis映射接口
├── entity/          # 实体类
├── dto/             # 数据传输对象
├── security/        # 安全相关类
├── websocket/       # WebSocket相关类
├── task/            # 定时任务
├── interceptor/     # 拦截器
├── exception/       # 异常处理
├── event/           # 事件处理
└── utils/           # 工具类
```

### 2.2 前端结构

```
frontend/
├── src/
│   ├── api/         # API接口封装
│   │   ├── auth.js          # 认证相关API
│   │   ├── admin.js         # 管理员API
│   │   ├── teacher.js       # 教师API
│   │   ├── student.js       # 学生API
│   │   ├── ai.js           # AI功能API
│   │   └── notification.js  # 通知API
│   ├── components/  # 公共组件
│   │   └── NotificationBell.vue # 通知铃铛组件
│   ├── layout/      # 布局组件
│   │   ├── AdminLayout.vue      # 管理员布局
│   │   ├── TeacherLayout.vue    # 教师布局
│   │   └── StudentLayout.vue    # 学生布局
│   ├── router/      # 路由配置
│   ├── stores/      # 状态管理
│   ├── styles/      # 样式文件
│   ├── utils/       # 工具类
│   │   ├── request.js       # HTTP请求封装
│   │   ├── auth.js         # 认证工具
│   │   ├── format.js       # 格式化工具
│   │   └── websocket.js    # WebSocket工具
│   └── views/       # 页面组件
│       ├── admin/           # 管理员页面
│       ├── teacher/         # 教师页面
│       ├── student/         # 学生页面
│       ├── auth/           # 认证页面
│       └── error/          # 错误页面
└── public/          # 静态资源
```

### 2.3 数据库结构

```
database/
├── init.sql                    # 初始化脚本
├── notifications.sql           # 通知系统表结构
├── learning_analytics.sql      # 学习分析表结构
├── insert_test_knowledge.sql   # 测试数据
└── ...                        # 其他数据脚本
```

## 3. 环境搭建指南

### 3.1 开发环境要求

- JDK 17+
- Node.js 16+
- MySQL 8.0+
- Redis 6.x+
- Maven 3.8+

### 3.2 后端环境配置

1. **安装 JDK 17**

2. **安装 MySQL**

```bash
# 安装MySQL 8.0
# 创建数据库
CREATE DATABASE aieduforge CHARACTER SET utf8mb4;
```

3. **修改 application.yml 配置**
   注意，这里使用的是 OpenRouter API 调用云端 AI 服务

```yaml
spring:
  datasource:
    url: ******************************************************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
```

```yaml
ai:
  openRouter:
    base-url: https://openrouter.ai/api/v1
    api-key: your_openrouter_api_key
    model-name: google/gemma-3n-e2b-it:free
    timeout: 30000
    max-tokens: 2048
    temperature: 0.7
```

### 3.3 前端环境配置

1. **安装 Node.js**

```bash
# 下载并安装Node.js 16+
# 验证安装
node -v
npm -v
```

2. **项目依赖安装**

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```


## 4. 部署指南

### 4.1 后端部署

1. **打包**

```bash
mvn clean package -DskipTests
```

2. **运行**

```bash
java -jar target/aieduforge-0.0.1-SNAPSHOT.jar
```

### 4.2 前端部署

1. **构建**

```bash
npm run build
```

2. **Nginx 配置**

```nginx
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080;
    }
}
```

## 5. 开发规范

### 5.1 后端开发规范

1. **命名规范**

   - 类名：大驼峰命名
   - 方法名：小驼峰命名
   - 常量：全大写下划线分隔

2. **接口规范**

   - RESTful API 设计
   - 统一响应格式
   - 请求参数验证

3. **代码规范**
   - 使用 Lombok 简化代码
   - 统一异常处理
   - 注释完善

### 5.2 前端开发规范

1. **组件开发规范**

   - 组件名称大驼峰
   - Props 类型声明
   - 组件文档注释

2. **样式规范**

   - BEM 命名规范
   - SCSS 预处理器
   - 主题变量统一管理

3. **状态管理规范**
   - Pinia 模块化
   - TypeScript 类型声明
   - 持久化处理

## 6. 常见问题解决

### 6.1 环境配置问题

1. MySQL 连接失败

```bash
# 检查MySQL服务状态
net start mysql
# 检查端口占用
netstat -ano | findstr "3306"
```

2. Redis 连接问题

```bash
# 检查Redis服务
redis-cli ping
# 应返回PONG
```

### 6.2 开发常见问题

1. 跨域问题

```java
// 后端配置跨域
@Configuration
public class CorsConfig {
    @Bean
    public CorsFilter corsFilter() {
        // ... 配置详情
    }
}
```

2. 前端路由刷新 404

```nginx
# Nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

### 6.3 MyBatis 相关问题

1. MyBatisSystemException 错误

```log
org.mybatis.spring.MyBatisSystemException: null
```

常见原因及解决方案：

- 检查 Mapper 接口与 XML 文件是否对应
- 验证 XML 文件中的 namespace 是否正确
- 确保实体类属性与数据库字段映射正确
- 检查 XML 文件中的 resultType/resultMap 配置

解决步骤：

```bash
# 1. 检查Mapper扫描配置
@MapperScan("com.example.aieduforge.mapper")

# 2. 验证XML文件位置配置
mybatis:
  mapper-locations: classpath:mapper/*.xml

# 3. 开启MyBatis日志，方便排查
logging:
  level:
    com.example.aieduforge.mapper: debug
```

2. 类型转换错误

```java
// 在application.yml中配置类型处理器
mybatis:
  type-handlers-package: com.example.aieduforge.handler

// 自定义类型处理器示例
@MappedTypes(YourEnum.class)
public class YourEnumTypeHandler extends BaseTypeHandler<YourEnum> {
    // 实现类型转换逻辑
}
```

3. 缓存相关问题

```xml
<!-- Mapper XML中配置缓存 -->
<cache
  eviction="LRU"
  flushInterval="60000"
  size="512"
  readOnly="true"/>
```

### 6.4 日志分析和错误排查

1. 日志配置

```yaml
# application.yml
logging:
  level:
    com.example.aieduforge: debug
    org.springframework.security: info
    org.springframework.web: info
    com.baomidou.mybatisplus: debug
    org.springframework.boot.autoconfigure: warn
    org.apache.ibatis: debug
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/aieduforge.log
  logback:
    rollingpolicy:
      max-history: 30
      max-file-size: 10MB
```

2. 常见错误日志分析

```log
# MyBatis执行日志
DEBUG c.e.a.mapper.UserMapper.selectById - ==>  Preparing: select * from sys_user where id = ?

# 事务异常日志
ERROR c.e.a.exception.GlobalExceptionHandler - Transaction rolled back because it has been marked as rollback-only

# 空指针异常
ERROR c.e.a.exception.GlobalExceptionHandler - NullPointerException occurred
```

3. 错误排查工具

```bash
# 查看错误日志
tail -f ./logs/aieduforge.log

# 检索特定错误
grep "ERROR" ./logs/aieduforge.log
```

## 7. 性能优化指南

### 7.1 后端优化

1. 数据库优化

   - 索引设计
   - SQL 语句优化
   - 连接池配置

2. 缓存优化
   - Redis 缓存策略
   - 本地缓存使用
   - 缓存更新机制

### 7.2 前端优化

1. 构建优化

   - 路由懒加载
   - 组件按需加载
   - 图片资源压缩

2. 运行时优化
   - 虚拟列表
   - 防抖节流
   - 内存泄漏处理

## 8. 监控运维

- 使用 Log4j2 配置
- ELK 日志收集
- 日志级别管理

## 9. 安全配置

### 9.1 后端安全

- Spring Security 配置
- JWT Token 验证
- SQL 注入防护

### 9.2 前端安全

- XSS 防护
- CSRF 防护
- 敏感信息加密

## 10. 版本更新记录

### v1.1.0 (2025-07-21)

- 升级 AI 服务从 Ollama 本地部署到 OpenRouter API
- 使用 Google Gemma 3 模型(gemma-3n-e2b-it:free)提升 AI 能力
- 增强编程题生成与解析功能
- 优化题目生成的错误处理和重试机制
- 前端界面交互优化

### v1.0.0 (2025-07-19)

- 初始版本发布
- 基础功能实现
- 核心模块完成

#### 主要功能模块

1. **用户认证系统**

   - JWT Token 认证
   - 角色权限管理(管理员/教师/学生)
   - 用户注册登录

2. **管理员功能**

   - 用户管理(增删改查)
   - 系统统计大屏
   - 资源管理
   - 通知管理

3. **教师功能**

   - 课程管理
   - 教学内容管理
   - AI 辅助教学
   - 作业评分
   - 教学效率分析

4. **学生功能**

   - 课程学习
   - 在线练习
   - 学习记录
   - AI 学习助手

5. **AI 智能功能**

   - 智能内容生成
   - 自动题目生成
   - 答案分析评分
   - 学习建议推荐

6. **通知系统**

   - 实时通知推送
   - WebSocket 长连接
   - 通知分类管理
   - 已读未读状态

7. **数据分析**
   - 学习行为分析
   - 教学效果统计
   - 使用情况监控
   - 个性化报告

## 11. 核心功能模块详解

### 11.1 通知系统架构

- **实时通知推送**: 基于 WebSocket 的长连接通信
- **通知分类管理**: 系统通知、课程通知、作业通知
- **优先级控制**: 普通、重要、紧急三级优先级
- **已读状态管理**: 精确的已读未读状态跟踪
- **定时任务清理**: 自动清理过期通知和旧数据

### 11.2 AI 智能模块

- **云端 API 调用**: 使用 OpenRouter API 调用 Google Gemma 3 模型
- **高性能响应**: 使用 gemma-3n-e2b-it:free 模型提供高质量输出
- **智能内容生成**: 根据教学需求生成个性化内容
- **多类型题目生成**: 支持单选、多选、填空、简答、编程和案例分析等多种题型
- **答案智能分析**: AI 辅助批改和分析学生答案
- **编程题专项处理**: 针对编程题的特殊生成和解析逻辑
- **错误重试机制**: 提供多次重试和补充生成机制，确保题目质量

### 11.3 权限控制系统

- **基于角色的访问控制(RBAC)**: 管理员、教师、学生三种角色
- **接口级权限控制**: 使用 Spring Security 注解控制
- **JWT Token 认证**: 无状态的 Token 认证机制
- **权限拦截器**: 统一的权限验证和拦截

### 11.4 数据统计分析

- **学习行为分析**: 记录和分析用户学习行为
- **教学效果统计**: 教师教学效率和效果分析
- **系统使用监控**: 实时监控系统使用情况
- **个性化报告**: 生成个性化的学习和教学报告

## 12. 系统架构设计

### 12.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 Vue3     │    │   后端 Spring   │    │   数据库 MySQL  │
│   + Element+    │◄──►│   Boot 3.5.3    │◄──►│   + MyBatis+    │
│   + WebSocket   │    │   + Security    │    │   + Redis       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  OpenRouter API │
                       │ gemma-3n-e2b-it │
                       └─────────────────┘
```

### 12.2 数据流架构

```
用户请求 → 前端路由 → API调用 → 后端控制器 → 服务层 → 数据访问层 → 数据库
                                    │
                                    ├─→ AI服务调用
                                    │
                                    └─→ WebSocket推送
```

### 12.3 安全架构

```
请求 → CORS过滤器 → JWT认证过滤器 → 权限验证 → 业务处理
                                    │
                                    ├─→ 角色权限检查
                                    │
                                    └─→ 接口权限验证
```

## 13. 数据库设计

### 13.1 核心表结构

- **sys_user**: 用户基础信息表
- **sys_role**: 角色信息表
- **course**: 课程信息表
- **teaching_content**: 教学内容表
- **exam_question**: 考试题目表
- **practice_record**: 练习记录表
- **learning_analytics**: 学习分析表
- **sys_notification**: 系统通知表
- **user_notification**: 用户通知关联表

### 13.2 索引设计

- 主键索引：所有表的主键自动创建
- 外键索引：关联表的外键字段
- 业务索引：根据查询频率创建的业务索引
- 复合索引：多字段组合查询的复合索引

## 14. 性能优化策略

### 14.1 数据库优化

- **连接池配置**: HikariCP 连接池优化
- **SQL 优化**: 避免 N+1 查询，使用批量操作
- **索引优化**: 合理设计索引，避免过度索引
- **分页优化**: 使用 MyBatis Plus 分页插件

### 14.2 缓存策略

- **本地缓存**: 使用 Caffeine 缓存热点数据
- **分布式缓存**: Redis 缓存用户会话和热点数据
- **查询缓存**: MyBatis 二级缓存配置
- **缓存更新**: 合理的缓存失效和更新策略

### 14.3 前端优化

- **路由懒加载**: 按需加载页面组件
- **组件按需引入**: Element Plus 按需引入
- **资源压缩**: Vite 构建时自动压缩
- **CDN 加速**: 静态资源 CDN 分发

## 15. 监控和运维

### 15.1 日志管理

- **分级日志**: DEBUG、INFO、WARN、ERROR 四级日志
- **日志轮转**: 按日期和大小自动轮转日志文件
- **日志分析**: 支持 ELK 日志分析栈
- **错误追踪**: 详细的错误堆栈信息记录

### 15.2 系统监控

- **健康检查**: 提供系统健康检查接口
- **性能监控**: JVM 性能指标监控
- **业务监控**: 关键业务指标监控
- **告警机制**: 异常情况自动告警

### 15.3 部署运维

- **Docker 容器化**: 支持 Docker 容器部署
- **自动化部署**: CI/CD 自动化部署流程
- **环境隔离**: 开发、测试、生产环境隔离
- **配置管理**: 外部化配置管理

## 16. 安全防护

### 16.1 认证安全

- **密码加密**: BCrypt 密码加密存储
- **Token 安全**: JWT Token 签名和过期控制
- **会话管理**: 安全的会话管理机制
- **登录保护**: 防暴力破解和异常登录检测

### 16.2 数据安全

- **SQL 注入防护**: 使用参数化查询
- **XSS 防护**: 前端输入验证和转义
- **CSRF 防护**: CSRF Token 验证
- **敏感数据加密**: 敏感信息加密存储

### 16.3 接口安全

- **权限控制**: 细粒度的接口权限控制
- **请求限流**: 防止接口被恶意调用
- **参数验证**: 严格的请求参数验证
- **错误处理**: 安全的错误信息返回

## 17. 文档

- 任务书: [任务书.md](%E4%BB%BB%E5%8A%A1%E4%B9%A6.md)
- 产品演示:[产品演示.md](%E4%BA%A7%E5%93%81%E6%BC%94%E7%A4%BA.md)


## 18. AI 服务实现详解

### 18.1 OpenRouter API 集成

AiEduForge 系统已从本地 Ollama 部署升级为使用 OpenRouter API 服务，通过调用 Google 的 Gemma 3 模型(gemma-3n-e2b-it:free)提供 AI 功能。这一升级带来了以下优势：

1. **无需本地部署**：无需维护本地模型和计算资源
2. **更高性能**：云端 API 提供更稳定、更快速的响应
3. **先进模型**：使用 Google Gemma 3 模型提供更高质量的 AI 生成内容
4. **可扩展性**：通过 API 密钥和参数配置轻松调整模型行为

配置示例：

```yaml
ai:
  openRouter:
    base-url: https://openrouter.ai/api/v1
    api-key: ******
    model-name: google/gemma-3n-e2b-it:free
    timeout: 30000
    max-tokens: 2048
    temperature: 0.7
```

### 18.2 增强的题目生成功能

系统现在支持更复杂的题目生成逻辑，特别是针对编程题的处理：

1. **多种题型支持**：

   - 单选题 (SINGLE_CHOICE)
   - 多选题 (MULTIPLE_CHOICE)
   - 填空题 (FILL_BLANK)
   - 简答题 (SHORT_ANSWER)
   - 编程题 (PROGRAMMING)
   - 案例分析题 (CASE_ANALYSIS)

2. **编程题专项处理**：

   - 专用提示词工程，提高代码生成质量
   - 多种解析策略，应对不同格式的响应
   - 代码格式化和清理
   - 相似度检测，避免重复题目

3. **错误处理与重试机制**：
   - 多次尝试生成，确保题目质量
   - 补充生成机制，保证题目数量
   - JSON 解析失败时的备选解析方法
   - 详细日志记录，便于问题排查

### 18.3 AI 服务核心方法

AIServiceImpl 类中实现了以下核心方法：

1. **testConnection**：测试与 OpenRouter API 的连接
2. **generateTeachingContent**：生成教学内容
3. **generateExamQuestions**：生成考试题目（主方法）
4. **generateProgrammingQuestions**：专门处理编程题生成
5. **generateNonProgrammingQuestions**：处理非编程类题目生成
6. **callOpenRouter**：封装对 OpenRouter API 的调用

示例代码片段：

```java
@Override
public String generateTeachingContent(String courseOutline, String chapterName, String contentType) {
    String prompt = String.format("""
        作为一名教育专家，请根据以下课程大纲和章节名称生成详细的教学内容。

        课程大纲：
        %s

        章节名称：%s
        内容类型：%s

        请生成结构化的教学内容，包含知识点讲解、案例分析和练习建议。
        """, courseOutline, chapterName, contentType);

    return callOpenRouter(prompt);
}
```

### 18.4 提示词工程优化

系统使用了先进的提示词工程技术，针对不同类型的题目和内容生成进行了优化：

1. **上下文感知提示**：根据教学内容自动提取关键概念和主题
2. **结构化输出指令**：明确指定输出格式，便于解析
3. **示例引导**：提供示例输出，引导模型生成符合要求的内容
4. **错误处理指导**：在提示中包含常见错误的避免方法
5. **多次尝试策略**：针对复杂生成任务，采用多次尝试和结果合并

示例提示词模板：

```
生成%d道编程题，要求与"%s"相关。

重点：%s
难度：%s
语言：%s

严格按照以下JSON格式输出，代码必须在一行内：

[
    {
        "questionType": "PROGRAMMING",
        "content": "题目描述",
        "correctAnswer": "def function(): return result",
        "answerAnalysis": "解题思路",
        "difficulty": %d
    }
]
```

### 18.5 前端交互优化

前端界面也进行了相应优化，以支持新的 AI 功能：

1. **流式响应处理**：实时显示 AI 生成内容
2. **题目预览与编辑**：教师可以预览和编辑 AI 生成的题目
3. **生成参数调整**：提供界面调整 AI 生成参数（如难度、数量等）
4. **错误反馈机制**：当 AI 生成失败时提供友好的错误提示
5. **批量操作支持**：支持批量生成和管理题目

## 19. 系统性能优化

### 19.1 AI 调用优化

1. **并发请求处理**：

   - 使用 CompletableFuture 处理并发 AI 请求
   - 请求超时控制
   - 结果缓存机制

2. **资源利用优化**：

   - 连接池管理
   - 请求限流
   - 响应压缩

3. **错误处理策略**：
   - 指数退避重试
   - 降级机制
   - 熔断保护

### 19.2 前端性能优化

1. **组件懒加载**：

   - 路由级别懒加载
   - 组件动态导入

2. **数据处理优化**：

   - 本地数据缓存
   - 分页加载
   - 虚拟滚动

3. **渲染性能**：
   - 组件缓存
   - 计算属性优化
   - 避免不必要的渲染

## 20. 未来规划

### 20.1 功能扩展计划

1. **多模型支持**：

   - 支持在多个 AI 模型间切换
   - 针对不同任务使用专门的模型

2. **高级分析功能**：

   - 学生学习行为深度分析
   - 个性化学习路径推荐
   - 教学效果预测

3. **内容协作功能**：
   - 多教师协作编辑课程内容
   - 内容版本控制
   - 教学资源共享平台

### 20.2 技术升级路线

1. **微服务架构**：

   - 服务拆分
   - 容器化部署
   - 服务网格

2. **实时数据处理**：

   - 引入消息队列
   - 实时数据分析
   - 事件驱动架构

3. **多端支持**：
   - 移动端应用
   - 小程序
   - 离线学习支持

---

© 2025 AiEduForge Team - @FengYe[github]  <span style="vertical-align:super;">© 2025 AiEduForge</span>
