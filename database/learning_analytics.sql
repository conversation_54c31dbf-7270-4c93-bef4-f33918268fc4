-- 学习分析记录表
CREATE TABLE IF NOT EXISTS learning_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    content_id BIGINT COMMENT '学习内容ID',
    record_type VARCHAR(20) NOT NULL COMMENT '记录类型：STUDY-学习，PRACTICE-练习，EXAM-考试',
    duration INT NOT NULL DEFAULT 0 COMMENT '学习时长(分钟)',
    score DECIMAL(5,2) COMMENT '得分',
    accuracy DECIMAL(5,2) COMMENT '正确率',
    completion_status VARCHAR(20) NOT NULL DEFAULT 'IN_PROGRESS' COMMENT '完成状态：NOT_STARTED,IN_PROGRESS,COMPLETED',
    details TEXT COMMENT '详细记录',
    feedback TEXT COMMENT 'AI反馈',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY idx_student_course (student_id, course_id),
    KEY idx_create_time (create_time),
    KEY idx_type (record_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学习分析记录表';

-- 学科信息表
CREATE TABLE IF NOT EXISTS subjects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '学科ID',
    subject_name VARCHAR(50) NOT NULL COMMENT '学科名称',
    subject_code VARCHAR(20) NOT NULL COMMENT '学科代码',
    description TEXT COMMENT '学科描述',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_code (subject_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学科信息表';

-- 在course表中添加subject_id字段
-- 先检查字段是否存在，如果不存在则添加
SET @dbname = DATABASE();
SET @tablename = "course";
SET @columnname = "subject_id";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      TABLE_SCHEMA = @dbname
      AND TABLE_NAME = @tablename
      AND COLUMN_NAME = @columnname
  ) > 0,
  "SELECT 1",
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " BIGINT COMMENT '所属学科ID' AFTER id")
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;
