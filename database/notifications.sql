-- 系统通知表
CREATE TABLE IF NOT EXISTS sys_notification (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知ID',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type TINYINT DEFAULT 1 COMMENT '通知类型：1-系统通知，2-课程通知，3-作业通知',
    target_role VARCHAR(20) COMMENT '目标角色：ALL-所有用户，TEACHER-教师，STUDENT-学生，ADMIN-管理员',
    target_user_id BIGINT COMMENT '目标用户ID（为空则按角色发送）',
    priority TINYINT DEFAULT 1 COMMENT '优先级：1-普通，2-重要，3-紧急',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读：1-已读，0-未读',
    read_time DATETIME COMMENT '阅读时间',
    expire_time DATETIME COMMENT '过期时间',
    create_user_id BIGINT NOT NULL COMMENT '创建用户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_target_role (target_role),
    INDEX idx_target_user_id (target_user_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_expire_time (expire_time)
) COMMENT '系统通知表';

-- 用户通知关联表（用于记录用户的通知阅读状态）
CREATE TABLE IF NOT EXISTS user_notification (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    notification_id BIGINT NOT NULL COMMENT '通知ID',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读：1-已读，0-未读',
    read_time DATETIME COMMENT '阅读时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_notification (user_id, notification_id),
    INDEX idx_user_id (user_id),
    INDEX idx_notification_id (notification_id),
    INDEX idx_is_read (is_read)
) COMMENT '用户通知关联表';

-- 插入一些示例通知数据
INSERT INTO sys_notification (title, content, type, target_role, priority, create_user_id) VALUES
('系统维护通知', '系统将于今晚22:00-24:00进行维护升级，期间可能影响正常使用，请提前保存工作内容。', 1, 'ALL', 2, 1),
('新功能上线', 'AI智能助手功能已正式上线，支持智能问答、作业批改等功能，欢迎体验！', 1, 'ALL', 1, 1),
('教师培训通知', '关于AI教学工具使用培训的通知，请各位教师于本周五下午参加线上培训。', 2, 'TEACHER', 2, 1),
('学期考试安排', '期末考试安排已发布，请同学们及时查看考试时间和地点，做好复习准备。', 3, 'STUDENT', 2, 1);

COMMIT;
-- 通知模板表
CREATE TABLE IF NOT EXISTS notification_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    title VARCHAR(200) NOT NULL COMMENT '通知标题模板',
    content TEXT NOT NULL COMMENT '通知内容模板',
    type TINYINT DEFAULT 1 COMMENT '通知类型：1-系统通知，2-课程通知，3-作业通知',
    target_role VARCHAR(20) DEFAULT 'ALL' COMMENT '目标角色：ALL-所有用户，TEACHER-教师，STUDENT-学生，ADMIN-管理员',
    priority TINYINT DEFAULT 1 COMMENT '优先级：1-普通，2-重要，3-紧急',
    description VARCHAR(500) COMMENT '模板描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_user_id BIGINT NOT NULL COMMENT '创建用户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_status (status)
) COMMENT '通知模板表';

-- 插入一些常用的通知模板
INSERT INTO notification_template (name, title, content, type, target_role, priority, description, create_user_id) VALUES
('系统维护通知', '系统维护通知', '系统将于{maintenance_time}进行维护升级，预计维护时间{duration}，期间可能影响正常使用，请提前保存工作内容。', 1, 'ALL', 2, '系统维护时使用的通知模板', 1),
('新功能发布', '新功能上线通知', '{feature_name}功能已正式上线，{feature_description}，欢迎体验！', 1, 'ALL', 1, '新功能发布时使用的通知模板', 1),
('课程开课提醒', '课程开课提醒', '您报名的课程《{course_name}》将于{start_time}开课，请及时参加学习。', 2, 'STUDENT', 2, '课程开课提醒模板', 1),
('作业提交提醒', '作业提交截止提醒', '课程《{course_name}》的作业《{assignment_name}》将于{deadline}截止提交，请尽快完成并提交。', 3, 'STUDENT', 2, '作业提交截止提醒模板', 1),
('教师培训通知', '教师培训通知', '关于{training_topic}的培训将于{training_time}举行，请相关教师准时参加。培训地点：{location}', 2, 'TEACHER', 2, '教师培训通知模板', 1);

COMMIT;