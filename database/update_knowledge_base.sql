-- 更新knowledge_base表，添加缺失的字段
USE aieduforge;

-- 添加文件大小字段
ALTER TABLE knowledge_base ADD COLUMN IF NOT EXISTS file_size BIGINT DEFAULT 0 COMMENT '文件大小（字节）';

-- 添加下载次数字段
ALTER TABLE knowledge_base ADD COLUMN IF NOT EXISTS download_count INT DEFAULT 0 COMMENT '下载次数';

-- 添加描述字段
ALTER TABLE knowledge_base ADD COLUMN IF NOT EXISTS description TEXT COMMENT '文件描述';

-- 更新现有记录的默认值
UPDATE knowledge_base SET file_size = 0 WHERE file_size IS NULL;
UPDATE knowledge_base SET download_count = 0 WHERE download_count IS NULL;
UPDATE knowledge_base SET description = '' WHERE description IS NULL;

COMMIT;