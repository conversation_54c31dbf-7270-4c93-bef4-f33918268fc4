-- 基于开源AI大模型的教学实训智能体软件数据库初始化脚本
-- 数据库：aieduforge
-- 字符集：utf8mb4

-- 创建数据库
CREATE DATABASE IF NOT EXISTS aieduforge DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE aieduforge;

-- ================================
-- 用户权限相关表
-- ================================

-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(50) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_role_code (role_code),
    INDEX idx_status (status)
) COMMENT '角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_type TINYINT DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '路径',
    component VARCHAR(200) COMMENT '组件',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status)
) COMMENT '权限表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id)
) COMMENT '用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
) COMMENT '角色权限关联表';

-- ================================
-- 教学相关表
-- ================================

-- 课程表
CREATE TABLE course (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '课程ID',
    course_name VARCHAR(100) NOT NULL COMMENT '课程名称',
    course_code VARCHAR(50) NOT NULL UNIQUE COMMENT '课程编码',
    description TEXT COMMENT '课程描述',
    subject VARCHAR(50) COMMENT '学科分类',
    teacher_id BIGINT COMMENT '授课教师ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_course_code (course_code),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_subject (subject),
    INDEX idx_status (status)
) COMMENT '课程表';

-- 课程大纲表
CREATE TABLE course_outline (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '大纲ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    chapter_name VARCHAR(100) NOT NULL COMMENT '章节名称',
    chapter_order INT DEFAULT 0 COMMENT '章节顺序',
    content TEXT COMMENT '章节内容',
    learning_objectives TEXT COMMENT '学习目标',
    key_points TEXT COMMENT '重点内容',
    difficulty_points TEXT COMMENT '难点内容',
    time_allocation INT COMMENT '时间分配（分钟）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_course_id (course_id),
    INDEX idx_chapter_order (chapter_order)
) COMMENT '课程大纲表';

-- 知识库文档表
CREATE TABLE knowledge_base (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档ID',
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    content LONGTEXT COMMENT '文档内容',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_type VARCHAR(20) COMMENT '文件类型',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小（字节）',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    description TEXT COMMENT '文件描述',
    course_id BIGINT COMMENT '关联课程ID',
    subject VARCHAR(50) COMMENT '学科分类',
    tags VARCHAR(200) COMMENT '标签（逗号分隔）',
    upload_user_id BIGINT COMMENT '上传用户ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_course_id (course_id),
    INDEX idx_subject (subject),
    INDEX idx_upload_user_id (upload_user_id),
    INDEX idx_status (status),
    FULLTEXT idx_content (title, content)
) COMMENT '知识库文档表';

-- 教学内容表
CREATE TABLE teaching_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '内容ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    chapter_name varchar(100) not null  comment '章-标题',
    outline_id BIGINT COMMENT '大纲ID',
    content_type TINYINT DEFAULT 1 COMMENT '内容类型：1-知识讲解，2-实训练习，3-指导说明',
    title VARCHAR(200) NOT NULL COMMENT '内容标题',
    content LONGTEXT COMMENT '内容详情',
    ai_generated TINYINT DEFAULT 0 COMMENT '是否AI生成：1-是，0-否',
    generation_prompt TEXT COMMENT 'AI生成时的提示词',
    time_allocation INT COMMENT '时间分配（分钟）',
    difficulty_level TINYINT DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
    create_user_id BIGINT COMMENT '创建用户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_course_id (course_id),
    INDEX idx_outline_id (outline_id),
    INDEX idx_content_type (content_type),
    INDEX idx_create_user_id (create_user_id)
) COMMENT '教学内容表';

-- 考核题目表
CREATE TABLE exam_question (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '题目ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    outline_id BIGINT COMMENT '大纲ID',
    question_type TINYINT DEFAULT 1 COMMENT '题目类型：1-选择题，2-填空题，3-简答题，4-编程题，5-综合题',
    title VARCHAR(500) NOT NULL COMMENT '题目标题',
    content LONGTEXT COMMENT '题目内容',
    options TEXT COMMENT '选项（JSON格式）',
    correct_answer TEXT COMMENT '正确答案',
    answer_analysis TEXT COMMENT '答案解析',
    difficulty_level TINYINT DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
    score DECIMAL(5,2) DEFAULT 0 COMMENT '分值',
    knowledge_points VARCHAR(500) COMMENT '知识点（逗号分隔）',
    ai_generated TINYINT DEFAULT 0 COMMENT '是否AI生成：1-是，0-否',
    generation_prompt TEXT COMMENT 'AI生成时的提示词',
    create_user_id BIGINT COMMENT '创建用户ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_course_id (course_id),
    INDEX idx_outline_id (outline_id),
    INDEX idx_question_type (question_type),
    INDEX idx_difficulty_level (difficulty_level),
    INDEX idx_create_user_id (create_user_id),
    INDEX idx_status (status)
) COMMENT '考核题目表';

-- 考核答案表
CREATE TABLE exam_answer (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '答案ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    student_answer TEXT COMMENT '学生答案',
    is_correct TINYINT COMMENT '是否正确：1-正确，0-错误，2-部分正确',
    score DECIMAL(5,2) DEFAULT 0 COMMENT '得分',
    ai_feedback TEXT COMMENT 'AI反馈',
    error_analysis TEXT COMMENT '错误分析',
    improvement_suggestion TEXT COMMENT '改进建议',
    answer_time DATETIME COMMENT '答题时间',
    evaluation_time DATETIME COMMENT '评测时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_question_id (question_id),
    INDEX idx_student_id (student_id),
    INDEX idx_is_correct (is_correct),
    INDEX idx_answer_time (answer_time)
) COMMENT '考核答案表';

-- ================================
-- 学习相关表
-- ================================

-- 学生提问表
CREATE TABLE student_question (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '提问ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    course_id BIGINT COMMENT '课程ID',
    question_content TEXT NOT NULL COMMENT '问题内容',
    question_type TINYINT DEFAULT 1 COMMENT '问题类型：1-知识询问，2-练习求助，3-其他',
    context_info TEXT COMMENT '上下文信息',
    status TINYINT DEFAULT 1 COMMENT '状态：1-待回答，2-已回答，3-已关闭',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_student_id (student_id),
    INDEX idx_course_id (course_id),
    INDEX idx_question_type (question_type),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) COMMENT '学生提问表';

-- AI回答表
CREATE TABLE ai_answer (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '回答ID',
    question_id BIGINT NOT NULL COMMENT '问题ID',
    answer_content LONGTEXT NOT NULL COMMENT '回答内容',
    confidence_score DECIMAL(3,2) COMMENT '置信度分数',
    reference_sources TEXT COMMENT '参考来源',
    ai_model VARCHAR(50) COMMENT 'AI模型名称',
    generation_time INT COMMENT '生成耗时（毫秒）',
    feedback_score TINYINT COMMENT '反馈评分：1-5分',
    feedback_comment TEXT COMMENT '反馈意见',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_question_id (question_id),
    INDEX idx_confidence_score (confidence_score),
    INDEX idx_feedback_score (feedback_score),
    INDEX idx_create_time (create_time)
) COMMENT 'AI回答表';

-- 练习记录表
CREATE TABLE practice_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '练习ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    course_id BIGINT COMMENT '课程ID',
    practice_type TINYINT DEFAULT 1 COMMENT '练习类型：1-随机练习，2-专项练习，3-错题重做',
    question_ids TEXT COMMENT '题目ID列表（逗号分隔）',
    total_questions INT DEFAULT 0 COMMENT '总题数',
    correct_count INT DEFAULT 0 COMMENT '正确题数',
    score DECIMAL(5,2) DEFAULT 0 COMMENT '总分',
    time_spent INT COMMENT '用时（秒）',
    completion_status TINYINT DEFAULT 1 COMMENT '完成状态：1-进行中，2-已完成，3-已放弃',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_student_id (student_id),
    INDEX idx_course_id (course_id),
    INDEX idx_practice_type (practice_type),
    INDEX idx_completion_status (completion_status),
    INDEX idx_start_time (start_time)
) COMMENT '练习记录表';

-- 错误分析表
CREATE TABLE error_analysis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分析ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    error_type TINYINT DEFAULT 1 COMMENT '错误类型：1-知识点不熟，2-理解偏差，3-计算错误，4-其他',
    error_description TEXT COMMENT '错误描述',
    knowledge_point VARCHAR(200) COMMENT '涉及知识点',
    difficulty_level TINYINT COMMENT '难度等级',
    error_frequency INT DEFAULT 1 COMMENT '错误频次',
    last_error_time DATETIME COMMENT '最后错误时间',
    improvement_suggestion TEXT COMMENT '改进建议',
    mastery_status TINYINT DEFAULT 0 COMMENT '掌握状态：0-未掌握，1-部分掌握，2-已掌握',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_student_id (student_id),
    INDEX idx_question_id (question_id),
    INDEX idx_error_type (error_type),
    INDEX idx_knowledge_point (knowledge_point),
    INDEX idx_mastery_status (mastery_status)
) COMMENT '错误分析表';

-- ================================
-- 统计分析表
-- ================================

-- 使用统计表
CREATE TABLE usage_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_type TINYINT NOT NULL COMMENT '用户类型：1-教师，2-学生，3-管理员',
    module_name VARCHAR(50) COMMENT '模块名称',
    action_type VARCHAR(50) COMMENT '操作类型',
    access_count INT DEFAULT 1 COMMENT '访问次数',
    duration_seconds INT COMMENT '使用时长（秒）',
    stat_date DATE COMMENT '统计日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_module_date (user_id, module_name, stat_date),
    INDEX idx_user_id (user_id),
    INDEX idx_user_type (user_type),
    INDEX idx_module_name (module_name),
    INDEX idx_stat_date (stat_date)
) COMMENT '使用统计表';

-- 学习分析表
CREATE TABLE learning_analytics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分析ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    course_id BIGINT COMMENT '课程ID',
    knowledge_point VARCHAR(200) COMMENT '知识点',
    total_questions INT DEFAULT 0 COMMENT '总练习题数',
    correct_questions INT DEFAULT 0 COMMENT '正确题数',
    accuracy_rate DECIMAL(5,2) DEFAULT 0 COMMENT '正确率',
    avg_score DECIMAL(5,2) DEFAULT 0 COMMENT '平均分',
    total_time_spent INT DEFAULT 0 COMMENT '总用时（秒）',
    mastery_level TINYINT DEFAULT 0 COMMENT '掌握程度：0-未掌握，1-初步掌握，2-熟练掌握，3-精通',
    last_practice_time DATETIME COMMENT '最后练习时间',
    stat_date DATE COMMENT '统计日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_student_course_knowledge_date (student_id, course_id, knowledge_point, stat_date),
    INDEX idx_student_id (student_id),
    INDEX idx_course_id (course_id),
    INDEX idx_knowledge_point (knowledge_point),
    INDEX idx_mastery_level (mastery_level),
    INDEX idx_stat_date (stat_date)
) COMMENT '学习分析表';

-- 教学效率表
CREATE TABLE teaching_efficiency (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '效率ID',
    teacher_id BIGINT NOT NULL COMMENT '教师ID',
    course_id BIGINT COMMENT '课程ID',
    preparation_time INT DEFAULT 0 COMMENT '备课时间（分钟）',
    correction_time INT DEFAULT 0 COMMENT '批改时间（分钟）',
    design_time INT DEFAULT 0 COMMENT '设计时间（分钟）',
    student_count INT DEFAULT 0 COMMENT '学生数量',
    avg_student_score DECIMAL(5,2) DEFAULT 0 COMMENT '学生平均分',
    pass_rate DECIMAL(5,2) DEFAULT 0 COMMENT '通过率',
    efficiency_index DECIMAL(5,2) DEFAULT 0 COMMENT '效率指数',
    optimization_suggestion TEXT COMMENT '优化建议',
    stat_date DATE COMMENT '统计日期',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_teacher_course_date (teacher_id, course_id, stat_date),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_course_id (course_id),
    INDEX idx_efficiency_index (efficiency_index),
    INDEX idx_stat_date (stat_date)
) COMMENT '教学效率表';

-- ================================
-- 系统配置表
-- ================================

-- AI配置表
CREATE TABLE ai_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_status (status)
) COMMENT 'AI配置表';

-- ================================
-- 初始化数据
-- ================================

-- 插入角色数据
INSERT INTO sys_role (role_name, role_code, description) VALUES
('管理员', 'ADMIN', '系统管理员，拥有所有权限'),
('教师', 'TEACHER', '教师用户，可以进行备课、出题、查看学情等'),
('学生', 'STUDENT', '学生用户，可以学习、练习、提问等');

-- 插入权限数据
INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, path, component, icon, sort_order) VALUES
-- 系统管理
('系统管理', 'system', 1, 0, '/system', '', 'el-icon-setting', 1),
('用户管理', 'system:user', 1, 1, '/system/user', 'system/user/index', 'el-icon-user', 1),
('角色管理', 'system:role', 1, 1, '/system/role', 'system/role/index', 'el-icon-s-custom', 2),
('权限管理', 'system:permission', 1, 1, '/system/permission', 'system/permission/index', 'el-icon-key', 3),

-- 教师功能
('教师工作台', 'teacher', 1, 0, '/teacher', '', 'el-icon-user-solid', 2),
('课程管理', 'teacher:course', 1, 5, '/teacher/course', 'teacher/course/index', 'el-icon-notebook-1', 1),
('备课设计', 'teacher:prepare', 1, 5, '/teacher/prepare', 'teacher/prepare/index', 'el-icon-edit-outline', 2),
('考核管理', 'teacher:exam', 1, 5, '/teacher/exam', 'teacher/exam/index', 'el-icon-document-checked', 3),
('学情分析', 'teacher:analytics', 1, 5, '/teacher/analytics', 'teacher/analytics/index', 'el-icon-data-analysis', 4),

-- 学生功能
('学生学习', 'student', 1, 0, '/student', '', 'el-icon-reading', 3),
('在线学习', 'student:learn', 1, 10, '/student/learn', 'student/learn/index', 'el-icon-reading', 1),
('练习测试', 'student:practice', 1, 10, '/student/practice', 'student/practice/index', 'el-icon-edit', 2),
('学习记录', 'student:record', 1, 10, '/student/record', 'student/record/index', 'el-icon-document', 3),

-- 资源管理
('资源管理', 'resource', 1, 0, '/resource', '', 'el-icon-folder-opened', 4),
('知识库', 'resource:knowledge', 1, 14, '/resource/knowledge', 'resource/knowledge/index', 'el-icon-collection', 1),
('课件管理', 'resource:courseware', 1, 14, '/resource/courseware', 'resource/courseware/index', 'el-icon-document-copy', 2),

-- 统计分析
('统计分析', 'statistics', 1, 0, '/statistics', '', 'el-icon-data-line', 5),
('使用统计', 'statistics:usage', 1, 17, '/statistics/usage', 'statistics/usage/index', 'el-icon-pie-chart', 1),
('效率分析', 'statistics:efficiency', 1, 17, '/statistics/efficiency', 'statistics/efficiency/index', 'el-icon-trend-charts', 2);

-- 插入管理员用户（密码：admin）
INSERT INTO sys_user (username, password, real_name, email, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO4jOjTMJHi', '系统管理员', '<EMAIL>', 1);

-- 插入示例教师用户（密码：teacher）
INSERT INTO sys_user (username, password, real_name, email, status) VALUES
('teacher001', '$2a$10$DOwjKAUjhXsraq8unyRxUeVPiAx/ce2trQsxM8JmG8NPG6kkO/EFu', '张老师', '<EMAIL>', 1);

-- 插入示例学生用户（密码：student）
INSERT INTO sys_user (username, password, real_name, email, status) VALUES
('student001', '$2a$10$YOsAPzlrgG7QducWnUlzUOWPjAaU9lnzXhQoYvXNzgWl.jXeu6tTi', '李同学', '<EMAIL>', 1);

-- 分配用户角色
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1), -- admin -> 管理员
(2, 2), -- teacher001 -> 教师
(3, 3); -- student001 -> 学生

-- 分配角色权限（管理员拥有所有权限）
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 1, id FROM sys_permission;

-- 教师权限
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(2, 5), (2, 6), (2, 7), (2, 8), (2, 9), -- 教师工作台相关
(2, 14), (2, 15), (2, 16); -- 资源管理相关

-- 学生权限
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(3, 10), (3, 11), (3, 12), (3, 13); -- 学生学习相关

-- 插入示例课程
INSERT INTO course (course_name, course_code, description, subject, teacher_id) VALUES
('Java程序设计', 'CS001', 'Java编程语言基础与应用开发', '计算机科学', 2),
('数据结构与算法', 'CS002', '数据结构原理与算法设计分析', '计算机科学', 2),
('Web前端开发', 'CS003', 'HTML、CSS、JavaScript前端技术', '计算机科学', 2);

-- 插入AI配置
INSERT INTO ai_config (config_key, config_value, config_type, description) VALUES
('ollama.base.url', 'http://localhost:11434', 'ollama', 'Ollama服务基础URL'),
('ollama.model.name', 'gemma2:1b', 'ollama', '使用的AI模型名称'),
('ollama.timeout', '30000', 'ollama', '请求超时时间（毫秒）'),
('ollama.max.tokens', '2048', 'ollama', '最大生成token数'),
('ai.prompt.question.generation', '请根据以下教学内容生成相关的练习题目：', 'prompt', '题目生成提示词'),
('ai.prompt.answer.analysis', '请分析以下学生答案并给出反馈：', 'prompt', '答案分析提示词'),
('ai.prompt.content.generation', '请根据以下课程大纲生成详细的教学内容：', 'prompt', '内容生成提示词');

COMMIT;
-- AI知识库数据表
CREATE TABLE ai_knowledge_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    knowledge_base_id BIGINT NOT NULL COMMENT '关联知识库文件ID',
    extracted_text LONGTEXT COMMENT '提取的文本内容',
    summary TEXT COMMENT 'AI生成的摘要',
    key_points TEXT COMMENT '关键知识点',
    smart_tags VARCHAR(500) COMMENT 'AI生成的智能标签',
    structured_data JSON COMMENT '结构化数据',
    vector_data TEXT COMMENT '向量化数据',
    word_count INT DEFAULT 0 COMMENT '词数统计',
    character_count INT DEFAULT 0 COMMENT '字符数统计',
    difficulty VARCHAR(20) COMMENT '难度等级',
    ai_model VARCHAR(50) COMMENT '使用的AI模型',
    processing_time INT COMMENT '处理耗时（毫秒）',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-失效',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_knowledge_base_id (knowledge_base_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_base(id) ON DELETE CASCADE
) COMMENT 'AI知识库数据表';